version: '3.8'

services:
  frontend:
    build:
      context: ../Frontend
      dockerfile: ../Docker/Dockerfile.frontend
    ports:
      - "3000:3000"
    volumes:
      - ../Frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - app-network
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api

  backend:
    build:
      context: ../Backend
      dockerfile: ../Docker/Dockerfile.backend
    ports:
      - "8000:8000"
    volumes:
      - ../Backend:/app
    depends_on:
      - db
    networks:
      - app-network
    environment:
      - DB_HOST=db
      - DB_PORT=3306
      - DB_NAME=adaptive_learning
      - DB_USERNAME=app_user
      - DB_PASSWORD=app_password

  db:
    image: mysql:8.0
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: always
    networks:
      - app-network
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=adaptive_learning
      - MYSQL_USER=app_user
      - MYSQL_PASSWORD=app_password

  adminer:
    image: adminer:latest
    ports:
      - "8181:8080"
    environment:
      ADMINER_DEFAULT_SERVER: db
    depends_on:
      - db
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  db_data: