import numpy as np
import pandas as pd
import pickle
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.model_selection import train_test_split
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

class PFAKnowledgeTracing:
    """
    Performance Factor Analysis (PFA) Model for Knowledge Tracing
    
    PFA models student performance based on the number of prior successes and failures
    for each skill, using a logistic regression framework.
    
    Expected DataFrame columns:
    - user_id: Student identifier
    - skill_id: Skill identifier 
    - skill_name: Skill name
    - correct: Binary outcome (0/1)
    - order_id: For ordering attempts
    - opportunity: Opportunity number for the skill
    """
    
    def __init__(self, learning_rate=0.01, max_iter=1000, tolerance=1e-6):
        """
        Initialize PFA Knowledge Tracing model
        
        Args:
            learning_rate: Learning rate for optimization
            max_iter: Maximum iterations for optimization
            tolerance: Convergence tolerance
        """
        self.learning_rate = learning_rate
        self.max_iter = max_iter
        self.tolerance = tolerance
        self.is_fitted = False
        
        # Model parameters
        self.beta = {}  # Difficulty parameters for each skill
        self.gamma = {}  # Learning rate from success for each skill
        self.rho = {}   # Learning rate from failure for each skill
        
        # Data storage
        self.skills = None
        self.students = None
        self.skill_to_idx = {}
        self.idx_to_skill = {}
        
    def clean_data(self, data):
        """
        Clean and preprocess the input data
        
        Args:
            data: DataFrame with assistments structure
            
        Returns:
            Cleaned DataFrame
        """
        # Make a copy to avoid modifying original data
        clean_data = data.copy()
        
        # Remove rows with missing critical columns
        required_columns = ['user_id', 'correct', 'order_id']
        clean_data = clean_data.dropna(subset=required_columns)
        
        # Handle skill identification - prefer skill_id, fallback to skill_name
        if 'skill_id' in clean_data.columns:
            # Remove rows with missing skill_id
            clean_data = clean_data.dropna(subset=['skill_id'])
            clean_data['skill'] = clean_data['skill_id'].astype(str)
        elif 'skill_name' in clean_data.columns:
            # Remove rows with missing skill_name
            clean_data = clean_data.dropna(subset=['skill_name'])
            clean_data['skill'] = clean_data['skill_name'].astype(str)
        else:
            raise ValueError("Neither skill_id nor skill_name found in data")
        
        # Ensure correct column is binary
        clean_data['correct'] = clean_data['correct'].astype(int)
        
        # Ensure user_id is string for consistency
        clean_data['user_id'] = clean_data['user_id'].astype(str)
        
        # Sort by user and order_id to ensure proper sequence
        clean_data = clean_data.sort_values(['user_id', 'order_id'])
        
        # Reset index
        clean_data = clean_data.reset_index(drop=True)
        
        print(f"Data cleaned: {len(clean_data)} rows remaining")
        print(f"Unique students: {clean_data['user_id'].nunique()}")
        print(f"Unique skills: {clean_data['skill'].nunique()}")
        
        return clean_data
    
    def _calculate_prior_attempts(self, data):
        """
        Calculate prior success and failure counts for each attempt
        
        Args:
            data: Cleaned DataFrame
            
        Returns:
            DataFrame with additional columns for prior attempts
        """
        result = data.copy()
        result['prior_success'] = 0
        result['prior_failure'] = 0
        
        # Group by student and skill to calculate cumulative counts
        for (student, skill), group in data.groupby(['user_id', 'skill']):
            indices = group.index
            
            # Calculate cumulative success and failure counts (excluding current attempt)
            cumulative_success = group['correct'].cumsum().shift(1, fill_value=0)
            cumulative_failure = ((1 - group['correct']).cumsum()).shift(1, fill_value=0)
            
            result.loc[indices, 'prior_success'] = cumulative_success
            result.loc[indices, 'prior_failure'] = cumulative_failure
        
        return result
    
    def _sigmoid(self, x):
        """Sigmoid activation function with numerical stability"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def _log_likelihood(self, params, X, y):
        """
        Calculate negative log-likelihood for optimization
        
        Args:
            params: Parameter vector [beta, gamma, rho] for all skills
            X: Feature matrix [prior_success, prior_failure, skill_indicators]
            y: Binary outcomes
            
        Returns:
            Negative log-likelihood
        """
        n_skills = len(self.skills)
        
        # Extract parameters
        beta = params[:n_skills]
        gamma = params[n_skills:2*n_skills]
        rho = params[2*n_skills:3*n_skills]
        
        # Calculate predictions
        logits = np.zeros(len(X))
        
        for i, skill_idx in enumerate(X[:, 2].astype(int)):
            s_count = X[i, 0]  # prior success
            f_count = X[i, 1]  # prior failure
            
            logits[i] = beta[skill_idx] + gamma[skill_idx] * s_count - rho[skill_idx] * f_count
        
        # Calculate probabilities
        probs = self._sigmoid(logits)
        
        # Avoid log(0) by clipping probabilities
        probs = np.clip(probs, 1e-15, 1-1e-15)
        
        # Calculate negative log-likelihood
        log_likelihood = np.sum(y * np.log(probs) + (1 - y) * np.log(1 - probs))
        
        return -log_likelihood
    
    def fit(self, data):
        """
        Fit the PFA model to the training data
        
        Args:
            data: DataFrame with assistments structure
        """
        # Clean data
        clean_data = self.clean_data(data)
        
        # Calculate prior attempts
        processed_data = self._calculate_prior_attempts(clean_data)
        
        # Get unique skills and students
        self.skills = sorted(processed_data['skill'].unique())
        self.students = sorted(processed_data['user_id'].unique())
        
        # Create skill mappings
        self.skill_to_idx = {skill: idx for idx, skill in enumerate(self.skills)}
        self.idx_to_skill = {idx: skill for idx, skill in enumerate(self.skills)}
        
        # Prepare features
        X = np.zeros((len(processed_data), 3))
        X[:, 0] = processed_data['prior_success']
        X[:, 1] = processed_data['prior_failure']
        X[:, 2] = processed_data['skill'].map(self.skill_to_idx)
        
        y = processed_data['correct'].values
        
        # Initialize parameters
        n_skills = len(self.skills)
        initial_params = np.concatenate([
            np.random.normal(0, 0.1, n_skills),  # beta (difficulty)
            np.random.normal(0.1, 0.05, n_skills),  # gamma (learning from success)
            np.random.normal(0.1, 0.05, n_skills)   # rho (learning from failure)
        ])
        
        # Add bounds to ensure reasonable parameter values
        bounds = []
        for i in range(n_skills):
            bounds.append((-5, 5))    # beta bounds
        for i in range(n_skills):
            bounds.append((0, 2))     # gamma bounds (positive learning)
        for i in range(n_skills):
            bounds.append((0, 2))     # rho bounds (positive penalty)
        
        # Optimize parameters
        result = minimize(
            self._log_likelihood,
            initial_params,
            args=(X, y),
            method='L-BFGS-B',
            bounds=bounds,
            options={'maxiter': self.max_iter, 'ftol': self.tolerance}
        )
        
        # Extract optimized parameters
        optimized_params = result.x
        
        for i, skill in enumerate(self.skills):
            self.beta[skill] = optimized_params[i]
            self.gamma[skill] = optimized_params[i + n_skills]
            self.rho[skill] = optimized_params[i + 2 * n_skills]
        
        self.is_fitted = True
        print(f"Model fitted successfully. Optimization converged: {result.success}")
        print(f"Final log-likelihood: {-result.fun:.4f}")
        
    def extract_skill_parameters(self):
        """
        Extract and return skill parameters
        
        Returns:
            Dictionary with skill parameters
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before extracting parameters")
        
        parameters = {}
        for skill in self.skills:
            parameters[skill] = {
                'beta': self.beta[skill],      # Difficulty (intercept)
                'gamma': self.gamma[skill],    # Learning from success
                'rho': self.rho[skill]         # Learning from failure
            }
        
        return parameters
    
    def predict(self, data):
        """
        Predict probabilities for new data
        
        Args:
            data: DataFrame with same format as training data
            
        Returns:
            Array of predicted probabilities
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        # Clean and process data
        clean_data = self.clean_data(data)
        processed_data = self._calculate_prior_attempts(clean_data)
        
        # Calculate predictions
        predictions = []
        
        for _, row in processed_data.iterrows():
            skill = row['skill']
            if skill not in self.beta:
                # Use average parameters for unknown skills
                beta_val = np.mean(list(self.beta.values()))
                gamma_val = np.mean(list(self.gamma.values()))
                rho_val = np.mean(list(self.rho.values()))
                print(f"Warning: Unknown skill '{skill}' using average parameters")
            else:
                beta_val = self.beta[skill]
                gamma_val = self.gamma[skill]
                rho_val = self.rho[skill]
            
            s_count = row['prior_success']
            f_count = row['prior_failure']
            
            logit = beta_val + gamma_val * s_count - rho_val * f_count
            prob = self._sigmoid(logit)
            predictions.append(prob)
        
        return np.array(predictions)
    
    def evaluate(self, data):
        """
        Evaluate model performance on test data
        
        Args:
            data: Test DataFrame
            
        Returns:
            Dictionary with evaluation metrics
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before evaluation")
        
        # Get predictions
        predictions = self.predict(data)
        
        # Get actual outcomes
        clean_data = self.clean_data(data)
        actual = clean_data['correct'].values
        
        # Calculate binary predictions
        binary_predictions = (predictions > 0.5).astype(int)
        
        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(actual, binary_predictions),
            'precision': precision_score(actual, binary_predictions, zero_division=0),
            'recall': recall_score(actual, binary_predictions, zero_division=0),
            'f1_score': f1_score(actual, binary_predictions, zero_division=0),
            'auc': roc_auc_score(actual, predictions) if len(np.unique(actual)) > 1 else 0.0,
            'log_loss': -np.mean(actual * np.log(np.clip(predictions, 1e-15, 1-1e-15)) + 
                               (1 - actual) * np.log(np.clip(1 - predictions, 1e-15, 1-1e-15)))
        }
        
        return metrics
    
    def save(self, filepath):
        """
        Save the fitted model to a file
        
        Args:
            filepath: Path to save the model
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before saving")
        
        model_data = {
            'beta': self.beta,
            'gamma': self.gamma,
            'rho': self.rho,
            'skills': self.skills,
            'students': self.students,
            'skill_to_idx': self.skill_to_idx,
            'idx_to_skill': self.idx_to_skill,
            'learning_rate': self.learning_rate,
            'max_iter': self.max_iter,
            'tolerance': self.tolerance,
            'is_fitted': self.is_fitted
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"Model saved to {filepath}")
    
    def load(self, filepath):
        """
        Load a saved model from file
        
        Args:
            filepath: Path to the saved model
        """
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        # Restore model state
        self.beta = model_data['beta']
        self.gamma = model_data['gamma']
        self.rho = model_data['rho']
        self.skills = model_data['skills']
        self.students = model_data['students']
        self.skill_to_idx = model_data['skill_to_idx']
        self.idx_to_skill = model_data['idx_to_skill']
        self.learning_rate = model_data['learning_rate']
        self.max_iter = model_data['max_iter']
        self.tolerance = model_data['tolerance']
        self.is_fitted = model_data['is_fitted']
        
        print(f"Model loaded from {filepath}")
    
    def split_train_test_sets(self, data, test_size=0.2, random_state=42):
        """
        Split data into training and testing sets
        
        Args:
            data: DataFrame to split
            test_size: Proportion of data for testing
            random_state: Random seed for reproducibility
            
        Returns:
            Tuple of (train_data, test_data)
        """
        # Group by student to ensure student data isn't split across train/test
        student_groups = data.groupby('user_id')
        
        # Get unique students
        students = list(student_groups.groups.keys())
        
        # Split students into train and test
        train_students, test_students = train_test_split(
            students, test_size=test_size, random_state=random_state
        )
        
        # Create train and test datasets
        train_data = data[data['user_id'].isin(train_students)].copy()
        test_data = data[data['user_id'].isin(test_students)].copy()
        
        print(f"Data split: {len(train_data)} train rows, {len(test_data)} test rows")
        print(f"Students: {len(train_students)} train, {len(test_students)} test")
        
        return train_data, test_data
    
    def get_student_skill_summary(self, data):
        """
        Get summary statistics for student-skill combinations
        
        Args:
            data: DataFrame with student interaction data
            
        Returns:
            DataFrame with summary statistics
        """
        clean_data = self.clean_data(data)
        
        summary = clean_data.groupby(['user_id', 'skill']).agg({
            'correct': ['count', 'sum', 'mean'],
            'order_id': ['min', 'max']
        }).round(3)
        
        # Flatten column names
        summary.columns = ['_'.join(col).strip() for col in summary.columns.values]
        summary = summary.reset_index()
        
        # Rename columns for clarity
        summary = summary.rename(columns={
            'correct_count': 'total_attempts',
            'correct_sum': 'total_correct',
            'correct_mean': 'success_rate',
            'order_id_min': 'first_attempt_order',
            'order_id_max': 'last_attempt_order'
        })
        
        return summary

def create_sample_assistments_data():
    """Create sample dataset mimicking assistments structure"""
    np.random.seed(42)
    
    data = []
    user_ids = [1001, 1002, 1003]
    skill_ids = [101, 102, 103]
    skill_names = ['Algebra_Basics', 'Fractions_Addition', 'Word_Problems']
    
    order_counter = 1
    
    for user_id in user_ids:
        for skill_idx, (skill_id, skill_name) in enumerate(zip(skill_ids, skill_names)):
            # Each student attempts each skill multiple times
            n_attempts = np.random.randint(3, 6)
            
            for attempt in range(n_attempts):
                # Base probability increases with attempts (learning effect)
                base_prob = 0.3 + 0.15 * attempt + np.random.normal(0, 0.1)
                base_prob = np.clip(base_prob, 0.1, 0.9)
                correct = int(np.random.random() < base_prob)
                
                # Create realistic assistments-like data
                data.append({
                    'order_id': order_counter,
                    'assignment_id': 2001,
                    'user_id': user_id,
                    'assistment_id': 3000 + skill_idx * 10 + attempt,
                    'problem_id': 4000 + skill_idx * 10 + attempt,
                    'original': 1,
                    'correct': correct,
                    'attempt_count': attempt + 1,
                    'ms_first_response': np.random.randint(2000, 30000),
                    'tutor_mode': 'tutor',
                    'answer_type': 'algebra',
                    'sequence_id': 5000 + skill_idx,
                    'student_class_id': 6001,
                    'position': attempt + 1,
                    'type': 'mastery',
                    'base_sequence_id': 5000 + skill_idx,
                    'skill_id': float(skill_id),
                    'skill_name': skill_name,
                    'teacher_id': 7001,
                    'school_id': 8001,
                    'hint_count': np.random.randint(0, 3),
                    'hint_total': np.random.randint(0, 5),
                    'overlap_time': np.random.randint(0, 5000),
                    'template_id': 9000 + skill_idx,
                    'answer_id': float(np.random.randint(10000, 10100)),
                    'answer_text': f'answer_{order_counter}',
                    'first_action': np.random.randint(0, 2),
                    'bottom_hint': float(np.random.randint(0, 2)),
                    'opportunity': attempt + 1,
                    'opportunity_original': float(attempt + 1)
                })
                
                order_counter += 1
    
    return pd.DataFrame(data)
def load_and_explore_data(file_path):
    """
    Load and explore the student dataset
    """
    print("Loading and exploring data...")
    
    # Load data
    df = pd.read_csv(file_path, encoding='latin1')
    
    print(f"Dataset shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Basic statistics
    print("\nBasic Statistics:")
    print(df.describe())
    
    # Check for missing values
    print(f"\nMissing values:\n{df.isnull().sum()}")
    
    # Target variable distribution
    print(f"\nTarget variable distribution:")
    print(df['correct'].value_counts())
    
    # Unique counts
    print(f"\nUnique counts:")
    print(f"Users: {df['user_id'].nunique()}")
    print(f"Skills: {df['skill_id'].nunique()}")
    print(f"Problems: {df['problem_id'].nunique()}")
    
    return df

def main():
    """Main function demonstrating PFA Knowledge Tracing model usage"""
    print("=== Performance Factor Analysis (PFA) Knowledge Tracing Model Demo ===\n")
    
    # Create sample assistments-like data
    print("1. Creating sample assistments dataset...")
    # data = create_sample_assistments_data()
  
    file_path = "/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv"  # Updated to correct file path
    data = load_and_explore_data(file_path)
    print(f"Sample data shape: {data.shape}")
    print(f"Data types:\n{data.dtypes}")
    print("\nSample data (first 10 rows):")
    print(data[['user_id', 'skill_id', 'skill_name', 'correct', 'order_id', 'opportunity']].head(10))

    # Clean data
    
    # Initialize model
    print("\n2. Initializing PFA Knowledge Tracing model...")
    model = PFAKnowledgeTracing(learning_rate=0.01, max_iter=1000, tolerance=1e-6)
        
    # Split data
    print("\n3. Splitting data into train/test sets...")
    train_data, test_data = model.split_train_test_sets(data, test_size=0.3, random_state=42)
    
    # Fit model
    print("\n4. Fitting model...")
    model.fit(train_data)
    
    # Extract parameters
    print("\n5. Extracting skill parameters...")
    parameters = model.extract_skill_parameters()
    for skill, params in parameters.items():
        print(f"Skill ID: {skill}")
        print(f"  Beta (difficulty): {params['beta']:.4f}")
        print(f"  Gamma (learning from success): {params['gamma']:.4f}")
        print(f"  Rho (learning from failure): {params['rho']:.4f}")
    
    # Make predictions
    print("\n6. Making predictions on test data...")
    predictions = model.predict(test_data)
    print(f"Sample predictions: {predictions[:5].round(4)}")
    
    # Evaluate model
    print("\n7. Evaluating model performance...")
    metrics = model.evaluate(test_data)
    for metric, value in metrics.items():
        print(f"{metric}: {value:.4f}")
    
    # Get student-skill summary
    print("\n8. Student-skill summary...")
    summary = model.get_student_skill_summary(data)
    print(summary.head())
    
    # Save model
    print("\n9. Saving model...")
    model.save('pfa_knowledge_tracing_model.pkl')
    
    # Load model (demonstration)
    print("\n10. Loading model...")
    new_model = PFAKnowledgeTracing()
    new_model.load('pfa_knowledge_tracing_model.pkl')
    
    # Verify loaded model works
    print("\n11. Verifying loaded model...")
    new_predictions = new_model.predict(test_data)
    print(f"Predictions match: {np.allclose(predictions, new_predictions)}")
    
    print("\n=== Demo completed successfully! ===")

if __name__ == "__main__":
    main()