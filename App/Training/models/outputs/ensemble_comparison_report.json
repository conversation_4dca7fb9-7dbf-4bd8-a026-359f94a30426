{"timestamp": "2025-07-19T22:10:58.590952", "test_sample_size": "entire_dataset", "models_evaluated": ["bkt", "pfa", "dkt"], "ensemble_methods": ["weighted_average", "stacking", "voting"], "individual_model_metrics": {"bkt": {"accuracy": 0.6065, "precision": 0.7253, "recall": 0.6993, "f1_score": 0.7121, "auc_roc": 0.5663}, "pfa": {"accuracy": 0.6499, "precision": 0.7552, "recall": 0.7352, "f1_score": 0.7451, "auc_roc": 0.6289}, "dkt": {"accuracy": 0.6447, "precision": 0.7566, "recall": 0.7216, "f1_score": 0.7387, "auc_roc": 0.622}}, "ensemble_metrics": {"weighted_average": {"accuracy": 0.6821797076526225, "precision": 0.7384785261812054, "recall": 0.8411518093556929, "f1_score": 0.786478373443641, "auc": 0.6082330286906108}, "stacking": {"accuracy": 0.7125046063137207, "precision": 0.7350882155358343, "recall": 0.9174977934686672, "f1_score": 0.8162259780539034, "auc": 0.6148442089513879}, "voting": {"accuracy": 0.4815133276010318, "precision": 0.7384215305869727, "recall": 0.39472639011473964, "f1_score": 0.5144508670520231, "auc": 0.5374025730864489}}, "best_model": "pfa", "best_ensemble": "stacking", "summary": {"total_models_trained": 3, "total_ensembles_trained": 3, "best_individual_f1": 0.7451, "best_ensemble_f1": 0.8162259780539034}}