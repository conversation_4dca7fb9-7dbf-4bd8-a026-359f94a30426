{"timestamp": "2025-07-19T17:45:47.151191", "test_sample_size": 10000, "models_evaluated": ["bkt", "pfa", "dkt"], "ensemble_methods": ["weighted_average", "stacking", "voting"], "individual_model_metrics": {"bkt": {"accuracy": 0.5889, "precision": 0.5892, "recall": 0.8842, "f1_score": 0.7072, "auc_roc": 0.6079}, "pfa": {"accuracy": 0.4385, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc_roc": 0.5}, "dkt": {"accuracy": 0.4385, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc_roc": 0.5}}, "ensemble_metrics": {"weighted_average": {"accuracy": 0.6848, "precision": 0.6906051694784089, "recall": 0.7946571682991985, "f1_score": 0.7389864193441537, "auc": 0.720604424335776}, "stacking": {"accuracy": 0.5762, "precision": 0.5705791901588929, "recall": 0.991273374888691, "f1_score": 0.7242680546519193, "auc": 0.7242151307125502}, "voting": {"accuracy": 0.5529, "precision": 0.7007017543859649, "recall": 0.35565449688334816, "f1_score": 0.47182516243354994, "auc": 0.5805638504941256}}, "best_model": "bkt", "best_ensemble": "weighted_average", "summary": {"total_models_trained": 3, "total_ensembles_trained": 3, "best_individual_f1": 0.7072, "best_ensemble_f1": 0.7389864193441537}}