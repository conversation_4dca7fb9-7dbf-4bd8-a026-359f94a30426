{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Math Dataset Knowledge Tracing Analysis\n", "\n", "This notebook provides comprehensive analysis of knowledge tracing models trained on the math dataset.\n", "\n", "## Contents:\n", "1. Data Loading and Exploration\n", "2. Exploratory Data Analysis\n", "3. Model Training\n", "4. Model Performance Analysis\n", "5. Per-Skill Performance Analysis\n", "6. Skills List Analysis\n", "7. Visualizations and Insights"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 11:41:16.674932: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ All imports successful!\n", "Notebook started at: 2025-07-17 11:41:20.594017\n"]}], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import json\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Add pipeline to path\n", "sys.path.append('../pipeline')\n", "\n", "# Import pipeline components\n", "from kt_training_pipeline import KnowledgeTracingPipeline\n", "from kt_evaluation import KTEvaluator\n", "from prediction import KTPredictor\n", "\n", "print(\"✅ All imports successful!\")\n", "print(f\"Notebook started at: {datetime.now()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading and Initial Exploration"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading math dataset...\n", "✅ Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 401756 entries, 0 to 401755\n", "Data columns (total 30 columns):\n", " #   Column                Non-Null Count   Dtype  \n", "---  ------                --------------   -----  \n", " 0   order_id              401756 non-null  int64  \n", " 1   assignment_id         401756 non-null  int64  \n", " 2   user_id               401756 non-null  int64  \n", " 3   assistment_id         401756 non-null  int64  \n", " 4   problem_id            401756 non-null  int64  \n", " 5   original              401756 non-null  int64  \n", " 6   correct               401756 non-null  int64  \n", " 7   attempt_count         401756 non-null  int64  \n", " 8   ms_first_response     401756 non-null  int64  \n", " 9   tutor_mode            401756 non-null  object \n", " 10  answer_type           401756 non-null  object \n", " 11  sequence_id           401756 non-null  int64  \n", " 12  student_class_id      401756 non-null  int64  \n", " 13  position              401756 non-null  int64  \n", " 14  type                  401756 non-null  object \n", " 15  base_sequence_id      401756 non-null  int64  \n", " 16  skill_id              338001 non-null  float64\n", " 17  skill_name            325637 non-null  object \n", " 18  teacher_id            401756 non-null  int64  \n", " 19  school_id             401756 non-null  int64  \n", " 20  hint_count            401756 non-null  int64  \n", " 21  hint_total            401756 non-null  int64  \n", " 22  overlap_time          401756 non-null  int64  \n", " 23  template_id           401756 non-null  int64  \n", " 24  answer_id             45454 non-null   float64\n", " 25  answer_text           312548 non-null  object \n", " 26  first_action          401756 non-null  int64  \n", " 27  bottom_hint           67044 non-null   float64\n", " 28  opportunity           401756 non-null  int64  \n", " 29  opportunity_original  328291 non-null  float64\n", "dtypes: float64(4), int64(21), object(5)\n", "memory usage: 92.0+ MB\n"]}], "source": ["# Load the math dataset\n", "data_path = \"/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading math dataset...\")\n", "df = pd.read_csv(data_path, encoding='latin1')\n", "\n", "print(f\"✅ Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "\n", "# Display basic info\n", "df.info()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 5 rows of the dataset:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>assignment_id</th>\n", "      <th>user_id</th>\n", "      <th>assistment_id</th>\n", "      <th>problem_id</th>\n", "      <th>original</th>\n", "      <th>correct</th>\n", "      <th>attempt_count</th>\n", "      <th>ms_first_response</th>\n", "      <th>tutor_mode</th>\n", "      <th>...</th>\n", "      <th>hint_count</th>\n", "      <th>hint_total</th>\n", "      <th>overlap_time</th>\n", "      <th>template_id</th>\n", "      <th>answer_id</th>\n", "      <th>answer_text</th>\n", "      <th>first_action</th>\n", "      <th>bottom_hint</th>\n", "      <th>opportunity</th>\n", "      <th>opportunity_original</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33022537</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33139</td>\n", "      <td>51424</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>32454</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>32454</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33022709</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33150</td>\n", "      <td>51435</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4922</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4922</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35450204</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33159</td>\n", "      <td>51444</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>25390</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>42000</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>88</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>35450295</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33110</td>\n", "      <td>51395</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4859</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4859</td>\n", "      <td>30059</td>\n", "      <td>NaN</td>\n", "      <td>41</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>35450311</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33196</td>\n", "      <td>51481</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>14</td>\n", "      <td>19813</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>124564</td>\n", "      <td>30060</td>\n", "      <td>NaN</td>\n", "      <td>65</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 30 columns</p>\n", "</div>"], "text/plain": ["   order_id  assignment_id  user_id  assistment_id  problem_id  original  \\\n", "0  33022537         277618    64525          33139       51424         1   \n", "1  33022709         277618    64525          33150       51435         1   \n", "2  35450204         220674    70363          33159       51444         1   \n", "3  35450295         220674    70363          33110       51395         1   \n", "4  35450311         220674    70363          33196       51481         1   \n", "\n", "   correct  attempt_count  ms_first_response tutor_mode  ... hint_count  \\\n", "0        1              1              32454      tutor  ...          0   \n", "1        1              1               4922      tutor  ...          0   \n", "2        0              2              25390      tutor  ...          0   \n", "3        1              1               4859      tutor  ...          0   \n", "4        0             14              19813      tutor  ...          3   \n", "\n", "   hint_total  overlap_time  template_id answer_id  answer_text  first_action  \\\n", "0           3         32454        30799       NaN           26             0   \n", "1           3          4922        30799       NaN           55             0   \n", "2           3         42000        30799       NaN           88             0   \n", "3           3          4859        30059       NaN           41             0   \n", "4           4        124564        30060       NaN           65             0   \n", "\n", "  bottom_hint  opportunity  opportunity_original  \n", "0         NaN            1                   1.0  \n", "1         NaN            2                   2.0  \n", "2         NaN            1                   1.0  \n", "3         NaN            2                   2.0  \n", "4         0.0            3                   3.0  \n", "\n", "[5 rows x 30 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display first few rows\n", "print(\"First 5 rows of the dataset:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Overview:\n", "- Total interactions: 401,756\n", "- Unique students: 4,217\n", "- Unique problems: 26,688\n", "- Unique skills: 110\n", "- Overall accuracy: 0.643\n", "- No timestamp data\n"]}], "source": ["# Basic statistics\n", "print(\"Dataset Overview:\")\n", "print(f\"- Total interactions: {len(df):,}\")\n", "print(f\"- Unique students: {df['user_id'].nunique():,}\")\n", "print(f\"- Unique problems: {df['problem_id'].nunique():,}\")\n", "print(f\"- Unique skills: {df['skill_name'].nunique():,}\")\n", "print(f\"- Overall accuracy: {df['correct'].mean():.3f}\")\n", "print(f\"- Date range: {df['start_time'].min()} to {df['start_time'].max()}\" if 'start_time' in df.columns else \"- No timestamp data\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df.columns:\n", "        missing = df[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df.columns]\n", "df_clean = df.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SKILLS ANALYSIS ===\n", "\n", "Top 15 skills by number of attempts:\n", "                                          Total_Attempts  Accuracy  \\\n", "skill_name                                                           \n", "Equation Solving Two or Fewer Steps                24253     0.679   \n", "Conversion of Fraction Decimals Percents           18742     0.637   \n", "Addition and Subtraction Integers                  12741     0.599   \n", "Addition and Subtraction Fractions                 11334     0.677   \n", "Percent Of                                          9497     0.595   \n", "Proportion                                          9054     0.641   \n", "Ordering Fractions                                  8539     0.792   \n", "Equation Solving More Than Two Steps                8115     0.758   \n", "Probability of Two Distinct Events                  7963     0.490   \n", "Finding Percents                                    7694     0.538   \n", "Subtraction Whole Numbers                           7669     0.641   \n", "Probability of a Single Event                       7438     0.742   \n", "Pattern Finding                                     7343     0.600   \n", "Absolute Value                                      7340     0.757   \n", "Ordering Positive Decimals                          7317     0.750   \n", "\n", "                                          Unique_Users  Unique_Problems  \n", "skill_name                                                               \n", "Equation Solving Two or Fewer Steps                961             1040  \n", "Conversion of Fraction Decimals Percents          1225              488  \n", "Addition and Subtraction Integers                 1226              413  \n", "Addition and Subtraction Fractions                1353              433  \n", "Percent Of                                        1115              465  \n", "Proportion                                         756              485  \n", "Ordering Fractions                                 882              464  \n", "Equation Solving More Than Two Steps               412              419  \n", "Probability of Two Distinct Events                 452              339  \n", "Finding Percents                                   771              371  \n", "Subtraction Whole Numbers                          903              242  \n", "Probability of a Single Event                      939              350  \n", "Pattern Finding                                    447              554  \n", "Absolute Value                                    1002              241  \n", "Ordering Positive Decimals                         942              543  \n"]}], "source": ["# Skills list and statistics\n", "print(\"\\n=== SKILLS ANALYSIS ===\")\n", "skill_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skill_stats.columns = ['Total_Attempts', 'Accuracy', 'Unique_Users', 'Unique_Problems']\n", "skill_stats = skill_stats.sort_values('Total_Attempts', ascending=False)\n", "\n", "print(f\"\\nTop 15 skills by number of attempts:\")\n", "print(skill_stats.head(15))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Exploratory Data Analysis"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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", "text/plain": ["<Figure size 2000x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📊 EDA plots saved to App/Training/plots/math/eda_analysis.png\n"]}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create comprehensive EDA plots\n", "fig, axes = plt.subplots(2, 3, figsize=(20, 12))\n", "fig.suptitle('Math Dataset Exploratory Data Analysis', fontsize=16)\n", "\n", "# 1. Skill frequency distribution\n", "skill_counts = df_clean['skill_name'].value_counts().head(20)\n", "axes[0, 0].barh(range(len(skill_counts)), skill_counts.values)\n", "axes[0, 0].set_yticks(range(len(skill_counts)))\n", "axes[0, 0].set_yticklabels(skill_counts.index, fontsize=8)\n", "axes[0, 0].set_title('Top 20 Skills by Frequency')\n", "axes[0, 0].set_xlabel('Number of Interactions')\n", "\n", "# 2. Accuracy distribution by skill\n", "skill_accuracy = df_clean.groupby('skill_name')['correct'].mean().sort_values(ascending=False).head(20)\n", "axes[0, 1].barh(range(len(skill_accuracy)), skill_accuracy.values)\n", "axes[0, 1].set_yticks(range(len(skill_accuracy)))\n", "axes[0, 1].set_yticklabels(skill_accuracy.index, fontsize=8)\n", "axes[0, 1].set_title('Top 20 Skills by Accuracy')\n", "axes[0, 1].set_xlabel('Accuracy Rate')\n", "\n", "# 3. Student sequence length distribution\n", "seq_lengths = df_clean.groupby('user_id').size()\n", "axes[0, 2].hist(seq_lengths, bins=50, alpha=0.7, edgecolor='black')\n", "axes[0, 2].set_title('Distribution of Student Sequence Lengths')\n", "axes[0, 2].set_xlabel('Number of Interactions per Student')\n", "axes[0, 2].set_ylabel('Number of Students')\n", "\n", "# 4. Overall accuracy distribution\n", "axes[1, 0].hist(df_clean['correct'], bins=2, alpha=0.7, edgecolor='black')\n", "axes[1, 0].set_title('Overall Accuracy Distribution')\n", "axes[1, 0].set_xlabel('Correct (0=Incorrect, 1=Correct)')\n", "axes[1, 0].set_ylabel('Number of Interactions')\n", "axes[1, 0].set_xticks([0, 1])\n", "\n", "# 5. Student accuracy distribution\n", "student_accuracy = df_clean.groupby('user_id')['correct'].mean()\n", "axes[1, 1].hist(student_accuracy, bins=30, alpha=0.7, edgecolor='black')\n", "axes[1, 1].set_title('Student Accuracy Distribution')\n", "axes[1, 1].set_xlabel('Student Accuracy Rate')\n", "axes[1, 1].set_ylabel('Number of Students')\n", "\n", "# 6. Problem difficulty distribution\n", "problem_difficulty = df_clean.groupby('problem_id')['correct'].mean()\n", "axes[1, 2].hist(problem_difficulty, bins=30, alpha=0.7, edgecolor='black')\n", "axes[1, 2].set_title('Problem Difficulty Distribution')\n", "axes[1, 2].set_xlabel('Problem Accuracy Rate (1-difficulty)')\n", "axes[1, 2].set_ylabel('Number of Problems')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Save the plot\n", "os.makedirs('App/Training/plots/math', exist_ok=True)\n", "plt.savefig('App/Training/plots/math/eda_analysis.png', dpi=300, bbox_inches='tight')\n", "print(\"📊 EDA plots saved to App/Training/plots/math/eda_analysis.png\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize skill distribution\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Top 20 skills by attempts\n", "top_skills = skill_stats.head(20)\n", "axes[0,0].barh(range(len(top_skills)), top_skills['Total_Attempts'])\n", "axes[0,0].set_yticks(range(len(top_skills)))\n", "axes[0,0].set_yticklabels(top_skills.index, fontsize=8)\n", "axes[0,0].set_xlabel('Number of Attempts')\n", "axes[0,0].set_title('Top 20 Skills by Attempts')\n", "axes[0,0].invert_yaxis()\n", "\n", "# Accuracy distribution\n", "axes[0,1].hist(skill_stats['Accuracy'], bins=20, alpha=0.7)\n", "axes[0,1].set_xlabel('Accuracy')\n", "axes[0,1].set_ylabel('Number of Skills')\n", "axes[0,1].set_title('Distribution of Skill Accuracy')\n", "\n", "# User performance distribution\n", "user_accuracy = df_clean.groupby('user_id')['correct'].mean()\n", "axes[1,0].hist(user_accuracy, bins=30, alpha=0.7)\n", "axes[1,0].set_xlabel('User Accuracy')\n", "axes[1,0].set_ylabel('Number of Users')\n", "axes[1,0].set_title('Distribution of User Accuracy')\n", "\n", "# Attempts per user\n", "user_attempts = df_clean.groupby('user_id').size()\n", "axes[1,1].hist(user_attempts, bins=50, alpha=0.7)\n", "axes[1,1].set_xlabel('Number of Attempts')\n", "axes[1,1].set_ylabel('Number of Users')\n", "axes[1,1].set_title('Distribution of Attempts per User')\n", "axes[1,1].set_xlim(0, user_attempts.quantile(0.95))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Skills List Analysis"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COMPREHENSIVE SKILLS ANALYSIS\n", "==================================================\n", "Total number of unique skills: 111\n", "\n", "Top 15 skills by number of interactions:\n", "                                          interactions  accuracy  accuracy_std  unique_students  unique_problems\n", "skill_name                                                                                                      \n", "Equation Solving Two or Fewer Steps              24253     0.679         0.467              961             1040\n", "Conversion of Fraction Decimals Percents         18742     0.637         0.481             1225              488\n", "Addition and Subtraction Integers                12741     0.599         0.490             1226              413\n", "Addition and Subtraction Fractions               11334     0.677         0.468             1353              433\n", "Percent Of                                        9497     0.595         0.491             1115              465\n", "Proportion                                        9054     0.641         0.480              756              485\n", "Ordering Fractions                                8539     0.792         0.406              882              464\n", "Equation Solving More Than Two Steps              8115     0.758         0.428              412              419\n", "Probability of Two Distinct Events                7963     0.490         0.500              452              339\n", "Finding Percents                                  7694     0.538         0.499              771              371\n", "Subtraction Whole Numbers                         7669     0.641         0.480              903              242\n", "Probability of a Single Event                     7438     0.742         0.437              939              350\n", "Pattern Finding                                   7343     0.600         0.490              447              554\n", "Absolute Value                                    7340     0.757         0.429             1002              241\n", "Ordering Positive Decimals                        7317     0.750         0.433              942              543\n", "\n", "Top 15 easiest skills (highest accuracy):\n", "                                              interactions  accuracy  unique_students\n", "skill_name                                                                           \n", "Nets of 3D Figures                                     280     0.950              229\n", "Area Parallelogram                                     115     0.922               95\n", "Congruence                                             587     0.894              364\n", "Distributive Property                                   18     0.889                5\n", "Mode                                                  1926     0.876              572\n", "Scatter Plot                                          1859     0.869              354\n", "Area Rectangle                                         495     0.863              215\n", "Area Triangle                                          286     0.857              168\n", "D.4.8-understanding-concept-of-probabilities           456     0.846              202\n", "Volume Rectangular Prism                               926     0.840              345\n", "Fraction Of                                            607     0.830              288\n", "Write Linear Equation from Situation                  1447     0.822              223\n", "Linear Equations                                        89     0.820               41\n", "Slope                                                   89     0.820               41\n", "Choose an Equation from Given Information               89     0.820               41\n", "\n", "Top 15 hardest skills (lowest accuracy):\n", "                                               interactions  accuracy  unique_students\n", "skill_name                                                                            \n", "Reading a Ruler or Scale                                  5     0.000                5\n", "Quadratic Formula to Solve Quadratic Equation            32     0.125               14\n", "Rotations                                               427     0.136              163\n", "Computation with Real Numbers                            21     0.190               21\n", "Solving Systems of Linear Equations                     234     0.192               22\n", "Percent Discount                                         47     0.234               29\n", "Surface Area Cylinder                                   491     0.316              135\n", "Finding Slope From Situation                              9     0.333                2\n", "Percents                                                117     0.333               41\n", "Algebraic Solving                                       389     0.368               88\n", "Reflection                                              459     0.373              176\n", "Rate                                                     91     0.374               39\n", "Algebraic Simplification                                 90     0.400               15\n", "Finding <PERSON><PERSON><PERSON> from Ordered Pairs                          5     0.400                2\n", "Multiplication Whole Numbers                            110     0.436               45\n"]}], "source": ["# Comprehensive skills analysis\n", "print(\"📋 COMPREHENSIVE SKILLS ANALYSIS\")\n", "print(\"=\"*50)\n", "\n", "# Get all unique skills\n", "all_skills = df['skill_name'].unique()\n", "print(f\"Total number of unique skills: {len(all_skills)}\")\n", "\n", "# Skills statistics\n", "skills_stats = df.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean', 'std'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skills_stats.columns = ['interactions', 'accuracy', 'accuracy_std', 'unique_students', 'unique_problems']\n", "skills_stats = skills_stats.sort_values('interactions', ascending=False)\n", "\n", "print(\"\\nTop 15 skills by number of interactions:\")\n", "print(skills_stats.head(15).to_string())\n", "\n", "print(\"\\nTop 15 easiest skills (highest accuracy):\")\n", "easiest_skills = skills_stats.sort_values('accuracy', ascending=False).head(15)\n", "print(easiest_skills[['interactions', 'accuracy', 'unique_students']].to_string())\n", "\n", "print(\"\\nTop 15 hardest skills (lowest accuracy):\")\n", "hardest_skills = skills_stats.sort_values('accuracy', ascending=True).head(15)\n", "print(hardest_skills[['interactions', 'accuracy', 'unique_students']].to_string())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 SKILLS CATEGORIZATION\n", "==============================\n", "Easy skills (≥80% accuracy): 17 skills\n", "Medium skills (60-80% accuracy): 54 skills\n", "Hard skills (<60% accuracy): 39 skills\n", "\n", "High frequency skills (≥1000 interactions): 63 skills\n", "Medium frequency skills (100-1000 interactions): 29 skills\n", "Low frequency skills (<100 interactions): 18 skills\n", "\n", "💾 Skills analysis saved to App/Training/reports/math/skills_analysis.csv\n"]}], "source": ["# Skills categorization analysis\n", "print(\"\\n📊 SKILLS CATEGORIZATION\")\n", "print(\"=\"*30)\n", "\n", "# Categorize skills by difficulty\n", "easy_skills = skills_stats[skills_stats['accuracy'] >= 0.8]\n", "medium_skills = skills_stats[(skills_stats['accuracy'] >= 0.6) & (skills_stats['accuracy'] < 0.8)]\n", "hard_skills = skills_stats[skills_stats['accuracy'] < 0.6]\n", "\n", "print(f\"Easy skills (≥80% accuracy): {len(easy_skills)} skills\")\n", "print(f\"Medium skills (60-80% accuracy): {len(medium_skills)} skills\")\n", "print(f\"Hard skills (<60% accuracy): {len(hard_skills)} skills\")\n", "\n", "# Categorize by frequency\n", "high_freq = skills_stats[skills_stats['interactions'] >= 1000]\n", "medium_freq = skills_stats[(skills_stats['interactions'] >= 100) & (skills_stats['interactions'] < 1000)]\n", "low_freq = skills_stats[skills_stats['interactions'] < 100]\n", "\n", "print(f\"\\nHigh frequency skills (≥1000 interactions): {len(high_freq)} skills\")\n", "print(f\"Medium frequency skills (100-1000 interactions): {len(medium_freq)} skills\")\n", "print(f\"Low frequency skills (<100 interactions): {len(low_freq)} skills\")\n", "\n", "# Save skills analysis\n", "os.makedirs('App/Training/reports/math', exist_ok=True)\n", "skills_stats.to_csv('App/Training/reports/math/skills_analysis.csv')\n", "print(\"\\n💾 Skills analysis saved to App/Training/reports/math/skills_analysis.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Model Training"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 00:32:45,184 - INFO - Exploring dataset...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 INITIALIZING <PERSON><PERSON><PERSON>LEDGE TRACING PIPELINE\n", "==================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-17 00:32:45,283 - INFO - Dataset exploration completed\n", "2025-07-17 00:32:45,284 - INFO -   total_interactions: 401756\n", "2025-07-17 00:32:45,285 - INFO -   unique_students: 4217\n", "2025-07-17 00:32:45,286 - INFO -   unique_problems: 26688\n", "2025-07-17 00:32:45,287 - INFO -   unique_skills: 110\n", "2025-07-17 00:32:45,287 - INFO -   overall_accuracy: 0.6429225699180597\n", "2025-07-17 00:32:45,288 - INFO -   correct_responses: 258298\n", "2025-07-17 00:32:45,289 - INFO -   incorrect_responses: 143458\n", "2025-07-17 00:32:45,289 - INFO -   avg_sequence_length: 95.27057149632441\n", "2025-07-17 00:32:45,292 - INFO -   max_sequence_length: 1606\n", "2025-07-17 00:32:45,292 - INFO -   min_sequence_length: 1\n", "2025-07-17 00:32:45,293 - INFO -   most_common_skill: Equation Solving Two or Fewer Steps\n", "2025-07-17 00:32:45,294 - INFO -   most_common_skill_count: 24253\n", "2025-07-17 00:32:45,295 - INFO -   least_common_skill: Finding Slope from Ordered Pairs\n", "2025-07-17 00:32:45,297 - INFO -   least_common_skill_count: 5\n", "2025-07-17 00:32:45,298 - INFO - Preprocessing data...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🔧 Preprocessing data...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-17 00:32:45,943 - INFO - Data preprocessing completed\n", "2025-07-17 00:32:45,944 - INFO - Original size: 401756, Cleaned size: 325637\n", "2025-07-17 00:32:45,944 - INFO - Removed 76119 rows\n", "2025-07-17 00:32:45,954 - INFO - Splitting data (test_size=0.2)...\n", "2025-07-17 00:32:46,120 - INFO - Train set: 259776 interactions from 3321 users\n", "2025-07-17 00:32:46,121 - INFO - Test set: 65861 interactions from 830 users\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📊 Splitting data...\n", "\n", "✅ Data preparation completed:\n", "- Training set: 259,776 interactions\n", "- Test set: 65,861 interactions\n", "- Training users: 3,321\n", "- Test users: 830\n"]}], "source": ["# Initialize the knowledge tracing pipeline\n", "print(\"🚀 INITIALIZING K<PERSON><PERSON>LEDGE TRACING PIPELINE\")\n", "print(\"=\"*50)\n", "\n", "pipeline = KnowledgeTracingPipeline()\n", "\n", "# Explore dataset\n", "dataset_stats = pipeline.explore_dataset(df)\n", "\n", "# Preprocess data\n", "print(\"\\n🔧 Preprocessing data...\")\n", "df_clean = pipeline.preprocess_data(df)\n", "\n", "# Split data\n", "print(\"📊 Splitting data...\")\n", "train_df, test_df = pipeline.split_data(df_clean, test_size=0.2)\n", "\n", "print(f\"\\n✅ Data preparation completed:\")\n", "print(f\"- Training set: {len(train_df):,} interactions\")\n", "print(f\"- Test set: {len(test_df):,} interactions\")\n", "print(f\"- Training users: {train_df['user_id'].nunique():,}\")\n", "print(f\"- Test users: {test_df['user_id'].nunique():,}\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 00:32:46,159 - INFO - Engineering features for knowledge tracing...\n", "2025-07-17 00:32:46,161 - INFO - Creating features for knowledge tracing models...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["⚙️ Engineering features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-17 00:32:47,371 - INFO - Feature engineering completed\n", "2025-07-17 00:32:47,372 - INFO - Creating features for knowledge tracing models...\n", "2025-07-17 00:32:47,734 - INFO - Feature engineering completed\n", "2025-07-17 00:32:47,735 - INFO - Feature engineering completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Feature engineering completed\n"]}], "source": ["# Store datasets in pipeline\n", "pipeline.datasets = {\n", "    'raw': df,\n", "    'clean': df_clean,\n", "    'train': train_df,\n", "    'test': test_df,\n", "    'stats': dataset_stats\n", "}\n", "\n", "# Feature engineering\n", "print(\"⚙️ Engineering features...\")\n", "train_features, test_features = pipeline.engineer_features(train_df, test_df)\n", "print(\"✅ Feature engineering completed\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# # Train models\n", "# print(\"\\n🎯 TRAINING K<PERSON><PERSON>LEDGE TRACING MODELS\")\n", "# print(\"=\"*50)\n", "\n", "# try:\n", "#     # Train models with progress tracking\n", "#     models, metrics = pipeline.train_models(train_df, test_df, models_to_train=['bkt'])\n", "#     print(\"✅ Individual models training completed\")\n", "    \n", "#     # # Create ensemble\n", "#     # print(\"\\n🔗 Creating ensemble model...\")\n", "#     # ensemble, ensemble_metrics = pipeline.create_ensemble(train_df, test_df)\n", "#     # print(\"✅ Ensemble model created\")\n", "    \n", "#     # Save models\n", "#     print(\"\\n💾 Saving models...\")\n", "#     os.makedirs('App/Training/pipeline/outputs/math', exist_ok=True)\n", "#     pipeline.save_models('App/Training/pipeline/outputs/math')\n", "#     print(\"✅ Models saved successfully\")\n", "    \n", "# except Exception as e:\n", "#     print(f\"❌ Error during model training: {e}\")\n", "#     print(\"Continuing with available models...\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 00:32:56,274 - INFO - Training knowledge tracing models...\n", "2025-07-17 00:32:56,276 - INFO - Training PFA model...\n", "2025-07-17 00:32:56,278 - INFO - Training PFA model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 TRAINING <PERSON><PERSON><PERSON><PERSON>D<PERSON> TRACING MODELS\n", "==================================================\n", "Creating PFA features...\n", "Training PFA model...\n", "Model training completed!\n", "Creating PFA features...\n", "❌ Error during model training: The feature names should match those that were passed during fit.\n", "Feature names unseen at fit time:\n", "- skill_2.0\n", "- skill_75.0\n", "Feature names seen at fit time, yet now missing:\n", "- skill_11.0\n", "- skill_13.0\n", "\n", "Continuing with available models...\n"]}], "source": ["# Train models\n", "print(\"\\n🎯 TRAINING K<PERSON><PERSON>LED<PERSON> TRACING MODELS\")\n", "print(\"=\"*50)\n", "\n", "try:\n", "    # Train models with progress tracking\n", "    models, metrics = pipeline.train_models(train_df, test_df, models_to_train=['pfa'])\n", "    print(\"✅ Individual models training completed\")\n", "    \n", "    # # Create ensemble\n", "    # print(\"\\n🔗 Creating ensemble model...\")\n", "    # ensemble, ensemble_metrics = pipeline.create_ensemble(train_df, test_df)\n", "    # print(\"✅ Ensemble model created\")\n", "    \n", "    # Save models\n", "    print(\"\\n💾 Saving models...\")\n", "    os.makedirs('App/Training/pipeline/outputs/math', exist_ok=True)\n", "    pipeline.save_models('App/Training/pipeline/outputs/math')\n", "    print(\"✅ Models saved successfully\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error during model training: {e}\")\n", "    print(\"Continuing with available models...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Model Performance Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate model performance\n", "print(\"📈 MODEL PERFORMANCE EVALUATION\")\n", "print(\"=\"*40)\n", "\n", "evaluator = KTEvaluator(output_dir='App/Training/plots/math')\n", "\n", "# Get available models\n", "available_models = {}\n", "if hasattr(pipeline, 'models') and pipeline.models:\n", "    available_models.update(pipeline.models)\n", "if hasattr(pipeline, 'ensemble') and pipeline.ensemble:\n", "    available_models['ensemble'] = pipeline.ensemble\n", "\n", "if available_models:\n", "    # Evaluate all models\n", "    performance_results = evaluator.evaluate_model_performance(available_models, test_df)\n", "    \n", "    # Display results\n", "    print(\"\\nModel Performance Results:\")\n", "    print(\"-\" * 50)\n", "    \n", "    metrics_to_show = ['accuracy', 'precision', 'recall', 'f1_score', 'auc']\n", "    \n", "    for model_name, results in performance_results.items():\n", "        if 'error' not in results:\n", "            print(f\"\\n{model_name.upper()} Model:\")\n", "            for metric in metrics_to_show:\n", "                if metric in results:\n", "                    print(f\"  {metric:12}: {results[metric]:.4f}\")\n", "        else:\n", "            print(f\"\\n{model_name.upper()} Model: Error - {results['error']}\")\n", "    \n", "    # Save performance results\n", "    with open('App/Training/reports/math/model_performance.json', 'w') as f:\n", "        json.dump(performance_results, f, indent=2)\n", "    print(\"\\n💾 Performance results saved to App/Training/reports/math/model_performance.json\")\n", "    \n", "else:\n", "    print(\"⚠️ No trained models available for evaluation\")\n", "    performance_results = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create model comparison visualization\n", "if performance_results and len(performance_results) > 1:\n", "    print(\"\\n📊 Creating model comparison visualization...\")\n", "    \n", "    # Prepare data for plotting\n", "    models = []\n", "    accuracies = []\n", "    precisions = []\n", "    recalls = []\n", "    f1_scores = []\n", "    aucs = []\n", "    \n", "    for model_name, results in performance_results.items():\n", "        if 'error' not in results:\n", "            models.append(model_name)\n", "            accuracies.append(results.get('accuracy', 0))\n", "            precisions.append(results.get('precision', 0))\n", "            recalls.append(results.get('recall', 0))\n", "            f1_scores.append(results.get('f1_score', 0))\n", "            aucs.append(results.get('auc', 0))\n", "    \n", "    if models:\n", "        # Create comparison plot\n", "        fig, ax = plt.subplots(figsize=(12, 8))\n", "        \n", "        x = np.arange(len(models))\n", "        width = 0.15\n", "        \n", "        ax.bar(x - 2*width, accuracies, width, label='Accuracy', alpha=0.8)\n", "        ax.bar(x - width, precisions, width, label='Precision', alpha=0.8)\n", "        ax.bar(x, recalls, width, label='Recall', alpha=0.8)\n", "        ax.bar(x + width, f1_scores, width, label='F1-Score', alpha=0.8)\n", "        ax.bar(x + 2*width, aucs, width, label='AUC', alpha=0.8)\n", "        \n", "        ax.set_xlabel('Models')\n", "        ax.set_ylabel('Score')\n", "        ax.set_title('Model Performance Comparison on Math Dataset')\n", "        ax.set_xticks(x)\n", "        ax.set_xticklabels(models)\n", "        ax.legend()\n", "        ax.grid(True, alpha=0.3)\n", "        ax.set_ylim(0, 1)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Save the plot\n", "        plt.savefig('App/Training/plots/math/model_comparison.png', dpi=300, bbox_inches='tight')\n", "        print(\"📊 Model comparison plot saved to App/Training/plots/math/model_comparison.png\")\n", "else:\n", "    print(\"⚠️ Insufficient models for comparison visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Per-Skill Performance Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Per-skill performance analysis\n", "if available_models:\n", "    print(\"🎯 PER-SKILL PERFORMANCE ANALYSIS\")\n", "    print(\"=\"*40)\n", "    \n", "    try:\n", "        skill_results = evaluator.generate_skill_performance_report(available_models, test_df)\n", "        \n", "        if skill_results:\n", "            print(f\"\\nAnalyzed {len(skill_results)} skills with sufficient data\")\n", "            \n", "            # Show top performing skills for each model\n", "            for model_name in available_models.keys():\n", "                print(f\"\\n{model_name.upper()} - Top 5 skills by accuracy:\")\n", "                skill_accs = []\n", "                for skill, metrics in skill_results.items():\n", "                    if model_name in metrics and 'accuracy' in metrics[model_name]:\n", "                        skill_accs.append((skill, metrics[model_name]['accuracy']))\n", "                \n", "                skill_accs.sort(key=lambda x: x[1], reverse=True)\n", "                for i, (skill, acc) in enumerate(skill_accs[:5]):\n", "                    print(f\"  {i+1}. {skill}: {acc:.3f}\")\n", "            \n", "            # Save skill performance results\n", "            with open('App/Training/reports/math/skill_performance.json', 'w') as f:\n", "                json.dump(skill_results, f, indent=2)\n", "            print(\"\\n💾 Skill performance results saved to App/Training/reports/math/skill_performance.json\")\n", "        else:\n", "            print(\"⚠️ No skill-specific results generated\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error in skill performance analysis: {e}\")\n", "else:\n", "    print(\"⚠️ No models available for skill performance analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. <PERSON><PERSON><PERSON> and Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"📋 TRAINING SUMMARY AND INSIGHTS\")\n", "print(\"=\"*50)\n", "\n", "print(f\"\\n📊 Dataset Summary:\")\n", "print(f\"- Total interactions: {len(df):,}\")\n", "print(f\"- Unique students: {df['user_id'].nunique():,}\")\n", "print(f\"- Unique skills: {df['skill_name'].nunique():,}\")\n", "print(f\"- Overall accuracy: {df['correct'].mean():.3f}\")\n", "\n", "print(f\"\\n🎯 Model Training Results:\")\n", "if available_models:\n", "    print(f\"- Successfully trained {len(available_models)} models\")\n", "    print(f\"- Models: {', '.join(available_models.keys())}\")\n", "    \n", "    if performance_results:\n", "        # Find best performing model\n", "        best_model = None\n", "        best_accuracy = 0\n", "        for model_name, results in performance_results.items():\n", "            if 'error' not in results and 'accuracy' in results:\n", "                if results['accuracy'] > best_accuracy:\n", "                    best_accuracy = results['accuracy']\n", "                    best_model = model_name\n", "        \n", "        if best_model:\n", "            print(f\"- Best performing model: {best_model} (accuracy: {best_accuracy:.3f})\")\n", "else:\n", "    print(\"- No models were successfully trained\")\n", "\n", "print(f\"\\n📁 Output Files Generated:\")\n", "print(f\"- Models: App/Training/pipeline/outputs/math/\")\n", "print(f\"- Reports: App/Training/reports/math/\")\n", "print(f\"- Plots: App/Training/plots/math/\")\n", "\n", "print(f\"\\n✅ Analysis completed at: {datetime.now()}\")\n", "print(\"\\n🎉 Math dataset knowledge tracing analysis complete!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}