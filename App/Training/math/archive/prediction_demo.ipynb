{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Knowledge Tracing Prediction Demo\n",
    "\n",
    "This notebook demonstrates how to use trained knowledge tracing models to predict student knowledge states and analyze learning mastery.\n",
    "\n",
    "## Contents:\n",
    "1. Setup and Model Loading\n",
    "2. Basic Prediction Examples\n",
    "3. Learning Mastery State Analysis\n",
    "4. Learning Trajectory Prediction\n",
    "5. Skill Difficulty Analysis\n",
    "6. Personalized Recommendations\n",
    "7. Real-time Prediction Simulation"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Setup and Model Loading"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import os\n",
    "import sys\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from datetime import datetime\n",
    "import warnings\n",
    "\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set plotting style\n",
    "plt.style.use('default')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "# Add pipeline to path\n",
    "sys.path.append('../pipeline')\n",
    "\n",
    "# Import prediction module\n",
    "from prediction import KTPredictor\n",
    "\n",
    "print(\"✅ Imports successful!\")\n",
    "print(f\"Prediction demo started at: {datetime.now()}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Initialize predictor and load trained models\n",
    "print(\"🔄 Loading trained models...\")\n",
    "\n",
    "models_dir = \"outputs\"  # Use local outputs directory\n",
    "predictor = KTPredictor(models_dir=models_dir)\n",
    "\n",
    "# Load all available models\n",
    "load_status = predictor.load_models()\n",
    "\n",
    "print(\"\\n📊 Model Loading Status:\")\n",
    "for model_type, status in load_status.items():\n",
    "    status_icon = \"✅\" if status else \"❌\"\n",
    "    print(f\"  {status_icon} {model_type.upper()}: {'Loaded' if status else 'Failed'}\")\n",
    "\n",
    "available_models = [model for model, status in load_status.items() if status]\n",
    "print(f\"\\n🎯 Available models for prediction: {available_models}\")\n",
    "\n",
    "if not available_models:\n",
    "    print(\"⚠️ No models loaded. Please train models first using the training notebook.\")\n",
    "else:\n",
    "    print(f\"✅ {len(available_models)} models loaded successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Basic Prediction Examples"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Example 1: Single prediction\n",
    "if available_models:\n",
    "    print(\"🎯 BASIC PREDICTION EXAMPLES\")\n",
    "    print(\"=\"*40)\n",
    "    \n",
    "    # Sample student interaction history\n",
    "    user_history = [1, 0, 1, 1, 0, 1]  # 1=correct, 0=incorrect\n",
    "    target_skill = \"Addition\"\n",
    "    \n",
    "    print(f\"\\n📚 Example 1: Single Prediction\")\n",
    "    print(f\"Student history: {user_history}\")\n",
    "    print(f\"Target skill: {target_skill}\")\n",
    "    print(f\"Accuracy rate so far: {np.mean(user_history):.2f}\")\n",
    "    \n",
    "    # Get predictions from all available models\n",
    "    predictions = predictor.compare_model_predictions(user_history, target_skill)\n",
    "    \n",
    "    print(f\"\\n🔮 Predictions for next '{target_skill}' problem:\")\n",
    "    for model_name, prob in predictions.items():\n",
    "        confidence = \"High\" if prob > 0.7 or prob < 0.3 else \"Medium\" if prob > 0.6 or prob < 0.4 else \"Low\"\n",
    "        print(f\"  {model_name:12}: {prob:.3f} ({confidence} confidence)\")\n",
    "    \n",
    "    # Calculate ensemble average\n",
    "    if len(predictions) > 1:\n",
    "        avg_prediction = np.mean(list(predictions.values()))\n",
    "        print(f\"  {'Average':12}: {avg_prediction:.3f}\")\nelse:\n",
    "    print(\"⚠️ No models available for predictions\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Example 2: Multiple skills comparison\n",
    "if available_models:\n",
    "    print(f\"\\n📊 Example 2: Multiple Skills Comparison\")\n",
    "    \n",
    "    skills_to_test = [\"Addition\", \"Subtraction\", \"Multiplication\", \"Division\", \"Fractions\"]\n",
    "    user_history = [1, 1, 0, 1, 0, 1, 1]  # Different history\n",
    "    \n",
    "    print(f\"Student history: {user_history}\")\n",
    "    print(f\"Current accuracy: {np.mean(user_history):.2f}\")\n",
    "    \n",
    "    skill_predictions = {}\n",
    "    \n",
    "    for skill in skills_to_test:\n",
    "        try:\n",
    "            # Use best available model\n",
    "            model_type = 'ensemble' if 'ensemble' in available_models else available_models[0]\n",
    "            prob = predictor.predict_student_performance(user_history, skill, model_type)\n",
    "            skill_predictions[skill] = prob\n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Error predicting for {skill}: {e}\")\n",
    "            skill_predictions[skill] = 0.5\n",
    "    \n",
    "    print(f\"\\n🎯 Predictions across different skills:\")\n",
    "    sorted_skills = sorted(skill_predictions.items(), key=lambda x: x[1], reverse=True)\n",
    "    \n",
    "    for i, (skill, prob) in enumerate(sorted_skills, 1):\n",
    "        difficulty = \"Easy\" if prob > 0.7 else \"Medium\" if prob > 0.5 else \"Hard\"\n",
    "        print(f\"  {i}. {skill:15}: {prob:.3f} ({difficulty})\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Learning Mastery State Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Learning mastery analysis function\n",
    "def analyze_learning_mastery(user_history, skills_list, predictor, threshold_mastery=0.8, threshold_struggling=0.4):\n",
    "    \"\"\"\n",
    "    Analyze learning mastery state across multiple skills\n",
    "    \"\"\"\n",
    "    mastery_analysis = {\n",
    "        'mastered_skills': [],\n",
    "        'learning_skills': [],\n",
    "        'struggling_skills': [],\n",
    "        'skill_predictions': {},\n",
    "        'overall_mastery_level': 0.0\n",
    "    }\n",
    "    \n",
    "    model_type = 'ensemble' if 'ensemble' in available_models else available_models[0] if available_models else None\n",
    "    \n",
    "    if not model_type:\n",
    "        return mastery_analysis\n",
    "    \n",
    "    predictions = []\n",
    "    \n",
    "    for skill in skills_list:\n",
    "        try:\n",
    "            prob = predictor.predict_student_performance(user_history, skill, model_type)\n",
    "            mastery_analysis['skill_predictions'][skill] = prob\n",
    "            predictions.append(prob)\n",
    "            \n",
    "            # Categorize skill mastery level\n",
    "            if prob >= threshold_mastery:\n",
    "                mastery_analysis['mastered_skills'].append((skill, prob))\n",
    "            elif prob <= threshold_struggling:\n",
    "                mastery_analysis['struggling_skills'].append((skill, prob))\n",
    "            else:\n",
    "                mastery_analysis['learning_skills'].append((skill, prob))\n",
    "                \n",
    "        except Exception as e:\n",
    "            print(f\"Warning: Could not predict for skill {skill}: {e}\")\n",
    "    \n",
    "    # Calculate overall mastery level\n",
    "    if predictions:\n",
    "        mastery_analysis['overall_mastery_level'] = np.mean(predictions)\n",
    "    \n",
    "    return mastery_analysis\n",
    "\n",
    "print(\"✅ Learning mastery analysis function defined\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Demonstrate learning mastery analysis\n",
    "if available_models:\n",
    "    print(\"🧠 LEARNING MASTERY STATE ANALYSIS\")\n",
    "    print(\"=\"*50)\n",
    "    \n",
    "    # Example student profiles\n",
    "    student_profiles = {\n",
    "        \"Beginner Student\": [0, 1, 0, 0, 1, 0, 1],\n",
    "        \"Average Student\": [1, 0, 1, 1, 0, 1, 1, 0, 1],\n",
    "        \"Advanced Student\": [1, 1, 1, 0, 1, 1, 1, 1, 1, 1]\n",
    "    }\n",
    "    \n",
    "    skills_to_analyze = [\"Addition\", \"Subtraction\", \"Multiplication\", \"Division\", \"Fractions\", \"Decimals\"]\n",
    "    \n",
    "    for student_name, history in student_profiles.items():\n",
    "        print(f\"\\n👤 {student_name}:\")\n",
    "        print(f\"   History: {history}\")\n",
    "        print(f\"   Current accuracy: {np.mean(history):.2f}\")\n",
    "        \n",
    "        # Analyze mastery\n",
    "        mastery = analyze_learning_mastery(history, skills_to_analyze, predictor)\n",
    "        \n",
    "        print(f\"   Overall mastery level: {mastery['overall_mastery_level']:.2f}\")\n",
    "        \n",
    "        if mastery['mastered_skills']:\n",
    "            print(f\"   ✅ Mastered skills ({len(mastery['mastered_skills'])}):\")\n",
    "            for skill, prob in mastery['mastered_skills']:\n",
    "                print(f\"      • {skill}: {prob:.3f}\")\n",
    "        \n",
    "        if mastery['learning_skills']:\n",
    "            print(f\"   📚 Learning skills ({len(mastery['learning_skills'])}):\")\n",
    "            for skill, prob in mastery['learning_skills']:\n",
    "                print(f\"      • {skill}: {prob:.3f}\")\n",
    "        \n",
    "        if mastery['struggling_skills']:\n",
    "            print(f\"   ⚠️ Struggling skills ({len(mastery['struggling_skills'])}):\")\n",
    "            for skill, prob in mastery['struggling_skills']:\n",
    "                print(f\"      • {skill}: {prob:.3f}\")\nelse:\n",
    "    print(\"⚠️ No models available for mastery analysis\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Learning Trajectory Prediction"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Learning trajectory visualization\n",
    "if available_models:\n",
    "    print(\"📈 LEARNING TRAJECTORY PREDICTION\")\n",
    "    print(\"=\"*40)\n",
    "    \n",
    "    # Example: Predict learning trajectory for a struggling student\n",
    "    struggling_history = [0, 0, 1, 0, 1, 0, 0, 1]\n",
    "    target_skill = \"Multiplication\"\n",
    "    \n",
    "    print(f\"\\n📊 Student Profile:\")\n",
    "    print(f\"   Current history: {struggling_history}\")\n",
    "    print(f\"   Current accuracy: {np.mean(struggling_history):.2f}\")\n",
    "    print(f\"   Target skill: {target_skill}\")\n",
    "    \n",
    "    # Predict trajectory\n",
    "    try:\n",
    "        model_type = 'ensemble' if 'ensemble' in available_models else available_models[0]\n",
    "        trajectory = predictor.predict_learning_trajectory(\n",
    "            struggling_history, target_skill, n_future_interactions=10, model_type=model_type\n",
    "        )\n",
    "        \n",
    "        print(f\"\\n🔮 Predicted learning trajectory (next 10 interactions):\")\n",
    "        for i, prob in enumerate(trajectory, 1):\n",
    "            trend = \"📈\" if i > 1 and prob > trajectory[i-2] else \"📉\" if i > 1 and prob < trajectory[i-2] else \"➡️\"\n",
    "            print(f\"   Interaction {i:2}: {prob:.3f} {trend}\")\n",
    "        \n",
    "        # Visualize trajectory\n",
    "        plt.figure(figsize=(12, 6))\n",
    "        \n",
    "        # Plot historical performance\n",
    "        hist_x = list(range(1, len(struggling_history) + 1))\n",
    "        plt.plot(hist_x, struggling_history, 'o-', label='Historical Performance', \n",
    "                linewidth=2, markersize=8, color='blue')\n",
    "        \n",
    "        # Plot predicted trajectory\n",
    "        traj_x = list(range(len(struggling_history) + 1, len(struggling_history) + len(trajectory) + 1))\n",
    "        plt.plot(traj_x, trajectory, 's--', label='Predicted Trajectory', \n",
    "                linewidth=2, markersize=6, color='red', alpha=0.7)\n",
    "        \n",
    "        # Add mastery threshold line\n",
    "        plt.axhline(y=0.8, color='green', linestyle=':', alpha=0.7, label='Mastery Threshold (0.8)')\n",
    "        plt.axhline(y=0.5, color='orange', linestyle=':', alpha=0.7, label='Average Performance (0.5)')\n",
    "        \n",
    "        plt.xlabel('Interaction Number')\n",
    "        plt.ylabel('Probability of Correct Response')\n",
    "        plt.title(f'Learning Trajectory Prediction for {target_skill}')\n",
    "        plt.legend()\n",
    "        plt.grid(True, alpha=0.3)\n",
    "        plt.ylim(0, 1)\n",
    "        \n",
    "        # Add vertical line to separate history from prediction\n",
    "        plt.axvline(x=len(struggling_history) + 0.5, color='gray', linestyle='-', alpha=0.5)\n",
    "        plt.text(len(struggling_history)/2, 0.9, 'Historical', ha='center', fontweight='bold')\n",
    "        plt.text(len(struggling_history) + len(trajectory)/2, 0.9, 'Predicted', ha='center', fontweight='bold')\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        # Save the plot\n",
    "        os.makedirs('App/Training/plots/math', exist_ok=True)\n",
    "        plt.savefig('App/Training/plots/math/learning_trajectory.png', dpi=300, bbox_inches='tight')\n",
    "        print(\"\\n📊 Learning trajectory plot saved to App/Training/plots/math/learning_trajectory.png\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error generating trajectory: {e}\")\nelse:\n",
    "    print(\"⚠️ No models available for trajectory prediction\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Skill Difficulty Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Skill difficulty ranking\n",
    "if available_models:\n",
    "    print(\"🎯 SKILL DIFFICULTY ANALYSIS\")\n",
    "    print(\"=\"*40)\n",
    "    \n",
    "    # Define comprehensive skill list\n",
    "    math_skills = [\n",
    "        \"Addition\", \"Subtraction\", \"Multiplication\", \"Division\",\n",
    "        \"Fractions\", \"Decimals\", \"Percentages\", \"Algebra\",\n",
    "        \"Geometry\", \"Word Problems\", \"Equations\", \"Inequalities\"\n",
    "    ]\n",
    "    \n",
    "    # Sample student histories for difficulty assessment\n",
    "    sample_histories = {\n",
    "        \"Beginner\": [0, 1, 0, 1],\n",
    "        \"Intermediate\": [1, 1, 0, 1, 1, 0, 1],\n",
    "        \"Advanced\": [1, 1, 1, 0, 1, 1, 1, 1]\n",
    "    }\n",
    "    \n",
    "    for level, history in sample_histories.items():\n",
    "        print(f\"\\n📊 Skill Difficulty for {level} Student:\")\n",
    "        print(f\"   Sample history: {history} (accuracy: {np.mean(history):.2f})\")\n",
    "        \n",
    "        try:\n",
    "            difficulty_ranking = predictor.get_skill_difficulty_ranking(math_skills, history)\n",
    "            \n",
    "            print(f\"   📈 Skills ranked by difficulty (hardest first):\")\n",
    "            for i, (skill, difficulty) in enumerate(difficulty_ranking[:8], 1):\n",
    "                success_prob = 1 - difficulty\n",
    "                level_desc = \"Very Hard\" if difficulty > 0.7 else \"Hard\" if difficulty > 0.5 else \"Medium\" if difficulty > 0.3 else \"Easy\"\n",
    "                print(f\"      {i:2}. {skill:15}: {success_prob:.3f} success prob ({level_desc})\")\n",
    "                \n",
    "        except Exception as e:\n",
    "            print(f\"   ❌ Error analyzing difficulty: {e}\")\nelse:\n",
    "    print(\"⚠️ No models available for difficulty analysis\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Personalized Recommendations"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Personalized skill recommendations\n",
    "if available_models:\n",
    "    print(\"🎯 PERSONALIZED SKILL RECOMMENDATIONS\")\n",
    "    print(\"=\"*50)\n",
    "    \n",
    "    # Different student profiles\n",
    "    student_scenarios = {\n",
    "        \"Struggling Student\": {\n",
    "            \"history\": [0, 0, 1, 0, 1, 0, 0],\n",
    "            \"target_difficulty\": 0.6,  # Easier problems\n",
    "            \"description\": \"Needs confidence building with easier problems\"\n",
    "        },\n",
    "        \"Average Student\": {\n",
    "            \"history\": [1, 0, 1, 1, 0, 1, 1],\n",
    "            \"target_difficulty\": 0.7,  # Moderate challenge\n",
    "            \"description\": \"Ready for moderate challenges\"\n",
    "        },\n",
    "        \"Advanced Student\": {\n",
    "            \"history\": [1, 1, 1, 0, 1, 1, 1, 1],\n",
    "            \"target_difficulty\": 0.4,  # Harder problems\n",
    "            \"description\": \"Needs challenging problems to stay engaged\"\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    available_skills = [\n",
    "        \"Addition\", \"Subtraction\", \"Multiplication\", \"Division\",\n",
    "        \"Fractions\", \"Decimals\", \"Percentages\", \"Algebra\", \"Geometry\"\n",
    "    ]\n",
    "    \n",
    "    for student_type, profile in student_scenarios.items():\n",
    "        print(f\"\\n👤 {student_type}:\")\n",
    "        print(f\"   History: {profile['history']} (accuracy: {np.mean(profile['history']):.2f})\")\n",
    "        print(f\"   Profile: {profile['description']}\")\n",
    "        print(f\"   Target difficulty: {profile['target_difficulty']} success probability\")\n",
    "        \n",
    "        try:\n",
    "            recommendations = predictor.recommend_next_problems(\n",
    "                profile['history'], \n",
    "                available_skills, \n",
    "                target_difficulty=profile['target_difficulty'],\n",
    "                n_recommendations=5\n",
    "            )\n",
    "            \n",
    "            print(f\"   🎯 Recommended skills:\")\n",
    "            for i, (skill, prob) in enumerate(recommendations, 1):\n",
    "                difficulty_match = abs(prob - profile['target_difficulty'])\n",
    "                match_quality = \"Perfect\" if difficulty_match < 0.1 else \"Good\" if difficulty_match < 0.2 else \"Fair\"\n",
    "                print(f\"      {i}. {skill:15}: {prob:.3f} success prob ({match_quality} match)\")\n",
    "                \n",
    "        except Exception as e:\n",
    "            print(f\"   ❌ Error generating recommendations: {e}\")\nelse:\n",
    "    print(\"⚠️ No models available for recommendations\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. Real-time Prediction Simulation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Simulate real-time prediction updates\n",
    "if available_models:\n",
    "    print(\"⚡ REAL-TIME PREDICTION SIMULATION\")\n",
    "    print(\"=\"*50)\n",
    "    \n",
    "    # Simulate a student working through problems\n",
    "    student_session = {\n",
    "        \"student_id\": \"student_demo_001\",\n",
    "        \"target_skill\": \"Multiplication\",\n",
    "        \"initial_history\": [1, 0, 1],\n",
    "        \"new_responses\": [0, 1, 1, 0, 1]  # Responses during the session\n",
    "    }\n",
    "    \n",
    "    print(f\"\\n👤 Student: {student_session['student_id']}\")\n",
    "    print(f\"🎯 Working on: {student_session['target_skill']}\")\n",
    "    print(f\"📚 Initial history: {student_session['initial_history']}\")\n",
    "    \n",
    "    # Track predictions as student progresses\n",
    "    current_history = student_session['initial_history'].copy()\n",
    "    predictions_over_time = []\n",
    "    \n",
    "    model_type = 'ensemble' if 'ensemble' in available_models else available_models[0]\n",
    "    \n",
    "    # Initial prediction\n",
    "    try:\n",
    "        initial_pred = predictor.predict_student_performance(\n",
    "            current_history, student_session['target_skill'], model_type\n",
    "        )\n",
    "        predictions_over_time.append(initial_pred)\n",
    "        \n",
    "        print(f\"\\n📊 Real-time prediction updates:\")\n",
    "        print(f\"   Initial prediction: {initial_pred:.3f}\")\n",
    "        \n",
    "        # Simulate each new response\n",
    "        for i, response in enumerate(student_session['new_responses'], 1):\n",
    "            current_history.append(response)\n",
    "            \n",
    "            # Get updated prediction\n",
    "            new_pred = predictor.predict_student_performance(\n",
    "                current_history, student_session['target_skill'], model_type\n",
    "            )\n",
    "            predictions_over_time.append(new_pred)\n",
    "            \n",
    "            # Calculate change\n",
    "            change = new_pred - predictions_over_time[-2]\n",
    "            change_icon = \"📈\" if change > 0.05 else \"📉\" if change < -0.05 else \"➡️\"\n",
    "            response_icon = \"✅\" if response == 1 else \"❌\"\n",
    "            \n",
    "            print(f\"   Problem {i}: {response_icon} → Prediction: {new_pred:.3f} (Δ{change:+.3f}) {change_icon}\")\n",
    "        \n",
    "        # Visualize the prediction evolution\n",
    "        plt.figure(figsize=(12, 6))\n",
    "        \n",
    "        interactions = list(range(len(predictions_over_time)))\n",
    "        plt.plot(interactions, predictions_over_time, 'o-', linewidth=3, markersize=8)\n",
    "        \n",
    "        # Add response annotations\n",
    "        all_responses = student_session['initial_history'] + student_session['new_responses']\n",
    "        for i, response in enumerate(all_responses):\n",
    "            if i < len(predictions_over_time) - 1:  # Don't annotate the last point\n",
    "                color = 'green' if response == 1 else 'red'\n",
    "                symbol = '✓' if response == 1 else '✗'\n",
    "                plt.annotate(symbol, (i+1, predictions_over_time[i+1]), \n",
    "                           xytext=(5, 10), textcoords='offset points', \n",
    "                           color=color, fontweight='bold', fontsize=12)\n",
    "        \n",
    "        plt.axhline(y=0.8, color='green', linestyle='--', alpha=0.7, label='Mastery Threshold')\n",
    "        plt.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='Average Performance')\n",
    "        \n",
    "        plt.xlabel('Interaction Number')\n",
    "        plt.ylabel('Predicted Success Probability')\n",
    "        plt.title(f'Real-time Prediction Updates - {student_session[\"target_skill\"]}')\n",
    "        plt.legend()\n",
    "        plt.grid(True, alpha=0.3)\n",
    "        plt.ylim(0, 1)\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        # Save the plot\n",
    "        plt.savefig('App/Training/plots/math/realtime_predictions.png', dpi=300, bbox_inches='tight')\n",
    "        print(\"\\n📊 Real-time predictions plot saved to App/Training/plots/math/realtime_predictions.png\")\n",
    "        \n",
    "        # Final analysis\n",
    "        final_accuracy = np.mean(current_history)\n",
    "        final_prediction = predictions_over_time[-1]\n",
    "        \n",
    "        print(f\"\\n📋 Session Summary:\")\n",
    "        print(f\"   Final accuracy: {final_accuracy:.3f}\")\n",
    "        print(f\"   Final prediction: {final_prediction:.3f}\")\n",
    "        print(f\"   Prediction change: {final_prediction - predictions_over_time[0]:+.3f}\")\n",
    "        \n",
    "        mastery_status = \"Mastered\" if final_prediction >= 0.8 else \"Learning\" if final_prediction >= 0.6 else \"Struggling\"\n",
    "        print(f\"   Mastery status: {mastery_status}\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error in real-time simulation: {e}\")\nelse:\n",
    "    print(\"⚠️ No models available for real-time simulation\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. Summary and Next Steps"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Summary of prediction capabilities\n",
    "print(\"📋 PREDICTION DEMO SUMMARY\")\n",
    "print(\"=\"*40)\n",
    "\n",
    "print(f\"\\n✅ Demonstrated Capabilities:\")\n",
    "capabilities = [\n",
    "    \"Single skill prediction\",\n",
    "    \"Multi-skill comparison\",\n",
    "    \"Learning mastery analysis\",\n",
    "    \"Learning trajectory prediction\",\n",
    "    \"Skill difficulty assessment\",\n",
    "    \"Personalized recommendations\",\n",
    "    \"Real-time prediction updates\"\n",
    "]\n",
    "\n",
    "for i, capability in enumerate(capabilities, 1):\n",
    "    print(f\"   {i}. {capability}\")\n",
    "\n",
    "if available_models:\n",
    "    print(f\"\\n🎯 Available Models: {', '.join(available_models)}\")\n",
    "    print(f\"\\n📊 Generated Visualizations:\")\n",
    "    print(f\"   - Learning trajectory plot\")\n",
    "    print(f\"   - Real-time prediction updates\")\n",
    "    \n",
    "    print(f\"\\n🚀 Ready for Real-time Integration:\")\n",
    "    print(f\"   ✅ Models trained and saved\")\n",
    "    print(f\"   ✅ Prediction API functional\")\n",
    "    print(f\"   ✅ Mastery analysis implemented\")\n",
    "    print(f\"   ✅ Real-time updates demonstrated\")\n",
    "    \n",
    "    print(f\"\\n🔮 Next Steps for Real-time App:\")\n",
    "    print(f\"   1. Integrate prediction API with web interface\")\n",
    "    print(f\"   2. Implement real-time data collection\")\n",
    "    print(f\"   3. Add adaptive problem selection\")\n",
    "    print(f\"   4. Create teacher dashboard for monitoring\")\n",
    "    print(f\"   5. Add performance analytics and reporting\")\nelse:\n",
    "    print(f\"\\n⚠️ No models were available for demonstration\")\n",
    "    print(f\"   Please run the training notebook first to train models\")\n",
    "\n",
    "print(f\"\\n🎉 Prediction demo completed at: {datetime.now()}\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",\n   "language": "python",\n   "name": "python3"\n  },\n  "language_info": {\n   "codemirror_mode": {\n    "name": "ipython",\n    "version": 3\n   },\n   "file_extension": ".py",\n   "mimetype": "text/x-python",\n   "name": "python",\n   "nbconvert_exporter": "python",\n   "pygments_lexer": "ipython3",\n   "version": "3.8.0"\n  }\n },\n "nbformat": 4,\n "nbformat_minor": 4\n}
