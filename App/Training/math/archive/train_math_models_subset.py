#!/usr/bin/env python3
"""
Math Dataset Knowledge Tracing Training Script (Subset Version)
==============================================================

This script uses the KT pipeline to train BKT, PFA, and DKT models on a subset
(~10%) of the math dataset for faster testing and validation.

Usage:
    python train_math_models_subset.py

Outputs:
    - Models: outputs/
    - Reports: ../reports/math/
    - Plots: ../plots/math/
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add the pipeline directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'pipeline'))

# Import pipeline components
from kt_training_pipeline import KnowledgeTracingPipeline
from kt_evaluation import KTEvaluator
from prediction import KTPredictor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('math_training_subset.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MathDatasetSubsetTrainer:
    """Trainer for a subset of the math dataset"""
    
    def __init__(self, subset_fraction=0.1):
        """Initialize the trainer"""
        self.data_path = "/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv"
        self.subset_fraction = subset_fraction
        self.models_output_dir = "outputs"
        self.reports_output_dir = "../reports/math"
        self.plots_output_dir = "../plots/math"
        
        # Create output directories
        self._create_output_directories()
        
        # Initialize pipeline
        self.pipeline = KnowledgeTracingPipeline()
        self.evaluator = KTEvaluator(output_dir=self.plots_output_dir)
        
    def _create_output_directories(self):
        """Create all necessary output directories"""
        directories = [
            self.models_output_dir,
            self.reports_output_dir,
            self.plots_output_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"Created directory: {directory}")
    
    def load_and_explore_data(self):
        """Load and explore a subset of the math dataset"""
        logger.info("="*60)
        logger.info("LOADING AND EXPLORING MATH DATASET SUBSET")
        logger.info("="*60)
        
        try:
            # Load full dataset first to get total size
            logger.info(f"Loading dataset from: {self.data_path}")
            df_full = pd.read_csv(self.data_path, encoding='latin1')
            total_size = len(df_full)
            
            # Calculate subset size
            subset_size = int(total_size * self.subset_fraction)
            logger.info(f"Full dataset size: {total_size:,}")
            logger.info(f"Using subset of {subset_size:,} rows ({self.subset_fraction*100:.1f}%)")
            
            # Sample data while maintaining user sequences
            unique_users = df_full['user_id'].unique()
            np.random.seed(42)  # For reproducibility
            
            # Sample users to get approximately the desired subset size
            n_users_needed = int(len(unique_users) * self.subset_fraction)
            sampled_users = np.random.choice(unique_users, size=n_users_needed, replace=False)
            
            # Get data for sampled users
            df = df_full[df_full['user_id'].isin(sampled_users)].copy()
            
            logger.info(f"Actual subset size: {len(df):,} rows from {len(sampled_users):,} users")
            logger.info(f"Columns: {list(df.columns)}")
            
            # Basic exploration
            logger.info("\nDataset Overview:")
            logger.info(f"- Total interactions: {len(df):,}")
            logger.info(f"- Unique students: {df['user_id'].nunique():,}")
            logger.info(f"- Unique problems: {df['problem_id'].nunique():,}")
            logger.info(f"- Unique skills: {df['skill_name'].nunique():,}")
            logger.info(f"- Overall accuracy: {df['correct'].mean():.3f}")
            
            # Show sample data
            logger.info("\nFirst 5 rows:")
            logger.info(df.head().to_string())
            
            # Show skill distribution
            logger.info("\nTop 10 skills by frequency:")
            skill_counts = df['skill_name'].value_counts().head(10)
            for skill, count in skill_counts.items():
                logger.info(f"  {skill}: {count:,} interactions")
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise
    
    def train_models(self, df):
        """Train all knowledge tracing models"""
        logger.info("="*60)
        logger.info("TRAINING KNOWLEDGE TRACING MODELS")
        logger.info("="*60)
        
        try:
            # Run the complete pipeline
            logger.info("Starting knowledge tracing pipeline...")
            
            # Explore dataset
            dataset_stats = self.pipeline.explore_dataset(df)
            
            # Preprocess data
            df_clean = self.pipeline.preprocess_data(df)
            
            # Split data
            train_df, test_df = self.pipeline.split_data(df_clean, test_size=0.2)
            
            # Store datasets
            self.pipeline.datasets = {
                'raw': df,
                'clean': df_clean,
                'train': train_df,
                'test': test_df,
                'stats': dataset_stats
            }
            
            # Feature engineering
            logger.info("Engineering features...")
            train_features, test_features = self.pipeline.engineer_features(train_df, test_df)
            
            # Train models with reduced complexity for subset
            logger.info("Training models...")
            
            # Initialize model trainer with reduced parameters for faster training
            from kt_model_trainer import KTModelTrainer
            trainer = KTModelTrainer(n_folds=3)  # Reduced folds for speed
            
            models = {}
            metrics = {}
            
            # Train BKT model
            try:
                logger.info("Training BKT model...")
                bkt_model, bkt_metrics = trainer.train_bkt(
                    train_df, test_df, hyperparameter_tuning=False
                )
                models['bkt'] = bkt_model
                metrics['bkt'] = bkt_metrics
                logger.info(f"BKT training completed - Accuracy: {bkt_metrics.get('accuracy', 'N/A'):.3f}")
            except Exception as e:
                logger.warning(f"BKT training failed: {e}")
            
            # Train PFA model
            try:
                logger.info("Training PFA model...")
                pfa_model, pfa_metrics = trainer.train_pfa(
                    train_df, test_df, hyperparameter_tuning=False
                )
                models['pfa'] = pfa_model
                metrics['pfa'] = pfa_metrics
                logger.info(f"PFA training completed - Accuracy: {pfa_metrics.get('accuracy', 'N/A'):.3f}")
            except Exception as e:
                logger.warning(f"PFA training failed: {e}")
            
            # For DKT, use the simple version that we know works
            try:
                logger.info("Training simplified DKT model...")
                from train_dkt import DeepKnowledgeTracing
                
                dkt_model = DeepKnowledgeTracing(
                    hidden_dim=32,      # Smaller for faster training
                    num_layers=1,       # Single layer
                    dropout=0.2,
                    learning_rate=0.001,
                    batch_size=16,
                    max_epochs=3,       # Few epochs for testing
                    patience=2,
                    max_seq_len=20      # Shorter sequences
                )
                
                dkt_model.fit(train_df, validation_split=0.2)
                dkt_metrics = dkt_model.evaluate(test_df)
                
                models['dkt'] = dkt_model
                metrics['dkt'] = dkt_metrics
                logger.info(f"DKT training completed - Accuracy: {dkt_metrics.get('accuracy', 'N/A'):.3f}")
                
            except Exception as e:
                logger.warning(f"DKT training failed: {e}")
            
            # Store models in pipeline
            self.pipeline.models = models
            self.pipeline.metrics = metrics
            
            # Save models to output directory
            self._save_models_to_output_dir()
            
            # Generate reports
            self._generate_reports(models, metrics, dataset_stats)
            
            logger.info("Model training completed successfully!")
            return models, metrics
            
        except Exception as e:
            logger.error(f"Error during model training: {e}")
            raise
    
    def _save_models_to_output_dir(self):
        """Save models to the output directory"""
        logger.info(f"Saving models to {self.models_output_dir}...")
        
        # Save individual models
        for model_name, model in self.pipeline.models.items():
            model_path = os.path.join(self.models_output_dir, f"{model_name}_model.joblib")
            try:
                model.save(model_path)
                logger.info(f"Saved {model_name} model to {model_path}")
            except Exception as e:
                logger.warning(f"Error saving {model_name} model: {e}")
        
        # Save metrics
        metrics_path = os.path.join(self.models_output_dir, "model_metrics.json")
        try:
            import json
            with open(metrics_path, 'w') as f:
                json.dump(self.pipeline.metrics, f, indent=2)
            logger.info(f"Saved metrics to {metrics_path}")
        except Exception as e:
            logger.warning(f"Error saving metrics: {e}")
    
    def _generate_reports(self, models, metrics, dataset_stats):
        """Generate comprehensive reports"""
        logger.info("="*60)
        logger.info("GENERATING REPORTS AND VISUALIZATIONS")
        logger.info("="*60)
        
        try:
            # Dataset report
            dataset_report = self.evaluator.generate_dataset_report(
                self.pipeline.datasets['clean'], 
                dataset_stats
            )
            
            # Save dataset report
            dataset_report_path = os.path.join(self.reports_output_dir, "dataset_subset_report.txt")
            with open(dataset_report_path, 'w') as f:
                f.write(dataset_report)
            logger.info(f"Dataset report saved to {dataset_report_path}")
            
            # Model performance evaluation
            test_df = self.pipeline.datasets['test']
            performance_results = self.evaluator.evaluate_model_performance(models, test_df)
            
            # Model comparison report
            comparison_report = self.evaluator.generate_model_comparison_report(performance_results)
            comparison_report_path = os.path.join(self.reports_output_dir, "model_comparison_subset_report.txt")
            with open(comparison_report_path, 'w') as f:
                f.write(comparison_report)
            logger.info(f"Model comparison report saved to {comparison_report_path}")
            
            # Save performance results
            import json
            performance_path = os.path.join(self.reports_output_dir, "model_performance_subset.json")
            with open(performance_path, 'w') as f:
                json.dump(performance_results, f, indent=2)
            logger.info(f"Performance results saved to {performance_path}")
            
        except Exception as e:
            logger.warning(f"Error generating reports: {e}")
    
    def test_predictions(self):
        """Test the prediction functionality"""
        logger.info("="*60)
        logger.info("TESTING PREDICTION FUNCTIONALITY")
        logger.info("="*60)
        
        try:
            # Initialize predictor with local models
            predictor = KTPredictor(models_dir=self.models_output_dir)
            
            # Load models
            load_status = predictor.load_models()
            logger.info(f"Model loading status: {load_status}")
            
            if any(load_status.values()):
                # Test predictions with sample data
                user_history = [1, 0, 1, 1]  # Sample interaction history
                skills_to_test = ["Addition", "Subtraction", "Multiplication"]
                
                logger.info("\nTesting predictions:")
                for skill in skills_to_test:
                    try:
                        # Get predictions from all available models
                        predictions = predictor.compare_model_predictions(user_history, skill)
                        logger.info(f"\nPredictions for skill '{skill}' with history {user_history}:")
                        for model_name, pred in predictions.items():
                            logger.info(f"  {model_name:12}: {pred:.3f}")
                    except Exception as e:
                        logger.warning(f"Error predicting for skill {skill}: {e}")
                
                logger.info("Prediction testing completed!")
            else:
                logger.warning("No models were loaded successfully for prediction testing")
                
        except Exception as e:
            logger.error(f"Error during prediction testing: {e}")
    
    def run_complete_training(self):
        """Run the complete training pipeline"""
        start_time = datetime.now()
        
        logger.info("="*80)
        logger.info("MATH DATASET SUBSET KNOWLEDGE TRACING TRAINING")
        logger.info("="*80)
        logger.info(f"Start time: {start_time}")
        logger.info(f"Subset fraction: {self.subset_fraction*100:.1f}%")
        
        try:
            # Step 1: Load and explore data
            df = self.load_and_explore_data()
            
            # Step 2: Train models
            models, metrics = self.train_models(df)
            
            # Step 3: Test predictions
            self.test_predictions()
            
            # Completion summary
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info("="*80)
            logger.info("TRAINING COMPLETED SUCCESSFULLY!")
            logger.info("="*80)
            logger.info(f"End time: {end_time}")
            logger.info(f"Total duration: {duration}")
            logger.info(f"Dataset subset: {self.subset_fraction*100:.1f}%")
            logger.info(f"\nTrained models: {list(models.keys())}")
            logger.info(f"\nOutputs saved to:")
            logger.info(f"- Models: {self.models_output_dir}")
            logger.info(f"- Reports: {self.reports_output_dir}")
            logger.info(f"- Plots: {self.plots_output_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            logger.error("Check the log file for detailed error information")
            return False


def main():
    """Main execution function"""
    print("Math Dataset Subset Knowledge Tracing Training")
    print("=" * 50)
    
    try:
        # Initialize trainer with 10% subset
        trainer = MathDatasetSubsetTrainer(subset_fraction=0.1)
        
        # Run complete training
        success = trainer.run_complete_training()
        
        if success:
            print("\n✅ Training completed successfully!")
            print("Check the output directories for results:")
            print(f"  - Models: {trainer.models_output_dir}")
            print(f"  - Reports: {trainer.reports_output_dir}")
            print(f"  - Plots: {trainer.plots_output_dir}")
        else:
            print("\n❌ Training failed. Check the logs for details.")
            return 1
            
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
