#!/usr/bin/env python3
"""
Math Dataset Knowledge Tracing Training Script
==============================================

This script uses the KT pipeline to train BKT, PFA, and DKT models on the math dataset.
It saves models, reports, and plots to specified directories.

Usage:
    python train_math_models.py

Outputs:
    - Models: /Training/pipeline/outputs/math/
    - Reports: /Training/reports/math/
    - Plots: /Training/plots/math/
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add the pipeline directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'pipeline'))

# Import pipeline components
from kt_training_pipeline import KnowledgeTracingPipeline
from kt_evaluation import KTEvaluator
from prediction import KTPredictor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('math_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MathDatasetTrainer:
    """Trainer specifically for the math dataset"""
    
    def __init__(self):
        """Initialize the math dataset trainer"""
        self.data_path = "../datasets/math/skill_builder_data_corrected.csv"
        self.models_output_dir = "../pipeline/outputs/math"
        self.reports_output_dir = "../reports/math"
        self.plots_output_dir = "../plots/math"
        
        # Create output directories
        self._create_output_directories()
        
        # Initialize pipeline
        self.pipeline = KnowledgeTracingPipeline()
        self.evaluator = KTEvaluator(output_dir=self.plots_output_dir)
        
    def _create_output_directories(self):
        """Create all necessary output directories"""
        directories = [
            self.models_output_dir,
            self.reports_output_dir,
            self.plots_output_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"Created directory: {directory}")
    
    def load_and_explore_data(self):
        """Load and explore the math dataset"""
        logger.info("="*60)
        logger.info("LOADING AND EXPLORING MATH DATASET")
        logger.info("="*60)
        
        try:
            # Load dataset
            logger.info(f"Loading dataset from: {self.data_path}")
            df = pd.read_csv(self.data_path, encoding='latin1')
            
            logger.info(f"Dataset loaded successfully!")
            logger.info(f"Shape: {df.shape}")
            logger.info(f"Columns: {list(df.columns)}")
            
            # Basic exploration
            logger.info("\nDataset Overview:")
            logger.info(f"- Total interactions: {len(df):,}")
            logger.info(f"- Unique students: {df['user_id'].nunique():,}")
            logger.info(f"- Unique problems: {df['problem_id'].nunique():,}")
            logger.info(f"- Unique skills: {df['skill_name'].nunique():,}")
            logger.info(f"- Overall accuracy: {df['correct'].mean():.3f}")
            
            # Show sample data
            logger.info("\nSample data:")
            logger.info(df.head().to_string())
            
            # Show skill distribution
            logger.info("\nTop 10 skills by frequency:")
            skill_counts = df['skill_name'].value_counts().head(10)
            for skill, count in skill_counts.items():
                logger.info(f"  {skill}: {count:,} interactions")
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise
    
    def train_models(self, df):
        """Train all knowledge tracing models"""
        logger.info("="*60)
        logger.info("TRAINING KNOWLEDGE TRACING MODELS")
        logger.info("="*60)
        
        try:
            # Run the complete pipeline
            logger.info("Starting knowledge tracing pipeline...")
            
            # Explore dataset
            dataset_stats = self.pipeline.explore_dataset(df)
            
            # Preprocess data
            df_clean = self.pipeline.preprocess_data(df)
            
            # Split data
            train_df, test_df = self.pipeline.split_data(df_clean, test_size=0.2)
            
            # Store datasets
            self.pipeline.datasets = {
                'raw': df,
                'clean': df_clean,
                'train': train_df,
                'test': test_df,
                'stats': dataset_stats
            }
            
            # Feature engineering
            logger.info("Engineering features...")
            train_features, test_features = self.pipeline.engineer_features(train_df, test_df)
            
            # Train models
            logger.info("Training models...")
            models, metrics = self.pipeline.train_models(train_df, test_df, models_to_train=['pfa'])
            
            # # Create ensemble
            # logger.info("Creating ensemble model...")
            # ensemble, ensemble_metrics = self.pipeline.create_ensemble(train_df, test_df)
            
            # Save models to math-specific directory
            self._save_models_to_math_dir()
            
            # Generate reports
            self._generate_math_reports(models, metrics, dataset_stats)
            
            logger.info("Model training completed successfully!")
            return models, metrics
            
        except Exception as e:
            logger.error(f"Error during model training: {e}")
            raise
    
    def _save_models_to_math_dir(self):
        """Save models to the math-specific output directory"""
        logger.info(f"Saving models to {self.models_output_dir}...")
        
        # Save individual models
        for model_name, model in self.pipeline.models.items():
            model_path = os.path.join(self.models_output_dir, f"{model_name}_model.joblib")
            try:
                model.save(model_path)
                logger.info(f"Saved {model_name} model to {model_path}")
            except Exception as e:
                logger.warning(f"Error saving {model_name} model: {e}")
        
        # Save ensemble
        if self.pipeline.ensemble:
            ensemble_path = os.path.join(self.models_output_dir, "ensemble_model.joblib")
            try:
                self.pipeline.ensemble.save(ensemble_path)
                logger.info(f"Saved ensemble model to {ensemble_path}")
            except Exception as e:
                logger.warning(f"Error saving ensemble model: {e}")
        
        # Save metrics
        metrics_path = os.path.join(self.models_output_dir, "model_metrics.json")
        try:
            import json
            with open(metrics_path, 'w') as f:
                json.dump(self.pipeline.metrics, f, indent=2)
            logger.info(f"Saved metrics to {metrics_path}")
        except Exception as e:
            logger.warning(f"Error saving metrics: {e}")
    
    def _generate_math_reports(self, models, metrics, dataset_stats):
        """Generate comprehensive reports for the math dataset"""
        logger.info("="*60)
        logger.info("GENERATING REPORTS AND VISUALIZATIONS")
        logger.info("="*60)
        
        try:
            # Dataset report
            dataset_report = self.evaluator.generate_dataset_report(
                self.pipeline.datasets['clean'], 
                dataset_stats
            )
            
            # Save dataset report to math reports directory
            dataset_report_path = os.path.join(self.reports_output_dir, "dataset_report.txt")
            with open(dataset_report_path, 'w') as f:
                f.write(dataset_report)
            logger.info(f"Dataset report saved to {dataset_report_path}")
            
            # Model performance evaluation
            test_df = self.pipeline.datasets['test']
            performance_results = self.evaluator.evaluate_model_performance(models, test_df)
            
            # Model comparison report
            comparison_report = self.evaluator.generate_model_comparison_report(performance_results)
            comparison_report_path = os.path.join(self.reports_output_dir, "model_comparison_report.txt")
            with open(comparison_report_path, 'w') as f:
                f.write(comparison_report)
            logger.info(f"Model comparison report saved to {comparison_report_path}")
            
            # Skill performance report
            skill_results = self.evaluator.generate_skill_performance_report(models, test_df)
            
            # Save performance results
            import json
            performance_path = os.path.join(self.reports_output_dir, "model_performance.json")
            with open(performance_path, 'w') as f:
                json.dump(performance_results, f, indent=2)
            logger.info(f"Performance results saved to {performance_path}")
            
            skill_performance_path = os.path.join(self.reports_output_dir, "skill_performance.json")
            with open(skill_performance_path, 'w') as f:
                json.dump(skill_results, f, indent=2)
            logger.info(f"Skill performance results saved to {skill_performance_path}")
            
        except Exception as e:
            logger.warning(f"Error generating reports: {e}")
    
    def test_predictions(self):
        """Test the prediction functionality"""
        logger.info("="*60)
        logger.info("TESTING PREDICTION FUNCTIONALITY")
        logger.info("="*60)
        
        try:
            # Initialize predictor with math models
            predictor = KTPredictor(models_dir=self.models_output_dir)
            
            # Load models
            load_status = predictor.load_models()
            logger.info(f"Model loading status: {load_status}")
            
            if any(load_status.values()):
                # Test predictions with sample data
                user_history = [1, 0, 1, 1]  # Sample interaction history
                skills_to_test = ["Addition", "Subtraction", "Multiplication"]
                
                logger.info("\nTesting predictions:")
                for skill in skills_to_test:
                    try:
                        # Get predictions from all available models
                        predictions = predictor.compare_model_predictions(user_history, skill)
                        logger.info(f"\nPredictions for skill '{skill}' with history {user_history}:")
                        for model_name, pred in predictions.items():
                            logger.info(f"  {model_name:12}: {pred:.3f}")
                    except Exception as e:
                        logger.warning(f"Error predicting for skill {skill}: {e}")
                
                logger.info("Prediction testing completed!")
            else:
                logger.warning("No models were loaded successfully for prediction testing")
                
        except Exception as e:
            logger.error(f"Error during prediction testing: {e}")
    
    def run_complete_training(self):
        """Run the complete training pipeline"""
        start_time = datetime.now()
        
        logger.info("="*80)
        logger.info("MATH DATASET KNOWLEDGE TRACING TRAINING")
        logger.info("="*80)
        logger.info(f"Start time: {start_time}")
        
        try:
            # Step 1: Load and explore data
            df = self.load_and_explore_data()
            
            # Step 2: Train models
            models, metrics = self.train_models(df)
            
            # Step 3: Test predictions
            self.test_predictions()
            
            # Completion summary
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info("="*80)
            logger.info("TRAINING COMPLETED SUCCESSFULLY!")
            logger.info("="*80)
            logger.info(f"End time: {end_time}")
            logger.info(f"Total duration: {duration}")
            logger.info(f"\nOutputs saved to:")
            logger.info(f"- Models: {self.models_output_dir}")
            logger.info(f"- Reports: {self.reports_output_dir}")
            logger.info(f"- Plots: {self.plots_output_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            logger.error("Check the log file for detailed error information")
            return False


def main():
    """Main execution function"""
    print("Math Dataset Knowledge Tracing Training")
    print("=" * 50)
    
    try:
        # Initialize trainer
        trainer = MathDatasetTrainer()
        
        # Run complete training
        success = trainer.run_complete_training()
        
        if success:
            print("\n✅ Training completed successfully!")
            print("Check the output directories for results:")
            print(f"  - Models: {trainer.models_output_dir}")
            print(f"  - Reports: {trainer.reports_output_dir}")
            print(f"  - Plots: {trainer.plots_output_dir}")
        else:
            print("\n❌ Training failed. Check the logs for details.")
            return 1
            
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
