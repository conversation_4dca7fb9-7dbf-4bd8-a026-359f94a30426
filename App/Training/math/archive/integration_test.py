#!/usr/bin/env python3
"""
Knowledge Tracing Integration Test
=================================

Final integration test to ensure all components work together
and are ready for real-time deployment.
"""

import os
import sys
import time
import pandas as pd
import numpy as np
from datetime import datetime

# Add the pipeline directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'pipeline'))

def test_model_loading():
    """Test loading trained models"""
    print("🔄 Testing model loading...")
    
    try:
        from prediction import KTPredictor
        
        # Initialize predictor
        predictor = KTPredictor(models_dir="outputs")
        
        # Load models
        load_status = predictor.load_models()
        
        loaded_models = [model for model, status in load_status.items() if status]
        
        print(f"✅ Models loaded: {loaded_models}")
        
        if not loaded_models:
            print("⚠️ No models loaded - running quick demo first...")
            return False, None
        
        return True, predictor
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False, None

def test_prediction_performance(predictor):
    """Test prediction performance and speed"""
    print("\n⚡ Testing prediction performance...")
    
    try:
        # Test data
        test_cases = [
            {"history": [1, 0, 1], "skill": "Addition"},
            {"history": [1, 1, 0, 1], "skill": "Subtraction"},
            {"history": [0, 1, 1, 1, 0], "skill": "Multiplication"},
            {"history": [1, 1, 1], "skill": "Division"},
            {"history": [0, 0, 1, 0, 1], "skill": "Fractions"}
        ]
        
        # Test prediction speed
        start_time = time.time()
        
        results = []
        for case in test_cases:
            pred_start = time.time()
            
            try:
                # Get predictions from all available models
                predictions = predictor.compare_model_predictions(
                    case["history"], case["skill"]
                )
                
                pred_time = (time.time() - pred_start) * 1000  # Convert to ms
                
                results.append({
                    "skill": case["skill"],
                    "history": case["history"],
                    "predictions": predictions,
                    "time_ms": pred_time
                })
                
                print(f"  {case['skill']:15}: {pred_time:.1f}ms - {len(predictions)} models")
                
            except Exception as e:
                print(f"  {case['skill']:15}: Error - {e}")
        
        total_time = (time.time() - start_time) * 1000
        avg_time = total_time / len(test_cases)
        
        print(f"\n📊 Performance Summary:")
        print(f"  Total time: {total_time:.1f}ms")
        print(f"  Average per prediction: {avg_time:.1f}ms")
        print(f"  Predictions per second: {1000/avg_time:.1f}")
        
        # Performance check
        if avg_time < 100:
            print("✅ Performance: Excellent (< 100ms)")
        elif avg_time < 500:
            print("✅ Performance: Good (< 500ms)")
        else:
            print("⚠️ Performance: Slow (> 500ms)")
        
        return True, results
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False, []

def test_learning_mastery_analysis(predictor):
    """Test learning mastery analysis functionality"""
    print("\n🧠 Testing learning mastery analysis...")
    
    try:
        # Sample student profiles
        students = {
            "Beginner": [0, 1, 0, 0, 1],
            "Average": [1, 0, 1, 1, 0, 1],
            "Advanced": [1, 1, 1, 0, 1, 1, 1]
        }
        
        skills = ["Addition", "Subtraction", "Multiplication"]
        
        for student_type, history in students.items():
            print(f"\n  👤 {student_type} Student:")
            print(f"     History: {history} (accuracy: {np.mean(history):.2f})")
            
            # Test predictions for each skill
            skill_predictions = {}
            for skill in skills:
                try:
                    # Use first available model
                    available_models = list(predictor.models.keys())
                    if available_models:
                        model_type = available_models[0]
                        prob = predictor.predict_student_performance(
                            history, skill, model_type
                        )
                        skill_predictions[skill] = prob
                        
                        # Categorize mastery level
                        if prob >= 0.8:
                            level = "Mastered"
                        elif prob >= 0.6:
                            level = "Learning"
                        else:
                            level = "Struggling"
                        
                        print(f"     {skill:15}: {prob:.3f} ({level})")
                        
                except Exception as e:
                    print(f"     {skill:15}: Error - {e}")
            
            # Overall mastery level
            if skill_predictions:
                overall = np.mean(list(skill_predictions.values()))
                print(f"     Overall mastery: {overall:.3f}")
        
        print("✅ Learning mastery analysis working")
        return True
        
    except Exception as e:
        print(f"❌ Learning mastery analysis failed: {e}")
        return False

def test_real_time_simulation(predictor):
    """Test real-time prediction updates"""
    print("\n⚡ Testing real-time prediction simulation...")
    
    try:
        # Simulate a student session
        student_session = {
            "skill": "Addition",
            "initial_history": [1, 0, 1],
            "new_responses": [0, 1, 1, 0, 1]
        }
        
        print(f"  🎯 Skill: {student_session['skill']}")
        print(f"  📚 Initial history: {student_session['initial_history']}")
        
        # Track predictions over time
        current_history = student_session['initial_history'].copy()
        
        # Get available model
        available_models = list(predictor.models.keys())
        if not available_models:
            print("  ⚠️ No models available for simulation")
            return False
        
        model_type = available_models[0]
        
        # Initial prediction
        initial_pred = predictor.predict_student_performance(
            current_history, student_session['skill'], model_type
        )
        print(f"  Initial prediction: {initial_pred:.3f}")
        
        # Simulate real-time updates
        for i, response in enumerate(student_session['new_responses'], 1):
            current_history.append(response)
            
            # Get updated prediction
            new_pred = predictor.predict_student_performance(
                current_history, student_session['skill'], model_type
            )
            
            # Calculate change
            change = new_pred - initial_pred if i == 1 else new_pred - prev_pred
            change_icon = "📈" if change > 0.05 else "📉" if change < -0.05 else "➡️"
            response_icon = "✅" if response == 1 else "❌"
            
            print(f"  Response {i}: {response_icon} → {new_pred:.3f} (Δ{change:+.3f}) {change_icon}")
            
            prev_pred = new_pred
        
        print("✅ Real-time simulation working")
        return True
        
    except Exception as e:
        print(f"❌ Real-time simulation failed: {e}")
        return False

def test_api_interface(predictor):
    """Test the complete API interface"""
    print("\n🔌 Testing API interface...")

    try:
        # Get available models
        available_models = list(predictor.models.keys())
        if not available_models:
            print("  ⚠️ No models available for API testing")
            return False

        model_type = available_models[0]  # Use first available model

        # Test 1: Basic prediction
        prob = predictor.predict_student_performance([1, 0, 1], "Addition", model_type)
        print(f"  ✅ Basic prediction: {prob:.3f}")

        # Test 2: Model comparison
        predictions = predictor.compare_model_predictions([1, 0, 1], "Addition")
        print(f"  ✅ Model comparison: {len(predictions)} models")

        # Test 3: Learning trajectory
        if hasattr(predictor, 'predict_learning_trajectory'):
            trajectory = predictor.predict_learning_trajectory([1, 0, 1], "Addition", 3, model_type)
            print(f"  ✅ Learning trajectory: {len(trajectory)} steps")

        # Test 4: Skill recommendations
        if hasattr(predictor, 'recommend_next_problems'):
            recommendations = predictor.recommend_next_problems(
                [1, 0, 1], ["Addition", "Subtraction"], target_difficulty=0.7, n_recommendations=2
            )
            print(f"  ✅ Skill recommendations: {len(recommendations)} skills")

        print("✅ API interface working")
        return True

    except Exception as e:
        print(f"❌ API interface test failed: {e}")
        return False

def run_integration_test():
    """Run complete integration test"""
    print("="*60)
    print("KNOWLEDGE TRACING INTEGRATION TEST")
    print("="*60)
    print(f"Start time: {datetime.now()}")
    
    # Test results
    results = {
        "model_loading": False,
        "prediction_performance": False,
        "learning_mastery": False,
        "real_time_simulation": False,
        "api_interface": False
    }
    
    # Test 1: Model Loading
    success, predictor = test_model_loading()
    results["model_loading"] = success
    
    if not success:
        print("\n⚠️ Running quick demo to create models...")
        os.system("python quick_training_demo.py")
        success, predictor = test_model_loading()
        results["model_loading"] = success
    
    if not success or not predictor:
        print("\n❌ Cannot proceed without loaded models")
        return False
    
    # Test 2: Prediction Performance
    success, perf_results = test_prediction_performance(predictor)
    results["prediction_performance"] = success
    
    # Test 3: Learning Mastery Analysis
    success = test_learning_mastery_analysis(predictor)
    results["learning_mastery"] = success
    
    # Test 4: Real-time Simulation
    success = test_real_time_simulation(predictor)
    results["real_time_simulation"] = success
    
    # Test 5: API Interface
    success = test_api_interface(predictor)
    results["api_interface"] = success
    
    # Summary
    print("\n" + "="*60)
    print("INTEGRATION TEST SUMMARY")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title():25}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("🚀 System is ready for real-time deployment!")
        print("\n📋 Deployment Checklist:")
        print("  ✅ Models trained and validated")
        print("  ✅ Prediction API functional")
        print("  ✅ Performance requirements met")
        print("  ✅ Real-time updates working")
        print("  ✅ Learning analytics ready")
        print("\n🔗 Ready for integration with educational applications!")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed")
        print("Please check the issues above before deployment")
    
    print(f"\nEnd time: {datetime.now()}")
    print("="*60)
    
    return passed == total

def main():
    """Main execution"""
    success = run_integration_test()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
