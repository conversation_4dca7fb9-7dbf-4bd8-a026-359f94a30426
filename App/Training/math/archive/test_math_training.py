#!/usr/bin/env python3
"""
Simple test for math dataset training
"""

import os
import sys
import pandas as pd

# Add the pipeline directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'pipeline'))

def test_data_loading():
    """Test loading the math dataset"""
    data_path = "/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv"
    
    print(f"Testing data loading from: {data_path}")
    
    try:
        # Load dataset
        df = pd.read_csv(data_path, encoding='latin1')
        
        print(f"✅ Dataset loaded successfully!")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        # Basic exploration
        print(f"\nDataset Overview:")
        print(f"- Total interactions: {len(df):,}")
        print(f"- Unique students: {df['user_id'].nunique():,}")
        print(f"- Unique problems: {df['problem_id'].nunique():,}")
        print(f"- Unique skills: {df['skill_name'].nunique():,}")
        print(f"- Overall accuracy: {df['correct'].mean():.3f}")
        
        # Show sample data
        print(f"\nFirst 5 rows:")
        print(df.head())
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return False

def test_pipeline_import():
    """Test importing pipeline components"""
    try:
        from kt_training_pipeline import KnowledgeTracingPipeline
        print("✅ KnowledgeTracingPipeline imported successfully")
        
        pipeline = KnowledgeTracingPipeline()
        print("✅ Pipeline initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing pipeline: {e}")
        return False

def main():
    """Run simple tests"""
    print("="*50)
    print("SIMPLE MATH DATASET TRAINING TEST")
    print("="*50)
    
    # Test 1: Pipeline import
    print("\n1. Testing pipeline import...")
    import_success = test_pipeline_import()
    
    # Test 2: Data loading
    print("\n2. Testing data loading...")
    data_success = test_data_loading()
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    print(f"Pipeline import: {'✅ PASSED' if import_success else '❌ FAILED'}")
    print(f"Data loading: {'✅ PASSED' if data_success else '❌ FAILED'}")
    
    if import_success and data_success:
        print("\n🎉 All tests passed! Ready for full training.")
    else:
        print("\n⚠️ Some tests failed. Check the issues above.")
    
    return import_success and data_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
