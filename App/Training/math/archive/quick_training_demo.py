#!/usr/bin/env python3
"""
Quick Math Dataset Training Demo
===============================

This script demonstrates the KT pipeline on a subset of the math dataset
for faster testing and validation.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# Add the pipeline directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'pipeline'))

# Import pipeline components
from kt_training_pipeline import KnowledgeTracingPipeline
from kt_evaluation import KTEvaluator
from prediction import KTPredictor

def load_sample_data(sample_size=5000):
    """Load a sample of the math dataset for quick testing"""
    data_path = "/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv"
    
    print(f"Loading sample of {sample_size} interactions from math dataset...")
    
    # Load full dataset
    df_full = pd.read_csv(data_path, encoding='latin1')
    
    # Sample data while maintaining user sequences
    users = df_full['user_id'].unique()
    np.random.seed(42)
    sample_users = np.random.choice(users, size=min(100, len(users)), replace=False)
    
    # Get data for sampled users
    df_sample = df_full[df_full['user_id'].isin(sample_users)].copy()
    
    # If still too large, take first N interactions
    if len(df_sample) > sample_size:
        df_sample = df_sample.head(sample_size)
    
    print(f"✅ Sample loaded: {len(df_sample)} interactions from {df_sample['user_id'].nunique()} users")
    print(f"Skills in sample: {df_sample['skill_name'].nunique()}")
    print(f"Sample accuracy: {df_sample['correct'].mean():.3f}")
    
    return df_sample

def run_quick_training():
    """Run a quick training demo"""
    print("="*60)
    print("QUICK MATH DATASET TRAINING DEMO")
    print("="*60)
    print(f"Start time: {datetime.now()}")
    
    try:
        # Step 1: Load sample data
        df = load_sample_data(sample_size=3000)
        
        # Step 2: Initialize pipeline
        print("\n🚀 Initializing pipeline...")
        pipeline = KnowledgeTracingPipeline()
        
        # Step 3: Explore dataset
        print("\n📊 Exploring dataset...")
        dataset_stats = pipeline.explore_dataset(df)
        
        # Step 4: Preprocess data
        print("\n🔧 Preprocessing data...")
        df_clean = pipeline.preprocess_data(df)
        
        # Step 5: Split data
        print("\n📊 Splitting data...")
        train_df, test_df = pipeline.split_data(df_clean, test_size=0.2)
        
        print(f"Training set: {len(train_df)} interactions")
        print(f"Test set: {len(test_df)} interactions")
        
        # Step 6: Feature engineering
        print("\n⚙️ Engineering features...")
        pipeline.datasets = {
            'raw': df,
            'clean': df_clean,
            'train': train_df,
            'test': test_df,
            'stats': dataset_stats
        }
        
        train_features, test_features = pipeline.engineer_features(train_df, test_df)
        print("✅ Feature engineering completed")
        
        # Step 7: Train models (simplified for demo)
        print("\n🎯 Training models (simplified for demo)...")
        
        # Create output directories
        os.makedirs('outputs', exist_ok=True)
        os.makedirs('../reports/math', exist_ok=True)
        os.makedirs('../plots/math', exist_ok=True)
        
        # For demo, we'll just test the BKT model to save time
        from kt_model_trainer import KTModelTrainer
        trainer = KTModelTrainer(n_folds=2)  # Reduced folds for speed
        
        try:
            print("Training BKT model...")
            bkt_model, bkt_metrics = trainer.train_bkt(train_df, test_df, hyperparameter_tuning=False)
            print(f"✅ BKT training completed - Accuracy: {bkt_metrics.get('accuracy', 'N/A'):.3f}")
            
            # Save BKT model
            bkt_model.save('outputs/bkt_model_demo.joblib')
            print("✅ BKT model saved")
            
        except Exception as e:
            print(f"⚠️ BKT training failed: {e}")
            bkt_model = None
            bkt_metrics = {}
        
        # Step 8: Test predictions
        print("\n🔮 Testing predictions...")
        
        if bkt_model:
            # Test some predictions
            sample_skills = df_clean['skill_name'].value_counts().head(3).index.tolist()
            user_history = [1, 0, 1, 1, 0]
            
            print(f"Sample prediction test:")
            print(f"User history: {user_history}")
            
            for skill in sample_skills:
                try:
                    prob = bkt_model.predict_proba(user_history, skill)
                    print(f"  {skill}: {prob[-1]:.3f} probability")
                except Exception as e:
                    print(f"  {skill}: Error - {e}")
        
        # Step 9: Generate basic report
        print("\n📋 Generating report...")
        
        evaluator = KTEvaluator(output_dir='../plots/math')
        report = evaluator.generate_dataset_report(df_clean, dataset_stats)
        
        # Save report
        with open('../reports/math/quick_demo_report.txt', 'w') as f:
            f.write(report)
        print("✅ Report saved to ../reports/math/quick_demo_report.txt")
        
        # Summary
        end_time = datetime.now()
        duration = end_time - datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        print("\n" + "="*60)
        print("QUICK DEMO COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"End time: {end_time}")
        print(f"Dataset size: {len(df)} interactions")
        print(f"Skills analyzed: {df['skill_name'].nunique()}")
        print(f"Students: {df['user_id'].nunique()}")
        
        if bkt_model:
            print(f"BKT model accuracy: {bkt_metrics.get('accuracy', 'N/A'):.3f}")
        
        print(f"\n📁 Outputs:")
        print(f"- Models: outputs/")
        print(f"- Reports: ../reports/math/")
        print(f"- Plots: ../plots/math/")
        
        print(f"\n🚀 Ready for full training with complete dataset!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    success = run_quick_training()
    
    if success:
        print("\n✅ Quick demo completed successfully!")
        print("You can now run the full training script: python train_math_models.py")
    else:
        print("\n❌ Demo failed. Check the errors above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
