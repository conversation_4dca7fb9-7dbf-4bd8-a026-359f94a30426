2025-07-16 22:44:10,895 - INFO - Training PFA model...
Creating PFA features...
Training PFA model...
Model training completed!
Creating PFA features...
2025-07-16 22:48:12,562 - ERROR - Error during model training: The feature names should match those that were passed during fit.
Feature names unseen at fit time:
- skill_2.0
- skill_75.0
Feature names seen at fit time, yet now missing:
- skill_11.0
- skill_13.0

2025-07-16 22:48:12,563 - ERROR - Training failed: The feature names should match those that were passed during fit.
Feature names unseen at fit time:
- skill_2.0
- skill_75.0
Feature names seen at fit time, yet now missing:
- skill_11.0
- skill_13.0

2025-07-16 22:48:12,563 - ERROR - Check the log file for detailed error information


2025-07-16 23:27:26,096 - INFO - Starting model training...
Epoch 1/50
2025-07-16 23:27:32.139964: I tensorflow/core/framework/local_rendezvous.cc:404] Local rendezvous is aborting with status: INVALID_ARGUMENT: Incompatible shapes: [32] vs. [32,200]
         [[{{function_node __inference_one_step_on_data_8991}}{{node gradient_tape/compile_loss/binary_crossentropy/mul_3/BroadcastGradientArgs}}]]
2025-07-16 23:27:32,244 - ERROR - Error during model training: Graph execution error:

Detected at node gradient_tape/compile_loss/binary_crossentropy/mul_3/BroadcastGradientArgs defined at (most recent call last):
  File "/home/<USER>/workspace/AClass/App/Training/math/train_math_models.py", line 355, in <module>

  File "/home/<USER>/workspace/AClass/App/Training/math/train_math_models.py", line 335, in main

  File "/home/<USER>/workspace/AClass/App/Training/math/train_math_models.py", line 298, in run_complete_training

  File "/home/<USER>/workspace/AClass/App/Training/math/train_math_models.py", line 148, in train_models

  File "/home/<USER>/workspace/AClass/App/Training/math/../pipeline/kt_training_pipeline.py", line 269, in train_models

  File "/home/<USER>/workspace/AClass/App/Training/math/../pipeline/kt_model_trainer.py", line 137, in train_dkt

  File "/home/<USER>/workspace/AClass/App/Training/math/../pipeline/../models/train_dkt.py", line 285, in fit

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 371, in fit

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 219, in function

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 132, in multi_step_on_iterator

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 113, in one_step_on_data

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 77, in train_step

Incompatible shapes: [32] vs. [32,200]
         [[{{node gradient_tape/compile_loss/binary_crossentropy/mul_3/BroadcastGradientArgs}}]] [Op:__inference_multi_step_on_iterator_9066]
2025-07-16 23:27:32,244 - ERROR - Training failed: Graph execution error:

Detected at node gradient_tape/compile_loss/binary_crossentropy/mul_3/BroadcastGradientArgs defined at (most recent call last):
  File "/home/<USER>/workspace/AClass/App/Training/math/train_math_models.py", line 355, in <module>

  File "/home/<USER>/workspace/AClass/App/Training/math/train_math_models.py", line 335, in main

  File "/home/<USER>/workspace/AClass/App/Training/math/train_math_models.py", line 298, in run_complete_training

  File "/home/<USER>/workspace/AClass/App/Training/math/train_math_models.py", line 148, in train_models

  File "/home/<USER>/workspace/AClass/App/Training/math/../pipeline/kt_training_pipeline.py", line 269, in train_models

  File "/home/<USER>/workspace/AClass/App/Training/math/../pipeline/kt_model_trainer.py", line 137, in train_dkt

  File "/home/<USER>/workspace/AClass/App/Training/math/../pipeline/../models/train_dkt.py", line 285, in fit

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 371, in fit

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 219, in function

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 132, in multi_step_on_iterator

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 113, in one_step_on_data

  File "/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/backend/tensorflow/trainer.py", line 77, in train_step

Incompatible shapes: [32] vs. [32,200]
         [[{{node gradient_tape/compile_loss/binary_crossentropy/mul_3/BroadcastGradientArgs}}]] [Op:__inference_multi_step_on_iterator_9066]
2025-07-16 23:27:32,244 - ERROR - Check the log file for detailed error information





Creating PFA features...
Training PFA model...
Model training completed!
Creating PFA features...
2025-07-17 01:04:44,282 - ERROR - Error during model training: The feature names should match those that were passed during fit.
Feature names seen at fit time, yet now missing:
- skill_340.0
- skill_348.0

2025-07-17 01:04:44,282 - ERROR - Training failed: The feature names should match those that were passed during fit.
Feature names seen at fit time, yet now missing:
- skill_340.0
- skill_348.0

2025-07-17 01:04:44,282 - ERROR - Check the log file for detailed error information

❌ T




Training completed but with different errors :

Restoring model weights from the end of the best epoch: 20.
2025-07-17 00:17:32,043 - INFO - Model training completed!
2025-07-17 00:17:32,064 - INFO - Evaluating DKT model...
2025-07-17 00:17:32,064 - INFO - Preparing data for DKT...
2025-07-17 00:17:32,113 - INFO - Data prepared: 65861 interactions
2025-07-17 00:17:32,121 - INFO - Creating sequences for DKT...
2025-07-17 00:17:32,441 - INFO - Created 805 sequences
2025-07-17 00:17:35,222 - ERROR - Error during model training: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-17 00:17:35,223 - ERROR - Training failed: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-17 00:17:35,223 - ERROR - Check the log file for detailed error information
