#!/usr/bin/env python3
"""
Simple DKT Model Testing Script
==============================

This script tests a simplified version of the DKT model to avoid shape issues.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, Embedding
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.utils import pad_sequences
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

def load_sample_data(n_rows=10000):
    """Load a sample of the math dataset"""
    data_path = "/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv"
    
    print(f"Loading {n_rows} rows from math dataset...")
    
    try:
        df = pd.read_csv(data_path, encoding='latin1', nrows=n_rows)
        
        print(f"✅ Dataset loaded successfully!")
        print(f"Shape: {df.shape}")
        
        # Basic info
        print(f"\nDataset Overview:")
        print(f"- Total interactions: {len(df):,}")
        print(f"- Unique students: {df['user_id'].nunique():,}")
        print(f"- Unique skills: {df['skill_name'].nunique():,}")
        print(f"- Overall accuracy: {df['correct'].mean():.3f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        raise

def preprocess_data(df):
    """Clean and preprocess the data"""
    print("\n🔧 Preprocessing data...")
    
    # Required columns
    required_cols = ['user_id', 'skill_name', 'correct']
    
    # Check for required columns
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing required columns: {missing_cols}")
    
    # Clean data
    df_clean = df.dropna(subset=required_cols).copy()
    df_clean = df_clean.drop_duplicates()
    
    # Ensure correct data types
    df_clean['correct'] = df_clean['correct'].astype(int)
    df_clean['user_id'] = df_clean['user_id'].astype(str)
    df_clean['skill_name'] = df_clean['skill_name'].astype(str)
    
    # Sort by user and create order
    df_clean = df_clean.sort_values(['user_id'])
    df_clean['order_id'] = df_clean.groupby('user_id').cumcount() + 1
    
    print(f"✅ Data preprocessing completed")
    print(f"Cleaned size: {len(df_clean)}")
    
    return df_clean

def create_simple_sequences(df, max_seq_len=20):
    """Create simple sequences for DKT"""
    print(f"\n⚙️ Creating sequences (max length: {max_seq_len})...")
    
    # Encode skills
    unique_skills = df['skill_name'].unique()
    skill_to_id = {skill: idx for idx, skill in enumerate(unique_skills)}
    n_skills = len(unique_skills)
    
    print(f"Number of skills: {n_skills}")
    
    sequences = []
    
    for user_id, user_data in df.groupby('user_id'):
        user_data = user_data.sort_values('order_id')
        
        if len(user_data) < 2:  # Need at least 2 interactions
            continue
        
        # Create sequences of fixed length
        skills = [skill_to_id[skill] for skill in user_data['skill_name']]
        corrects = user_data['correct'].tolist()
        
        # Create input-output pairs
        for i in range(1, min(len(skills), max_seq_len)):
            # Input: previous skills and responses
            input_seq = []
            for j in range(i):
                # Combine skill and previous response
                skill_response = skills[j] + (n_skills * corrects[j])
                input_seq.append(skill_response)
            
            # Pad sequence to fixed length
            while len(input_seq) < max_seq_len - 1:
                input_seq.append(0)  # Padding
            
            # Target: current response
            target = corrects[i]
            
            sequences.append({
                'input': input_seq[:max_seq_len-1],  # Ensure fixed length
                'target': target,
                'length': i
            })
    
    print(f"✅ Created {len(sequences)} sequences")
    return sequences, n_skills

def build_simple_dkt_model(vocab_size, seq_len, hidden_dim=32):
    """Build a simple DKT model"""
    print(f"\n🏗️ Building simple DKT model...")
    print(f"Vocab size: {vocab_size}, Sequence length: {seq_len}, Hidden dim: {hidden_dim}")
    
    model = Sequential([
        Embedding(vocab_size + 1, hidden_dim, input_length=seq_len, mask_zero=True),
        LSTM(hidden_dim, dropout=0.2),
        Dense(16, activation='relu'),
        Dropout(0.2),
        Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['accuracy']
    )
    
    print("✅ Model built successfully")
    model.summary()
    
    return model

def test_simple_dkt():
    """Test the simple DKT model"""
    print("="*60)
    print("SIMPLE DKT MODEL TESTING")
    print("="*60)
    print(f"Start time: {datetime.now()}")
    
    try:
        # Step 1: Load and preprocess data
        df = load_sample_data(n_rows=5000)  # Smaller dataset for testing
        df_clean = preprocess_data(df)
        
        # Step 2: Create sequences
        sequences, n_skills = create_simple_sequences(df_clean, max_seq_len=10)
        
        if len(sequences) == 0:
            print("❌ No sequences created")
            return False
        
        # Step 3: Prepare data for training
        print(f"\n📊 Preparing training data...")
        
        X = np.array([seq['input'] for seq in sequences])
        y = np.array([seq['target'] for seq in sequences])
        
        print(f"X shape: {X.shape}")
        print(f"y shape: {y.shape}")
        
        # Split data
        split_idx = int(len(sequences) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        print(f"Training set: {X_train.shape}")
        print(f"Test set: {X_test.shape}")
        
        # Step 4: Build and train model
        vocab_size = n_skills * 2  # skills * 2 (for correct/incorrect)
        seq_len = X.shape[1]
        
        model = build_simple_dkt_model(vocab_size, seq_len, hidden_dim=32)
        
        print(f"\n🎯 Training model...")
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=3,  # Few epochs for testing
            batch_size=32,
            verbose=1
        )
        
        # Step 5: Evaluate model
        print(f"\n📊 Evaluating model...")
        
        y_pred_proba = model.predict(X_test)
        y_pred = (y_pred_proba > 0.5).astype(int).flatten()
        
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)
        f1 = f1_score(y_test, y_pred, zero_division=0)
        
        print(f"\n📈 Model Performance:")
        print(f"  Accuracy:  {accuracy:.4f}")
        print(f"  Precision: {precision:.4f}")
        print(f"  Recall:    {recall:.4f}")
        print(f"  F1-Score:  {f1:.4f}")
        
        # Step 6: Test predictions
        print(f"\n🔮 Testing predictions...")
        
        # Test with a few examples
        test_examples = X_test[:5]
        test_predictions = model.predict(test_examples)
        
        for i, (example, pred, actual) in enumerate(zip(test_examples, test_predictions, y_test[:5])):
            print(f"  Example {i+1}: Predicted {pred[0]:.3f}, Actual {actual}")
        
        # Step 7: Save model
        print(f"\n💾 Saving model...")
        os.makedirs('outputs', exist_ok=True)
        model.save('outputs/simple_dkt_model.h5')
        print("✅ Model saved to outputs/simple_dkt_model.h5")
        
        print(f"\n✅ Simple DKT test completed successfully!")
        print(f"Final accuracy: {accuracy:.3f}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Simple DKT test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    success = test_simple_dkt()
    
    if success:
        print("\n🎉 Simple DKT model test passed!")
    else:
        print("\n❌ Simple DKT model test failed!")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
