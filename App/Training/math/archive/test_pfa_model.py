#!/usr/bin/env python3
"""
PFA Model Testing Script
=======================

This script tests the PFA (Performance Factor Analysis) model using 10,000 rows
from the math dataset to validate functionality and fix any issues.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add the models directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'models'))

# Import PFA model
from train_pfa import PFAKnowledgeTracing

def load_sample_data(n_rows=10000):
    """Load a sample of the math dataset"""
    data_path = "/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv"
    
    print(f"Loading {n_rows} rows from math dataset...")
    
    try:
        # Load dataset with specified number of rows
        df = pd.read_csv(data_path, encoding='latin1', nrows=n_rows)
        
        print(f"✅ Dataset loaded successfully!")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        # Basic info
        print(f"\nDataset Overview:")
        print(f"- Total interactions: {len(df):,}")
        print(f"- Unique students: {df['user_id'].nunique():,}")
        print(f"- Unique problems: {df['problem_id'].nunique():,}")
        print(f"- Unique skills: {df['skill_name'].nunique():,}")
        print(f"- Overall accuracy: {df['correct'].mean():.3f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        raise

def preprocess_data(df):
    """Clean and preprocess the data"""
    print("\n🔧 Preprocessing data...")
    
    # Required columns for PFA
    required_cols = ['user_id', 'problem_id', 'skill_name', 'correct']
    
    # Check for required columns
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing required columns: {missing_cols}")
    
    # Remove rows with missing values in essential columns
    df_clean = df.dropna(subset=required_cols).copy()
    
    # Remove duplicates
    df_clean = df_clean.drop_duplicates()
    
    # Ensure correct data types
    df_clean['correct'] = df_clean['correct'].astype(int)
    df_clean['user_id'] = df_clean['user_id'].astype(str)
    df_clean['problem_id'] = df_clean['problem_id'].astype(str)
    df_clean['skill_name'] = df_clean['skill_name'].astype(str)
    
    # Sort by user and order if available
    if 'order_id' in df_clean.columns:
        df_clean = df_clean.sort_values(['user_id', 'order_id'])
    else:
        # Create a simple ordering based on row index
        df_clean = df_clean.sort_values(['user_id'])
        df_clean['order_id'] = df_clean.groupby('user_id').cumcount() + 1
    
    print(f"✅ Data preprocessing completed")
    print(f"Original size: {len(df)}, Cleaned size: {len(df_clean)}")
    print(f"Removed {len(df) - len(df_clean)} rows")
    
    return df_clean

def split_data(df, test_size=0.2, random_state=42):
    """Split data into train and test sets by users"""
    print(f"\n📊 Splitting data (test_size={test_size})...")
    
    # Split by users to avoid data leakage
    unique_users = df['user_id'].unique()
    np.random.seed(random_state)
    np.random.shuffle(unique_users)
    
    n_test_users = int(len(unique_users) * test_size)
    test_users = unique_users[:n_test_users]
    train_users = unique_users[n_test_users:]
    
    train_df = df[df['user_id'].isin(train_users)].copy()
    test_df = df[df['user_id'].isin(test_users)].copy()
    
    print(f"✅ Data split completed")
    print(f"Train set: {len(train_df)} interactions from {len(train_users)} users")
    print(f"Test set: {len(test_df)} interactions from {len(test_users)} users")
    
    return train_df, test_df

def test_pfa_model(train_df, test_df):
    """Test the PFA model training and evaluation"""
    print("\n🎯 TESTING PFA MODEL")
    print("="*40)
    
    try:
        # Initialize PFA model
        print("Initializing PFA model...")
        pfa = PFAKnowledgeTracing()
        
        # Prepare features for training
        print("Preparing training features...")
        X_train, y_train, train_features_df = pfa.prepare_features(train_df)
        
        print(f"Training features shape: {X_train.shape}")
        print(f"Training targets shape: {y_train.shape}")
        print(f"Feature columns: {X_train.shape[1]}")
        
        # Train the model
        print("Training PFA model...")
        pfa.fit(X_train, y_train, train_features_df)
        print("✅ PFA model training completed!")
        
        # Prepare test features
        print("Preparing test features...")
        X_test, y_test, test_features_df = pfa.prepare_features(test_df)
        
        print(f"Test features shape: {X_test.shape}")
        print(f"Test targets shape: {y_test.shape}")
        
        # Evaluate the model
        print("Evaluating PFA model...")
        metrics = pfa.evaluate(X_test, y_test)
        
        print(f"\n📊 PFA Model Performance:")
        for metric, value in metrics.items():
            print(f"  {metric:12}: {value:.4f}")
        
        # Test predictions
        print("\n🔮 Testing predictions...")
        
        # Get some sample skills from the test set
        sample_skills = test_df['skill_name'].value_counts().head(3).index.tolist()
        user_history = [1, 0, 1, 1, 0]  # Sample history
        
        for skill in sample_skills:
            try:
                # Create a simple test case
                test_case = pd.DataFrame({
                    'user_id': ['test_user'],
                    'skill_name': [skill],
                    'problem_id': ['test_problem'],
                    'correct': [1],  # Dummy value
                    'order_id': [1]
                })
                
                X_pred, _, _ = pfa.prepare_features(test_case)
                if len(X_pred) > 0:
                    pred_proba = pfa.model.predict_proba(pfa.scaler.transform(X_pred))
                    if pred_proba.shape[1] > 1:
                        prob = pred_proba[0, 1]
                    else:
                        prob = pred_proba[0, 0]
                    print(f"  {skill}: {prob:.3f} probability")
                else:
                    print(f"  {skill}: No features generated")
                    
            except Exception as e:
                print(f"  {skill}: Error - {e}")
        
        # Save the model
        print("\n💾 Saving PFA model...")
        os.makedirs('outputs', exist_ok=True)
        pfa.save('outputs/pfa_model_test.joblib')
        print("✅ PFA model saved to outputs/pfa_model_test.joblib")
        
        return True, pfa, metrics
        
    except Exception as e:
        print(f"❌ PFA model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None, {}

def main():
    """Main execution function"""
    print("="*60)
    print("PFA MODEL TESTING SCRIPT")
    print("="*60)
    print(f"Start time: {datetime.now()}")
    
    try:
        # Step 1: Load sample data
        df = load_sample_data(n_rows=10000)
        
        # Step 2: Preprocess data
        df_clean = preprocess_data(df)
        
        # Step 3: Split data
        train_df, test_df = split_data(df_clean, test_size=0.2)
        
        # Step 4: Test PFA model
        success, pfa_model, metrics = test_pfa_model(train_df, test_df)
        
        # Summary
        end_time = datetime.now()
        
        print("\n" + "="*60)
        if success:
            print("✅ PFA MODEL TEST COMPLETED SUCCESSFULLY!")
            print(f"Model accuracy: {metrics.get('accuracy', 'N/A'):.3f}")
            print(f"Model saved to: outputs/pfa_model_test.joblib")
        else:
            print("❌ PFA MODEL TEST FAILED!")
            print("Check the error messages above for details")
        
        print(f"End time: {end_time}")
        print("="*60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ Test script failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
