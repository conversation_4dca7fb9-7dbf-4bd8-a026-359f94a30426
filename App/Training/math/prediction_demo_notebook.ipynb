{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Knowledge Tracing Prediction Demo\n", "\n", "This notebook demonstrates how to use the knowledge tracing prediction system to predict student knowledge states and analyze learning mastery. It showcases real-time prediction capabilities for adaptive learning applications.\n", "\n", "## Features\n", "- Load and use trained KT models (BKT, PFA, DKT, Ensemble)\n", "- Predict student performance for specific skills\n", "- Analyze learning mastery states\n", "- Compare different model predictions\n", "- Simulate real-time learning scenarios\n", "\n", "## Use Cases\n", "- Real-time student assessment\n", "- Adaptive learning path recommendations\n", "- Learning analytics and progress tracking\n", "- Educational intervention triggers"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:29:13.457971: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📚 Libraries imported successfully!\n", "Current working directory: /home/<USER>/workspace/AClass/App/Training/math\n"]}], "source": ["# Import required libraries\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add pipeline directory to path\n", "sys.path.append('../pipeline')\n", "\n", "# Import prediction module\n", "from prediction import KTPredictor\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📚 Libraries imported successfully!\")\n", "print(f\"Current working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Initialize Prediction System"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Initializing Knowledge Tracing Predictor...\n", "\n", "📊 Loading trained models...\n", "\n", "✅ Models loaded successfully!\n", "Available models: ['bkt', 'pfa', 'dkt', 'ensemble']\n", "\n", "🎯 Ready for prediction with 4 models!\n"]}], "source": ["# Initialize the knowledge tracing predictor\n", "print(\"🚀 Initializing Knowledge Tracing Predictor...\")\n", "predictor = KTPredictor(models_dir=\"../models/outputs\")\n", "\n", "# Load trained models\n", "print(\"\\n📊 Loading trained models...\")\n", "load_status = predictor.load_models()\n", "\n", "print(f\"\\n✅ Models loaded successfully!\")\n", "loaded_models = [model for model, status in load_status.items() if status]\n", "print(f\"Available models: {loaded_models}\")\n", "\n", "if not loaded_models:\n", "    print(\"⚠️ No models loaded. Please ensure models are trained and saved.\")\n", "else:\n", "    print(f\"\\n🎯 Ready for prediction with {len(loaded_models)} models!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Basic Prediction Examples"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📈 Example 1: Single Prediction\n", "========================================\n", "Student history: [1, 0, 1, 1, 0, 1]\n", "Target skill: Median\n", "Recent performance: 0.67\n", "\n", "Predictions from different models:\n", "  BKT         : 0.580\n", "  PFA         : 0.600\n", "  DKT         : 0.671\n", "  ENSEMBLE    : 0.572\n"]}], "source": ["# Example 1: Single prediction\n", "print(\"📈 Example 1: Single Prediction\")\n", "print(\"=\" * 40)\n", "\n", "# Student interaction history (1=correct, 0=incorrect)\n", "user_history = [1, 0, 1, 1, 0, 1]\n", "target_skill = \"Median\"\n", "\n", "print(f\"Student history: {user_history}\")\n", "print(f\"Target skill: {target_skill}\")\n", "print(f\"Recent performance: {np.mean(user_history[-3:]):.2f}\")\n", "\n", "# Get predictions from all available models\n", "print(\"\\nPredictions from different models:\")\n", "for model_name in loaded_models:\n", "    try:\n", "        prediction = predictor.predict_student_performance(\n", "            user_history=user_history,\n", "            skill=target_skill,\n", "            model_type=model_name\n", "        )\n", "        print(f\"  {model_name.upper():12}: {prediction:.3f}\")\n", "    except Exception as e:\n", "        print(f\"  {model_name.upper():12}: Error - {e}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Example 2: Model Comparison Visualization\n", "=============================================\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📋 Summary Statistics:\n", "                              mean    std\n", "<PERSON><PERSON>                        \n", "BKT      Average Student     0.559  0.164\n", "         High Performer      0.559  0.164\n", "         Improving Student   0.559  0.164\n", "         Struggling Student  0.559  0.164\n", "DKT      Average Student     0.671  0.000\n", "         High Performer      0.788  0.000\n", "         Improving Student   0.646  0.000\n", "         Struggling Student  0.452  0.000\n", "ENSEMBLE Average Student     0.557  0.093\n", "         High Performer      0.546  0.094\n", "         Improving Student   0.560  0.093\n", "         Struggling Student  0.578  0.092\n", "PFA      Average Student     0.600  0.000\n", "         High Performer      0.800  0.000\n", "         Improving Student   0.600  0.000\n", "         Struggling Student  0.400  0.000\n"]}], "source": ["# Example 2: Model comparison visualization\n", "print(\"\\n📊 Example 2: Model Comparison Visualization\")\n", "print(\"=\" * 45)\n", "\n", "# Test different scenarios\n", "scenarios = {\n", "    \"Struggling Student\": [0, 0, 1, 0, 0, 1],\n", "    \"Average Student\": [1, 0, 1, 1, 0, 1],\n", "    \"High Performer\": [1, 1, 1, 1, 0, 1],\n", "    \"Improving Student\": [0, 0, 0, 1, 1, 1]\n", "}\n", "\n", "skills = [\"Median\", \"Mean\", \"<PERSON>\", \"Percent Of\"]\n", "\n", "# Create comparison data\n", "comparison_data = []\n", "for scenario_name, history in scenarios.items():\n", "    for skill in skills:\n", "        for model_name in loaded_models:\n", "            try:\n", "                prediction = predictor.predict_student_performance(\n", "                    user_history=history,\n", "                    skill=skill,\n", "                    model_type=model_name\n", "                )\n", "                comparison_data.append({\n", "                    'Scenario': scenario_name,\n", "                    'Skill': skill,\n", "                    'Model': model_name.upper(),\n", "                    'Prediction': prediction,\n", "                    'Recent_Performance': np.mean(history[-3:])\n", "                })\n", "            except Exception as e:\n", "                print(f\"Error with {model_name} for {scenario_name}-{skill}: {e}\")\n", "\n", "# Convert to DataFrame\n", "comparison_df = pd.DataFrame(comparison_data)\n", "\n", "if not comparison_df.empty:\n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    axes = axes.flatten()\n", "    \n", "    for i, skill in enumerate(skills):\n", "        skill_data = comparison_df[comparison_df['Skill'] == skill]\n", "        if not skill_data.empty:\n", "            sns.barplot(data=skill_data, x='Scenario', y='Prediction', hue='Model', ax=axes[i])\n", "            axes[i].set_title(f'{skill} - Model Predictions', fontweight='bold')\n", "            axes[i].set_xlabel('Student Scenario')\n", "            axes[i].set_ylabel('Predicted Success Probability')\n", "            axes[i].tick_params(axis='x', rotation=45)\n", "            axes[i].legend(title='Model')\n", "            axes[i].set_ylim(0, 1)\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle('Knowledge Tracing Model Predictions Comparison', fontsize=16, fontweight='bold', y=1.02)\n", "    plt.show()\n", "    \n", "    print(\"\\n📋 Summary Statistics:\")\n", "    print(comparison_df.groupby(['Model', 'Sc<PERSON><PERSON>'])['Prediction'].agg(['mean', 'std']).round(3))\n", "else:\n", "    print(\"No comparison data available.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Learning Mastery State Analysis"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🎯 Learning Mastery State Analysis\n", "========================================\n", "Using ENSEMBLE model for mastery analysis\n", "\n", "📊 Student A Mastery Analysis:\n", "------------------------------\n", "  Median          🟠 Developing   (Score: 0.549)\n", "    💡 📖 Review Median fundamentals and practice\n", "  Mean            🟠 Developing   (Score: 0.487)\n", "    💡 📖 Review Mean fundamentals and practice\n", "  Mode            🟡 Proficient   (Score: 0.706)\n", "    💡 📚 Practice more complex Mode problems\n", "  Percent Of      🟠 Developing   (Score: 0.524)\n", "    💡 📖 Review Percent Of fundamentals and practice\n", "\n", "📊 Student B Mastery Analysis:\n", "------------------------------\n", "  Median          🟠 Developing   (Score: 0.553)\n", "    💡 📖 Review Median fundamentals and practice\n", "  Mean            🟠 Developing   (Score: 0.508)\n", "    💡 📖 Review Mean fundamentals and practice\n", "  Mode            🟡 Proficient   (Score: 0.685)\n", "    💡 📚 Practice more complex Mode problems\n", "  Percent Of      🟠 Developing   (Score: 0.452)\n", "    💡 📖 Review Percent Of fundamentals and practice\n", "\n", "📊 Student C Mastery Analysis:\n", "------------------------------\n", "  Median          🟡 Proficient   (Score: 0.613)\n", "    💡 📚 Practice more complex Median problems\n", "  Mean            🟠 Developing   (Score: 0.558)\n", "    💡 📖 Review Mean fundamentals and practice\n", "  Mode            🟡 Proficient   (Score: 0.708)\n", "    💡 📚 Practice more complex Mode problems\n", "  Percent Of      🟠 Developing   (Score: 0.528)\n", "    💡 📖 Review Percent Of fundamentals and practice\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Mastery Distribution:\n", "  Developing  :  8 ( 66.7%)\n", "  Proficient  :  4 ( 33.3%)\n"]}], "source": ["# Learning mastery analysis\n", "print(\"🎯 Learning Mastery State Analysis\")\n", "print(\"=\" * 40)\n", "\n", "def analyze_mastery_state(prediction_score):\n", "    \"\"\"Analyze learning mastery based on prediction score\"\"\"\n", "    if prediction_score >= 0.8:\n", "        return \"Mastered\", \"🟢\"\n", "    elif prediction_score >= 0.6:\n", "        return \"Proficient\", \"🟡\"\n", "    elif prediction_score >= 0.4:\n", "        return \"Developing\", \"🟠\"\n", "    else:\n", "        return \"Struggling\", \"🔴\"\n", "\n", "def get_learning_recommendations(mastery_state, skill):\n", "    \"\"\"Get learning recommendations based on mastery state\"\"\"\n", "    recommendations = {\n", "        \"Mastered\": f\"✅ Move to advanced {skill} topics or related skills\",\n", "        \"Proficient\": f\"📚 Practice more complex {skill} problems\",\n", "        \"Developing\": f\"📖 Review {skill} fundamentals and practice\",\n", "        \"Struggling\": f\"🆘 Provide Medianal support and scaffolding for {skill}\"\n", "    }\n", "    return recommendations.get(mastery_state, \"Continue practicing\")\n", "\n", "# Analyze different student profiles\n", "student_profiles = {\n", "    \"Student A\": {\n", "        \"Median\": [1, 1, 1, 1, 1],\n", "        \"Mean\": [1, 0, 1, 1, 1],\n", "        \"Mode\": [0, 1, 0, 1, 0],\n", "        \"Percent Of\": [0, 0, 1, 0, 0]\n", "    },\n", "    \"Student B\": {\n", "        \"Median\": [1, 1, 0, 1, 1],\n", "        \"Mean\": [0, 1, 1, 0, 1],\n", "        \"Mode\": [1, 1, 1, 1, 0],\n", "        \"Percent Of\": [1, 0, 1, 1, 1]\n", "    },\n", "    \"Student C\": {\n", "        \"Median\": [0, 0, 0, 1, 0],\n", "        \"Mean\": [0, 0, 1, 0, 0],\n", "        \"Mode\": [0, 0, 0, 0, 1],\n", "        \"Percent Of\": [0, 0, 0, 0, 0]\n", "    }\n", "}\n", "\n", "# Use the best available model for analysis\n", "best_model = 'ensemble' if 'ensemble' in loaded_models else loaded_models[0]\n", "print(f\"Using {best_model.upper()} model for mastery analysis\\n\")\n", "\n", "mastery_results = []\n", "\n", "for student_name, skills_data in student_profiles.items():\n", "    print(f\"📊 {student_name} Mastery Analysis:\")\n", "    print(\"-\" * 30)\n", "    \n", "    for skill, history in skills_data.items():\n", "        try:\n", "            prediction = predictor.predict_student_performance(\n", "                user_history=history,\n", "                skill=skill,\n", "                model_type=best_model\n", "            )\n", "            \n", "            mastery_state, emoji = analyze_mastery_state(prediction)\n", "            recommendation = get_learning_recommendations(mastery_state, skill)\n", "            \n", "            print(f\"  {skill:15} {emoji} {mastery_state:12} (Score: {prediction:.3f})\")\n", "            print(f\"    💡 {recommendation}\")\n", "            \n", "            mastery_results.append({\n", "                'Student': student_name,\n", "                'Skill': skill,\n", "                'Prediction': prediction,\n", "                'Mastery_State': mastery_state,\n", "                'Recent_Performance': np.mean(history[-3:])\n", "            })\n", "            \n", "        except Exception as e:\n", "            print(f\"  {skill:15} ❌ Error: {e}\")\n", "    \n", "    print()\n", "\n", "# Create mastery visualization\n", "if mastery_results:\n", "    mastery_df = pd.DataFrame(mastery_results)\n", "    \n", "    # Create heatmap of mastery states\n", "    pivot_df = mastery_df.pivot(index='Student', columns='Skill', values='Prediction')\n", "    \n", "    plt.figure(figsize=(10, 6))\n", "    sns.heatmap(pivot_df, annot=True, cmap='RdYlGn', center=0.5, \n", "                fmt='.3f', cbar_kws={'label': 'Predicted Success Probability'})\n", "    plt.title('Student Mastery Heatmap', fontsize=14, fontweight='bold')\n", "    plt.xlabel('Skills')\n", "    plt.ylabel('Students')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"\\n📈 Mastery Distribution:\")\n", "    mastery_counts = mastery_df['Mastery_State'].value_counts()\n", "    for state, count in mastery_counts.items():\n", "        percentage = (count / len(mastery_df)) * 100\n", "        print(f\"  {state:12}: {count:2d} ({percentage:5.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Real-Time Learning Simulation"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔄 Real-Time Learning Simulation\n", "========================================\n", "🎯 Simulating learning session for 'Median' using ENSEMBLE model\n", "Initial history: [0, 1, 0, 1, 0]\n", "Initial performance: 0.40\n", "\n", "Interaction  1: ✅ Correct | Prediction: 0.599 | State: 🟠 Developing\n", "Interaction  2: ✅ Correct | Prediction: 0.584 | State: 🟠 Developing\n", "Interaction  3: ✅ Correct | Prediction: 0.569 | State: 🟠 Developing\n", "Interaction  4: ✅ Correct | Prediction: 0.569 | State: 🟠 Developing\n", "Interaction  5: ✅ Correct | Prediction: 0.564 | State: 🟠 Developing\n", "Interaction  6: ❌ Incorrect | Prediction: 0.561 | State: 🟠 Developing\n", "Interaction  7: ❌ Incorrect | Prediction: 0.578 | State: 🟠 Developing\n", "Interaction  8: ❌ Incorrect | Prediction: 0.586 | State: 🟠 Developing\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Session Summary:\n", "Initial prediction: 0.599\n", "Final prediction: 0.586\n", "Improvement: -0.013\n", "Session accuracy: 0.62\n", "Total interactions: 8\n", "Final mastery state: 🟠 <PERSON><PERSON>ping\n"]}], "source": ["# Real-time learning simulation\n", "print(\"🔄 Real-Time Learning Simulation\")\n", "print(\"=\" * 40)\n", "\n", "def simulate_learning_session(predictor, initial_history, skill, model_type, n_interactions=10):\n", "    \"\"\"Simulate a real-time learning session\"\"\"\n", "    history = initial_history.copy()\n", "    predictions = []\n", "    interactions = []\n", "    \n", "    print(f\"🎯 Simulating learning session for '{skill}' using {model_type.upper()} model\")\n", "    print(f\"Initial history: {initial_history}\")\n", "    print(f\"Initial performance: {np.mean(initial_history):.2f}\\n\")\n", "    \n", "    for i in range(n_interactions):\n", "        # Get current prediction\n", "        try:\n", "            current_prediction = predictor.predict_student_performance(\n", "                user_history=history,\n", "                skill=skill,\n", "                model_type=model_type\n", "            )\n", "            predictions.append(current_prediction)\n", "            \n", "            # Simulate student response based on prediction + some randomness\n", "            # Higher prediction = higher chance of correct response\n", "            response_prob = current_prediction * 0.8 + np.random.normal(0, 0.1)\n", "            response_prob = max(0.1, min(0.9, response_prob))  # Clamp between 0.1 and 0.9\n", "            \n", "            student_response = 1 if np.random.random() < response_prob else 0\n", "            history.append(student_response)\n", "            interactions.append(student_response)\n", "            \n", "            # Real-time feedback\n", "            status = \"✅ Correct\" if student_response == 1 else \"❌ Incorrect\"\n", "            mastery_state, emoji = analyze_mastery_state(current_prediction)\n", "            \n", "            print(f\"Interaction {i+1:2d}: {status} | Prediction: {current_prediction:.3f} | State: {emoji} {mastery_state}\")\n", "            \n", "            # Adaptive feedback\n", "            if current_prediction < 0.4 and i > 2:\n", "                print(f\"              💡 Suggestion: Provide Medianal scaffolding\")\n", "            elif current_prediction > 0.8:\n", "                print(f\"              🚀 Suggestion: Increase difficulty level\")\n", "                \n", "        except Exception as e:\n", "            print(f\"Interaction {i+1:2d}: Error - {e}\")\n", "            predictions.append(0.5)\n", "            interactions.append(0)\n", "    \n", "    return history, predictions, interactions\n", "\n", "# Run simulation\n", "if loaded_models:\n", "    initial_student_history = [0, 1, 0, 1, 0]  # Mixed performance\n", "    target_skill = \"Median\"\n", "    model_to_use = 'ensemble' if 'ensemble' in loaded_models else loaded_models[0]\n", "    \n", "    final_history, prediction_trajectory, new_interactions = simulate_learning_session(\n", "        predictor=predictor,\n", "        initial_history=initial_student_history,\n", "        skill=target_skill,\n", "        model_type=model_to_use,\n", "        n_interactions=8\n", "    )\n", "    \n", "    # Visualize learning trajectory\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # Plot 1: Prediction trajectory\n", "    plt.subplot(2, 1, 1)\n", "    interaction_numbers = range(1, len(prediction_trajectory) + 1)\n", "    plt.plot(interaction_numbers, prediction_trajectory, 'b-o', linewidth=2, markersize=6)\n", "    plt.axhline(y=0.8, color='g', linestyle='--', alpha=0.7, label='Mastery Threshold')\n", "    plt.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='Proficiency Threshold')\n", "    plt.axhline(y=0.4, color='r', linestyle='--', alpha=0.7, label='Struggling Threshold')\n", "    plt.xlabel('Interaction Number')\n", "    plt.ylabel('Predicted Success Probability')\n", "    plt.title(f'Learning Trajectory for {target_skill} ({model_to_use.upper()} Model)', fontweight='bold')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    plt.ylim(0, 1)\n", "    \n", "    # Plot 2: Student responses\n", "    plt.subplot(2, 1, 2)\n", "    colors = ['red' if x == 0 else 'green' for x in new_interactions]\n", "    plt.bar(interaction_numbers, new_interactions, color=colors, alpha=0.7)\n", "    plt.xlabel('Interaction Number')\n", "    plt.ylabel('Student Response (1=Correct, 0=Incorrect)')\n", "    plt.title('Student Responses During Session', fontweight='bold')\n", "    plt.ylim(-0.1, 1.1)\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Session summary\n", "    print(f\"\\n📊 Session Summary:\")\n", "    print(f\"Initial prediction: {prediction_trajectory[0]:.3f}\")\n", "    print(f\"Final prediction: {prediction_trajectory[-1]:.3f}\")\n", "    print(f\"Improvement: {prediction_trajectory[-1] - prediction_trajectory[0]:+.3f}\")\n", "    print(f\"Session accuracy: {np.mean(new_interactions):.2f}\")\n", "    print(f\"Total interactions: {len(new_interactions)}\")\n", "    \n", "    final_mastery, final_emoji = analyze_mastery_state(prediction_trajectory[-1])\n", "    print(f\"Final mastery state: {final_emoji} {final_mastery}\")\n", "else:\n", "    print(\"No models available for simulation.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Production Integration Example"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏭 Production Integration Example\n", "========================================\n", "\n", "🔄 Simulating Real-Time System:\n", "-----------------------------------\n", "📚 Started session for Student student_123\n", "  Median       ✅ Correct | Prediction: 0.580 | 🟠 Developing\n", "               💡 📖 Review Median concepts before proceeding\n", "  Median       ❌ Incorrect | Prediction: 0.580 | 🟠 Developing\n", "               💡 📖 Review Median concepts before proceeding\n", "  Median       ✅ Correct | Prediction: 0.580 | 🟠 Developing\n", "               💡 📖 Review Median concepts before proceeding\n", "  Mean         ✅ Correct | Prediction: 0.467 | 🟠 Developing\n", "               💡 📖 Review Mean concepts before proceeding\n", "  Mean         ✅ Correct | Prediction: 0.467 | 🟠 Developing\n", "               💡 📖 Review Mean concepts before proceeding\n", "\n", "📊 Session Summary for Student 123:\n", "  Duration: 0.0 minutes\n", "  Interactions: 5\n", "  Accuracy: 0.80\n", "  Final Prediction: 0.467\n", "  Improvement: -0.113\n", "\n", "✅ Prediction demo completed successfully!\n", "\n", "💡 This system can be integrated into:\n", "   - Adaptive learning platforms\n", "   - Intelligent tutoring systems\n", "   - Educational assessment tools\n", "   - Learning analytics dashboards\n"]}], "source": ["# Production integration example\n", "print(\"🏭 Production Integration Example\")\n", "print(\"=\" * 40)\n", "\n", "class RealTimeKTSystem:\n", "    \"\"\"Example of how to integrate KT prediction in a real-time system\"\"\"\n", "    \n", "    def __init__(self, predictor, model_type='ensemble'):\n", "        self.predictor = predictor\n", "        self.model_type = model_type\n", "        self.student_sessions = {}\n", "    \n", "    def start_session(self, student_id, initial_history=None):\n", "        \"\"\"Start a new learning session for a student\"\"\"\n", "        self.student_sessions[student_id] = {\n", "            'history': initial_history or [],\n", "            'start_time': datetime.now(),\n", "            'interactions': 0,\n", "            'predictions': []\n", "        }\n", "        print(f\"📚 Started session for Student {student_id}\")\n", "    \n", "    def record_interaction(self, student_id, skill, response):\n", "        \"\"\"Record a student interaction and get real-time prediction\"\"\"\n", "        if student_id not in self.student_sessions:\n", "            self.start_session(student_id)\n", "        \n", "        session = self.student_sessions[student_id]\n", "        session['history'].append(response)\n", "        session['interactions'] += 1\n", "        \n", "        # Get prediction\n", "        try:\n", "            prediction = self.predictor.predict_student_performance(\n", "                user_history=session['history'],\n", "                skill=skill,\n", "                model_type=self.model_type\n", "            )\n", "            session['predictions'].append(prediction)\n", "            \n", "            # Generate adaptive recommendations\n", "            mastery_state, emoji = analyze_mastery_state(prediction)\n", "            recommendation = self.get_adaptive_recommendation(prediction, skill, session)\n", "            \n", "            return {\n", "                'prediction': prediction,\n", "                'mastery_state': mastery_state,\n", "                'emoji': emoji,\n", "                'recommendation': recommendation,\n", "                'session_progress': session['interactions']\n", "            }\n", "            \n", "        except Exception as e:\n", "            return {'error': str(e)}\n", "    \n", "    def get_adaptive_recommendation(self, prediction, skill, session):\n", "        \"\"\"Generate adaptive learning recommendations\"\"\"\n", "        recent_performance = np.mean(session['history'][-3:]) if len(session['history']) >= 3 else np.mean(session['history'])\n", "        \n", "        if prediction >= 0.8:\n", "            return f\"🚀 Ready for advanced {skill} challenges\"\n", "        elif prediction >= 0.6:\n", "            return f\"📚 Continue with current {skill} difficulty\"\n", "        elif prediction >= 0.4:\n", "            return f\"📖 Review {skill} concepts before proceeding\"\n", "        else:\n", "            return f\"🆘 Provide immediate support for {skill}\"\n", "    \n", "    def get_session_summary(self, student_id):\n", "        \"\"\"Get summary of student session\"\"\"\n", "        if student_id not in self.student_sessions:\n", "            return None\n", "        \n", "        session = self.student_sessions[student_id]\n", "        duration = datetime.now() - session['start_time']\n", "        \n", "        return {\n", "            'duration_minutes': duration.total_seconds() / 60,\n", "            'total_interactions': session['interactions'],\n", "            'accuracy': np.mean(session['history']) if session['history'] else 0,\n", "            'final_prediction': session['predictions'][-1] if session['predictions'] else None,\n", "            'improvement': session['predictions'][-1] - session['predictions'][0] if len(session['predictions']) > 1 else 0\n", "        }\n", "\n", "# Demo the real-time system\n", "if loaded_models:\n", "    rt_system = RealTimeKTSystem(predictor, model_type=loaded_models[0])\n", "    \n", "    # Simulate real-time interactions\n", "    print(\"\\n🔄 Simulating Real-Time System:\")\n", "    print(\"-\" * 35)\n", "    \n", "    # Student interactions\n", "    interactions = [\n", "        (\"student_123\", \"Median\", 1),\n", "        (\"student_123\", \"Median\", 0),\n", "        (\"student_123\", \"Median\", 1),\n", "        (\"student_123\", \"Mean\", 1),\n", "        (\"student_123\", \"Mean\", 1),\n", "    ]\n", "    \n", "    for student_id, skill, response in interactions:\n", "        result = rt_system.record_interaction(student_id, skill, response)\n", "        \n", "        if 'error' not in result:\n", "            status = \"✅ Correct\" if response == 1 else \"❌ Incorrect\"\n", "            print(f\"  {skill:12} {status} | Prediction: {result['prediction']:.3f} | {result['emoji']} {result['mastery_state']}\")\n", "            print(f\"               💡 {result['recommendation']}\")\n", "        else:\n", "            print(f\"  Error: {result['error']}\")\n", "    \n", "    # Session summary\n", "    summary = rt_system.get_session_summary(\"student_123\")\n", "    if summary:\n", "        print(f\"\\n📊 Session Summary for Student 123:\")\n", "        print(f\"  Duration: {summary['duration_minutes']:.1f} minutes\")\n", "        print(f\"  Interactions: {summary['total_interactions']}\")\n", "        print(f\"  Accuracy: {summary['accuracy']:.2f}\")\n", "        print(f\"  Final Prediction: {summary['final_prediction']:.3f}\")\n", "        print(f\"  Improvement: {summary['improvement']:+.3f}\")\n", "else:\n", "    print(\"No models available for real-time system demo.\")\n", "\n", "print(\"\\n✅ Prediction demo completed successfully!\")\n", "print(\"\\n💡 This system can be integrated into:\")\n", "print(\"   - Adaptive learning platforms\")\n", "print(\"   - Intelligent tutoring systems\")\n", "print(\"   - Educational assessment tools\")\n", "print(\"   - Learning analytics dashboards\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}