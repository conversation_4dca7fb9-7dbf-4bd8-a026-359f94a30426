{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f0a48d31-8eb5-4911-baf2-84c8a2764d7d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ All imports successful!\n", "Notebook started at: 2025-07-17 18:13:23.710468\n"]}], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import json\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Add pipeline to path\n", "sys.path.append('/home/<USER>/workspace/AClass/App/Training/pipeline')\n", "\n", "\n", "\n", "print(\"✅ All imports successful!\")\n", "print(f\"Notebook started at: {datetime.now()}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "2d7d3782-ab92-4bbe-a216-05c345427b09", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading math dataset...\n", "✅ Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the math dataset\n", "data_path = \"/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading math dataset...\")\n", "df= pd.read_csv(data_path, encoding='latin1')\n", "\n", "print(f\"✅ Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "\n", "# Display basic info\n", "# df_clean.info()"]}, {"cell_type": "code", "execution_count": 3, "id": "fddd4be9-a7e2-455b-b6f5-2ef56067b6e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df.columns:\n", "        missing = df[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": 14, "id": "0411ed18-a337-45c7-be25-f085fde33515", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 50,000\n", "Removed: 351,756 rows (87.55%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df.columns]\n", "df_clean = df.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "df_clean = df_clean.head(50000)\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": 15, "id": "668d0a24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COMPREHENSIVE SKILLS ANALYSIS\n", "==================================================\n", "Total number of unique skills: 13\n", "\n", "Top 15 skills by number of interactions:\n", "                             interactions  accuracy  accuracy_std  unique_students  unique_problems\n", "skill_name                                                                                         \n", "Table                                6834     0.739         0.439              713              546\n", "Circle Graph                         6208     0.523         0.500              616              298\n", "Venn Diagram                         5809     0.709         0.454              552              218\n", "Median                               5783     0.656         0.475              791              351\n", "Range                                4034     0.762         0.426              769              246\n", "Box and Whisker                      3957     0.753         0.431              505              132\n", "Stem and Leaf Plot                   3828     0.679         0.467              467              280\n", "Counting Methods                     3124     0.499         0.500              315              203\n", "Mean                                 2939     0.526         0.499              680              227\n", "Mode                                 1926     0.876         0.330              572              123\n", "Number Line                          1895     0.595         0.491              167              353\n", "Scatter Plot                         1859     0.869         0.337              354              159\n", "Histogram as Table or Graph          1804     0.711         0.454              230              142\n", "\n", "Top 15 easiest skills (highest accuracy):\n", "                             interactions  accuracy  unique_students\n", "skill_name                                                          \n", "Mode                                 1926     0.876              572\n", "Scatter Plot                         1859     0.869              354\n", "Range                                4034     0.762              769\n", "Box and Whisker                      3957     0.753              505\n", "Table                                6834     0.739              713\n", "Histogram as Table or Graph          1804     0.711              230\n", "Venn Diagram                         5809     0.709              552\n", "Stem and Leaf Plot                   3828     0.679              467\n", "Median                               5783     0.656              791\n", "Number Line                          1895     0.595              167\n", "Mean                                 2939     0.526              680\n", "Circle Graph                         6208     0.523              616\n", "Counting Methods                     3124     0.499              315\n", "\n", "Top 15 hardest skills (lowest accuracy):\n", "                             interactions  accuracy  unique_students\n", "skill_name                                                          \n", "Counting Methods                     3124     0.499              315\n", "Circle Graph                         6208     0.523              616\n", "Mean                                 2939     0.526              680\n", "Number Line                          1895     0.595              167\n", "Median                               5783     0.656              791\n", "Stem and Leaf Plot                   3828     0.679              467\n", "Venn Diagram                         5809     0.709              552\n", "Histogram as Table or Graph          1804     0.711              230\n", "Table                                6834     0.739              713\n", "Box and Whisker                      3957     0.753              505\n", "Range                                4034     0.762              769\n", "Scatter Plot                         1859     0.869              354\n", "Mode                                 1926     0.876              572\n"]}], "source": ["# Comprehensive skills analysis\n", "print(\"📋 COMPREHENSIVE SKILLS ANALYSIS\")\n", "print(\"=\"*50)\n", "\n", "# Get all unique skills\n", "all_skills = df_clean['skill_name'].unique()\n", "print(f\"Total number of unique skills: {len(all_skills)}\")\n", "\n", "# Skills statistics\n", "skills_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean', 'std'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skills_stats.columns = ['interactions', 'accuracy', 'accuracy_std', 'unique_students', 'unique_problems']\n", "skills_stats = skills_stats.sort_values('interactions', ascending=False)\n", "\n", "print(\"\\nTop 15 skills by number of interactions:\")\n", "print(skills_stats.head(15).to_string())\n", "\n", "print(\"\\nTop 15 easiest skills (highest accuracy):\")\n", "easiest_skills = skills_stats.sort_values('accuracy', ascending=False).head(15)\n", "print(easiest_skills[['interactions', 'accuracy', 'unique_students']].to_string())\n", "\n", "print(\"\\nTop 15 hardest skills (lowest accuracy):\")\n", "hardest_skills = skills_stats.sort_values('accuracy', ascending=True).head(15)\n", "print(hardest_skills[['interactions', 'accuracy', 'unique_students']].to_string())"]}, {"cell_type": "code", "execution_count": null, "id": "0cedca97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Splitting data into train/test sets...\n", "Training set: 39,945 interactions from 1,242 users\n", "Test set: 10,055 interactions from 311 users\n", "Train accuracy: 0.672\n", "Test accuracy: 0.679\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "# Split data by users to avoid data leakage\n", "print(\"Splitting data into train/test sets...\")\n", "\n", "# df_clean = df_clean.head(50000)\n", "# Get unique users\n", "unique_users = df_clean['user_id'].unique()\n", "train_users, test_users = train_test_split(unique_users, test_size=0.2, random_state=42)\n", "\n", "# Split data based on users\n", "train_df = df_clean[df_clean['user_id'].isin(train_users)].copy()\n", "test_df = df_clean[df_clean['user_id'].isin(test_users)].copy()\n", "\n", "\n", "print(f\"Training set: {len(train_df):,} interactions from {len(train_users):,} users\")\n", "print(f\"Test set: {len(test_df):,} interactions from {len(test_users):,} users\")\n", "print(f\"Train accuracy: {train_df['correct'].mean():.3f}\")\n", "print(f\"Test accuracy: {test_df['correct'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "b6c9773e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 18:15:02,783 - INFO - Exploring dataset...\n", "2025-07-17 18:15:02,853 - INFO - Dataset exploration completed\n", "2025-07-17 18:15:02,854 - INFO -   total_interactions: 401756\n", "2025-07-17 18:15:02,855 - INFO -   unique_students: 4217\n", "2025-07-17 18:15:02,856 - INFO -   unique_problems: 26688\n", "2025-07-17 18:15:02,857 - INFO -   unique_skills: 110\n", "2025-07-17 18:15:02,858 - INFO -   overall_accuracy: 0.6429225699180597\n", "2025-07-17 18:15:02,859 - INFO -   correct_responses: 258298\n", "2025-07-17 18:15:02,860 - INFO -   incorrect_responses: 143458\n", "2025-07-17 18:15:02,862 - INFO -   avg_sequence_length: 95.27057149632441\n", "2025-07-17 18:15:02,863 - INFO -   max_sequence_length: 1606\n", "2025-07-17 18:15:02,864 - INFO -   min_sequence_length: 1\n", "2025-07-17 18:15:02,865 - INFO -   most_common_skill: Equation Solving Two or Fewer Steps\n", "2025-07-17 18:15:02,866 - INFO -   most_common_skill_count: 24253\n", "2025-07-17 18:15:02,869 - INFO -   least_common_skill: Finding Slope from Ordered Pairs\n", "2025-07-17 18:15:02,870 - INFO -   least_common_skill_count: 5\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 INITIALIZING <PERSON><PERSON><PERSON>LEDGE TRACING PIPELINE\n", "==================================================\n"]}], "source": ["# from kt_model_trainer import KTModelTrainer\n", "# from train_pfa import PFAKnowledgeTracing\n", "# # Initialize trainer with custom settings\n", "# trainer = KTModelTrainer(n_folds=10, random_state=42)\n", "\n", "# Import pipeline components\n", "from kt_training_pipeline import KnowledgeTracingPipeline\n", "from kt_evaluation import KTEvaluator\n", "from prediction import KTPredictor\n", "from train_pfa import PFAKnowledgeTracing\n", "\n", "# Initialize the knowledge tracing pipeline\n", "print(\"🚀 INITIALIZING K<PERSON><PERSON>LEDGE TRACING PIPELINE\")\n", "print(\"=\"*50)\n", "\n", "pipeline = KnowledgeTracingPipeline()\n", "\n", "dataset_stats = pipeline.explore_dataset(df)\n", "\n", "\n", "pipeline.datasets = {\n", "    'raw': df,\n", "    'clean': df_clean,\n", "    'train': train_df,\n", "    'test': test_df,\n", "    'stats': skills_stats\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "id": "1734f391", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 18:15:04,182 - INFO - Loading models from App/Training/models/outputs...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading models...\n", "✅ Models loaded successfully\n"]}, {"data": {"text/plain": ["{}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load models\n", "print(\"Loading models...\")\n", "models = pipeline.load_models('App/Training/models/outputs')\n", "print(\"✅ Models loaded successfully\")\n", "models\n", "# Extract individual models\n", "# bkt_model = models['bkt']\n", "# pfa_model = models['pfa']\n", "# dkt_model = models['dkt']"]}, {"cell_type": "code", "execution_count": 28, "id": "83e74fc4", "metadata": {}, "outputs": [], "source": ["#import joblib lib\n", "import joblib\n", "# load trained model in /home/<USER>/workspace/AClass/App/Training/models/outputs/bkt_model.joblib\n", "bkt_model = joblib.load('/home/<USER>/workspace/AClass/App/Training/models/outputs/bkt_model.joblib')\n", "pfa_model = joblib.load('/home/<USER>/workspace/AClass/App/Training/models/outputs/pfa_model.joblib')\n", "dkt_model = joblib.load('/home/<USER>/workspace/AClass/App/Training/models/outputs/dkt_model.h5')"]}, {"cell_type": "code", "execution_count": 29, "id": "5214a752", "metadata": {}, "outputs": [], "source": ["from kt_ensemble import KTModelEnsemble\n", "\n", "\n", "\n", "# Create ensemble with trained models\n", "ensemble = KTModelEnsemble([bkt_model, pfa_model,dkt_model])"]}, {"cell_type": "code", "execution_count": 30, "id": "d336cbec", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# # Train ensemble using different strategies\n", "# metrics_weighted = ensemble.train_ensemble(train_df, test_df, ensemble_type='weighted_average')\n"]}, {"cell_type": "code", "execution_count": 31, "id": "133af204", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 19:57:33,491 - INFO - Training ensemble model using stacking...\n", "2025-07-17 19:57:33,493 - INFO - Training stacking ensemble...\n", "2025-07-17 19:57:33,493 - INFO - Getting predictions from bkt model...\n", "2025-07-17 20:00:33,833 - INFO - Getting predictions from pfa model...\n", "2025-07-17 20:00:33,834 - INFO - Getting predictions from dkt model...\n", "2025-07-17 20:03:19,753 - INFO - Getting predictions from bkt model...\n", "2025-07-17 20:03:41,145 - INFO - Getting predictions from pfa model...\n", "2025-07-17 20:03:41,146 - INFO - Getting predictions from dkt model...\n", "2025-07-17 20:03:58,160 - INFO - Ensemble training completed\n"]}], "source": ["metrics_stacking = ensemble.train_ensemble(train_df, test_df, ensemble_type='stacking')"]}, {"cell_type": "code", "execution_count": 32, "id": "7335c28c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'accuracy': 0.7090999502734958,\n", " 'precision': 0.7242448604571035,\n", " 'recall': 0.9232796486090776,\n", " 'f1_score': 0.8117397180922958,\n", " 'auc': 0.6724253918530877}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["metrics_stacking"]}, {"cell_type": "code", "execution_count": 35, "id": "02afc2a1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 20:18:01,187 - INFO - Ensemble model saved to /home/<USER>/workspace/AClass/App/Training/models/outputs/ensembel_model.joblib\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💾 Saving models...\n", "✅ Models saved successfully\n"]}], "source": ["# Save models\n", "print(\"\\n💾 Saving models...\")\n", "# os.makedirs('App/Training/pipeline/outputs/math', exist_ok=True)\n", "OUTPUT_DIR='/home/<USER>/workspace/AClass/App/Training/models/outputs'\n", "ensemble.save(OUTPUT_DIR+'/ensembel_model.joblib')\n", "print(\"✅ Models saved successfully\")"]}, {"cell_type": "code", "execution_count": null, "id": "5e8cd2ee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "44c69ab4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}