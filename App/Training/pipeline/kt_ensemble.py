#!/usr/bin/env python3
"""
Knowledge Tracing Ensemble Model
===============================

This module implements ensemble methods for combining BKT, PFA, and DKT models
using weighted averaging and stacking approaches.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import logging
import joblib

logger = logging.getLogger(__name__)

class KTModelEnsemble:
    """Ensemble model for knowledge tracing combining BKT, PFA, and DKT"""
    
    def __init__(self, models: List[Any], model_names: List[str] = None):
        """
        Initialize ensemble model
        
        Args:
            models: List of trained models [bkt, pfa, dkt]
            model_names: Names of the models
        """
        self.models = models
        self.model_names = model_names or ['bkt', 'pfa', 'dkt']
        self.weights = None
        self.meta_model = None
        self.ensemble_type = None
        self.is_fitted = False
        
    def train_ensemble(self, train_df: pd.DataFrame, test_df: pd.DataFrame,
                      ensemble_type: str = 'weighted_average') -> Dict[str, float]:
        """
        Train the ensemble model
        
        Args:
            train_df: Training dataset
            test_df: Test dataset
            ensemble_type: Type of ensemble ('weighted_average', 'stacking', 'voting')
            
        Returns:
            Dictionary of ensemble metrics
        """
        logger.info(f"Training ensemble model using {ensemble_type}...")
        
        self.ensemble_type = ensemble_type
        
        if ensemble_type == 'weighted_average':
            metrics = self._train_weighted_average(train_df, test_df)
        elif ensemble_type == 'stacking':
            metrics = self._train_stacking(train_df, test_df)
        elif ensemble_type == 'voting':
            metrics = self._train_voting(train_df, test_df)
        else:
            raise ValueError(f"Unknown ensemble type: {ensemble_type}")
        
        self.is_fitted = True
        logger.info("Ensemble training completed")
        return metrics
    
    def _train_weighted_average(self, train_df: pd.DataFrame, test_df: pd.DataFrame) -> Dict[str, float]:
        """Train weighted average ensemble"""
        logger.info("Training weighted average ensemble...")
        
        # Get predictions from all models on validation set
        val_predictions = self._get_model_predictions(train_df)
        val_targets = self._get_targets(train_df)
        
        # Optimize weights using grid search
        best_weights = self._optimize_weights(val_predictions, val_targets)
        self.weights = best_weights
        
        # Evaluate on test set
        test_predictions = self._get_model_predictions(test_df)
        test_targets = self._get_targets(test_df)
        
        ensemble_pred = self._weighted_average_predict(test_predictions)
        metrics = self._calculate_metrics(test_targets, ensemble_pred)
        
        logger.info(f"Optimal weights: {dict(zip(self.model_names, self.weights))}")
        return metrics
    
    def _train_stacking(self, train_df: pd.DataFrame, test_df: pd.DataFrame) -> Dict[str, float]:
        """Train stacking ensemble with meta-learner"""
        logger.info("Training stacking ensemble...")
        
        # Get predictions from base models
        train_predictions = self._get_model_predictions(train_df)
        train_targets = self._get_targets(train_df)
        
        # Train meta-model
        self.meta_model = LogisticRegression(random_state=42, max_iter=1000)
        
        # Stack predictions as features
        X_meta = np.column_stack(train_predictions)
        self.meta_model.fit(X_meta, train_targets)
        
        # Evaluate on test set
        test_predictions = self._get_model_predictions(test_df)
        test_targets = self._get_targets(test_df)
        
        X_test_meta = np.column_stack(test_predictions)
        ensemble_pred_proba = self.meta_model.predict_proba(X_test_meta)[:, 1]
        ensemble_pred = (ensemble_pred_proba > 0.5).astype(int)
        
        metrics = self._calculate_metrics(test_targets, ensemble_pred, ensemble_pred_proba)
        return metrics
    
    def _train_voting(self, train_df: pd.DataFrame, test_df: pd.DataFrame) -> Dict[str, float]:
        """Train simple majority voting ensemble"""
        logger.info("Training voting ensemble...")
        
        # Get predictions from all models on test set
        test_predictions = self._get_model_predictions(test_df)
        test_targets = self._get_targets(test_df)
        
        # Simple majority voting
        ensemble_pred = self._majority_vote(test_predictions)
        metrics = self._calculate_metrics(test_targets, ensemble_pred)
        
        return metrics
    
    def _get_model_predictions(self, df: pd.DataFrame) -> List[np.ndarray]:
        """Get predictions from all models"""
        predictions = []
        
        for i, model in enumerate(self.models):
            model_name = self.model_names[i]
            logger.info(f"Getting predictions from {model_name} model...")
            
            try:
                if model_name == 'bkt':
                    pred = self._get_bkt_predictions(model, df)
                elif model_name == 'pfa':
                    pred = self._get_pfa_predictions(model, df)
                elif model_name == 'dkt':
                    pred = self._get_dkt_predictions(model, df)
                else:
                    logger.warning(f"Unknown model type: {model_name}")
                    pred = np.random.random(len(df))  # Random predictions as fallback
                
                predictions.append(pred)
                
            except Exception as e:
                logger.warning(f"Error getting predictions from {model_name}: {e}")
                # Use random predictions as fallback
                predictions.append(np.random.random(len(df)))
        
        return predictions
    
    def _get_bkt_predictions(self, model, df: pd.DataFrame) -> np.ndarray:
        """Get predictions from BKT model"""
        predictions = []
        
        for _, row in df.iterrows():
            user_id = row['user_id']
            skill = row['skill_name']
            
            # Get user history up to this point
            user_data = df[(df['user_id'] == user_id) & (df['order_id'] < row['order_id'])]
            skill_data = user_data[user_data['skill_name'] == skill]
            
            if len(skill_data) == 0:
                # No history, use default prediction
                pred = 0.5
            else:
                history = skill_data['correct'].tolist()
                try:
                    proba = model.predict_proba(history, skill)
                    pred = proba[-1] if proba else 0.5
                except:
                    pred = 0.5
            
            predictions.append(pred)
        
        return np.array(predictions)
    
    def _get_pfa_predictions(self, model, df: pd.DataFrame) -> np.ndarray:
        """Get predictions from PFA model"""
        try:
            X, _, _ = model.prepare_features(df)
            predictions = model.model.predict_proba(model.scaler.transform(X))[:, 1]
            return predictions
        except:
            return np.random.random(len(df))
    
    def _get_dkt_predictions(self, model, df: pd.DataFrame) -> np.ndarray:
        """Get predictions from DKT model"""
        try:
            # This is a simplified version - in practice, DKT predictions are more complex
            predictions = []
            for _, row in df.iterrows():
                user_id = row['user_id']
                skill = row['skill_name']
                
                # Get user history
                user_data = df[(df['user_id'] == user_id) & (df['order_id'] < row['order_id'])]
                history = user_data['correct'].tolist()
                
                try:
                    proba = model.predict_proba(history, skill)
                    pred = proba[0] if proba else 0.5
                except:
                    pred = 0.5
                
                predictions.append(pred)
            
            return np.array(predictions)
        except:
            return np.random.random(len(df))
    
    def _get_targets(self, df: pd.DataFrame) -> np.ndarray:
        """Get target values from dataframe"""
        return df['correct'].values
    
    def _optimize_weights(self, predictions: List[np.ndarray], targets: np.ndarray) -> np.ndarray:
        """Optimize ensemble weights using grid search"""
        best_score = -1
        best_weights = None
        
        # Grid search over possible weights
        weight_options = [0.1, 0.2, 0.3]
        weight_options = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        for w1 in weight_options:
            for w2 in weight_options:
                w3 = 1.0 - w1 - w2
                if w3 < 0 or w3 > 1:
                    continue
                
                weights = np.array([w1, w2, w3])
                ensemble_pred = np.average(predictions, axis=0, weights=weights)
                ensemble_binary = (ensemble_pred > 0.5).astype(int)
                
                score = accuracy_score(targets, ensemble_binary)
                
                if score > best_score:
                    best_score = score
                    best_weights = weights
        
        return best_weights if best_weights is not None else np.array([1/3, 1/3, 1/3])
    
    def _weighted_average_predict(self, predictions: List[np.ndarray]) -> np.ndarray:
        """Make weighted average predictions"""
        return np.average(predictions, axis=0, weights=self.weights)
    
    def _majority_vote(self, predictions: List[np.ndarray]) -> np.ndarray:
        """Make majority vote predictions"""
        binary_predictions = [(pred > 0.5).astype(int) for pred in predictions]
        return np.round(np.mean(binary_predictions, axis=0)).astype(int)
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                          y_pred_proba: np.ndarray = None) -> Dict[str, float]:
        """Calculate evaluation metrics"""
        if y_pred_proba is None:
            y_pred_proba = y_pred
        
        metrics = {
            'accuracy': accuracy_score(y_true, (y_pred > 0.5).astype(int)),
            'precision': precision_score(y_true, (y_pred > 0.5).astype(int), zero_division=0),
            'recall': recall_score(y_true, (y_pred > 0.5).astype(int), zero_division=0),
            'f1_score': f1_score(y_true, (y_pred > 0.5).astype(int), zero_division=0)
        }
        
        # Add AUC if we have probability predictions
        if len(np.unique(y_true)) > 1:
            try:
                metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
            except:
                metrics['auc'] = 0.5
        else:
            metrics['auc'] = 0.5
        
        return metrics
    
    def predict(self, user_history: List[int], skill: str, user_id: str = None) -> float:
        """
        Make ensemble prediction for a single instance
        
        Args:
            user_history: User's interaction history
            skill: Target skill
            user_id: User ID (optional)
            
        Returns:
            Predicted probability
        """
        if not self.is_fitted:
            raise ValueError("Ensemble must be trained before making predictions")
        
        # Get predictions from all models
        predictions = []
        
        for i, model in enumerate(self.models):
            model_name = self.model_names[i]
            
            try:
                if hasattr(model, 'predict_proba'):
                    pred = model.predict_proba(user_history, skill)[0]
                else:
                    pred = 0.5  # Default prediction
                
                predictions.append(pred)
                
            except Exception as e:
                logger.warning(f"Error getting prediction from {model_name}: {e}")
                predictions.append(0.5)
        
        # Combine predictions based on ensemble type
        if self.ensemble_type == 'weighted_average':
            return np.average(predictions, weights=self.weights)
        elif self.ensemble_type == 'stacking':
            X_meta = np.array(predictions).reshape(1, -1)
            return self.meta_model.predict_proba(X_meta)[0, 1]
        elif self.ensemble_type == 'voting':
            return np.mean([(p > 0.5) for p in predictions])
        else:
            return np.mean(predictions)
    
    def save(self, filepath: str):
        """Save the ensemble model"""
        ensemble_data = {
            'weights': self.weights,
            'meta_model': self.meta_model,
            'ensemble_type': self.ensemble_type,
            'model_names': self.model_names,
            'is_fitted': self.is_fitted
        }
        
        joblib.dump(ensemble_data, filepath)
        logger.info(f"Ensemble model saved to {filepath}")
    
    @classmethod
    def load(cls, filepath: str, models: List[Any]):
        """Load ensemble model"""
        ensemble_data = joblib.load(filepath)
        
        instance = cls(models, ensemble_data['model_names'])
        instance.weights = ensemble_data['weights']
        instance.meta_model = ensemble_data['meta_model']
        instance.ensemble_type = ensemble_data['ensemble_type']
        instance.is_fitted = ensemble_data['is_fitted']
        
        logger.info(f"Ensemble model loaded from {filepath}")
        return instance
