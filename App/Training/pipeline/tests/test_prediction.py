#!/usr/bin/env python3
"""
Unit Tests for Knowledge Tracing Prediction Module
==================================================

This module contains comprehensive unit tests for the prediction system,
testing model loading, prediction accuracy, and edge cases.

Usage:
    pytest test_prediction.py -v
    python -m pytest test_prediction.py::TestKTPredictor::test_model_loading -v
"""

import os
import sys
import pytest
import numpy as np
import pandas as pd
import tempfile
import joblib
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from prediction import KTPredictor, PFAModelWrapper, DKTModelWrapper, EnsembleModelWrapper


class TestPFAModelWrapper:
    """Test PFA model wrapper functionality"""
    
    def test_pfa_wrapper_initialization(self):
        """Test PFA wrapper initialization"""
        pfa_data = {
            'model': Mock(),
            'scaler': <PERSON>ck(),
            'skill_params': {'Addition': {'difficulty': 0.1}},
            'user_skill_history': {},
            'is_fitted': True
        }
        
        wrapper = PFAModelWrapper(pfa_data)
        
        assert wrapper.is_fitted == True
        assert 'Addition' in wrapper.skill_params
        assert wrapper.skill_params['Addition']['difficulty'] == 0.1
    
    def test_pfa_prediction_empty_history(self):
        """Test PFA prediction with empty history"""
        pfa_data = {'model': Mock(), 'scaler': Mock(), 'skill_params': {}, 'is_fitted': True}
        wrapper = PFAModelWrapper(pfa_data)
        
        prediction = wrapper.predict_proba([], "Addition", 1)
        assert prediction == 0.5
        
        predictions = wrapper.predict_proba([], "Addition", 3)
        assert predictions == [0.5, 0.5, 0.5]
    
    def test_pfa_prediction_with_history(self):
        """Test PFA prediction with interaction history"""
        pfa_data = {
            'model': Mock(),
            'scaler': Mock(),
            'skill_params': {'Addition': {'difficulty': 0.1}},
            'is_fitted': True
        }
        wrapper = PFAModelWrapper(pfa_data)
        
        # Test with good performance
        good_history = [1, 1, 1, 1]
        prediction = wrapper.predict_proba(good_history, "Addition", 1)
        assert 0.5 < prediction <= 1.0
        
        # Test with poor performance
        poor_history = [0, 0, 0, 0]
        prediction = wrapper.predict_proba(poor_history, "Addition", 1)
        assert 0.0 <= prediction < 0.5


class TestDKTModelWrapper:
    """Test DKT model wrapper functionality"""
    
    def test_dkt_wrapper_initialization(self):
        """Test DKT wrapper initialization"""
        dkt_data = {
            'skill_encoders': {'Addition': 0, 'Subtraction': 1},
            'n_skills': 2,
            'vocab_size': 100,
            'hidden_dim': 32,
            'max_seq_len': 50,
            'is_fitted': True
        }
        
        wrapper = DKTModelWrapper(dkt_data)
        
        assert wrapper.n_skills == 2
        assert wrapper.hidden_dim == 32
        assert wrapper.max_seq_len == 50
        assert wrapper.is_fitted == True
    
    def test_dkt_prediction_empty_history(self):
        """Test DKT prediction with empty history"""
        dkt_data = {'skill_encoders': {}, 'n_skills': 0, 'is_fitted': True}
        wrapper = DKTModelWrapper(dkt_data)
        
        prediction = wrapper.predict_proba([], "Addition", 1)
        assert prediction == 0.5
    
    def test_dkt_prediction_with_sequence(self):
        """Test DKT prediction with sequence data"""
        dkt_data = {
            'skill_encoders': {},
            'n_skills': 10,
            'max_seq_len': 5,
            'is_fitted': True
        }
        wrapper = DKTModelWrapper(dkt_data)
        
        # Test with improving sequence
        improving_sequence = [0, 0, 1, 1, 1]
        prediction = wrapper.predict_proba(improving_sequence, "Addition", 1)
        assert 0.0 <= prediction <= 1.0
        
        # Test with long sequence (should be truncated)
        long_sequence = [1, 0, 1, 0, 1, 0, 1, 0, 1, 0]
        prediction = wrapper.predict_proba(long_sequence, "Addition", 1)
        assert 0.0 <= prediction <= 1.0


class TestEnsembleModelWrapper:
    """Test ensemble model wrapper functionality"""
    
    def test_ensemble_wrapper_initialization(self):
        """Test ensemble wrapper initialization"""
        mock_models = [Mock(), Mock(), Mock()]
        ensemble_data = {
            'weights': [0.4, 0.3, 0.3],
            'meta_model': Mock(),
            'ensemble_type': 'weighted_average',
            'model_names': ['bkt', 'pfa', 'dkt'],
            'is_fitted': True
        }
        
        wrapper = EnsembleModelWrapper(ensemble_data, mock_models)
        
        assert wrapper.ensemble_type == 'weighted_average'
        assert len(wrapper.weights) == 3
        assert len(wrapper.individual_models) == 3
        assert wrapper.is_fitted == True
    
    def test_ensemble_weighted_average_prediction(self):
        """Test ensemble weighted average prediction"""
        # Create mock models with predict_proba method
        mock_model1 = Mock()
        mock_model1.predict_proba.return_value = 0.8
        mock_model2 = Mock()
        mock_model2.predict_proba.return_value = 0.6
        mock_model3 = Mock()
        mock_model3.predict_proba.return_value = 0.4
        
        mock_models = [mock_model1, mock_model2, mock_model3]
        
        ensemble_data = {
            'weights': [0.5, 0.3, 0.2],
            'ensemble_type': 'weighted_average',
            'model_names': ['bkt', 'pfa', 'dkt'],
            'is_fitted': True
        }
        
        wrapper = EnsembleModelWrapper(ensemble_data, mock_models)
        
        prediction = wrapper.predict_proba([1, 0, 1], "Addition", 1)
        
        # Expected: 0.8*0.5 + 0.6*0.3 + 0.4*0.2 = 0.4 + 0.18 + 0.08 = 0.66
        expected = 0.66
        assert abs(prediction - expected) < 0.01


class TestKTPredictor:
    """Test main KT predictor functionality"""
    
    @pytest.fixture
    def temp_models_dir(self):
        """Create temporary directory with mock model files"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create mock BKT model
            bkt_path = os.path.join(temp_dir, 'bkt_model.joblib')
            mock_bkt = Mock()
            mock_bkt.predict_proba.return_value = 0.7
            joblib.dump(mock_bkt, bkt_path)
            
            # Create mock PFA model (as dictionary)
            pfa_path = os.path.join(temp_dir, 'pfa_model.joblib')
            pfa_data = {
                'model': Mock(),
                'scaler': Mock(),
                'skill_params': {},
                'is_fitted': True
            }
            joblib.dump(pfa_data, pfa_path)
            
            # Create mock DKT model (as dictionary)
            dkt_path = os.path.join(temp_dir, 'dkt_model.joblib')
            dkt_data = {
                'skill_encoders': {},
                'n_skills': 10,
                'is_fitted': True
            }
            joblib.dump(dkt_data, dkt_path)
            
            # Create mock ensemble model
            ensemble_path = os.path.join(temp_dir, 'ensemble_model.joblib')
            ensemble_data = {
                'weights': [0.4, 0.3, 0.3],
                'ensemble_type': 'weighted_average',
                'model_names': ['bkt', 'pfa', 'dkt'],
                'is_fitted': True
            }
            joblib.dump(ensemble_data, ensemble_path)
            
            yield temp_dir
    
    def test_predictor_initialization(self, temp_models_dir):
        """Test predictor initialization"""
        predictor = KTPredictor(models_dir=temp_models_dir)
        
        assert predictor.models_dir == temp_models_dir
        assert len(predictor.model_paths) == 4  # bkt, pfa, dkt, ensemble
        assert predictor.models == {}
    
    @patch('prediction.BayesianKnowledgeTracer')
    def test_model_loading(self, mock_bkt_class, temp_models_dir):
        """Test model loading functionality"""
        # Mock BKT class load method
        mock_bkt_instance = Mock()
        mock_bkt_instance.predict_proba.return_value = 0.7
        mock_bkt_class.load.return_value = mock_bkt_instance
        
        predictor = KTPredictor(models_dir=temp_models_dir)
        load_status = predictor.load_models()
        
        # Check that models were loaded
        assert load_status['bkt'] == True
        assert load_status['pfa'] == True
        assert load_status['dkt'] == True
        assert load_status['ensemble'] == True
        
        # Check that models are stored
        assert 'bkt' in predictor.models
        assert 'pfa' in predictor.models
        assert 'dkt' in predictor.models
        assert 'ensemble' in predictor.models
    
    @patch('prediction.BayesianKnowledgeTracer')
    def test_prediction_functionality(self, mock_bkt_class, temp_models_dir):
        """Test prediction functionality"""
        # Mock BKT class
        mock_bkt_instance = Mock()
        mock_bkt_instance.predict_proba.return_value = 0.75
        mock_bkt_class.load.return_value = mock_bkt_instance
        
        predictor = KTPredictor(models_dir=temp_models_dir)
        predictor.load_models()
        
        # Test single prediction
        prediction = predictor.predict_student_performance(
            user_history=[1, 0, 1, 1],
            skill="Addition",
            model_type="bkt"
        )
        
        assert prediction == 0.75
        mock_bkt_instance.predict_proba.assert_called_once()
    
    def test_prediction_with_invalid_model(self, temp_models_dir):
        """Test prediction with invalid model type"""
        predictor = KTPredictor(models_dir=temp_models_dir)
        
        with pytest.raises(ValueError, match="Model invalid_model not loaded"):
            predictor.predict_student_performance(
                user_history=[1, 0, 1],
                skill="Addition",
                model_type="invalid_model"
            )
    
    @patch('prediction.BayesianKnowledgeTracer')
    def test_model_comparison(self, mock_bkt_class, temp_models_dir):
        """Test model comparison functionality"""
        # Mock BKT class
        mock_bkt_instance = Mock()
        mock_bkt_instance.predict_proba.return_value = 0.8
        mock_bkt_class.load.return_value = mock_bkt_instance
        
        predictor = KTPredictor(models_dir=temp_models_dir)
        predictor.load_models()
        
        # Test model comparison
        predictions = predictor.compare_model_predictions(
            user_history=[1, 1, 0, 1],
            skill="Addition"
        )
        
        assert isinstance(predictions, dict)
        assert 'bkt' in predictions
        assert 'pfa' in predictions
        assert 'dkt' in predictions
        assert 'ensemble' in predictions
    
    def test_edge_cases(self, temp_models_dir):
        """Test edge cases and error handling"""
        predictor = KTPredictor(models_dir=temp_models_dir)
        
        # Test with empty history
        predictor.load_models()
        
        # Should not raise error with empty history
        prediction = predictor.predict_student_performance(
            user_history=[],
            skill="Addition",
            model_type="pfa"
        )
        
        assert 0.0 <= prediction <= 1.0
    
    def test_multiple_step_prediction(self, temp_models_dir):
        """Test multi-step prediction"""
        predictor = KTPredictor(models_dir=temp_models_dir)
        predictor.load_models()
        
        # Test multi-step prediction
        predictions = predictor.predict_student_performance(
            user_history=[1, 0, 1],
            skill="Addition",
            model_type="pfa",
            n_steps=3
        )
        
        assert isinstance(predictions, list)
        assert len(predictions) == 3
        assert all(0.0 <= p <= 1.0 for p in predictions)


class TestIntegration:
    """Integration tests for the complete prediction system"""
    
    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow"""
        # This test would require actual model files
        # For now, we'll test the workflow structure
        
        # Create predictor
        predictor = KTPredictor(models_dir="nonexistent_dir")
        
        # Try to load models (should fail gracefully)
        load_status = predictor.load_models()
        
        # All should be False since directory doesn't exist
        assert all(status == False for status in load_status.values())
    
    def test_real_time_simulation_structure(self):
        """Test real-time simulation structure"""
        # Test that the simulation components work together
        
        # Mock predictor
        mock_predictor = Mock()
        mock_predictor.predict_student_performance.return_value = 0.65
        
        # Simulate a learning session
        student_history = [1, 0, 1]
        skill = "Addition"
        
        # Get prediction
        prediction = mock_predictor.predict_student_performance(
            user_history=student_history,
            skill=skill,
            model_type="ensemble"
        )
        
        assert prediction == 0.65
        mock_predictor.predict_student_performance.assert_called_once()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
