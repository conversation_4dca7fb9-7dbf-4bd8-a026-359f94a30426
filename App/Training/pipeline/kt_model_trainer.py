#!/usr/bin/env python3
"""
Knowledge Tracing Model Trainer
==============================

This module implements training for BKT, PFA, and DKT models with
k-fold cross validation and hyperparameter tuning.
"""

import os
import sys
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from sklearn.model_selection import KFold, GridSearchCV
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import logging
import warnings
warnings.filterwarnings('ignore')

# Add the models directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'models'))

# Import the models
from train_bkt import BayesianKnowledgeTracer
from train_pfa import PFAKnowledgeTracing
from train_dkt import DeepKnowledgeTracing

logger = logging.getLogger(__name__)

class KTModelTrainer:
    """Model trainer for knowledge tracing models with cross-validation and hyperparameter tuning"""
    
    def __init__(self, n_folds: int = 5, random_state: int = 42):
        """
        Initialize the model trainer
        
        Args:
            n_folds: Number of folds for cross-validation
            random_state: Random seed for reproducibility
        """
        self.n_folds = n_folds
        self.random_state = random_state
        self.trained_models = {}
        self.cv_results = {}
        
    def train_bkt(self, train_df: pd.DataFrame, test_df: pd.DataFrame, 
                  hyperparameter_tuning: bool = False) -> Tuple[BayesianKnowledgeTracer, Dict[str, float]]:
        """
        Train BKT model with optional hyperparameter tuning
        
        Args:
            train_df: Training dataset
            test_df: Test dataset
            hyperparameter_tuning: Whether to perform hyperparameter tuning
            
        Returns:
            Tuple of (trained_model, metrics)
        """
        logger.info("Training BKT model...")
        
        if hyperparameter_tuning:
            logger.info("Performing hyperparameter tuning for BKT...")
            best_model, best_params = self._tune_bkt_hyperparameters(train_df)
        else:
            # Use default parameters
            best_model = BayesianKnowledgeTracer()
            best_model.fit(train_df)
            best_params = {}
        
        # Evaluate on test set
        metrics = self._evaluate_bkt(best_model, test_df)
        
        # Perform k-fold cross-validation
        cv_metrics = self._cross_validate_bkt(train_df, best_params)
        metrics.update({f'cv_{k}': v for k, v in cv_metrics.items()})
        
        logger.info("BKT training completed")
        return best_model, metrics
    
    def train_pfa(self, train_df: pd.DataFrame, test_df: pd.DataFrame, datasets: pd.DataFrame,
                  hyperparameter_tuning: bool = False) -> Tuple[PFAKnowledgeTracing, Dict[str, float]]:
        """
        Train PFA model with optional hyperparameter tuning
        
        Args:
            train_df: Training dataset
            test_df: Test dataset
            hyperparameter_tuning: Whether to perform hyperparameter tuning
            
        Returns:
            Tuple of (trained_model, metrics)
        """
        logger.info("Training PFA model...")
        
        if hyperparameter_tuning:
            logger.info("Performing hyperparameter tuning for PFA...")
            best_model, best_params = self._tune_pfa_hyperparameters(train_df)
        else:
            # Use default parameters
            best_model = PFAKnowledgeTracing()
            # X, y, df_features = best_model.prepare_features(train_df)
            # best_model.fit(X, y, df_features)
            best_params = {}
            print("START PFA MODIFIED : AG")
            df_clean = datasets['clean']
            
            all_skills = sorted(df_clean['skill_id'].unique())
            skill_col_names = [f'skill_{skill}' for skill in all_skills]

            print(f"Total unique skills: {len(all_skills)}")
            print(f"Creating {len(skill_col_names)} skill columns")

            # Prepare complete feature matrix with all skills
            X, y, df_features = best_model.prepare_features(df_clean, all_skills=all_skills, skill_col_names=skill_col_names)

            print(f"Feature matrix shape: {X.shape}")
            print(f"Feature columns: {list(X.columns)}")

            # 5. Split data (80% train, 20% test) - now X already has all skill columns
            print("\nSplitting data for train/test...")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            # Split the dataframe for analysis
            df_train = df_features.iloc[X_train.index]
            df_test = df_features.iloc[X_test.index]

            datasets['df_test']=df_test
            datasets['X_test']=X_test

            print(f"Training set size: {len(X_train)}")
            print(f"Test set size: {len(X_test)}")
            print(f"Training features: {X_train.shape[1]}")
            print(f"Test features: {X_test.shape[1]}")

            # Verify that train and test have the same columns
            train_cols = set(X_train.columns)
            test_cols = set(X_test.columns)
            if train_cols != test_cols:
                print(f"WARNING: Column mismatch!")
                print(f"Missing in test: {train_cols - test_cols}")
                print(f"Missing in train: {test_cols - train_cols}")
            else:
                print("✓ Train and test have identical feature columns")

            # 6. Train PFA model
            best_model.fit(X_train, y_train, df_train)
            
            # train_metrics = pfa_model.evaluate(X_train, y_train)
            metrics = best_model.evaluate(X_test, y_test)
        
        # Evaluate on test set
        # metrics={'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'auc': 0.5}
        # metrics = self._evaluate_pfa(best_model, test_df)
        
        # # Perform k-fold cross-validation
        # cv_metrics = self._cross_validate_pfa(train_df, best_params)
        # metrics.update({f'cv_{k}': v for k, v in cv_metrics.items()})
        
        logger.info("PFA training completed")
        return best_model, metrics
    
    def train_dkt(self, train_df: pd.DataFrame, test_df: pd.DataFrame, datasets: pd.DataFrame,
                  hyperparameter_tuning: bool = False) -> Tuple[DeepKnowledgeTracing, Dict[str, float]]:
        """
        Train DKT model with optional hyperparameter tuning
        
        Args:
            train_df: Training dataset
            test_df: Test dataset
            hyperparameter_tuning: Whether to perform hyperparameter tuning
            
        Returns:
            Tuple of (trained_model, metrics)
        """
        logger.info("Training DKT model...")
        
        if hyperparameter_tuning:
            logger.info("Performing hyperparameter tuning for DKT...")
            best_model, best_params = self._tune_dkt_hyperparameters(train_df)
        else:
            # Use default parameters
            df_clean = datasets['clean']

            best_model = DeepKnowledgeTracing(** datasets['dkt_config'])
            best_model.fit(df_clean, validation_split=0.2)
            best_params = {}
        
        # Evaluate on test set
        metrics = self._evaluate_dkt(best_model, test_df)
        
        # Note: DKT cross-validation is computationally expensive, so we skip it by default
        logger.info("DKT training completed")
        return best_model, metrics
    
    def _tune_bkt_hyperparameters(self, train_df: pd.DataFrame) -> Tuple[BayesianKnowledgeTracer, Dict]:
        """Tune BKT hyperparameters"""
        param_grid = {
            'p_init': [0.05, 0.1, 0.15, 0.2],
            'p_learn': [0.05, 0.1, 0.15, 0.2],
            'p_guess': [0.1, 0.15, 0.2, 0.25],
            'p_slip': [0.05, 0.1, 0.15, 0.2]
        }
        
        best_score = -float('inf')
        best_params = {}
        best_model = None
        
        # Simple grid search (since BKT doesn't fit sklearn's interface well)
        for p_init in param_grid['p_init']:
            for p_learn in param_grid['p_learn']:
                for p_guess in param_grid['p_guess']:
                    for p_slip in param_grid['p_slip']:
                        params = {
                            'p_init': p_init,
                            'p_learn': p_learn,
                            'p_guess': p_guess,
                            'p_slip': p_slip
                        }
                        
                        # Cross-validate this parameter set
                        cv_score = self._cross_validate_bkt(train_df, params)['accuracy']
                        
                        if cv_score > best_score:
                            best_score = cv_score
                            best_params = params
        
        # Train final model with best parameters
        best_model = BayesianKnowledgeTracer(**best_params)
        best_model.fit(train_df)
        
        logger.info(f"Best BKT parameters: {best_params}")
        logger.info(f"Best CV score: {best_score:.4f}")
        
        return best_model, best_params
    
    def _tune_pfa_hyperparameters(self, train_df: pd.DataFrame) -> Tuple[PFAKnowledgeTracing, Dict]:
        """Tune PFA hyperparameters"""
        # PFA uses logistic regression, so we can tune its parameters
        param_grid = {
            'C': [0.1, 1.0, 10.0, 100.0],
            'max_iter': [1000, 2000, 5000]
        }
        
        best_score = -float('inf')
        best_params = {}
        best_model = None
        
        for C in param_grid['C']:
            for max_iter in param_grid['max_iter']:
                params = {'C': C, 'max_iter': max_iter}
                
                # Cross-validate this parameter set
                cv_score = self._cross_validate_pfa(train_df, params)['accuracy']
                
                if cv_score > best_score:
                    best_score = cv_score
                    best_params = params
        
        # Train final model with best parameters
        best_model = PFAKnowledgeTracing()
        # Note: We'd need to modify PFA to accept these parameters
        X, y, df_features = best_model.prepare_features(train_df)
        best_model.fit(X, y, df_features)
        
        logger.info(f"Best PFA parameters: {best_params}")
        logger.info(f"Best CV score: {best_score:.4f}")
        
        return best_model, best_params
    
    def _tune_dkt_hyperparameters(self, train_df: pd.DataFrame) -> Tuple[DeepKnowledgeTracing, Dict]:
        """Tune DKT hyperparameters"""
        param_grid = {
            'hidden_dim': [64, 128, 256],
            'num_layers': [1, 2, 3],
            'dropout': [0.1, 0.2, 0.3],
            'learning_rate': [0.001, 0.01, 0.1]
        }
        
        best_score = -float('inf')
        best_params = {}
        best_model = None
        
        # Simple random search due to computational cost
        import random
        random.seed(self.random_state)
        
        n_trials = 10  # Limit number of trials
        for _ in range(n_trials):
            params = {
                'hidden_dim': random.choice(param_grid['hidden_dim']),
                'num_layers': random.choice(param_grid['num_layers']),
                'dropout': random.choice(param_grid['dropout']),
                'learning_rate': random.choice(param_grid['learning_rate']),
                'max_epochs': 20,  # Reduced for tuning
                'patience': 5
            }
            
            # Train and evaluate
            model = DeepKnowledgeTracing(**params)
            model.fit(train_df, validation_split=0.3)
            
            # Use validation accuracy as score
            if hasattr(model, 'history') and model.history:
                score = max(model.history.history['val_accuracy'])
                
                if score > best_score:
                    best_score = score
                    best_params = params
                    best_model = model
        
        # If no model was trained successfully, use default
        if best_model is None:
            best_model = DeepKnowledgeTracing()
            best_model.fit(train_df, validation_split=0.2)
            best_params = {}
        
        logger.info(f"Best DKT parameters: {best_params}")
        logger.info(f"Best validation score: {best_score:.4f}")
        
        return best_model, best_params
    
    def _cross_validate_bkt(self, df: pd.DataFrame, params: Dict) -> Dict[str, float]:
        """Perform k-fold cross-validation for BKT"""
        kf = KFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
        
        scores = []
        users = df['user_id'].unique()
        
        for train_idx, val_idx in kf.split(users):
            train_users = users[train_idx]
            val_users = users[val_idx]
            
            train_fold = df[df['user_id'].isin(train_users)]
            val_fold = df[df['user_id'].isin(val_users)]
            
            # Train model
            model = BayesianKnowledgeTracer(**params)
            model.fit(train_fold)
            
            # Evaluate
            metrics = self._evaluate_bkt(model, val_fold)
            scores.append(metrics['accuracy'])
        
        return {
            'accuracy': np.mean(scores),
            'accuracy_std': np.std(scores)
        }
    
    def _cross_validate_pfa(self, df: pd.DataFrame, params: Dict) -> Dict[str, float]:
        """Perform k-fold cross-validation for PFA"""
        kf = KFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
        
        scores = []
        users = df['user_id'].unique()
        
        for train_idx, val_idx in kf.split(users):
            train_users = users[train_idx]
            val_users = users[val_idx]
            
            train_fold = df[df['user_id'].isin(train_users)]
            val_fold = df[df['user_id'].isin(val_users)]
            
            # Train model
            model = PFAKnowledgeTracing()
            X, y, df_features = model.prepare_features(train_fold)
            model.fit(X, y, df_features)
            
            # Evaluate
            metrics = self._evaluate_pfa(model, val_fold)
            scores.append(metrics['accuracy'])
        
        return {
            'accuracy': np.mean(scores),
            'accuracy_std': np.std(scores)
        }
    
    def _evaluate_bkt(self, model: BayesianKnowledgeTracer, test_df: pd.DataFrame) -> Dict[str, float]:
        """Evaluate BKT model"""
        # BKT evaluation is complex due to its sequential nature
        # For simplicity, we'll use a basic accuracy calculation
        
        predictions = []
        actuals = []
        
        for user_id, user_data in test_df.groupby('user_id'):
            user_data = user_data.sort_values('order_id')
            
            for skill in user_data['skill_name'].unique():
                skill_data = user_data[user_data['skill_name'] == skill]
                if len(skill_data) < 2:
                    continue
                
                # Use all but last interaction as history
                history = skill_data['correct'].iloc[:-1].tolist()
                actual = skill_data['correct'].iloc[-1]
                
                # Predict
                try:
                    proba = model.predict_proba(history, skill)
                    pred = 1 if proba[-1] > 0.5 else 0
                    predictions.append(pred)
                    actuals.append(actual)
                except:
                    continue
        
        if len(predictions) == 0:
            return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0}
        
        return {
            'accuracy': accuracy_score(actuals, predictions),
            'precision': precision_score(actuals, predictions, zero_division=0),
            'recall': recall_score(actuals, predictions, zero_division=0),
            'f1_score': f1_score(actuals, predictions, zero_division=0)
        }
    
    def _evaluate_pfa(self, model: PFAKnowledgeTracing, test_df: pd.DataFrame) -> Dict[str, float]:
        """Evaluate PFA model"""
        X_test, y_test, _ = model.prepare_features(test_df)
        return model.evaluate(X_test, y_test)
    
    def _evaluate_dkt(self, model: DeepKnowledgeTracing, test_df: pd.DataFrame) -> Dict[str, float]:
        """Evaluate DKT model"""
        return model.evaluate(test_df)
