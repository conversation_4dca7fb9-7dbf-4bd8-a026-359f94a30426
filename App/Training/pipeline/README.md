# Knowledge Tracing Pipeline

A comprehensive machine learning pipeline for knowledge tracing using BKT (Bayesian Knowledge Tracing), PFA (Performance Factor Analysis), and DKT (Deep Knowledge Tracing) models.

## Overview

This pipeline implements a complete knowledge tracing system that can:
- Load and preprocess knowledge tracing datasets
- Train multiple knowledge tracing models (BKT, PFA, DKT)
- Create ensemble models combining all three approaches
- Evaluate model performance with comprehensive metrics
- Generate detailed reports and visualizations
- Make predictions for student knowledge states

## Features

### Models Supported
- **BKT (Bayesian Knowledge Tracing)**: Classic probabilistic model tracking knowledge state transitions
- **PFA (Performance Factor Analysis)**: Logistic regression-based model using success/failure counts
- **DKT (Deep Knowledge Tracing)**: LSTM-based deep learning model for sequence prediction
- **Ensemble**: Combines all three models using weighted averaging or stacking

### Key Capabilities
- **Dataset Agnostic**: Works with any knowledge tracing dataset with required columns
- **K-fold Cross Validation**: Robust model evaluation with cross-validation
- **Hyperparameter Tuning**: Automatic optimization of model parameters
- **Comprehensive Evaluation**: Multiple metrics and per-skill analysis
- **Prediction Interface**: Easy-to-use prediction API for new students
- **Visualization**: Automatic generation of plots and reports

## Installation

### Requirements
```bash
pip install pandas numpy scikit-learn tensorflow matplotlib seaborn joblib
```

### Optional Dependencies
- TensorFlow (for DKT model)
- Matplotlib/Seaborn (for visualizations)

## Quick Start

### 1. Prepare Your Dataset

Your dataset should have the following columns:
- `user_id`: Student identifier
- `problem_id`: Problem/question identifier  
- `skill_name`: Skill being practiced
- `correct`: Binary indicator (1=correct, 0=incorrect)
- `order_id`: Temporal ordering of interactions (optional)

Example:
```csv
user_id,problem_id,skill_name,correct,order_id
student_1,prob_001,Addition,1,1
student_1,prob_002,Addition,0,2
student_1,prob_003,Subtraction,1,3
```

### 2. Run the Pipeline

```python
from kt_training_pipeline import KnowledgeTracingPipeline

# Initialize pipeline
pipeline = KnowledgeTracingPipeline()

# Run complete pipeline
pipeline.run_pipeline(data_path="your_dataset.csv")
```

### 3. Make Predictions

```python
from prediction import KTPredictor

# Load trained models
predictor = KTPredictor()
predictor.load_models()

# Predict student performance
user_history = [1, 0, 1, 1]  # Previous correct/incorrect responses
skill = "Addition"
probability = predictor.predict_student_performance(user_history, skill)

print(f"Probability of success: {probability:.3f}")
```

## Pipeline Components

### Core Modules

1. **kt_training_pipeline.py**: Main pipeline orchestrator
2. **kt_feature_engineer.py**: Feature engineering for different models
3. **kt_model_trainer.py**: Model training with cross-validation and hyperparameter tuning
4. **kt_ensemble.py**: Ensemble model implementation
5. **kt_evaluation.py**: Comprehensive evaluation and reporting
6. **prediction.py**: Prediction interface for trained models

### Model Implementations

1. **train_bkt.py**: Bayesian Knowledge Tracing model
2. **train_pfa.py**: Performance Factor Analysis model  
3. **train_dkt.py**: Deep Knowledge Tracing model

## Usage Examples

### Basic Pipeline Usage

```python
# Initialize and run pipeline
pipeline = KnowledgeTracingPipeline()
success = pipeline.run_pipeline("data/skill_builder_data.csv")

if success:
    print("Pipeline completed successfully!")
    print("Check outputs/ directory for results")
```

### Advanced Model Training

```python
from kt_model_trainer import KTModelTrainer

# Initialize trainer with custom settings
trainer = KTModelTrainer(n_folds=10, random_state=42)

# Train individual models with hyperparameter tuning
bkt_model, bkt_metrics = trainer.train_bkt(train_df, test_df, hyperparameter_tuning=True)
pfa_model, pfa_metrics = trainer.train_pfa(train_df, test_df, hyperparameter_tuning=True)
dkt_model, dkt_metrics = trainer.train_dkt(train_df, test_df, hyperparameter_tuning=True)
```

### Ensemble Model Creation

```python
from kt_ensemble import KTModelEnsemble

# Create ensemble with trained models
ensemble = KTModelEnsemble([bkt_model, pfa_model, dkt_model])

# Train ensemble using different strategies
metrics_weighted = ensemble.train_ensemble(train_df, test_df, ensemble_type='weighted_average')
metrics_stacking = ensemble.train_ensemble(train_df, test_df, ensemble_type='stacking')
```

### Comprehensive Evaluation

```python
from kt_evaluation import KTEvaluator

evaluator = KTEvaluator()

# Evaluate all models
models = {'bkt': bkt_model, 'pfa': pfa_model, 'dkt': dkt_model, 'ensemble': ensemble}
results = evaluator.evaluate_model_performance(models, test_df)

# Generate reports
dataset_report = evaluator.generate_dataset_report(df, stats)
comparison_report = evaluator.generate_model_comparison_report(results)
skill_report = evaluator.generate_skill_performance_report(models, test_df)
```

### Prediction Examples

```python
from prediction import KTPredictor

predictor = KTPredictor()
predictor.load_models()

# Single prediction
prob = predictor.predict_student_performance([1, 0, 1], "Addition", model_type="ensemble")

# Compare all models
predictions = predictor.compare_model_predictions([1, 0, 1], "Addition")

# Learning trajectory
trajectory = predictor.predict_learning_trajectory([1, 0, 1], "Addition", n_future_interactions=5)

# Skill recommendations
recommendations = predictor.recommend_next_problems([1, 0, 1], ["Addition", "Subtraction"], target_difficulty=0.7)
```

## Testing

Run the test suite to verify everything works:

```bash
# Run simple tests
python tests/run_kt_tests.py

# Run comprehensive tests
python tests/test_kt_pipeline.py
```

## Output Files

The pipeline generates several output files:

### Models
- `models/output/bkt_model.joblib`: Trained BKT model
- `models/output/pfa_model.joblib`: Trained PFA model  
- `models/output/dkt_model.joblib`: Trained DKT model
- `models/output/ensemble_model.joblib`: Trained ensemble model

### Reports
- `outputs/dataset_report.txt`: Dataset analysis report
- `outputs/model_comparison_report.txt`: Model performance comparison
- `outputs/model_performance.json`: Detailed metrics in JSON format
- `outputs/skill_performance.json`: Per-skill performance analysis

### Visualizations
- `outputs/dataset_analysis.png`: Dataset visualization plots
- `outputs/model_comparison.png`: Model performance comparison plots

## Configuration

### Pipeline Configuration

```python
pipeline = KnowledgeTracingPipeline()
# Modify pipeline settings
pipeline.experiment_name = "my_experiment"
```

### Model Configuration

```python
# BKT parameters
bkt = BayesianKnowledgeTracer(
    p_init=0.1,      # Initial knowledge probability
    p_learn=0.1,     # Learning rate
    p_guess=0.2,     # Guess probability
    p_slip=0.1       # Slip probability
)

# DKT parameters  
dkt = DeepKnowledgeTracing(
    hidden_dim=128,      # LSTM hidden dimension
    num_layers=2,        # Number of LSTM layers
    dropout=0.2,         # Dropout rate
    learning_rate=0.001, # Learning rate
    max_epochs=50        # Maximum training epochs
)
```

## Troubleshooting

### Common Issues

1. **Missing TensorFlow**: DKT model requires TensorFlow. Install with `pip install tensorflow`
2. **Memory Issues**: For large datasets, reduce batch size or sequence length in DKT
3. **Convergence Issues**: Try different hyperparameters or increase training epochs

### Performance Tips

1. **Data Size**: Start with smaller datasets for testing
2. **Cross-Validation**: Reduce n_folds for faster training during development
3. **Hyperparameter Tuning**: Disable for faster initial testing
4. **Model Selection**: Train individual models first before ensemble

## Contributing

1. Follow the existing code structure and patterns
2. Add tests for new functionality
3. Update documentation for new features
4. Ensure compatibility with the existing pipeline

## License

This project follows the same license as the parent repository.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test suite to identify problems
3. Review the generated logs for error details
