{"Equation Solving More Than Two Steps": {"bkt": {"accuracy": 0.9518072289156626, "precision": 0.9634146341463414, "recall": 0.9875, "f1_score": 0.9753086419753086, "auc": 0.49375}}, "Subtraction Whole Numbers": {"bkt": {"accuracy": 0.7777777777777778, "precision": 0.8727272727272727, "recall": 0.8421052631578947, "f1_score": 0.8571428571428571, "auc": 0.687719298245614}}, "Absolute Value": {"bkt": {"accuracy": 0.8961748633879781, "precision": 0.9289940828402367, "recall": 0.9573170731707317, "f1_score": 0.9429429429429429, "auc": 0.6628690629011553}}, "Addition Whole Numbers": {"bkt": {"accuracy": 0.8187919463087249, "precision": 0.8992248062015504, "recall": 0.8923076923076924, "f1_score": 0.8957528957528957, "auc": 0.6040485829959514}}, "Addition and Subtraction Integers": {"bkt": {"accuracy": 0.8620689655172413, "precision": 0.92, "recall": 0.92, "f1_score": 0.92, "auc": 0.71}}, "Multiplication and Division Integers": {"bkt": {"accuracy": 0.9382022471910112, "precision": 0.9585798816568047, "recall": 0.9759036144578314, "f1_score": 0.9671641791044776, "auc": 0.696285140562249}}, "Pattern Finding ": {"bkt": {"accuracy": 0.9, "precision": 0.9183673469387755, "recall": 0.9375, "f1_score": 0.9278350515463918, "auc": 0.8778409090909091}}, "Square Root": {"bkt": {"accuracy": 0.8923076923076924, "precision": 0.9206349206349206, "recall": 0.9666666666666667, "f1_score": 0.943089430894309, "auc": 0.48333333333333334}}, "Addition and Subtraction Positive Decimals": {"bkt": {"accuracy": 0.7983193277310925, "precision": 0.9021739130434783, "recall": 0.8469387755102041, "f1_score": 0.8736842105263158, "auc": 0.7091836734693878}}, "Multiplication and Division Positive Decimals": {"bkt": {"accuracy": 0.9538461538461539, "precision": 0.9841269841269841, "recall": 0.96875, "f1_score": 0.9763779527559056, "auc": 0.484375}}, "Conversion of Fraction Decimals Percents": {"bkt": {"accuracy": 0.825531914893617, "precision": 0.8421052631578947, "recall": 0.9565217391304348, "f1_score": 0.8956743002544529, "auc": 0.6547314578005115}}, "Multiplication Fractions": {"bkt": {"accuracy": 0.7903225806451613, "precision": 0.92, "recall": 0.8363636363636363, "f1_score": 0.8761904761904762, "auc": 0.6324675324675324}}, "Ordering Integers": {"bkt": {"accuracy": 0.8888888888888888, "precision": 0.8876404494382022, "recall": 1.0, "f1_score": 0.9404761904761905, "auc": 0.5454545454545454}}, "Ordering Positive Decimals": {"bkt": {"accuracy": 0.9213483146067416, "precision": 0.9520958083832335, "recall": 0.9636363636363636, "f1_score": 0.9578313253012049, "auc": 0.6741258741258741}}, "Ordering Fractions": {"bkt": {"accuracy": 0.9453551912568307, "precision": 0.95, "recall": 0.9941860465116279, "f1_score": 0.9715909090909091, "auc": 0.5880021141649048}}, "Equation Solving Two or Fewer Steps": {"bkt": {"accuracy": 0.8081395348837209, "precision": 0.802547770700637, "recall": 0.984375, "f1_score": 0.8842105263157894, "auc": 0.6399147727272727}}, "Solving for a variable": {"bkt": {"accuracy": 0.9574468085106383, "precision": 0.9545454545454546, "recall": 1.0, "f1_score": 0.9767441860465116, "auc": 0.8}}, "Area Trapezoid": {"bkt": {"accuracy": 0.7567567567567568, "precision": 0.92, "recall": 0.7666666666666667, "f1_score": 0.8363636363636363, "auc": 0.7404761904761905}}, "Addition and Subtraction Fractions": {"bkt": {"accuracy": 0.8829268292682927, "precision": 0.907103825136612, "recall": 0.9595375722543352, "f1_score": 0.9325842696629213, "auc": 0.7141437861271676}}, "Rounding": {"bkt": {"accuracy": 0.8857142857142857, "precision": 0.9375, "recall": 0.9375, "f1_score": 0.9375, "auc": 0.6354166666666667}}, "Congruence": {"bkt": {"accuracy": 0.9130434782608695, "precision": 0.9545454545454546, "recall": 0.9545454545454546, "f1_score": 0.9545454545454546, "auc": 0.4772727272727273}}, "Scientific Notation": {"bkt": {"accuracy": 0.9375, "precision": 0.9508196721311475, "recall": 0.9830508474576272, "f1_score": 0.9666666666666667, "auc": 0.6915254237288135}}, "Perimeter of a Polygon": {"bkt": {"accuracy": 0.8636363636363636, "precision": 0.9333333333333333, "recall": 0.875, "f1_score": 0.9032258064516129, "auc": 0.8541666666666666}}, "Circumference ": {"bkt": {"accuracy": 0.7843137254901961, "precision": 0.8297872340425532, "recall": 0.9285714285714286, "f1_score": 0.8764044943820225, "auc": 0.5198412698412699}}, "Division Fractions": {"bkt": {"accuracy": 0.853448275862069, "precision": 0.9306930693069307, "recall": 0.9038461538461539, "f1_score": 0.9170731707317074, "auc": 0.6602564102564101}}, "D.4.8-understanding-concept-of-probabilities": {"bkt": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "auc": 0.5}}, "Order of Operations +,-,/,* () positive reals": {"bkt": {"accuracy": 0.8585858585858586, "precision": 0.927710843373494, "recall": 0.9058823529411765, "f1_score": 0.9166666666666666, "auc": 0.7386554621848739}}, "Unit Conversion Within a System": {"bkt": {"accuracy": 0.7272727272727273, "precision": 0.9117647058823529, "recall": 0.775, "f1_score": 0.8378378378378378, "auc": 0.5125}}, "Unit Rate": {"bkt": {"accuracy": 0.7222222222222222, "precision": 0.8596491228070176, "recall": 0.7424242424242424, "f1_score": 0.7967479674796748, "auc": 0.7045454545454546}}, "Percent Of": {"bkt": {"accuracy": 0.751219512195122, "precision": 0.8482758620689655, "recall": 0.8092105263157895, "f1_score": 0.8282828282828283, "auc": 0.697058093346574}}, "Exponents": {"bkt": {"accuracy": 0.8533333333333334, "precision": 0.8857142857142857, "recall": 0.9538461538461539, "f1_score": 0.9185185185185185, "auc": 0.576923076923077}}, "Divisibility Rules": {"bkt": {"accuracy": 0.9074074074074074, "precision": 0.94, "recall": 0.9591836734693877, "f1_score": 0.9494949494949495, "auc": 0.6795918367346938}}, "Least Common Multiple": {"bkt": {"accuracy": 0.8985507246376812, "precision": 0.9508196721311475, "recall": 0.9354838709677419, "f1_score": 0.943089430894309, "auc": 0.7534562211981566}}, "Table": {"bkt": {"accuracy": 0.8978102189781022, "precision": 0.944, "recall": 0.944, "f1_score": 0.944, "auc": 0.6803333333333332}}, "Mean": {"bkt": {"accuracy": 0.584070796460177, "precision": 0.7169811320754716, "recall": 0.5428571428571428, "f1_score": 0.6178861788617886, "auc": 0.5970099667774086}}, "Number Line": {"bkt": {"accuracy": 0.7352941176470589, "precision": 0.8518518518518519, "recall": 0.8214285714285714, "f1_score": 0.8363636363636363, "auc": 0.5773809523809523}}, "Solving Inequalities": {"bkt": {"accuracy": 0.7352941176470589, "precision": 0.8518518518518519, "recall": 0.8214285714285714, "f1_score": 0.8363636363636363, "auc": 0.5773809523809523}}, "Write Linear Equation from Graph": {"bkt": {"accuracy": 0.7777777777777778, "precision": 0.9090909090909091, "recall": 0.7692307692307693, "f1_score": 0.8333333333333334, "auc": 0.7846153846153847}}, "Finding Percents": {"bkt": {"accuracy": 0.8104575163398693, "precision": 0.875, "recall": 0.8504672897196262, "f1_score": 0.8625592417061612, "auc": 0.7839292970337262}}, "Order of Operations All": {"bkt": {"accuracy": 0.7321428571428571, "precision": 0.9014084507042254, "recall": 0.735632183908046, "f1_score": 0.810126582278481, "auc": 0.727816091954023}}, "Estimation": {"bkt": {"accuracy": 0.9069767441860465, "precision": 0.9069767441860465, "recall": 1.0, "f1_score": 0.9512195121951219, "auc": 0.5}}, "Ordering Real Numbers": {"bkt": {"accuracy": 0.8571428571428571, "precision": 0.868421052631579, "recall": 0.9705882352941176, "f1_score": 0.9166666666666666, "auc": 0.6727941176470589}}, "Median": {"bkt": {"accuracy": 0.8345864661654135, "precision": 0.8468468468468469, "recall": 0.9494949494949495, "f1_score": 0.8952380952380953, "auc": 0.7247474747474747}}, "Mode": {"bkt": {"accuracy": 0.9405940594059405, "precision": 0.968421052631579, "recall": 0.968421052631579, "f1_score": 0.968421052631579, "auc": 0.7342105263157894}}, "Range": {"bkt": {"accuracy": 0.8596491228070176, "precision": 0.9230769230769231, "recall": 0.9230769230769231, "f1_score": 0.9230769230769231, "auc": 0.5615384615384615}}, "Box and Whisker": {"bkt": {"accuracy": 0.9611650485436893, "precision": 0.9696969696969697, "recall": 0.9896907216494846, "f1_score": 0.9795918367346939, "auc": 0.7448453608247423}}, "Histogram as Table or Graph": {"bkt": {"accuracy": 0.8181818181818182, "precision": 0.85, "recall": 0.9444444444444444, "f1_score": 0.8947368421052632, "auc": 0.5972222222222222}}, "Venn Diagram": {"bkt": {"accuracy": 0.9134615384615384, "precision": 0.9081632653061225, "recall": 1.0, "f1_score": 0.9518716577540107, "auc": 0.7}}, "Complementary and Supplementary Angles": {"bkt": {"accuracy": 0.9117647058823529, "precision": 0.9661016949152542, "recall": 0.9344262295081968, "f1_score": 0.95, "auc": 0.8243559718969555}}, "Pythagorean Theorem": {"bkt": {"accuracy": 0.8035714285714286, "precision": 0.8, "recall": 0.975609756097561, "f1_score": 0.8791208791208791, "auc": 0.6544715447154472}}, "Prime Number": {"bkt": {"accuracy": 0.8333333333333334, "precision": 1.0, "recall": 0.8333333333333334, "f1_score": 0.9090909090909091, "auc": 0.5}}, "Proportion": {"bkt": {"accuracy": 0.8695652173913043, "precision": 0.9008264462809917, "recall": 0.9478260869565217, "f1_score": 0.923728813559322, "auc": 0.7130434782608696}}, "Scale Factor": {"bkt": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "auc": 0.5}}, "Circle Graph": {"bkt": {"accuracy": 0.8319327731092437, "precision": 0.9230769230769231, "recall": 0.8372093023255814, "f1_score": 0.8780487804878049, "auc": 0.8276955602536997}}, "Nets of 3D Figures": {"bkt": {"accuracy": 0.6666666666666666, "precision": 0.6666666666666666, "recall": 1.0, "f1_score": 0.8, "auc": 0.5}}, "Calculations with Similar Figures": {"bkt": {"accuracy": 0.8059701492537313, "precision": 0.8709677419354839, "recall": 0.9152542372881356, "f1_score": 0.8925619834710744, "auc": 0.4576271186440678}}, "Interior Angles Figures with More than 3 Sides": {"bkt": {"accuracy": 0.5, "precision": 0.9375, "recall": 0.4838709677419355, "f1_score": 0.6382978723404256, "auc": 0.5752688172043011}}, "Interior Angles Triangle": {"bkt": {"accuracy": 0.7777777777777778, "precision": 0.8333333333333334, "recall": 0.9090909090909091, "f1_score": 0.8695652173913043, "auc": 0.5545454545454545}}, "Rotations": {"bkt": {"accuracy": 0.6875, "precision": 1.0, "recall": 0.16666666666666666, "f1_score": 0.2857142857142857, "auc": 0.5833333333333334}}, "Translations": {"bkt": {"accuracy": 0.47058823529411764, "precision": 0.75, "recall": 0.46153846153846156, "f1_score": 0.5714285714285714, "auc": 0.48076923076923084}}, "Reflection": {"bkt": {"accuracy": 0.5238095238095238, "precision": 1.0, "recall": 0.16666666666666666, "f1_score": 0.2857142857142857, "auc": 0.5833333333333334}}, "Counting Methods": {"bkt": {"accuracy": 0.8169014084507042, "precision": 0.8867924528301887, "recall": 0.8703703703703703, "f1_score": 0.8785046728971962, "auc": 0.758714596949891}}, "Probability of Two Distinct Events": {"bkt": {"accuracy": 0.6444444444444445, "precision": 0.7692307692307693, "recall": 0.6666666666666666, "f1_score": 0.7142857142857143, "auc": 0.6333333333333333}}, "Write Linear Equation from Situation": {"bkt": {"accuracy": 0.9512195121951219, "precision": 0.9736842105263158, "recall": 0.9736842105263158, "f1_score": 0.9736842105263158, "auc": 0.8201754385964914}}, "Volume Rectangular Prism": {"bkt": {"accuracy": 0.8958333333333334, "precision": 0.9347826086956522, "recall": 0.9555555555555556, "f1_score": 0.945054945054945, "auc": 0.4777777777777778}}, "Stem and Leaf Plot": {"bkt": {"accuracy": 0.7848101265822784, "precision": 0.7866666666666666, "recall": 0.9833333333333333, "f1_score": 0.8740740740740741, "auc": 0.5706140350877194}}, "Scatter Plot": {"bkt": {"accuracy": 0.9285714285714286, "precision": 1.0, "recall": 0.9272727272727272, "f1_score": 0.9622641509433962, "auc": 0.9636363636363636}}, "Fraction Of": {"bkt": {"accuracy": 0.9090909090909091, "precision": 0.9090909090909091, "recall": 1.0, "f1_score": 0.9523809523809523, "auc": 0.5}}, "Probability of a Single Event": {"bkt": {"accuracy": 0.9351351351351351, "precision": 0.9470588235294117, "recall": 0.9817073170731707, "f1_score": 0.9640718562874252, "auc": 0.7765679442508711}}, "Equivalent Fractions": {"bkt": {"accuracy": 0.7126436781609196, "precision": 0.8888888888888888, "recall": 0.6666666666666666, "f1_score": 0.7619047619047619, "auc": 0.7407407407407407}}, "Area Circle": {"bkt": {"accuracy": 0.9565217391304348, "precision": 0.9534883720930233, "recall": 1.0, "f1_score": 0.9761904761904762, "auc": 0.8}}, "Greatest Common Factor": {"bkt": {"accuracy": 0.7777777777777778, "precision": 0.875, "recall": 0.875, "f1_score": 0.875, "auc": 0.4375}}, "Volume Sphere": {"bkt": {"accuracy": 0.92, "precision": 0.9090909090909091, "recall": 1.0, "f1_score": 0.9523809523809523, "auc": 0.8}}, "Area Irregular Figure": {"bkt": {"accuracy": 0.9, "precision": 0.8947368421052632, "recall": 1.0, "f1_score": 0.9444444444444444, "auc": 0.6666666666666667}}, "Effect of Changing Dimensions of a Shape Prportionally": {"bkt": {"accuracy": 0.9111111111111111, "precision": 0.9523809523809523, "recall": 0.9523809523809523, "f1_score": 0.9523809523809523, "auc": 0.6428571428571428}}, "Write Linear Equation from Ordered Pairs": {"bkt": {"accuracy": 0.8076923076923077, "precision": 0.8181818181818182, "recall": 0.9473684210526315, "f1_score": 0.8780487804878049, "auc": 0.6879699248120301}}, "Surface Area Rectangular Prism": {"bkt": {"accuracy": 0.6086956521739131, "precision": 0.9130434782608695, "recall": 0.5675675675675675, "f1_score": 0.7, "auc": 0.6726726726726727}}, "Surface Area Cylinder": {"bkt": {"accuracy": 0.3125, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc": 0.35714285714285715}}, "Multiplication Whole Numbers": {"bkt": {"accuracy": 0.75, "precision": 1.0, "recall": 0.5, "f1_score": 0.6666666666666666, "auc": 0.75}}, "Volume Cylinder": {"bkt": {"accuracy": 0.6428571428571429, "precision": 0.8666666666666667, "recall": 0.7027027027027027, "f1_score": 0.7761194029850746, "auc": 0.4513513513513513}}, "Area Rectangle": {"bkt": {"accuracy": 0.9047619047619048, "precision": 0.9047619047619048, "recall": 1.0, "f1_score": 0.95, "auc": 0.5}}, "Angles on Parallel Lines Cut by a Transversal": {"bkt": {"accuracy": 0.75, "precision": 0.75, "recall": 1.0, "f1_score": 0.8571428571428571, "auc": 0.5}}, "Polynomial Factors": {"bkt": {"accuracy": 0.5, "precision": 0.3333333333333333, "recall": 1.0, "f1_score": 0.5, "auc": 0.6666666666666667}}, "Solving Systems of Linear Equations by Graphing": {"bkt": {"accuracy": 0.5, "precision": 0.3333333333333333, "recall": 1.0, "f1_score": 0.5, "auc": 0.6666666666666667}}, "Quadratic Formula to Solve Quadratic Equation": {"bkt": {"accuracy": 1.0, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc": 0.5}}, "Area Parallelogram": {"bkt": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "auc": 0.5}}, "Area Triangle": {"bkt": {"accuracy": 0.7857142857142857, "precision": 0.9166666666666666, "recall": 0.8461538461538461, "f1_score": 0.88, "auc": 0.4230769230769231}}, "Algebraic Solving": {"bkt": {"accuracy": 0.2857142857142857, "precision": 0.3333333333333333, "recall": 0.1111111111111111, "f1_score": 0.16666666666666666, "auc": 0.3555555555555555}}, "Percent Discount": {"bkt": {"accuracy": 0.5, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc": 0.5}}, "Percents": {"bkt": {"accuracy": 0.6, "precision": 1.0, "recall": 0.3333333333333333, "f1_score": 0.5, "auc": 0.6666666666666666}}, "Rate": {"bkt": {"accuracy": 0.3333333333333333, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc": 0.5}}, "Algebraic Simplification": {"bkt": {"accuracy": 0.5, "precision": 0.5, "recall": 0.5, "f1_score": 0.5, "auc": 0.5}}, "Solving Systems of Linear Equations": {"bkt": {"accuracy": 0.4, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc": 0.33333333333333337}}, "Choose an Equation from Given Information": {"bkt": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "auc": 0.5}}, "Intercept": {"bkt": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "auc": 0.5}}, "Linear Equations": {"bkt": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "auc": 0.5}}, "Slope": {"bkt": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "auc": 0.5}}, "Simplifying Expressions positive exponents": {"bkt": {"accuracy": 0.3333333333333333, "precision": 0.3333333333333333, "recall": 1.0, "f1_score": 0.5, "auc": 0.5}}}