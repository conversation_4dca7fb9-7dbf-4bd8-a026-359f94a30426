# services/ml-pipeline/src/models/ensemble.py
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from typing import List, Dict, Any
import mlflow

class ModelEnsemble:
    def __init__(self, models: List[Any], weights: List[float] = None):
        self.models = models
        self.weights = weights or [1/len(models)] * len(models)
        self.meta_model = None
        
    def simple_average(self, X) -> np.ndarray:
        """Simple weighted average ensemble"""
        predictions = []
        for model, weight in zip(self.models, self.weights):
            pred = model.predict(X)
            predictions.append(pred * weight)
        
        return np.sum(predictions, axis=0)
    
    def train_stacking(self, X_train, y_train, X_val, y_val):
        """Train stacking ensemble with meta-learner"""
        # Generate out-of-fold predictions
        train_preds = np.zeros((X_train.shape[0], len(self.models)))
        val_preds = np.zeros((X_val.shape[0], len(self.models)))
        
        for i, model in enumerate(self.models):
            # Use pre-trained models to get predictions
            train_preds[:, i] = model.predict(X_train)
            val_preds[:, i] = model.predict(X_val)
        
        # Train meta-model
        self.meta_model = LinearRegression()
        self.meta_model.fit(train_preds, y_train)
        
        return self.meta_model.predict(val_preds)
    
    def predict_stacking(self, X) -> np.ndarray:
        """Make predictions using stacking ensemble"""
        if self.meta_model is None:
            raise ValueError("Stacking ensemble not trained")
        
        # Get base model predictions
        base_preds = np.zeros((X.shape[0], len(self.models)))
        for i, model in enumerate(self.models):
            base_preds[:, i] = model.predict(X)
        
        # Meta-model prediction
        return self.meta_model.predict(base_preds)