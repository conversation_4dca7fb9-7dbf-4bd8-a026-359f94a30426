#!/usr/bin/env python3
"""
Simple test script to verify the weather training pipeline works
without the holidays dependency.
"""

import sys
import os

# Add current directory to path for imports
sys.path.append('/home/<USER>/work/weather')
sys.path.append('/home/<USER>/work')

try:
    print("Testing imports...")
    
    # Test feature engineer import
    from feature_engineer import FeatureEngineer
    print("✓ FeatureEngineer imported successfully")
    
    # Test model trainer import
    from model_trainer import ModelTrainer
    print("✓ ModelTrainer imported successfully")
    
    # Test ensemble import
    from ensemble import ModelEnsemble
    print("✓ ModelEnsemble imported successfully")
    
    # Test main pipeline import
    from weather_training_pipeline import WeatherEnergyPipeline
    print("✓ WeatherEnergyPipeline imported successfully")
    
    print("\n✅ All imports successful! The pipeline should work now.")
    print("\nTo run the full pipeline:")
    print("python /home/<USER>/work/weather/weather_training_pipeline.py")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please check the missing dependencies.")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
