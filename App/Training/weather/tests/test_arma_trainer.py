#!/usr/bin/env python3
"""
Test script for ARMA Weather Trainer

This script tests the ARMA weather forecasting pipeline with a small dataset
to validate functionality before running on the full dataset.
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_data():
    """Create synthetic test data for ARMA model validation"""
    logger.info("Creating synthetic test data...")
    
    # Generate timestamps for 30 days with hourly frequency
    start_date = datetime(2023, 1, 1)
    timestamps = [start_date + timedelta(hours=i) for i in range(30 * 24)]
    
    # Create synthetic energy demand with seasonal patterns
    np.random.seed(42)
    base_demand = 30000  # Base demand in MW
    
    # Add daily and weekly seasonality
    daily_pattern = np.sin(np.arange(len(timestamps)) * 2 * np.pi / 24) * 5000
    weekly_pattern = np.sin(np.arange(len(timestamps)) * 2 * np.pi / (24 * 7)) * 2000
    
    # Add trend and noise
    trend = np.arange(len(timestamps)) * 10
    noise = np.random.normal(0, 1000, len(timestamps))
    
    demand = base_demand + daily_pattern + weekly_pattern + trend + noise
    
    # Create synthetic weather data
    temperature = 15 + 10 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / 24) + np.random.normal(0, 3, len(timestamps))
    humidity = 60 + 20 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / 24) + np.random.normal(0, 10, len(timestamps))
    wind_speed = np.random.exponential(5, len(timestamps))
    precipitation = np.random.exponential(0.1, len(timestamps))
    
    # Create demand dataframe
    demand_df = pd.DataFrame({
        'date': [ts.strftime('%Y-%m-%d %H:%M:%S') for ts in timestamps],
        'time': [ts.strftime('%H:%M') for ts in timestamps],
        'consumption': demand
    })
    
    # Create weather dataframe (simplified with one city)
    weather_df = pd.DataFrame({
        'date_time': [ts.strftime('%Y-%m-%d %H:%M:%S') for ts in timestamps],
        'temperature_2m_Istanbul': temperature,
        'relative_humidity_2m_Istanbul': humidity,
        'wind_speed_10m_Istanbul': wind_speed,
        'precipitation_Istanbul': precipitation
    })
    
    logger.info(f"Created test data: {len(demand_df)} records")
    return demand_df, weather_df


def test_arma_trainer():
    """Test the ARMA trainer with synthetic data"""
    logger.info("Testing ARMA Weather Trainer...")
    
    try:
        # Import the ARMA trainer
        from arma_weather_trainer import ARMAWeatherTrainer
        
        # Create test data
        demand_df, weather_df = create_test_data()
        
        # Save test data temporarily
        test_demand_path = "/tmp/test_demand_data.csv"
        test_weather_path = "/tmp/test_weather_data.csv"
        
        demand_df.to_csv(test_demand_path, index=False)
        weather_df.to_csv(test_weather_path, index=False)
        
        logger.info("Test data saved to temporary files")
        
        # Create a modified trainer for testing
        class TestARMAWeatherTrainer(ARMAWeatherTrainer):
            def load_data(self):
                """Load test datasets"""
                logger.info("Loading test datasets...")
                
                try:
                    demand_df = self.spark.read.csv(test_demand_path, 
                                                  header=True, inferSchema=True)
                    weather_df = self.spark.read.csv(test_weather_path, 
                                                   header=True, inferSchema=True)
                    
                    logger.info(f"Loaded test demand data: {demand_df.count()} records")
                    logger.info(f"Loaded test weather data: {weather_df.count()} records")
                    
                    return demand_df, weather_df
                    
                except Exception as e:
                    logger.error(f"Error loading test data: {str(e)}")
                    raise
        
        # Initialize and run test pipeline
        test_pipeline = TestARMAWeatherTrainer()
        test_pipeline.mlflow_uri = "sqlite:///test_mlflow.db"  # Use local SQLite for testing
        
        # Run the pipeline
        test_pipeline.run_pipeline()
        
        # Check results
        if test_pipeline.models and test_pipeline.metrics:
            logger.info("✅ ARMA trainer test completed successfully!")
            logger.info(f"Models trained: {list(test_pipeline.models.keys())}")
            logger.info(f"Metrics available: {list(test_pipeline.metrics.keys())}")
            
            # Print sample metrics
            for model_name, metrics in test_pipeline.metrics.items():
                logger.info(f"{model_name} metrics:")
                for metric, value in metrics.items():
                    logger.info(f"  {metric}: {value:.4f}")
        else:
            logger.error("❌ ARMA trainer test failed - no models or metrics generated")
            return False
        
        # Cleanup test files
        os.remove(test_demand_path)
        os.remove(test_weather_path)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ARMA trainer test failed: {str(e)}")
        return False


def test_stationarity_analysis():
    """Test stationarity analysis functions"""
    logger.info("Testing stationarity analysis...")
    
    try:
        from arma_weather_trainer import ARMAWeatherTrainer
        
        # Create synthetic non-stationary data
        np.random.seed(42)
        n = 1000
        trend = np.arange(n) * 0.1
        seasonal = 10 * np.sin(np.arange(n) * 2 * np.pi / 24)
        noise = np.random.normal(0, 1, n)
        non_stationary_series = trend + seasonal + noise
        
        # Create trainer instance
        trainer = ARMAWeatherTrainer()
        
        # Test stationarity check
        stationarity = trainer.check_stationarity(non_stationary_series, "Test Series")
        logger.info(f"Stationarity check result: {stationarity}")
        
        # Test making series stationary
        stationary_series, diff_count = trainer.make_stationary(non_stationary_series)
        logger.info(f"Series made stationary with {diff_count} differences")
        
        # Check if stationary series is actually stationary
        final_stationarity = trainer.check_stationarity(stationary_series, "Stationary Series")
        logger.info(f"Final stationarity check: {final_stationarity}")
        
        logger.info("✅ Stationarity analysis test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Stationarity analysis test failed: {str(e)}")
        return False


def test_arma_order_selection():
    """Test ARMA order selection"""
    logger.info("Testing ARMA order selection...")
    
    try:
        from arma_weather_trainer import ARMAWeatherTrainer
        
        # Create synthetic ARMA data
        np.random.seed(42)
        n = 500
        
        # Generate ARMA(2,1) process
        errors = np.random.normal(0, 1, n)
        series = np.zeros(n)
        
        # ARMA(2,1) parameters
        phi1, phi2 = 0.6, 0.2  # AR coefficients
        theta1 = 0.3  # MA coefficient
        
        for t in range(2, n):
            series[t] = phi1 * series[t-1] + phi2 * series[t-2] + theta1 * errors[t-1] + errors[t]
        
        # Create trainer instance
        trainer = ARMAWeatherTrainer()
        
        # Test order selection
        best_order, results = trainer.find_optimal_arma_order(series, max_p=3, max_q=3)
        logger.info(f"Best ARMA order found: {best_order}")
        logger.info(f"Number of models tested: {len(results)}")
        
        # Check if best order is reasonable
        if best_order[0] <= 3 and best_order[1] <= 3:
            logger.info("✅ ARMA order selection test completed successfully!")
            return True
        else:
            logger.warning("⚠️ Best order seems unusual, but test completed")
            return True
        
    except Exception as e:
        logger.error(f"❌ ARMA order selection test failed: {str(e)}")
        return False


if __name__ == "__main__":
    logger.info("Starting ARMA Weather Trainer Tests")
    
    # Run all tests
    tests = [
        ("Stationarity Analysis", test_stationarity_analysis),
        ("ARMA Order Selection", test_arma_order_selection),
        ("Full ARMA Trainer", test_arma_trainer)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {str(e)}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"TEST SUMMARY: {passed}/{total} tests passed")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 All tests passed! ARMA trainer is ready for production use.")
    else:
        logger.error("⚠️ Some tests failed. Please check the implementation.") 