#!/usr/bin/env python3
"""
Weather Energy Forecasting Training Pipeline

This script implements a complete ML pipeline for energy demand forecasting
using weather data from multiple Turkish cities. It includes data preprocessing,
feature engineering, multiple model training, and ensemble methods.

Usage:
    python weather_training_pipeline.py

Requirements:
    - MLflow tracking server running on http://mlflow-tracking:5000
    - Spark cluster running on spark://spark-master:7077
    - Weather datasets in /Dataset/weather/ directory
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append('/home/<USER>/work/weather')
sys.path.append('/home/<USER>/work')

import mlflow
import mlflow.sklearn
import mlflow.xgboost
import mlflow.lightgbm
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# PySpark imports
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, to_timestamp, lit, hour, dayofweek, month, quarter, year, when, date_add, date_sub
from pyspark.sql.types import *
from pyspark.sql.window import Window

# ML imports
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import TimeSeriesSplit

# Local imports
from feature_engineer import FeatureEngineer
from model_trainer import ModelTrainer
from ensemble import ModelEnsemble

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WeatherEnergyPipeline:
    """Complete pipeline for weather-based energy demand forecasting"""
    
    def __init__(self):
        """Initialize the pipeline with Spark and MLflow configuration"""
        self.spark = None
        self.mlflow_uri = "http://mlflow-tracking:5000"
        self.experiment_name = "weather-energy-forecasting"
        self.models = {}
        self.metrics = {}
        
    def initialize_spark(self):
        """Initialize Spark session with optimized configuration"""
        logger.info("Initializing Spark session...")
        
        self.spark = SparkSession.builder \
            .appName("WeatherEnergyForecasting") \
            .config("spark.master", "spark://spark-master:7077") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .config("spark.sql.adaptive.skewJoin.enabled", "true") \
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
            .getOrCreate()
        
        # Set log level to reduce verbosity
        self.spark.sparkContext.setLogLevel("WARN")
        logger.info("Spark session initialized successfully")
        
    def setup_mlflow(self):
        """Setup MLflow tracking and experiment"""
        logger.info("Setting up MLflow tracking...")
        
        mlflow.set_tracking_uri(self.mlflow_uri)
        
        # Create or get experiment
        try:
            experiment_id = mlflow.create_experiment(self.experiment_name)
            logger.info(f"Created new experiment: {self.experiment_name}")
        except mlflow.exceptions.MlflowException:
            experiment_id = mlflow.get_experiment_by_name(self.experiment_name).experiment_id
            logger.info(f"Using existing experiment: {self.experiment_name}")
        
        mlflow.set_experiment(self.experiment_name)
        
    def load_data(self):
        """Load and validate weather and energy demand datasets"""
        logger.info("Loading datasets...")
        
        try:
            # Load energy demand data
            demand_df = self.spark.read.csv("/Dataset/weather/epias_demand_data.csv", 
                                          header=True, inferSchema=True)
            
            # Load weather data
            weather_df = self.spark.read.csv("/Dataset/weather/openmeteo_weather_data.csv", 
                                           header=True, inferSchema=True)
            
            logger.info(f"Loaded demand data: {demand_df.count()} records")
            logger.info(f"Loaded weather data: {weather_df.count()} records")
            
            return demand_df, weather_df
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def preprocess_data(self, demand_df, weather_df):
        """Preprocess and clean the datasets"""
        logger.info("Preprocessing data...")
        
        # Rename columns for consistency
        demand_df = demand_df.withColumnRenamed("date", "timestamp") \
                            .withColumnRenamed("consumption", "load_mw")
        
        # Convert timestamp columns
        demand_df = demand_df.withColumn("timestamp", 
                                        to_timestamp(col("timestamp"), "yyyy-MM-dd HH:mm:ss"))
        weather_df = weather_df.withColumn("timestamp", 
                                          to_timestamp(col("date_time"), "yyyy-MM-dd HH:mm:ss"))
        
        # Remove rows with null timestamps
        demand_df = demand_df.filter(col("timestamp").isNotNull())
        weather_df = weather_df.filter(col("timestamp").isNotNull())
        
        logger.info("Data preprocessing completed")
        return demand_df, weather_df
    
    def create_weather_aggregations(self, weather_df):
        """Create population-weighted weather aggregations"""
        logger.info("Creating weather aggregations...")
        
        # Major Turkish cities with approximate population weights
        city_weights = {
            'Istanbul': 0.18, 'Ankara': 0.06, 'Izmir': 0.05, 'Bursa': 0.03,
            'Antalya': 0.025, 'Konya': 0.02, 'Adana': 0.02, 'Gaziantep': 0.015,
            'Kocaeli': 0.015, 'Mersin': 0.015, 'Diyarbakir': 0.01, 'Hatay': 0.01,
            'Manisa': 0.01, 'Kayseri': 0.01, 'Samsun': 0.01, 'Balikesir': 0.01,
            'Tekirdag': 0.008, 'Aydin': 0.008, 'Kahramanmaras': 0.008, 'Van': 0.008,
            'Sakarya': 0.008, 'Mugla': 0.008, 'Denizli': 0.008, 'Sanliurfa': 0.015
        }
        
        # Calculate weighted averages for key weather variables
        weighted_temp = lit(0.0)
        weighted_humidity = lit(0.0)
        weighted_wind = lit(0.0)
        weighted_precipitation = lit(0.0)
        
        for city, weight in city_weights.items():
            temp_col = f"temperature_2m_{city}"
            humidity_col = f"relative_humidity_2m_{city}"
            wind_col = f"wind_speed_10m_{city}"
            precip_col = f"precipitation_{city}"
            
            if temp_col in weather_df.columns:
                weighted_temp = weighted_temp + (col(temp_col) * lit(weight))
                weighted_humidity = weighted_humidity + (col(humidity_col) * lit(weight))
                weighted_wind = weighted_wind + (col(wind_col) * lit(weight))
                weighted_precipitation = weighted_precipitation + (col(precip_col) * lit(weight))
        
        # Create aggregated weather dataframe
        weather_agg = weather_df.select(
            col("timestamp"),
            weighted_temp.alias("avg_temperature"),
            weighted_humidity.alias("avg_humidity"),
            weighted_wind.alias("avg_wind_speed"),
            weighted_precipitation.alias("avg_precipitation")
        )
        
        logger.info("Weather aggregations created")
        return weather_agg
    
    def engineer_features(self, demand_df, weather_agg):
        """Create comprehensive feature set"""
        logger.info("Engineering features...")
        
        # Initialize feature engineer
        fe = FeatureEngineer(self.spark)
        
        # Create time-based features (these don't leak data)
        demand_df = fe.create_time_features(demand_df)
        
        # Join with weather data first
        final_df = demand_df.join(weather_agg, on="timestamp", how="inner")
        
        # Convert to Pandas for easier manipulation
        final_df_pandas = final_df.toPandas()
        
        # Sort by timestamp to ensure proper time series order
        final_df_pandas = final_df_pandas.sort_values('timestamp').reset_index(drop=True)
        
        # Split data BEFORE creating lag/rolling features to prevent data leakage
        split_idx = int(len(final_df_pandas) * 0.8)
        train_data = final_df_pandas.iloc[:split_idx].copy()
        val_data = final_df_pandas.iloc[split_idx:].copy()
        
        logger.info(f"Split data: {len(train_data)} training samples, {len(val_data)} validation samples")
        
        # Create lag features ONLY on training data
        for lag_period in [1, 2, 24, 48, 168]:
            train_data[f'load_mw_lag_{lag_period}'] = train_data['load_mw'].shift(lag_period)
        
        # Create rolling features ONLY on training data
        for window in [24, 48, 168]:
            train_data[f'load_mw_roll_mean_{window}'] = train_data['load_mw'].rolling(window=window, min_periods=1).mean()
            train_data[f'load_mw_roll_std_{window}'] = train_data['load_mw'].rolling(window=window, min_periods=1).std()
            train_data[f'load_mw_roll_min_{window}'] = train_data['load_mw'].rolling(window=window, min_periods=1).min()
            train_data[f'load_mw_roll_max_{window}'] = train_data['load_mw'].rolling(window=window, min_periods=1).max()
        
        # For validation data, we need to create lag features using the last values from training data
        # This simulates real-world scenario where we only have historical data
        for lag_period in [1, 2, 24, 48, 168]:
            if lag_period <= len(train_data):
                # Create lag features for validation data
                val_data[f'load_mw_lag_{lag_period}'] = val_data['load_mw'].shift(lag_period)
                
                # Fill the first lag_period values with the last values from training data
                # This ensures we don't have NaN values at the beginning of validation set
                if len(val_data) > 0:
                    # Get the last lag_period values from training data
                    last_train_values = train_data['load_mw'].iloc[-lag_period:].values
                    
                    # Fill the first lag_period positions in validation data
                    for i in range(__builtins__.min(lag_period, len(val_data))):
                        val_data.iloc[i, val_data.columns.get_loc(f'load_mw_lag_{lag_period}')] = last_train_values[-(lag_period-i)]
        
        # Create rolling features for validation data using expanding windows
        for window in [24, 48, 168]:
            # For validation, use expanding window starting from training data
            combined_data = pd.concat([train_data[['load_mw']], val_data[['load_mw']]], ignore_index=True)
            
            # Calculate rolling statistics on combined data, then extract validation portion
            roll_mean = combined_data['load_mw'].rolling(window=window, min_periods=1).mean()
            roll_std = combined_data['load_mw'].rolling(window=window, min_periods=1).std()
            roll_min = combined_data['load_mw'].rolling(window=window, min_periods=1).min()
            roll_max = combined_data['load_mw'].rolling(window=window, min_periods=1).max()
            
            # Extract validation portion
            val_data[f'load_mw_roll_mean_{window}'] = roll_mean.iloc[len(train_data):].values
            val_data[f'load_mw_roll_std_{window}'] = roll_std.iloc[len(train_data):].values
            val_data[f'load_mw_roll_min_{window}'] = roll_min.iloc[len(train_data):].values
            val_data[f'load_mw_roll_max_{window}'] = roll_max.iloc[len(train_data):].values
        
        # Remove rows with null values (especially from lag features at the beginning)
        train_data = train_data.dropna()
        val_data = val_data.dropna()
        
        logger.info(f"After removing nulls: {len(train_data)} training samples, {len(val_data)} validation samples")
        
        # Save processed data
        train_data.to_csv('weather/outputs/train_data_processed.csv', index=False)
        val_data.to_csv('weather/outputs/val_data_processed.csv', index=False)
        
        return train_data, val_data
    
    def prepare_ml_data(self, train_data, val_data):
        """Prepare train and validation data for ML"""
        logger.info("Preparing data for ML training...")
        
        # Prepare features and target for training data
        exclude_cols = ['timestamp', 'load_mw', 'time', 'date','month','quarter','year']
        feature_cols = [col for col in train_data.columns if col not in exclude_cols]
        
        X_train = train_data[feature_cols]
        y_train = train_data['load_mw']
        
        X_val = val_data[feature_cols]
        y_val = val_data['load_mw']
        
        logger.info(f"Training set: {len(X_train)} samples")
        logger.info(f"Validation set: {len(X_val)} samples")
        logger.info(f"Features: {len(feature_cols)}")

        X_train.to_csv('weather/outputs/X_train_df.csv', index=False)
        X_val.to_csv('weather/outputs/X_val_df.csv', index=False)

        
        return X_train, X_val, y_train, y_val, feature_cols
    
    def train_models(self, X_train, X_val, y_train, y_val):
        """Train multiple ML models with fallbacks for missing libraries"""
        logger.info("Training ML models...")

        # Initialize model trainer
        trainer = ModelTrainer(self.mlflow_uri)

        # Train models with fallbacks
        # try:
        #     # LightGBM parameters (will fallback to GradientBoosting if not available)
        #     lgb_params = {
        #         'objective': 'regression',
        #         'metric': 'rmse',
        #         'boosting_type': 'gbdt',
        #         'num_leaves': 31,
        #         'learning_rate': 0.05,
        #         'feature_fraction': 0.9,
        #         'bagging_fraction': 0.8,
        #         'bagging_freq': 5,
        #         'verbose': -1,
        #         'random_state': 42
        #     }

        #     logger.info("Training LightGBM model (or GradientBoosting fallback)...")
        #     self.models['lightgbm'], self.metrics['lightgbm'] = trainer.train_lightgbm(
        #         X_train, y_train, X_val, y_val, lgb_params)
        # except Exception as e:
        #     logger.error(f"LightGBM training failed: {str(e)}")

        # try:
        #     # XGBoost parameters (will fallback to RandomForest if not available)
        #     xgb_params = {
        #         'objective': 'reg:squarederror',
        #         'eval_metric': 'rmse',
        #         'max_depth': 6,
        #         'learning_rate': 0.05,
        #         'subsample': 0.8,
        #         'colsample_bytree': 0.8,
        #         'verbosity': 0,
        #         'random_state': 42
        #     }

        #     logger.info("Training XGBoost model (or RandomForest fallback)...")
        #     self.models['xgboost'], self.metrics['xgboost'] = trainer.train_xgboost(
        #         X_train, y_train, X_val, y_val, xgb_params)
        # except Exception as e:
        #     logger.error(f"XGBoost training failed: {str(e)}")

        # try:
        #     # LSTM parameters (will fallback to Ridge if TensorFlow not available)
        #     lstm_params = {
        #         'lstm_units': 50,
        #         'dense_units': 25,
        #         'dropout_rate': 0.2,
        #         'learning_rate': 0.001,
        #         'epochs': 50,  # Reduced for faster training
        #         'batch_size': 32
        #     }

        #     logger.info("Training LSTM model (or Ridge fallback)...")
        #     self.models['lstm'], self.metrics['lstm'] = trainer.train_lstm(
        #         X_train, y_train, X_val, y_val, lstm_params)
        # except Exception as e:
        #     logger.error(f"LSTM training failed: {str(e)}")

        # Train additional sklearn models for comparison
        try:
            # Random Forest
            rf_params = {
                'n_estimators': 100,
                'max_depth': 10,
                'random_state': 42
            }
            logger.info("Training Random Forest model...")
            self.models['random_forest'], self.metrics['random_forest'] = \
                trainer.train_sklearn_model(X_train, y_train, X_val, y_val,
                                           'RandomForest', rf_params)
        except Exception as e:
            logger.error(f"Random Forest training failed: {str(e)}")

        logger.info(f"Model training completed. Trained {len(self.models)} models.")
    
    def create_ensemble(self, X_train, X_val, y_train, y_val):
        """Create and train ensemble model"""
        logger.info("Creating ensemble model...")
        
        if len(self.models) < 2:
            logger.warning("Not enough models for ensemble. Skipping ensemble creation.")
            return
        
        # Create ensemble with available models
        ensemble = ModelEnsemble(list(self.models.values()))
        
        with mlflow.start_run(run_name="Ensemble_Stacking"):
            try:
                # Train stacking ensemble
                ensemble_pred = ensemble.train_stacking(X_train, y_train, X_val, y_val)
                
                # Calculate ensemble metrics
                ensemble_metrics = {
                    'mae': mean_absolute_error(y_val, ensemble_pred),
                    'rmse': np.sqrt(mean_squared_error(y_val, ensemble_pred)),
                    'mape': np.mean(np.abs((y_val - ensemble_pred) / y_val)) * 100,
                    'r2': r2_score(y_val, ensemble_pred)
                }
                
                # Log ensemble metrics and parameters
                mlflow.log_metrics(ensemble_metrics)
                mlflow.log_param("ensemble_type", "stacking")
                mlflow.log_param("base_models", list(self.models.keys()))
                mlflow.log_param("num_base_models", len(self.models))
                
                self.metrics['ensemble'] = ensemble_metrics
                
                logger.info(f"Ensemble RMSE: {ensemble_metrics['rmse']:.2f}")
                logger.info(f"Ensemble MAPE: {ensemble_metrics['mape']:.2f}%")
                
            except Exception as e:
                logger.error(f"Ensemble training failed: {str(e)}")
    
    def print_results(self):
        """Print training results summary"""
        logger.info("\n" + "="*60)
        logger.info("TRAINING RESULTS SUMMARY")
        logger.info("="*60)
        
        for model_name, metrics in self.metrics.items():
            logger.info(f"\n{model_name.upper()} Model:")
            logger.info(f"  RMSE: {metrics['rmse']:.2f} MW")
            logger.info(f"  MAE:  {metrics['mae']:.2f} MW")
            logger.info(f"  MAPE: {metrics['mape']:.2f}%")
            logger.info(f"  R²:   {metrics['r2']:.4f}")
        
        logger.info(f"\nMLflow UI: {self.mlflow_uri}")
        logger.info("="*60)
    
    def cleanup(self):
        """Clean up resources"""
        if self.spark:
            self.spark.stop()
            logger.info("Spark session stopped")
    
    def run_pipeline(self):
        """Execute the complete training pipeline"""
        try:
            logger.info("Starting Weather Energy Forecasting Pipeline")
            
            # Initialize components
            self.initialize_spark()
            self.setup_mlflow()
            
            # Load and preprocess data
            demand_df, weather_df = self.load_data()
            demand_df, weather_df = self.preprocess_data(demand_df, weather_df)
            
            # Feature engineering
            weather_agg = self.create_weather_aggregations(weather_df)
            train_data, val_data = self.engineer_features(demand_df, weather_agg)
            
            # Prepare ML data
            X_train, X_val, y_train, y_val, feature_cols = self.prepare_ml_data(train_data, val_data)
            
            # Train models
            self.train_models(X_train, X_val, y_train, y_val)
            
            # Create ensemble
            self.create_ensemble(X_train, X_val, y_train, y_val)
            
            # Print results
            self.print_results()
            
            logger.info("Pipeline completed successfully!")
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise
        finally:
            self.cleanup()

if __name__ == "__main__":
    # Run the complete pipeline
    pipeline = WeatherEnergyPipeline()
    pipeline.run_pipeline()
