# services/ml-pipeline/src/features/feature_engineer.py
import pandas as pd
import numpy as np
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, hour, dayofweek, month, quarter, year, when, date_add, date_sub, avg, stddev, min as spark_min, max as spark_max, sum
from pyspark.sql.functions import lag as spark_lag  # Explicit import to avoid naming conflicts
from pyspark.sql.types import *
from pyspark.sql.window import Window

class FeatureEngineer:
    def __init__(self, spark: SparkSession):
        self.spark = spark
        # Define major Turkish holidays for 2023 (can be extended)
        self.turkish_holidays_2023 = [
            '2023-01-01',  # New Year's Day
            '2023-04-23',  # National Sovereignty and Children's Day
            '2023-05-01',  # Labour Day
            '2023-05-19',  # Commemoration of Atatürk, Youth and Sports Day
            '2023-07-15',  # Democracy and National Unity Day
            '2023-08-30',  # Victory Day
            '2023-10-29',  # Republic Day
            # Religious holidays (approximate dates for 2023)
            '2023-04-21',  # Ramadan Feast (Eid al-Fitr) - Day 1
            '2023-04-22',  # Ramadan Feast - Day 2
            '2023-04-23',  # Ramadan Feast - Day 3
            '2023-06-28',  # Sacrifice Feast (Eid al-Adha) - Day 1
            '2023-06-29',  # Sacrifice Feast - Day 2
            '2023-06-30',  # Sacrifice Feast - Day 3
            '2023-07-01',  # Sacrifice Feast - Day 4
        ]

    def create_time_features(self, df):
        """Create time-based features"""
        df = df.withColumn('hour', hour('timestamp'))
        df = df.withColumn('day_of_week', dayofweek('timestamp'))
        df = df.withColumn('month', month('timestamp'))
        df = df.withColumn('quarter', quarter('timestamp'))
        df = df.withColumn('year', year('timestamp'))

        # Weekend indicator
        df = df.withColumn('is_weekend',
                          when(col('day_of_week').isin([1, 7]), 1).otherwise(0))

        # Working hours indicator
        df = df.withColumn('is_working_hours',
                          when((col('hour') >= 8) & (col('hour') <= 18), 1).otherwise(0))

        # Holiday indicator
        df = self.add_holiday_features(df)

        return df

    def add_holiday_features(self, df):
        """Add Turkish holiday indicators"""
        # Create holiday indicator
        holiday_condition = col('timestamp').cast('date').isin(self.turkish_holidays_2023)
        df = df.withColumn('is_holiday', when(holiday_condition, 1).otherwise(0))

        # Day before holiday
        df = df.withColumn('date_only', col('timestamp').cast('date'))
        df = df.withColumn('next_day', date_add(col('date_only'), 1))
        next_day_holiday = col('next_day').isin(self.turkish_holidays_2023)
        df = df.withColumn('is_pre_holiday', when(next_day_holiday, 1).otherwise(0))

        # Day after holiday
        df = df.withColumn('prev_day', date_sub(col('date_only'), 1))
        prev_day_holiday = col('prev_day').isin(self.turkish_holidays_2023)
        df = df.withColumn('is_post_holiday', when(prev_day_holiday, 1).otherwise(0))

        # Clean up temporary columns
        df = df.drop('date_only', 'next_day', 'prev_day')

        return df

    def create_lag_features(self, df, target_col='load_mw', lags=[1, 2, 24, 48, 168]):
        """Create lag features for time series"""
        window_spec = Window.orderBy('timestamp')

        for lag_period in lags:
            df = df.withColumn(f'{target_col}_lag_{lag_period}',
                             spark_lag(col(target_col), lag_period).over(window_spec))

        return df

    def create_rolling_features(self, df, target_col='load_mw', windows=[24, 48, 168]):
        """Create rolling statistics features"""
        for window in windows:
            window_spec = Window.orderBy('timestamp').rowsBetween(-window, -1)

            df = df.withColumn(f'{target_col}_roll_mean_{window}',
                             avg(col(target_col)).over(window_spec))

            df = df.withColumn(f'{target_col}_roll_std_{window}',
                             stddev(col(target_col)).over(window_spec))

            df = df.withColumn(f'{target_col}_roll_min_{window}',
                             spark_min(col(target_col)).over(window_spec))

            df = df.withColumn(f'{target_col}_roll_max_{window}',
                             spark_max(col(target_col)).over(window_spec))

        return df

    def create_weather_aggregations(self, weather_df):
        """Aggregate weather data across cities"""
        # Population-weighted averages (approximate weights)
        city_weights = {
            'istanbul': 0.18,
            'ankara': 0.06,
            'izmir': 0.05,
            'bursa': 0.03,
            'antalya': 0.02
        }

        # Add weights
        for city, weight in city_weights.items():
            weather_df = weather_df.withColumn('weight',
                                             when(col('city') == city, weight).otherwise(0))

        # Weighted aggregations
        agg_df = weather_df.groupBy('timestamp').agg(
            sum(col('temperature') * col('weight')).alias('weighted_avg_temp'),
            sum(col('apparent_temperature') * col('weight')).alias('weighted_avg_apparent_temp'),
            sum(col('humidity') * col('weight')).alias('weighted_avg_humidity'),
            sum(col('wind_speed') * col('weight')).alias('weighted_avg_wind_speed'),
            sum(col('cloud_cover') * col('weight')).alias('weighted_avg_cloud_cover'),
            sum(col('solar_radiation') * col('weight')).alias('weighted_avg_solar_radiation')
        )

        return agg_df