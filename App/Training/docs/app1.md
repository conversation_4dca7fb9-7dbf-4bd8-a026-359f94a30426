Task: build a training pipeline for knowledge tracing using BKT, PFA, and DKT models that is dataset agnostic following the structure of pipeline in /Training/weather folder.
1- build KT pipeline in /Training/pipeline folder
2- use models in /Training/models for bkt and pfa
3- for dkt generate a new model file in /Training/models and use it in building the pipeline

- The pipeline need to explore the dataset and extract skills and students and problems
- The pipeline need to split the dataset into train and test
- The pipeline need to train the model on the train set using k-fold cross validation
- The pipeline need to evaluate the model on the test set
- The pipeline need to save the model to a file /Training/models/output
- The pipeline need to load the model from the file /
- there should be ensamble model that combines the 3 models
- there should be a hyperparameter tuning for the 3 models
- there should be a function that generates a report for the model performance
- there should be a function that generates a report for the dataset
- there should be a function that generates a report for the model performance per skill

in /Training/pipeline create a predection.py that use th trained models to predict the knowledge state of a student for a given skill with few examples.