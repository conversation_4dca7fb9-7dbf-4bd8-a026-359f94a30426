START PFA MODIFIED : AG
Total unique skills: 111
Creating 111 skill columns
Creating PFA features...
Feature matrix shape: (325637, 118)
Feature columns: ['success_count', 'failure_count', 'total_attempts', 'attempt_count', 'hint_count', 'ms_first_response', 'opportunity', 'skill_1.0', 'skill_2.0', 'skill_4.0', 'skill_5.0', 'skill_8.0', 'skill_9.0', 'skill_10.0', 'skill_11.0', 'skill_12.0', 'skill_13.0', 'skill_14.0', 'skill_15.0', 'skill_16.0', 'skill_17.0', 'skill_18.0', 'skill_21.0', 'skill_22.0', 'skill_24.0', 'skill_25.0', 'skill_26.0', 'skill_27.0', 'skill_32.0', 'skill_34.0', 'skill_35.0', 'skill_39.0', 'skill_40.0', 'skill_42.0', 'skill_43.0', 'skill_46.0', 'skill_47.0', 'skill_48.0', 'skill_49.0', 'skill_50.0', 'skill_51.0', 'skill_53.0', 'skill_54.0', 'skill_58.0', 'skill_61.0', 'skill_63.0', 'skill_64.0', 'skill_65.0', 'skill_67.0', 'skill_69.0', 'skill_70.0', 'skill_74.0', 'skill_75.0', 'skill_77.0', 'skill_79.0', 'skill_80.0', 'skill_81.0', 'skill_82.0', 'skill_83.0', 'skill_84.0', 'skill_85.0', 'skill_86.0', 'skill_92.0', 'skill_110.0', 'skill_163.0', 'skill_165.0', 'skill_166.0', 'skill_173.0', 'skill_190.0', 'skill_193.0', 'skill_203.0', 'skill_204.0', 'skill_217.0', 'skill_221.0', 'skill_276.0', 'skill_277.0', 'skill_278.0', 'skill_279.0', 'skill_280.0', 'skill_290.0', 'skill_292.0', 'skill_293.0', 'skill_294.0', 'skill_295.0', 'skill_296.0', 'skill_297.0', 'skill_298.0', 'skill_299.0', 'skill_301.0', 'skill_303.0', 'skill_307.0', 'skill_308.0', 'skill_309.0', 'skill_310.0', 'skill_311.0', 'skill_312.0', 'skill_314.0', 'skill_317.0', 'skill_321.0', 'skill_322.0', 'skill_323.0', 'skill_324.0', 'skill_325.0', 'skill_331.0', 'skill_333.0', 'skill_334.0', 'skill_340.0', 'skill_343.0', 'skill_346.0', 'skill_348.0', 'skill_350.0', 'skill_356.0', 'skill_362.0', 'skill_365.0', 'skill_368.0', 'skill_371.0', 'skill_375.0', 'skill_378.0']

Splitting data for train/test...
Training set size: 260509
Test set size: 65128
Training features: 118
Test features: 118
✓ Train and test have identical feature columns
Training PFA model...


==================================================
MODEL PARAMETER ANALYSIS
==================================================

Feature Importance (Coefficients):
success_count: 0.4848
failure_count: -0.5316
total_attempts: 0.0055
attempt_count: -27.9998
hint_count: -7.5071
ms_first_response: -0.0527
opportunity: 0.0359

Skill Difficulty Analysis:
Total skills analyzed: 111

Top 5 Most Difficult Skills:
Skill 16.0: Difficulty = 0.1768, Frequency = 0
Skill 17.0: Difficulty = 0.1719, Frequency = 0
Skill 278.0: Difficulty = 0.1621, Frequency = 0
Skill 81.0: Difficulty = 0.1591, Frequency = 0
Skill 77.0: Difficulty = 0.1366, Frequency = 0

Top 5 Easiest Skills:
Skill 1.0: Difficulty = -0.0968, Frequency = 0
Skill 67.0: Difficulty = -0.1094, Frequency = 0
Skill 61.0: Difficulty = -0.1110, Frequency = 0
Skill 280.0: Difficulty = -0.1668, Frequency = 0
Skill 47.0: Difficulty = -0.2055, Frequency = 0


==================================================
USER MASTERY ANALYSIS
==================================================

Top 5 Users by Mastery Level:
User 82672: Mastery = 1.000, Accuracy = 0.576, Skills = 1
User 82032: Mastery = 1.000, Accuracy = 0.714, Skills = 1
User 86678: Mastery = 1.000, Accuracy = 1.000, Skills = 1
User 83277: Mastery = 1.000, Accuracy = 0.526, Skills = 1
User 81624: Mastery = 1.000, Accuracy = 0.915, Skills = 1

==================================================
TRAINING STATISTICS
==================================================
Total interactions: 325637
Total users: 4151
Total skills: 111
Total problems: 16891
Overall accuracy: 0.658

Skill Statistics:
                                               Total_Interactions  Accuracy  \
skill_name                                                                    
Absolute Value                                               7340     0.757   
Addition Whole Numbers                                       4704     0.761   
Addition and Subtraction Fractions                          11334     0.677   
Addition and Subtraction Integers                           12741     0.599   
Addition and Subtraction Positive Decimals                   5012     0.580   
Algebraic Simplification                                       90     0.400   
Algebraic Solving                                             389     0.368   
Angles - Obtuse, Acute, and Right                             305     0.600   
Angles on Parallel Lines Cut by a Transversal                 278     0.802   
Area Circle                                                  1149     0.683   

                                               Accuracy_Std  Avg_Attempts  \
skill_name                                                                  
Absolute Value                                        0.429         1.048   
Addition Whole Numbers                                0.427         1.202   
Addition and Subtraction Fractions                    0.468         1.843   
Addition and Subtraction Integers                     0.490         2.123   
Addition and Subtraction Positive Decimals            0.494         0.913   
Algebraic Simplification                              0.493         1.656   
Algebraic Solving                                     0.483         3.031   
Angles - Obtuse, Acute, and Right                     0.491         1.397   
Angles on Parallel Lines Cut by a Transversal         0.399         1.076   
Area Circle                                           0.465         1.688   

                                               Unique_Users  
skill_name                                                   
Absolute Value                                         1002  
Addition Whole Numbers                                  988  
Addition and Subtraction Fractions                     1353  
Addition and Subtraction Integers                      1226  
Addition and Subtraction Positive Decimals              753  
Algebraic Simplification                                 15  
Algebraic Solving                                        88  
Angles - Obtuse, Acute, and Right                        11  
Angles on Parallel Lines Cut by a Transversal           140  
Area Circle                                             264  

Problem Statistics (Top 10):
            Total_Interactions  Accuracy  Avg_Attempts  Unique_Users
problem_id                                                          
49325                      272     0.765         0.941           116
49313                      272     0.735         0.868           114
49373                      270     0.726         0.896           106
49319                      270     0.726         0.889           114
49331                      262     0.718         0.939           106
49352                      260     0.769         0.862           103
49328                      250     0.720         0.936           106
49301                      248     0.677         0.919           100
49292                      248     0.815         0.911           102
49376                      248     0.710         0.903            91

Student Statistics (Top 10 by activity):
         Total_Interactions  Accuracy  Skills_Practiced  Problems_Solved
user_id                                                                 
79021                  1261     0.552                71             1000
75169                  1235     0.806                83              942
78970                  1177     0.589                68              961
79032                  1146     0.476                67              923
71881                  1118     0.714                80              876
96274                  1103     0.482                66              901
78979                  1093     0.549                66              915
79029                  1083     0.519                69              777
78978                  1066     0.591                69              842
96244                  1066     0.554                65              901


==================================================
PREDICTION EXAMPLES
==================================================
Showing 5 prediction examples:

Example 1:
  User: 78572, Skill: 74.0
  Prior Success: 0, Prior Failures: 0
  Predicted Probability: 0.702
  Predicted Class: 1
  Actual: 0
  Correct: ✗

Example 2:
  User: 96274, Skill: 4.0
  Prior Success: 2, Prior Failures: 0
  Predicted Probability: 0.000
  Predicted Class: 0
  Actual: 0
  Correct: ✓

Example 3:
  User: 89252, Skill: 86.0
  Prior Success: 1, Prior Failures: 2
  Predicted Probability: 0.000
  Predicted Class: 0
  Actual: 0
  Correct: ✓

Example 4:
  User: 79483, Skill: 65.0
  Prior Success: 5, Prior Failures: 2
  Predicted Probability: 0.927
  Predicted Class: 1
  Actual: 1
  Correct: ✓

Example 5:
  User: 73685, Skill: 279.0
  Prior Success: 12, Prior Failures: 0
  Predicted Probability: 0.900
  Predicted Class: 1
  Actual: 1
  Correct: ✓