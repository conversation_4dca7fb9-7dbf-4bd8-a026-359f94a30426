Loading math dataset...
✅ Dataset loaded successfully!
Shape: (401756, 30)
Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']

Missing values in key columns:
user_id: 0 (0.00%)
problem_id: 0 (0.00%)
skill_name: 76119 (18.95%)
correct: 0 (0.00%)

Cleaning dataset...
Original size: 401,756
Cleaned size: 325,637
Removed: 76,119 rows (18.95%)


COMPREHENSIVE SKILLS ANALYSIS
==================================================
Total number of unique skills: 110

Top 15 skills by number of interactions:
                                          interactions  accuracy  accuracy_std  unique_students  unique_problems
skill_name                                                                                                      
Equation Solving Two or Fewer Steps              24253     0.679         0.467              961             1040
Conversion of Fraction Decimals Percents         18742     0.637         0.481             1225              488
Addition and Subtraction Integers                12741     0.599         0.490             1226              413
Addition and Subtraction Fractions               11334     0.677         0.468             1353              433
Percent Of                                        9497     0.595         0.491             1115              465
Proportion                                        9054     0.641         0.480              756              485
Ordering Fractions                                8539     0.792         0.406              882              464
Equation Solving More Than Two Steps              8115     0.758         0.428              412              419
Probability of Two Distinct Events                7963     0.490         0.500              452              339
Finding Percents                                  7694     0.538         0.499              771              371
Subtraction Whole Numbers                         7669     0.641         0.480              903              242
Probability of a Single Event                     7438     0.742         0.437              939              350
Pattern Finding                                   7343     0.600         0.490              447              554
Absolute Value                                    7340     0.757         0.429             1002              241
Ordering Positive Decimals                        7317     0.750         0.433              942              543

Top 15 easiest skills (highest accuracy):
                                              interactions  accuracy  unique_students
skill_name                                                                           
Nets of 3D Figures                                     280     0.950              229
Area Parallelogram                                     115     0.922               95
Congruence                                             587     0.894              364
Distributive Property                                   18     0.889                5
Mode                                                  1926     0.876              572
Scatter Plot                                          1859     0.869              354
Area Rectangle                                         495     0.863              215
Area Triangle                                          286     0.857              168
D.4.8-understanding-concept-of-probabilities           456     0.846              202
Volume Rectangular Prism                               926     0.840              345
Fraction Of                                            607     0.830              288
Write Linear Equation from Situation                  1447     0.822              223
Linear Equations                                        89     0.820               41
Slope                                                   89     0.820               41
Choose an Equation from Given Information               89     0.820               41

Top 15 hardest skills (lowest accuracy):
                                               interactions  accuracy  unique_students
skill_name                                                                            
Reading a Ruler or Scale                                  5     0.000                5
Quadratic Formula to Solve Quadratic Equation            32     0.125               14
Rotations                                               427     0.136              163
Computation with Real Numbers                            21     0.190               21
Solving Systems of Linear Equations                     234     0.192               22
Percent Discount                                         47     0.234               29
Surface Area Cylinder                                   491     0.316              135
Finding Slope From Situation                              9     0.333                2
Percents                                                117     0.333               41
Algebraic Solving                                       389     0.368               88
Reflection                                              459     0.373              176
Rate                                                     91     0.374               39
Algebraic Simplification                                 90     0.400               15
Finding Slope from Ordered Pairs                          5     0.400                2
Multiplication Whole Numbers                            110     0.436               45

SKILLS CATEGORIZATION
==============================
Easy skills (≥80% accuracy): 17 skills
Medium skills (60-80% accuracy): 54 skills
Hard skills (<60% accuracy): 39 skills

High frequency skills (≥1000 interactions): 63 skills
Medium frequency skills (100-1000 interactions): 29 skills
Low frequency skills (<100 interactions): 18 skills


Splitting data into train/test sets...
Training set: 255,487 interactions from 3,320 users
Test set: 70,150 interactions from 831 users
Train accuracy: 0.655
Test accuracy: 0.670
Training set: 255,487 interactions from 3,320 users
Test set: 70,150 interactions from 831 users
Train accuracy: 0.655
Test accuracy: 0.670

Individual models training completed
Training completed in 36.05 seconds
Model trained on 110 skills
Each skill now has unique parameters estimated from student data!

=== MODEL EVALUATION ===
Test Accuracy: 0.831
Test AUC: 0.746
Number of predictions: 6907
Mean predicted probability: 0.780
Actual success rate: 0.832

=== TRAINING STATISTICS ===
Training time: 36.05 seconds
Number of skills trained: 110
Training data size: 255,487 interactions
Test data size: 70,150 interactions

=== SKILL-SPECIFIC PARAMETERS TABLE ===
Skill                               Interactions Accuracy P(Init)  P(Learn) P(Guess) P(Slip) 
---------------------------------------------------------------------------------------------------------
Equation Solving Two or Fewer Step  24253    0.679    0.530    0.135    0.104    0.099   
Conversion of Fraction Decimals Pe  18742    0.637    0.619    0.143    0.138    0.073   
Addition and Subtraction Integers   12741    0.599    0.622    0.189    0.134    0.050   
Addition and Subtraction Fractions  11334    0.677    0.501    0.205    0.100    0.066   
Percent Of                          9497     0.595    0.375    0.236    0.100    0.076   
Proportion                          9054     0.641    0.542    0.156    0.100    0.067   
Ordering Fractions                  8539     0.792    0.718    0.127    0.100    0.050   
Equation Solving More Than Two Ste  8115     0.758    0.531    0.124    0.100    0.087   
Probability of Two Distinct Events  7963     0.490    0.419    0.147    0.117    0.052   
Finding Percents                    7694     0.538    0.351    0.225    0.100    0.069   
Subtraction Whole Numbers           7669     0.641    0.566    0.225    0.100    0.051   
Probability of a Single Event       7438     0.742    0.658    0.171    0.100    0.050   
Pattern Finding                     7343     0.600    0.480    0.175    0.100    0.109   
Absolute Value                      7340     0.757    0.618    0.178    0.148    0.050   
Ordering Positive Decimals          7317     0.750    0.677    0.178    0.138    0.050   


=== SKILL COMPARISON BY CHARACTERISTICS ===

🟢 EASIEST SKILLS (Highest P(Init) - Students often know these initially):
  1. Mode: P(Init)=0.800, P(Learn)=0.117
  2. Ordering Integers: P(Init)=0.800, P(Learn)=0.138
  3. Area Parallelogram: P(Init)=0.800, P(Learn)=0.167
  4. Distributive Property: P(Init)=0.800, P(Learn)=0.083
  5. Recognize Quadratic Pattern: P(Init)=0.800, P(Learn)=0.050

🔴 HARDEST SKILLS (Lowest P(Init) - Students rarely know these initially):
  1. Finding Slope From Situation: P(Init)=0.010, P(Learn)=0.050
  2. Midpoint: P(Init)=0.010, P(Learn)=0.100
  3. Simplifying Expressions positive exponents: P(Init)=0.010, P(Learn)=0.050
  4. Rotations: P(Init)=0.035, P(Learn)=0.156
  5. Solving Systems of Linear Equations: P(Init)=0.077, P(Learn)=0.251

🚀 FASTEST LEARNING SKILLS (Highest P(Learn) - Quick to master):
  1. Effect of Changing Dimensions of a Shape Prportionally: P(Learn)=0.500, P(Init)=0.095
  2. Scale Factor: P(Learn)=0.475, P(Init)=0.150
  3. Fraction Of: P(Learn)=0.439, P(Init)=0.698
  4. Prime Number: P(Learn)=0.438, P(Init)=0.250
  5. Surface Area Cylinder: P(Learn)=0.434, P(Init)=0.082

🐌 SLOWEST LEARNING SKILLS (Lowest P(Learn) - Take time to master):
  1. Angles on Parallel Lines Cut by a Transversal: P(Learn)=0.050, P(Init)=0.704
  2. Percent Discount: P(Learn)=0.050, P(Init)=0.250
  3. Finding Slope From Situation: P(Learn)=0.050, P(Init)=0.010
  4. Recognize Quadratic Pattern: P(Learn)=0.050, P(Init)=0.800
  5. Quadratic Formula to Solve Quadratic Equation: P(Learn)=0.050, P(Init)=0.125


  === PREDICTION EXAMPLES ===

Example 1: Struggling Student
Skill: Pattern Finding 
Response history: [0, 0, 0, 1, 0, 1]
Predicted probabilities: ['0.480', '0.304', '0.265', '0.259', '0.689', '0.409']
Final knowledge probability: 0.409

Example 2: Successful Student
Skill: Pattern Finding 
Response history: [1, 1, 0, 1, 1, 1]
Predicted probabilities: ['0.480', '0.820', '0.884', '0.846', '0.887', '0.891']
Final knowledge probability: 0.891

Example 4: Interactive Prediction Function
Prediction result:
  skill: Range
  history: [0, 1, 0, 1, 1]
  knowledge_progression: ['0.602', '0.327', '0.800', '0.416', '0.848']
  current_knowledge_probability: 0.848
  next_response_probability: 0.848
  recommendation: Skill mastered