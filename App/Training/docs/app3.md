Working models:
Training/models/train_bkt.py
Training/models/train_pfa.py
Training/models/train_dkt.py

Saved models are in:
/Training/models/outputs
bkt -> joblib = "Training/models/outputs/bkt_model.joblib"
pfa -> joblib = "Training/models/outputs/pfa_model.joblib"
dkt -> h5 = "Training/models/outputs/dkt_model.h5"

dataset: /Training/datasets/math/skill_builder_data_corrected.csv

models notebook:
Training/math/math_bkt.ipynb
Training/math/math_pfa.ipynb
Training/math/math_dkt.ipynb
Training/math/2009_math_dataset.ipynb

env: conda activate tf-cpu-env


Task 1:
create new ensameble traing python script and notebook in /Training/math using Training/pipeline/kt_training_pipeline.py, using stacking and voting.

saved models to genereate a new ensembled model in Training/models/outputs/ensemble_model.joblib

test the ensamble model with first 10000 samples from the test dataset

generate metrics comparison between all models


Task 3:
Fix prediction.py, each diffrent model has differnt prediction interface.

create a prediction notebook in /Training/math that uses the prediction.py to predict the knowledge state of a student for a given skill with few examples.

in the prediction notebook we need to include the learning mastery state analyis

test prediction.py with unit tests using pytest and with firt 10000 samples from the test dataset

aim of prediction is to be used in real-time testing app that collects student interactions and predict their knowledge state. the demo notbook should include a demo of how the prediction can be used in a real-time app.



Task 4:
analyze Training/docs/report1.pdf and create an updated version of the report that is more formal and include more details. with less repeatation and more structure.

experiments results are in Training/docs/experiments/ html files

reflect the technical details of the project in the report.
reflect the findings of the thesis in the report.
reflect that this pahse was for building the knowledge tracing engine part and further work is needed to build the adaptive learning system with nlp and explaible AI.


in Training/docs create:
Appendix A: Model Implementation Details with Detailed code snippets and configuration parameters for each model.

Appendix B: System Architecture Diagrams, Technical architecture and data flow diagrams.





