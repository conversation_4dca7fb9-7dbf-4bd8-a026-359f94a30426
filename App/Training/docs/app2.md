create a training script in /Training/math that uses the pipeline on the math dataset 

- it should utilize the kt pipeline to train the models on the math dataset
- it should save the models to /Training/pipeline/outputs/math
- it should save the reports to /Training/reports/math
- it should save the plots to /Training/plots/math

create 2 version of the script .py and .ipynb
- in the notebook version include cells for exploratory data analysis and model performance analysis
- show statistics and plots for the dataset
- show statistics and plots for the model performance
- show statistics and plots for the model performance per skill
- show skills list analysis 

create a prediction notebook in /Training/math that uses the prediction.py to predict the knowledge state of a student for a given skill with few examples.

in the prediction notebook we need to include the learning mastery state analyis


using conda env tf-cpu-env run the scripts to train the models on the math dataset and generate the reports and plots fix any issues that arise.

also using same conda env run Training/pipeline/tests/test_kt_pipeline.py and fix any issues that arise.



After training on math dataset, we need a ready models to be integrated in real-time testing app that collects student interactions and predict their knowledge state. plus giving realtime analysis on their learning mastery state.

the real-time testing app will be built in the future, for now we need to make sure the training pipeline and prediction pipeline are ready and tested.