update all training models to include metrics calculation and save the metrics in a list inside the model object.

in pfa model create predict_proba method that uses predict method to return probability for each sample in X_test. add to main an example of calling predict_proba and printing the metrics.

in dka model create predict_proba method that uses predict method to return probability for each sample in X_test. add to main an example of calling predict_proba and printing the metrics.

update notebooks to use the new metrics calculation and print the metrics for each model after training block. accuracy, precision, recall, f1_score, auc_roc need to be calculated and printed.

in train_ensemble.py update pfa and dkt predict proba to prepare data correctly before calling predict method as each model main() predict example

pfa requreid features prepration to inlcude all skills feature and scale it before predicting.