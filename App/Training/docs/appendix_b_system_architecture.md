# Appendix B: System Architecture Diagrams

## B.1 Overall System Architecture

### B.1.1 High-Level Architecture Diagram

```mermaid
graph TB
    subgraph "Data Layer"
        DS[Educational Datasets]
        CSV[CSV Files]
        DB[(Database)]
    end
    
    subgraph "Training Pipeline"
        DP[Data Preprocessing]
        FE[Feature Engineering]
        MV[Model Validation]
        MT[Model Training]
        ME[Model Evaluation]
    end
    
    subgraph "Model Repository"
        BKT[BKT Model]
        PFA[PFA Model]
        DKT[DKT Model]
        ENS[Ensemble Model]
    end
    
    subgraph "Prediction Engine"
        ML[Model Loader]
        PI[Prediction Interface]
        SM[Session Manager]
        AF[Adaptive Feedback]
    end
    
    subgraph "Application Layer"
        API[REST API]
        WEB[Web Interface]
        RT[Real-Time App]
    end
    
    DS --> DP
    CSV --> DP
    DB --> DP
    
    DP --> FE
    FE --> MV
    MV --> MT
    MT --> ME
    
    MT --> BKT
    MT --> PFA
    MT --> DKT
    MT --> ENS
    
    BKT --> ML
    PFA --> ML
    DKT --> ML
    ENS --> ML
    
    ML --> PI
    PI --> SM
    SM --> AF
    
    AF --> API
    API --> WEB
    API --> RT
```

### B.1.2 Component Descriptions

**Data Layer Components:**
- **Educational Datasets**: Raw educational interaction data
- **CSV Files**: Structured data files (ASSISTments format)
- **Database**: Persistent storage for processed data

**Training Pipeline Components:**
- **Data Preprocessing**: Data cleaning, validation, and formatting
- **Feature Engineering**: Skill encoding, sequence preparation
- **Model Validation**: Cross-validation and hyperparameter tuning
- **Model Training**: Algorithm-specific training procedures
- **Model Evaluation**: Performance assessment and comparison

**Model Repository Components:**
- **BKT Model**: Bayesian Knowledge Tracing implementation
- **PFA Model**: Performance Factor Analysis implementation
- **DKT Model**: Deep Knowledge Tracing implementation
- **Ensemble Model**: Combined model predictions

**Prediction Engine Components:**
- **Model Loader**: Efficient model deserialization and caching
- **Prediction Interface**: Unified API for all model types
- **Session Manager**: Student interaction history tracking
- **Adaptive Feedback**: Real-time recommendation generation

---

## B.2 Data Flow Architecture

### B.2.1 Training Data Flow

```mermaid
flowchart TD
    A[Raw Dataset] --> B[Data Validation]
    B --> C{Valid Data?}
    C -->|No| D[Error Logging]
    C -->|Yes| E[Data Cleaning]
    
    E --> F[Missing Value Handling]
    F --> G[Duplicate Removal]
    G --> H[Temporal Ordering]
    
    H --> I[Feature Engineering]
    I --> J[Skill Encoding]
    J --> K[Sequence Creation]
    K --> L[Train-Test Split]
    
    L --> M[BKT Training]
    L --> N[PFA Training]
    L --> O[DKT Training]
    
    M --> P[BKT Model]
    N --> Q[PFA Model]
    O --> R[DKT Model]
    
    P --> S[Ensemble Training]
    Q --> S
    R --> S
    
    S --> T[Ensemble Model]
    
    P --> U[Model Evaluation]
    Q --> U
    R --> U
    T --> U
    
    U --> V[Performance Reports]
    U --> W[Model Persistence]
```

### B.2.2 Prediction Data Flow

```mermaid
flowchart TD
    A[Student Interaction] --> B[Input Validation]
    B --> C{Valid Input?}
    C -->|No| D[Error Response]
    C -->|Yes| E[Session Lookup]
    
    E --> F{Existing Session?}
    F -->|No| G[Create New Session]
    F -->|Yes| H[Update Session History]
    
    G --> I[Initialize History]
    I --> J[Model Selection]
    H --> J
    
    J --> K[Load Model]
    K --> L[Generate Prediction]
    
    L --> M[Mastery Analysis]
    M --> N[Adaptive Recommendations]
    
    N --> O[Response Generation]
    O --> P[Session Update]
    P --> Q[Logging]
    
    Q --> R[Return Prediction]
```

---

## B.3 Model Training Architecture

### B.3.1 BKT Training Flow

```mermaid
flowchart TD
    A[Student Interaction Data] --> B[Group by Skill]
    B --> C[For Each Skill]
    
    C --> D[Initialize Parameters]
    D --> E[P_init Estimation]
    E --> F[P_learn Estimation]
    F --> G[P_guess Estimation]
    G --> H[P_slip Estimation]
    
    H --> I[EM Algorithm]
    I --> J[E-Step: Calculate Knowledge States]
    J --> K[M-Step: Update Parameters]
    K --> L{Converged?}
    
    L -->|No| J
    L -->|Yes| M[Store Skill Parameters]
    
    M --> N{More Skills?}
    N -->|Yes| C
    N -->|No| O[BKT Model Complete]
```

### B.3.2 DKT Training Flow

```mermaid
flowchart TD
    A[Student Sequences] --> B[Skill Encoding]
    B --> C[Sequence Padding]
    C --> D[Input-Output Pairs]
    
    D --> E[LSTM Model Architecture]
    E --> F[Embedding Layer]
    F --> G[LSTM Layer 1]
    G --> H[LSTM Layer 2]
    H --> I[LSTM Layer 3]
    I --> J[Dense Output Layer]
    
    J --> K[Model Compilation]
    K --> L[Training Loop]
    
    L --> M[Forward Pass]
    M --> N[Loss Calculation]
    N --> O[Backward Pass]
    O --> P[Parameter Update]
    
    P --> Q{Epoch Complete?}
    Q -->|No| M
    Q -->|Yes| R[Validation]
    
    R --> S{Early Stopping?}
    S -->|No| L
    S -->|Yes| T[Best Model Restoration]
    
    T --> U[DKT Model Complete]
```

---

## B.4 Prediction Engine Architecture

### B.4.1 Real-Time Prediction System

```mermaid
graph TB
    subgraph "Input Layer"
        UI[User Interaction]
        SH[Student History]
        SK[Skill Identifier]
    end
    
    subgraph "Processing Layer"
        IV[Input Validation]
        SM[Session Management]
        ML[Model Loading]
    end
    
    subgraph "Model Layer"
        BM[BKT Model]
        PM[PFA Model]
        DM[DKT Model]
        EM[Ensemble Model]
    end
    
    subgraph "Analysis Layer"
        PP[Prediction Processing]
        MA[Mastery Analysis]
        AR[Adaptive Recommendations]
    end
    
    subgraph "Output Layer"
        PR[Prediction Result]
        MS[Mastery State]
        RC[Recommendations]
        LG[Logging]
    end
    
    UI --> IV
    SH --> IV
    SK --> IV
    
    IV --> SM
    SM --> ML
    
    ML --> BM
    ML --> PM
    ML --> DM
    ML --> EM
    
    BM --> PP
    PM --> PP
    DM --> PP
    EM --> PP
    
    PP --> MA
    MA --> AR
    
    AR --> PR
    AR --> MS
    AR --> RC
    AR --> LG
```

### B.4.2 Session Management Architecture

```mermaid
stateDiagram-v2
    [*] --> SessionStart
    SessionStart --> ActiveSession : Initialize
    
    ActiveSession --> PredictionRequest : New Interaction
    PredictionRequest --> ModelInference : Validate Input
    ModelInference --> ResponseGeneration : Process Prediction
    ResponseGeneration --> ActiveSession : Return Result
    
    ActiveSession --> SessionUpdate : Update History
    SessionUpdate --> ActiveSession : History Updated
    
    ActiveSession --> SessionEnd : Timeout/Explicit End
    SessionEnd --> SessionArchive : Save Session Data
    SessionArchive --> [*]
    
    PredictionRequest --> ErrorHandling : Invalid Input
    ErrorHandling --> ActiveSession : Error Response
    
    ModelInference --> ErrorHandling : Model Error
```

---

## B.5 Ensemble Architecture

### B.5.1 Ensemble Model Structure

```mermaid
graph TB
    subgraph "Input Processing"
        UH[User History]
        SK[Skill]
        IP[Input Preprocessing]
    end
    
    subgraph "Base Models"
        BKT[BKT Model]
        PFA[PFA Model]
        DKT[DKT Model]
    end
    
    subgraph "Ensemble Methods"
        WA[Weighted Average]
        ST[Stacking]
        VT[Voting]
    end
    
    subgraph "Meta Learning"
        ML[Meta Learner]
        WO[Weight Optimization]
        CV[Cross Validation]
    end
    
    subgraph "Output Processing"
        PC[Prediction Combination]
        FP[Final Prediction]
        CF[Confidence Score]
    end
    
    UH --> IP
    SK --> IP
    
    IP --> BKT
    IP --> PFA
    IP --> DKT
    
    BKT --> WA
    PFA --> WA
    DKT --> WA
    
    BKT --> ST
    PFA --> ST
    DKT --> ST
    
    BKT --> VT
    PFA --> VT
    DKT --> VT
    
    ST --> ML
    WA --> WO
    WO --> CV
    
    WA --> PC
    ST --> PC
    VT --> PC
    
    PC --> FP
    PC --> CF
```

### B.5.2 Ensemble Training Process

```mermaid
sequenceDiagram
    participant TD as Training Data
    participant BKT as BKT Model
    participant PFA as PFA Model
    participant DKT as DKT Model
    participant EM as Ensemble Manager
    participant VD as Validation Data
    
    TD->>BKT: Train Individual Model
    TD->>PFA: Train Individual Model
    TD->>DKT: Train Individual Model
    
    BKT->>EM: Base Predictions
    PFA->>EM: Base Predictions
    DKT->>EM: Base Predictions
    
    EM->>VD: Generate Meta Features
    VD->>EM: Validation Performance
    
    EM->>EM: Optimize Weights
    EM->>EM: Train Meta Learner
    
    EM->>EM: Final Ensemble Model
```

---

## B.6 Deployment Architecture

### B.6.1 Production Deployment Structure

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Load Balancer]
    end
    
    subgraph "Application Servers"
        AS1[App Server 1]
        AS2[App Server 2]
        AS3[App Server N]
    end
    
    subgraph "Model Serving"
        MS1[Model Server 1]
        MS2[Model Server 2]
        MC[Model Cache]
    end
    
    subgraph "Data Storage"
        RD[(Redis Cache)]
        PG[(PostgreSQL)]
        FS[File Storage]
    end
    
    subgraph "Monitoring"
        MT[Metrics Collection]
        LG[Logging Service]
        AL[Alerting]
    end
    
    LB --> AS1
    LB --> AS2
    LB --> AS3
    
    AS1 --> MS1
    AS2 --> MS2
    AS3 --> MS1
    
    MS1 --> MC
    MS2 --> MC
    
    AS1 --> RD
    AS2 --> RD
    AS3 --> RD
    
    AS1 --> PG
    AS2 --> PG
    AS3 --> PG
    
    MC --> FS
    
    AS1 --> MT
    AS2 --> MT
    AS3 --> MT
    
    MT --> LG
    LG --> AL
```

### B.6.2 Scalability Architecture

```mermaid
graph LR
    subgraph "Horizontal Scaling"
        direction TB
        LB[Load Balancer]
        AS1[App Instance 1]
        AS2[App Instance 2]
        AS3[App Instance 3]
        ASN[App Instance N]
        
        LB --> AS1
        LB --> AS2
        LB --> AS3
        LB --> ASN
    end
    
    subgraph "Model Scaling"
        direction TB
        MS1[Model Server 1]
        MS2[Model Server 2]
        MSN[Model Server N]
        
        AS1 --> MS1
        AS2 --> MS2
        AS3 --> MSN
    end
    
    subgraph "Data Scaling"
        direction TB
        RC1[Redis Cluster 1]
        RC2[Redis Cluster 2]
        DB1[(Database Replica 1)]
        DB2[(Database Replica 2)]
        
        MS1 --> RC1
        MS2 --> RC2
        MS1 --> DB1
        MS2 --> DB2
    end
```

---

## B.7 Security Architecture

### B.7.1 Security Layers

```mermaid
graph TB
    subgraph "Network Security"
        FW[Firewall]
        WAF[Web Application Firewall]
        VPN[VPN Gateway]
    end
    
    subgraph "Application Security"
        AUTH[Authentication]
        AUTHZ[Authorization]
        RATE[Rate Limiting]
        VAL[Input Validation]
    end
    
    subgraph "Data Security"
        ENC[Encryption at Rest]
        TLS[TLS in Transit]
        MASK[Data Masking]
        AUDIT[Audit Logging]
    end
    
    subgraph "Model Security"
        MS[Model Signing]
        VI[Version Integrity]
        AM[Access Monitoring]
    end
    
    FW --> AUTH
    WAF --> AUTH
    VPN --> AUTH
    
    AUTH --> AUTHZ
    AUTHZ --> RATE
    RATE --> VAL
    
    VAL --> ENC
    ENC --> TLS
    TLS --> MASK
    MASK --> AUDIT
    
    AUDIT --> MS
    MS --> VI
    VI --> AM
```

---

## B.8 Monitoring and Observability

### B.8.1 Monitoring Architecture

```mermaid
graph TB
    subgraph "Application Metrics"
        APM[Application Performance]
        BM[Business Metrics]
        UM[User Metrics]
    end
    
    subgraph "Infrastructure Metrics"
        CPU[CPU Usage]
        MEM[Memory Usage]
        NET[Network I/O]
        DISK[Disk I/O]
    end
    
    subgraph "Model Metrics"
        ACC[Accuracy Metrics]
        LAT[Latency Metrics]
        THR[Throughput Metrics]
        ERR[Error Rates]
    end
    
    subgraph "Collection & Storage"
        PROM[Prometheus]
        GRAF[Grafana]
        ELK[ELK Stack]
    end
    
    subgraph "Alerting"
        ALERT[Alert Manager]
        SLACK[Slack Integration]
        EMAIL[Email Notifications]
    end
    
    APM --> PROM
    BM --> PROM
    UM --> PROM
    
    CPU --> PROM
    MEM --> PROM
    NET --> PROM
    DISK --> PROM
    
    ACC --> PROM
    LAT --> PROM
    THR --> PROM
    ERR --> PROM
    
    PROM --> GRAF
    PROM --> ELK
    
    PROM --> ALERT
    ALERT --> SLACK
    ALERT --> EMAIL
```

This comprehensive system architecture documentation provides detailed technical diagrams and explanations for all major components of the knowledge tracing engine, from data processing through model training to production deployment and monitoring.
