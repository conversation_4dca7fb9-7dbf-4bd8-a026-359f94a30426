# Appendix A: Model Implementation Details

## A.1 Bayesian Knowledge Tracing (BKT) Implementation

### A.1.1 Core Algorithm Implementation

```python
class BayesianKnowledgeTracer:
    """
    Bayesian Knowledge Tracing implementation using Hidden Markov Models
    """
    
    def __init__(self, skills=None):
        self.skills = skills or []
        self.skill_params = {}
        self.is_fitted = False
    
    def fit(self, df, skill_column='skill_name', user_column='user_id', 
            correct_column='correct'):
        """
        Train BKT model using Expectation-Maximization algorithm
        
        Parameters:
        -----------
        df : pandas.DataFrame
            Training data with user interactions
        skill_column : str
            Column name containing skill identifiers
        user_column : str
            Column name containing user identifiers
        correct_column : str
            Column name containing correctness (0/1)
        """
        
        # Initialize parameters for each skill
        for skill in df[skill_column].unique():
            if pd.isna(skill):
                continue
                
            skill_data = df[df[skill_column] == skill]
            
            # Initialize BKT parameters
            params = self._initialize_parameters(skill_data, correct_column)
            
            # Run EM algorithm for parameter estimation
            optimized_params = self._em_algorithm(skill_data, params, 
                                                user_column, correct_column)
            
            self.skill_params[skill] = optimized_params
        
        self.is_fitted = True
        return self
    
    def _initialize_parameters(self, skill_data, correct_column):
        """Initialize BKT parameters using heuristics"""
        overall_accuracy = skill_data[correct_column].mean()
        
        return {
            'p_init': min(0.85, max(0.01, overall_accuracy * 0.8)),
            'p_learn': min(0.3, max(0.01, 0.1)),
            'p_guess': min(0.3, max(0.01, (1 - overall_accuracy) * 0.5)),
            'p_slip': min(0.2, max(0.01, (1 - overall_accuracy) * 0.3))
        }
    
    def _em_algorithm(self, skill_data, initial_params, user_column, 
                     correct_column, max_iterations=50, tolerance=1e-4):
        """
        Expectation-Maximization algorithm for BKT parameter estimation
        """
        params = initial_params.copy()
        
        for iteration in range(max_iterations):
            old_params = params.copy()
            
            # E-step: Calculate expected knowledge states
            knowledge_states = self._e_step(skill_data, params, 
                                          user_column, correct_column)
            
            # M-step: Update parameters
            params = self._m_step(skill_data, knowledge_states, 
                                user_column, correct_column)
            
            # Check convergence
            if self._has_converged(old_params, params, tolerance):
                break
        
        return params
    
    def predict_proba(self, user_history, skill, n_steps=1):
        """
        Predict probability of correct response for given skill
        
        Parameters:
        -----------
        user_history : list
            List of previous responses (0/1) for the skill
        skill : str
            Skill identifier
        n_steps : int
            Number of future steps to predict
            
        Returns:
        --------
        float or list
            Predicted probability(ies) of correct response
        """
        if skill not in self.skill_params:
            return [0.5] * n_steps if n_steps > 1 else 0.5
        
        params = self.skill_params[skill]
        
        # Initialize knowledge probability
        p_know = params['p_init']
        
        # Update knowledge state based on history
        for response in user_history:
            p_know = self._update_knowledge_state(p_know, response, params)
        
        # Predict next response probability
        p_correct = (p_know * (1 - params['p_slip']) + 
                    (1 - p_know) * params['p_guess'])
        
        return [p_correct] * n_steps if n_steps > 1 else p_correct
```

### A.1.2 Configuration Parameters

```python
# BKT Model Configuration
BKT_CONFIG = {
    'em_max_iterations': 50,
    'em_tolerance': 1e-4,
    'parameter_bounds': {
        'p_init': (0.01, 0.99),
        'p_learn': (0.01, 0.50),
        'p_guess': (0.01, 0.30),
        'p_slip': (0.01, 0.20)
    },
    'initialization_strategy': 'data_driven',
    'convergence_check': 'parameter_change'
}
```

---

## A.2 Performance Factor Analysis (PFA) Implementation

### A.2.1 Core Algorithm Implementation

```python
class PFAKnowledgeTracing:
    """
    Performance Factor Analysis implementation using logistic regression
    """
    
    def __init__(self, regularization='l2', C=1.0):
        self.regularization = regularization
        self.C = C
        self.models = {}
        self.skill_encoders = {}
        self.is_fitted = False
    
    def fit(self, df, skill_column='skill_name', user_column='user_id',
            correct_column='correct'):
        """
        Train PFA model using logistic regression
        
        Parameters:
        -----------
        df : pandas.DataFrame
            Training data with user interactions
        """
        
        # Prepare features for each skill
        for skill in df[skill_column].unique():
            if pd.isna(skill):
                continue
            
            skill_data = df[df[skill_column] == skill].copy()
            
            # Create PFA features (success/failure counts)
            features = self._create_pfa_features(skill_data, user_column, 
                                               correct_column)
            
            if len(features) > 10:  # Minimum data requirement
                # Train logistic regression model
                model = LogisticRegression(
                    penalty=self.regularization,
                    C=self.C,
                    random_state=42,
                    max_iter=1000
                )
                
                X = features[['success_count', 'failure_count']].values
                y = features[correct_column].values
                
                model.fit(X, y)
                self.models[skill] = model
        
        self.is_fitted = True
        return self
    
    def _create_pfa_features(self, skill_data, user_column, correct_column):
        """
        Create PFA features: cumulative success and failure counts
        """
        features = []
        
        for user_id in skill_data[user_column].unique():
            user_data = skill_data[skill_data[user_column] == user_id].copy()
            user_data = user_data.sort_values('order_id')  # Temporal order
            
            success_count = 0
            failure_count = 0
            
            for idx, row in user_data.iterrows():
                features.append({
                    'user_id': user_id,
                    'success_count': success_count,
                    'failure_count': failure_count,
                    correct_column: row[correct_column]
                })
                
                # Update counts for next iteration
                if row[correct_column] == 1:
                    success_count += 1
                else:
                    failure_count += 1
        
        return pd.DataFrame(features)
    
    def predict_proba(self, user_history, skill, n_steps=1):
        """
        Predict probability using PFA model
        """
        if skill not in self.models:
            return [0.5] * n_steps if n_steps > 1 else 0.5
        
        model = self.models[skill]
        
        # Calculate success/failure counts from history
        success_count = sum(user_history)
        failure_count = len(user_history) - success_count
        
        # Predict probability
        X = np.array([[success_count, failure_count]])
        prob = model.predict_proba(X)[0, 1]
        
        return [prob] * n_steps if n_steps > 1 else prob
```

### A.2.2 Configuration Parameters

```python
# PFA Model Configuration
PFA_CONFIG = {
    'regularization': 'l2',
    'regularization_strength': 1.0,
    'solver': 'liblinear',
    'max_iterations': 1000,
    'minimum_data_points': 10,
    'feature_engineering': {
        'success_weight': 1.0,
        'failure_weight': 1.0,
        'temporal_decay': False
    }
}
```

---

## A.3 Deep Knowledge Tracing (DKT) Implementation

### A.3.1 Core Algorithm Implementation

```python
class DeepKnowledgeTracing:
    """
    Deep Knowledge Tracing implementation using LSTM networks
    """
    
    def __init__(self, n_skills, hidden_dim=64, num_layers=2, 
                 dropout=0.2, max_seq_len=100):
        self.n_skills = n_skills
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.max_seq_len = max_seq_len
        self.vocab_size = n_skills * 2  # skill + correctness
        self.model = None
        self.skill_encoders = {}
        self.is_fitted = False
    
    def _build_model(self):
        """
        Build LSTM-based DKT model architecture
        """
        model = tf.keras.Sequential([
            # Input layer
            tf.keras.layers.Embedding(
                input_dim=self.vocab_size,
                output_dim=self.hidden_dim,
                input_length=self.max_seq_len,
                mask_zero=True
            ),
            
            # LSTM layers
            tf.keras.layers.LSTM(
                self.hidden_dim,
                return_sequences=True,
                dropout=self.dropout,
                recurrent_dropout=self.dropout
            ),
            
            tf.keras.layers.LSTM(
                self.hidden_dim,
                return_sequences=True,
                dropout=self.dropout,
                recurrent_dropout=self.dropout
            ),
            
            tf.keras.layers.LSTM(
                self.hidden_dim,
                return_sequences=True,
                dropout=self.dropout,
                recurrent_dropout=self.dropout
            ),
            
            # Output layer
            tf.keras.layers.TimeDistributed(
                tf.keras.layers.Dense(self.n_skills, activation='sigmoid')
            )
        ])
        
        # Compile model
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def fit(self, df, skill_column='skill_name', user_column='user_id',
            correct_column='correct', epochs=50, batch_size=32,
            validation_split=0.2):
        """
        Train DKT model using student interaction sequences
        """
        
        # Encode skills
        unique_skills = df[skill_column].dropna().unique()
        self.skill_encoders = {skill: idx for idx, skill in enumerate(unique_skills)}
        self.n_skills = len(unique_skills)
        self.vocab_size = self.n_skills * 2
        
        # Prepare sequences
        X, y = self._prepare_sequences(df, skill_column, user_column, 
                                     correct_column)
        
        # Build and train model
        self.model = self._build_model()
        
        # Training callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-6
            )
        ]
        
        # Train model
        history = self.model.fit(
            X, y,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            callbacks=callbacks,
            verbose=1
        )
        
        self.is_fitted = True
        return history
    
    def _prepare_sequences(self, df, skill_column, user_column, correct_column):
        """
        Prepare student interaction sequences for LSTM training
        """
        sequences_X = []
        sequences_y = []
        
        for user_id in df[user_column].unique():
            user_data = df[df[user_column] == user_id].copy()
            user_data = user_data.sort_values('order_id')
            
            if len(user_data) < 2:
                continue
            
            # Create input sequence (skill + correctness encoding)
            sequence_x = []
            sequence_y = []
            
            for idx, row in user_data.iterrows():
                skill = row[skill_column]
                correct = row[correct_column]
                
                if pd.isna(skill) or skill not in self.skill_encoders:
                    continue
                
                skill_idx = self.skill_encoders[skill]
                # Encode as skill_idx + n_skills * correctness
                encoded_input = skill_idx + self.n_skills * correct
                
                sequence_x.append(encoded_input)
                
                # Target: next skill mastery (one-hot encoded)
                target = np.zeros(self.n_skills)
                target[skill_idx] = correct
                sequence_y.append(target)
            
            if len(sequence_x) >= 2:
                # Pad sequences
                padded_x = self._pad_sequence(sequence_x)
                padded_y = self._pad_sequence(sequence_y)
                
                sequences_X.append(padded_x)
                sequences_y.append(padded_y)
        
        return np.array(sequences_X), np.array(sequences_y)
    
    def predict_proba(self, user_history, skill, n_steps=1):
        """
        Predict probability using trained DKT model
        """
        if not self.is_fitted or skill not in self.skill_encoders:
            return [0.5] * n_steps if n_steps > 1 else 0.5
        
        skill_idx = self.skill_encoders[skill]
        
        # Prepare input sequence
        sequence = []
        for i, correct in enumerate(user_history):
            encoded_input = skill_idx + self.n_skills * correct
            sequence.append(encoded_input)
        
        # Pad sequence
        padded_sequence = self._pad_sequence(sequence)
        X = np.array([padded_sequence])
        
        # Predict
        predictions = self.model.predict(X, verbose=0)
        prob = predictions[0, -1, skill_idx]  # Last timestep, target skill
        
        return [prob] * n_steps if n_steps > 1 else float(prob)
```

### A.3.2 Configuration Parameters

```python
# DKT Model Configuration
DKT_CONFIG = {
    'architecture': {
        'hidden_dim': 64,
        'num_layers': 3,
        'dropout': 0.2,
        'max_seq_len': 100
    },
    'training': {
        'epochs': 50,
        'batch_size': 32,
        'validation_split': 0.2,
        'learning_rate': 0.001
    },
    'callbacks': {
        'early_stopping_patience': 10,
        'lr_reduction_patience': 5,
        'lr_reduction_factor': 0.5,
        'min_learning_rate': 1e-6
    },
    'data_preparation': {
        'min_sequence_length': 2,
        'padding_value': 0,
        'skill_encoding': 'index_based'
    }
}
```

---

## A.4 Ensemble Methods Implementation

### A.4.1 Weighted Average Ensemble

```python
class WeightedAverageEnsemble:
    """
    Weighted average ensemble for knowledge tracing models
    """
    
    def __init__(self, models, model_names):
        self.models = models
        self.model_names = model_names
        self.weights = None
        self.is_fitted = False
    
    def fit(self, validation_data):
        """
        Learn optimal weights using validation data
        """
        predictions = []
        
        # Get predictions from each model
        for model in self.models:
            model_preds = []
            for user_history, skill, target in validation_data:
                pred = model.predict_proba(user_history, skill)
                model_preds.append(pred)
            predictions.append(model_preds)
        
        # Optimize weights using grid search
        best_weights = self._optimize_weights(predictions, 
                                            [target for _, _, target in validation_data])
        
        self.weights = best_weights
        self.is_fitted = True
        return self
    
    def _optimize_weights(self, predictions, targets):
        """
        Optimize ensemble weights using validation performance
        """
        from scipy.optimize import minimize
        
        def objective(weights):
            weights = weights / np.sum(weights)  # Normalize
            ensemble_preds = np.average(predictions, axis=0, weights=weights)
            return -roc_auc_score(targets, ensemble_preds)
        
        # Initial weights (equal)
        initial_weights = np.ones(len(self.models)) / len(self.models)
        
        # Constraints: weights sum to 1, all positive
        constraints = {'type': 'eq', 'fun': lambda w: np.sum(w) - 1}
        bounds = [(0, 1) for _ in range(len(self.models))]
        
        result = minimize(objective, initial_weights, 
                         constraints=constraints, bounds=bounds)
        
        return result.x
    
    def predict_proba(self, user_history, skill, n_steps=1):
        """
        Predict using weighted average of base models
        """
        if not self.is_fitted:
            return [0.5] * n_steps if n_steps > 1 else 0.5
        
        predictions = []
        for model in self.models:
            pred = model.predict_proba(user_history, skill, 1)
            predictions.append(pred)
        
        # Weighted average
        ensemble_pred = np.average(predictions, weights=self.weights)
        
        return [ensemble_pred] * n_steps if n_steps > 1 else ensemble_pred
```

### A.4.2 Stacking Ensemble Configuration

```python
# Stacking Ensemble Configuration
STACKING_CONFIG = {
    'meta_learner': {
        'algorithm': 'logistic_regression',
        'regularization': 'l2',
        'regularization_strength': 1.0
    },
    'cross_validation': {
        'folds': 5,
        'stratified': True,
        'random_state': 42
    },
    'feature_engineering': {
        'include_base_features': False,
        'prediction_aggregation': 'none'
    }
}
```

---

## A.5 Model Persistence and Loading

### A.5.1 Model Serialization

```python
# Model saving configuration
MODEL_PERSISTENCE_CONFIG = {
    'bkt_format': 'joblib',
    'pfa_format': 'joblib_dict',
    'dkt_format': 'tensorflow_savedmodel',
    'ensemble_format': 'joblib_dict',
    'compression': True,
    'versioning': True
}

# Example model saving
def save_models(models, output_dir):
    """Save all trained models with appropriate formats"""
    
    # BKT model
    joblib.dump(models['bkt'], f"{output_dir}/bkt_model.joblib")
    
    # PFA model (as dictionary)
    pfa_data = {
        'model': models['pfa'].models,
        'skill_encoders': models['pfa'].skill_encoders,
        'is_fitted': models['pfa'].is_fitted
    }
    joblib.dump(pfa_data, f"{output_dir}/pfa_model.joblib")
    
    # DKT model (TensorFlow format)
    dkt_data = {
        'model_weights': models['dkt'].model.get_weights(),
        'skill_encoders': models['dkt'].skill_encoders,
        'config': models['dkt'].get_config()
    }
    joblib.dump(dkt_data, f"{output_dir}/dkt_model.joblib")
    
    # Ensemble model
    ensemble_data = {
        'weights': models['ensemble'].weights,
        'model_names': models['ensemble'].model_names,
        'ensemble_type': 'weighted_average'
    }
    joblib.dump(ensemble_data, f"{output_dir}/ensemble_model.joblib")
```

---

## A.6 Performance Optimization

### A.6.1 Memory Optimization

```python
# Memory optimization settings
MEMORY_CONFIG = {
    'batch_processing': True,
    'batch_size': 1000,
    'lazy_loading': True,
    'model_caching': True,
    'garbage_collection': True
}
```

### A.6.2 Computational Optimization

```python
# Computational optimization
COMPUTE_CONFIG = {
    'parallel_processing': True,
    'n_jobs': -1,  # Use all available cores
    'gpu_acceleration': True,  # For DKT training
    'mixed_precision': False,  # TensorFlow mixed precision
    'model_quantization': False  # Post-training quantization
}
```
