using math dataset

1- bkt model
2- pfa model
3- dkt model

4- create ensambled model
5- apply hyperparamters tunning
6- check for data mining techniques to apply

generate skills report

demo
- create testing questions for the same skills (2 kills out of the 4 groups, 20 questions per skill)
- show mastery learning after each attempt (answer) per skill
- calculate user profile (session) score throught trained model


----

1- Datasets explore and prepare notebooks

2- process documentation
3- models documentaion 
4- training pipeline documentation
5- dashboard documentation
6- prediction documentation
7- learning mastery analysis in all models
8- ensembling modeling
9- models comparison and evaluation




Final Documents
1- proposal
2- abstract
-- thesis, objectives, methodology, results, conclusion
3- plan
-- models selection
-- dataset selection
-- dataset preprocessing
-- models training
-- models evaluation
-- models comparison
-- models deployment
-- models testing
4- final report
-- models comparison
-- models evaluation
-- models deployment
-- models testing
-- conclusion