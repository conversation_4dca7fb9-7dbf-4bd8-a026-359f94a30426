For master's thesis, we are building a machine learnign project for adaptive learning.
The project is for building an AI-driven adaptive learning platform for turoring that is domain agnostic.
The project is built on top of a math dataset, but the goal is to make it domain agnostic.
The project is built using python and react.js
The project is built using a microservice architecture.
The project is built using docker and docker compose.

Knoweldge Tracing is the process of tracking student knowledge states over time.
Knowledge tracing is the process of tracking student knowledge states over time. It involves modeling the relationship between a student's performance on a set of problems and their underlying knowledge of the concepts being taught. The goal is to estimate a student's knowledge state (e.g., mastery level) for each concept and use this information to adapt the learning experience to the student's needs.

For measuring student knowledge states, we are using three different models:
1. Bayesian Knowledge Tracing (BKT)
2. Performance Factor Analysis (PFA)
3. Deep Knowledge Tracing (DKT)

each model has its own set of parameters and assumptions. 
1. bkt measures probability of correct response based on:
    - Prior success count for each skill
    - Prior failure count for each skill
    - Skill difficulty parameters
    - Student-specific initial knowledge
    - Problem-specific difficulty adjustments
    - Temporal ordering of data
    - Iterative parameter optimization using gradient descent
    - Skill-specific parameter estimation with better convergence
    and provides a probability of correct response for each student-skill pair.

2. pfa works by:
    - Modeling student performance as a function of:
    - Student ability (bias term for each student)
    - Problem difficulty (bias term for each problem)
    - Learning from previous attempts on the same skill (opportunity count × learning rate)
    - Skill difficulty parameters
    - Student-specific initial knowledge
    - Problem-specific difficulty adjustments
    - Temporal ordering of data
    - Iterative parameter optimization using gradient descent
    - Skill-specific parameter estimation with better convergence
    using sigmoid function and provides a probability of correct response for each student-skill pair.

3. dkt works by:
    - Using a LSTM-based neural network to model student performance
    - Modeling student performance as a function of:
    - Student ability (bias term for each student)
    - Problem difficulty (bias term for each problem)
    - Learning from previous attempts on the same skill (opportunity count × learning rate)
    - Skill difficulty parameters
    - Student-specific initial knowledge
    - Problem-specific difficulty adjustments
    - Temporal ordering of data
    - Iterative parameter optimization using gradient descent
    - Skill-specific parameter estimation with better convergence
    and provides a probability of correct response for each student-skill pair.

differences between the three models are in the way they measure the probability of correct response. BKT uses a probabilistic approach, PFA uses a logistic regression approach, and DKT uses a neural network approach.
on the latent knowledge state, BKT and PFA are similar, while DKT is different as it uses a neural network to model the relationship between the student's performance and the latent knowledge state.

The three models are trained on the same dataset and then ensembled together to get a final knowledge state estimate.

The ensembled model is then used to predict the next action for the student. The next action can be one of the following:
1. Provide scaffolding 
2. Provide remediation
3. Provide enrichment
4. Provide assessment
5. Provide feedback

our training pipeline is built using scikit-learn pipeline. the pipeline architecture is as follows:
1. load data
2. explore data
3. preprocess data
4. split data
5. engineer features
6. train models
7. create ensemble
8. save models
9. generate reports

our evaluation methodology is as follows:
1. split data into train and test
2. train models on train data
3. evaluate models on test data
4. compare models performance
5. generate reports

for predication examples on each model diffrent paramters are used as follows:
1. BKT: user history, skill, model_type
2. PFA: user history, skill, user_id
3. DKT: user history, skill, user_id
4. Ensemble: user history, skill, model_type