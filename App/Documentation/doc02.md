For master's thesis, we are building a machine learnign project for adaptive learning.
The project is for building an AI-driven adaptive learning platform for turoring that is domain agnostic.

Task: help building a project submision documentaion draft. goal is to create a final report for the project that is ready for submission to the university.

Analyze details in doc01.md
Analyze other file for more details

Project implmentaion will be provided in python and notebooks for epxeriments and testing.

* for results and evaluation section extract results from attached experiments .html notebook exports

documentation should cover following:

1. introduction
2. related work
3. proposed work
4. experimantal setup
5. results
6. conclusion
7. references

1- process documentation
2- dataset exploration and statistics
3- models documentaion 
4- training pipeline documentation
6- prediction documentation
7- learning mastery analysis in all models
8- ensembling modeling
9- models comparison and evaluation




Final Documents
1- proposal
2- abstract
-- thesis, objectives, methodology, results, conclusion
3- plan
-- models selection
-- dataset selection
-- dataset preprocessing
-- models training
-- models evaluation
-- models comparison
-- models deployment
-- models testing
4- final report
-- models comparison
-- models evaluation
-- models deployment
-- models testing
-- conclusion


