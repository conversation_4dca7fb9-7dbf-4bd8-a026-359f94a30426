from flask import Flask, render_template, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os
from dotenv import load_dotenv
import subprocess
import json

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///kt_dashboard.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# Models
class TrainingSession(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    model_type = db.Column(db.String(50), nullable=False)
    dataset = db.Column(db.String(100), nullable=False)
    validation_method = db.Column(db.String(50), nullable=False)
    parameters = db.Column(db.JSON)
    results = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='pending')

# Routes
@app.route('/')
def dashboard():
    sessions = TrainingSession.query.order_by(TrainingSession.created_at.desc()).all()
    return render_template('dashboard.html', sessions=sessions)

@app.route('/api/training/start', methods=['POST'])
def start_training():
    data = request.json
    session = TrainingSession(
        model_type=data['model_type'],
        dataset=data['dataset'],
        validation_method=data['validation_method'],
        parameters=data['parameters']
    )
    db.session.add(session)
    db.session.commit()
    # Start training process (will implement async training later)
    return jsonify({'status': 'success', 'session_id': session.id})

@app.route('/api/training/status/<int:session_id>')
def training_status(session_id):
    session = TrainingSession.query.get_or_404(session_id)
    return jsonify({
        'status': session.status,
        'results': session.results
    })

@app.route('/api/datasets')
def list_datasets():
    # Will implement dataset listing later
    return jsonify({'datasets': []})

@app.route('/api/datasets/list')
def list_available_datasets():
    """List all available datasets from EduData CLI and compare with local /datasets"""
    # Run 'edudata ls' CLI
    try:
        result = subprocess.run(['edudata', 'ls', '--json'], capture_output=True, text=True, check=True)
        datasets = json.loads(result.stdout)
    except Exception as e:
        return jsonify({'error': f'Failed to list datasets: {str(e)}'}), 500

    # List downloaded datasets in /datasets
    local_files = set(os.listdir('datasets')) if os.path.exists('datasets') else set()
    for ds in datasets:
        ds['downloaded'] = any(ds['id'] in f for f in local_files)
    return jsonify(datasets)

@app.route('/api/datasets/download', methods=['POST'])
def download_dataset():
    """Download a dataset using EduData CLI to /datasets directory"""
    data = request.json
    dataset_id = data.get('id')
    if not dataset_id:
        return jsonify({'error': 'No dataset id provided'}), 400
    try:
        os.makedirs('datasets', exist_ok=True)
        result = subprocess.run(['edudata', 'download', dataset_id, '--output', 'datasets'], capture_output=True, text=True, check=True)
        return jsonify({'status': 'success', 'output': result.stdout})
    except Exception as e:
        return jsonify({'error': f'Failed to download dataset: {str(e)}'}), 500

@app.route('/api/datasets/preview/<dataset_id>')
def preview_dataset(dataset_id):
    """Preview and analyze the dataset from /datasets directory"""
    import pandas as pd
    # Try to find the file in /datasets
    dataset_dir = 'datasets'
    files = [f for f in os.listdir(dataset_dir) if dataset_id in f]
    if not files:
        return jsonify({'error': 'Dataset not downloaded'}), 404
    file_path = os.path.join(dataset_dir, files[0])
    try:
        data = pd.read_csv(file_path)
        analysis = {
            'total_interactions': len(data),
            'unique_students': data['user_id'].nunique() if 'user_id' in data else None,
            'unique_skills': data['skill_id'].nunique() if 'skill_id' in data else None,
            'success_rate': float(data['correct'].mean()) if 'correct' in data else None,
            'sample_rows': data.head(10).to_dict('records')
        }
        return jsonify(analysis)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True) 