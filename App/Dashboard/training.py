from EduKTM import BKT, PFA, DKT
from EduData import get_data
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, KFold, LeaveOneOut
import json
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class KTTrainer:
    def __init__(self):
        self.models = {
            'BKT': BKT,
            'PFA': PFA,
            'DKT': DKT
        }
        self.datasets = {
            'assistments_2009': 'assistments-2009-2010',
            'assistments_2012': 'assistments-2012-2013'
        }
    
    def load_dataset(self, dataset_name):
        """Load and preprocess dataset"""
        try:
            data = get_data(self.datasets[dataset_name])
            return self.preprocess_data(data)
        except Exception as e:
            logger.error(f"Error loading dataset {dataset_name}: {str(e)}")
            raise
    
    def preprocess_data(self, data):
        """Preprocess the data for training"""
        # Basic preprocessing steps
        data = data.sort_values(['user_id', 'timestamp'])
        return data
    
    def get_validation_split(self, data, method, **kwargs):
        """Get train-test split based on validation method"""
        if method == 'split':
            return train_test_split(data, test_size=0.2, random_state=42)
        elif method == 'kfold':
            kf = KFold(n_splits=kwargs.get('n_splits', 5), shuffle=True, random_state=42)
            return next(kf.split(data))
        elif method == 'loo':
            loo = LeaveOneOut()
            return next(loo.split(data))
        else:
            raise ValueError(f"Unknown validation method: {method}")
    
    def train(self, session_id, model_type, dataset_name, validation_method, parameters):
        """Train a model with given parameters"""
        try:
            # Load and prepare data
            data = self.load_dataset(dataset_name)
            train_data, test_data = self.get_validation_split(data, validation_method)
            
            # Initialize model
            model_class = self.models[model_type]
            model = model_class(**parameters)
            
            # Train model
            logger.info(f"Starting training for session {session_id}")
            training_start = datetime.now()
            
            model.train(train_data)
            
            # Evaluate
            predictions = model.eval(test_data)
            metrics = self.calculate_metrics(test_data['correct'].values, predictions['prediction'].values)
            
            training_end = datetime.now()
            
            return {
                'status': 'completed',
                'training_time': str(training_end - training_start),
                'metrics': metrics,
                'model_parameters': model.get_params()
            }
            
        except Exception as e:
            logger.error(f"Error in training session {session_id}: {str(e)}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def calculate_metrics(self, y_true, y_pred):
        """Calculate evaluation metrics"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
        
        return {
            'accuracy': float(accuracy_score(y_true, y_pred > 0.5)),
            'precision': float(precision_score(y_true, y_pred > 0.5)),
            'recall': float(recall_score(y_true, y_pred > 0.5)),
            'f1': float(f1_score(y_true, y_pred > 0.5)),
            'auc': float(roc_auc_score(y_true, y_pred))
        }
    
    def analyze_dataset(self, dataset_name):
        """Generate dataset analysis metrics"""
        data = self.load_dataset(dataset_name)
        
        return {
            'total_interactions': len(data),
            'unique_students': data['user_id'].nunique(),
            'unique_skills': data['skill_id'].nunique(),
            'success_rate': float(data['correct'].mean()),
            'date_range': {
                'start': str(data['timestamp'].min()),
                'end': str(data['timestamp'].max())
            },
            'interactions_per_student': {
                'mean': float(data.groupby('user_id').size().mean()),
                'min': int(data.groupby('user_id').size().min()),
                'max': int(data.groupby('user_id').size().max())
            },
            'skill_difficulty': {
                'mean': float(data.groupby('skill_id')['correct'].mean().mean()),
                'hardest_skill': str(data.groupby('skill_id')['correct'].mean().idxmin()),
                'easiest_skill': str(data.groupby('skill_id')['correct'].mean().idxmax())
            }
        } 