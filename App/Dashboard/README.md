# Knowledge Tracing Model Training Dashboard

A web-based dashboard for training and evaluating Knowledge Tracing (KT) models using various datasets.

## Features

- Train multiple KT models (BKT, PFA, DKT)
- Support for different datasets (ASSISTments)
- Real-time training progress monitoring
- Comprehensive evaluation metrics
- Dataset analysis and reporting
- Model parameter configuration
- Training session management

## Setup

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
# venv\Scripts\activate  # Windows
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Create a `.env` file:
```bash
SECRET_KEY=your_secret_key_here
DATABASE_URL=sqlite:///kt_dashboard.db
```

## Running the Dashboard

1. Initialize the database:
```bash
python
>>> from app import db
>>> db.create_all()
>>> exit()
```

2. Start the Flask server:
```bash
python app.py
```

3. Access the dashboard at `http://localhost:5000`

## Project Structure

```
Dashboard/
├── app.py              # Main Flask application
├── training.py         # KT model training implementation
├── requirements.txt    # Project dependencies
├── static/            # Static files (CSS, JS)
├── templates/         # HTML templates
├── datasets/         # Dataset storage
└── models/           # Trained model storage
```

## Usage

1. Start a new training session using the "Start New Session" button
2. Configure the training parameters:
   - Select KT model
   - Choose dataset
   - Set validation method
   - Adjust model-specific parameters
3. Start training and monitor progress
4. View results and metrics
5. Save or export results as needed

## Supported Models

- **BKT (Bayesian Knowledge Tracing)**
  - Traditional probabilistic model
  - Parameters: prior, transit, emit, guess, slip

- **PFA (Performance Factors Analysis)**
  - Logistic regression based model
  - Parameters: learning rates, difficulty factors

- **DKT (Deep Knowledge Tracing)**
  - LSTM-based deep learning model
  - Parameters: hidden size, num layers, learning rate

## Validation Methods

- Train-Test Split (80/20)
- K-Fold Cross Validation
- Leave One Out Cross Validation

## Dataset Metrics

- Total interactions
- Unique students/skills
- Success rates
- Temporal analysis
- Skill difficulty distribution
- Student performance patterns 