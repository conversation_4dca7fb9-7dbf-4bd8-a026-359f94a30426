<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KT Model Training Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex">
        <!-- Left Sidebar - Training Sessions List -->
        <div class="w-1/5 bg-white shadow-lg p-4">
            <h2 class="text-xl font-bold mb-4">Training Sessions</h2>
            <button class="w-full bg-blue-500 text-white py-2 px-4 rounded mb-4" onclick="startNewSession()">
                Start New Session
            </button>
            <div id="sessions-list" class="space-y-2">
                {% for session in sessions %}
                <div class="p-2 hover:bg-gray-100 cursor-pointer rounded" onclick="loadSession({{ session.id }})">
                    <div class="font-semibold">{{ session.model_type }}</div>
                    <div class="text-sm text-gray-600">{{ session.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    <div class="text-sm text-gray-500">{{ session.status }}</div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 p-6 space-y-6">
            <!-- Training Configuration Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">Training Configuration</h2>
                <form id="training-config-form" class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">KT Model</label>
                            <select name="model_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                <option value="BKT">Bayesian Knowledge Tracing (BKT)</option>
                                <option value="PFA">Performance Factors Analysis (PFA)</option>
                                <option value="DKT">Deep Knowledge Tracing (DKT)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Dataset</label>
                            <select name="dataset" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                <option value="assistments_2009">ASSISTments 2009-2010</option>
                                <option value="assistments_2012">ASSISTments 2012-2013</option>
                            </select>
                            <div class="mt-2">
                                <a href="#" class="text-blue-500 text-sm" onclick="previewDataset()">Preview Dataset</a>
                                <a href="#" class="text-blue-500 text-sm ml-4" onclick="showAvailableDatasets()">Available Datasets</a>
                                <a href="#" class="text-blue-500 text-sm ml-4" onclick="uploadDataset()">Upload New Dataset</a>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Validation Method</label>
                        <select name="validation_method" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="split">Train-Test Split</option>
                            <option value="kfold">K-Fold Cross Validation</option>
                            <option value="loo">Leave One Out</option>
                        </select>
                    </div>
                    <div id="model-parameters" class="space-y-4">
                        <!-- Dynamic parameters based on selected model -->
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" class="bg-gray-200 px-4 py-2 rounded" onclick="saveParameters()">
                            Save Parameters
                        </button>
                        <button type="button" class="bg-blue-500 text-white px-4 py-2 rounded" onclick="startTraining()">
                            Start Training
                        </button>
                    </div>
                </form>
            </div>

            <!-- Training Results Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">Training Results</h2>
                <div class="grid grid-cols-2 gap-4">
                    <div class="border rounded p-4">
                        <h3 class="font-semibold mb-2">Training Console</h3>
                        <pre id="training-console" class="bg-gray-100 p-4 h-64 overflow-auto"></pre>
                    </div>
                    <div class="border rounded p-4">
                        <h3 class="font-semibold mb-2">Evaluation Metrics</h3>
                        <div id="evaluation-metrics" class="space-y-2"></div>
                    </div>
                </div>
            </div>

            <!-- Dataset Reporting Metrics Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">Dataset Reporting Metrics</h2>
                <div id="dataset-metrics" class="grid grid-cols-2 gap-4">
                    <!-- Dataset metrics will be loaded here -->
                </div>
            </div>
        </div>
    </div>


    <div id="datasetModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-4xl">
            <h3 class="text-xl font-bold mb-4">Available Datasets</h3>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="datasetList" class="bg-white divide-y divide-gray-200">
                    <!-- Will be populated by JavaScript -->
                </tbody>
            </table>
            <div class="mt-4 flex justify-end">
                <button onclick="closeModal('datasetModal')" class="px-4 py-2 bg-gray-300 rounded">Close</button>
            </div>
        </div>
    </div>
    
    <div id="previewModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-auto">
            <h3 class="text-xl font-bold mb-4">Dataset Preview</h3>
            <div id="previewContent">
                <!-- Will be populated by JavaScript -->
            </div>
            <div class="mt-4 flex justify-end">
                <button onclick="closeModal('previewModal')" class="px-4 py-2 bg-gray-300 rounded">Close</button>
            </div>
        </div>
    </div>

    <script>
        function startNewSession() {
            // Reset form and show configuration section
            document.getElementById('training-config-form').reset();
            // Additional logic for new session
        }

        function loadSession(sessionId) {
            // Load existing session data
            fetch(`/api/training/status/${sessionId}`)
                .then(response => response.json())
                .then(data => {
                    // Update UI with session data
                });
        }

        function previewDataset() {
            // Show dataset preview modal
        }

        function uploadDataset() {
            // Show dataset upload modal
        }

        function saveParameters() {
            // Save current parameters
        }

        function startTraining() {
            const formData = new FormData(document.getElementById('training-config-form'));
            const data = Object.fromEntries(formData.entries());
            
            fetch('/api/training/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                // Handle training start response
            });
        }
          // Add these functions to the existing script section
    function showAvailableDatasets() {
        fetch('/api/datasets/list')
            .then(response => response.json())
            .then(datasets => {
                const tableBody = document.getElementById('datasetList');
                tableBody.innerHTML = datasets.map(dataset => `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">${dataset.name}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${dataset.size}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            ${dataset.downloaded ? 'Downloaded' : 'Not downloaded'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <button onclick="downloadDataset('${dataset.id}')" 
                                    class="text-blue-500 hover:text-blue-700">
                                Download
                            </button>
                            <button onclick="previewDataset('${dataset.id}')" 
                                    class="ml-4 text-blue-500 hover:text-blue-700">
                                Preview
                            </button>
                        </td>
                    </tr>
                `).join('');
                document.getElementById('datasetModal').classList.remove('hidden');
            });
    }

    function previewDataset(datasetId) {
        fetch(`/api/datasets/preview/${datasetId}`)
            .then(response => response.json())
            .then(data => {
                const previewContent = document.getElementById('previewContent');
                if (data.error) {
                    previewContent.innerHTML = `<p class="text-red-500">Error: ${data.error}</p>`;
                } else {
                    previewContent.innerHTML = `
                        <div class="mb-4">
                            <h4 class="font-semibold">Summary</h4>
                            <p>Total interactions: ${data.total_interactions}</p>
                            <p>Unique students: ${data.unique_students}</p>
                            <p>Unique skills: ${data.unique_skills}</p>
                            <p>Success rate: ${(data.success_rate * 100).toFixed(2)}%</p>
                        </div>
                        <div>
                            <h4 class="font-semibold">Sample Data</h4>
                            <table class="min-w-full divide-y divide-gray-200 mt-2">
                                <thead class="bg-gray-50">
                                    <tr>
                                        ${Object.keys(data.sample_rows[0]).map(key => `
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">${key}</th>
                                        `).join('')}
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    ${data.sample_rows.map(row => `
                                        <tr>
                                            ${Object.values(row).map(val => `
                                                <td class="px-6 py-4 whitespace-nowrap">${val}</td>
                                            `).join('')}
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                }
                document.getElementById('datasetModal').classList.add('hidden');
                document.getElementById('previewModal').classList.remove('hidden');
            });
    }

    function downloadDataset(datasetId) {
        fetch('/api/datasets/download', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id: datasetId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('Download complete!');
                showAvailableDatasets();
            } else {
                alert('Download failed: ' + (data.error || 'Unknown error'));
            }
        });
    }

    function closeModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }

    // Update the existing previewDataset and uploadDataset functions
    function previewDataset() {
        const datasetSelect = document.querySelector('select[name="dataset"]');
        previewDataset(datasetSelect.value);
    }

    function uploadDataset() {
        // Implement upload functionality
        alert('Upload dataset functionality will be implemented here');
    }
    </script>
</body>
</html> 