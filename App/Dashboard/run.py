#!/usr/bin/env python3
import os
import sys
import subprocess
import platform

def run_command(command, shell=True):
    """Run a command and return its output"""
    try:
        result = subprocess.run(command, shell=shell, check=True, capture_output=True, text=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {command}")
        print(f"Error output: {e.stderr}")
        sys.exit(1)

def check_conda_installed():
    """Check if conda is installed and accessible"""
    try:
        run_command("conda --version")
        return True
    except subprocess.CalledProcessError:
        print("Conda is not installed or not in PATH. Please install Conda first.")
        sys.exit(1)

def check_env_exists(env_name):
    """Check if conda environment exists"""
    envs = run_command("conda env list")
    return env_name in envs

def create_conda_env(env_name):
    """Create a new conda environment"""
    print(f"Creating new conda environment: {env_name}")
    python_version = "3.9"  # Updated to Python 3.9 for better package compatibility
    run_command(f"conda create -n {env_name} python={python_version} -y")

def install_requirements(env_name):
    """Install requirements in the conda environment"""
    print("Installing requirements...")
    
    # Get the absolute path to requirements.txt
    script_dir = os.path.dirname(os.path.abspath(__file__))
    requirements_path = os.path.join(script_dir, 'requirements.txt')
    
    # Use conda run instead of activate
    cmd = f"conda run -n {env_name} pip install -r {requirements_path}"
    run_command(cmd)

def run_app(env_name):
    """Run the Flask application"""
    print("Starting the application...")
    
    # Get the absolute path to app.py
    script_dir = os.path.dirname(os.path.abspath(__file__))
    app_path = os.path.join(script_dir, 'app.py')
    
    # Use conda run instead of activate
    cmd = f"conda run -n {env_name} python {app_path}"
    
    # Run the command
    try:
        subprocess.run(cmd, shell=True, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error starting the application: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nApplication stopped by user")
        sys.exit(0)

def main():
    env_name = "ml_panel"
    
    # Check if conda is installed
    check_conda_installed()
    
    # Create environment if it doesn't exist
    if not check_env_exists(env_name):
        create_conda_env(env_name)
        install_requirements(env_name)
    else:
        print(f"Using existing conda environment: {env_name}")
        # Optionally update requirements
        install_requirements(env_name)
    
    # Run the application
    run_app(env_name)

if __name__ == "__main__":
    main() 