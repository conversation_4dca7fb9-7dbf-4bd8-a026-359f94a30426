# Deep Knowledge Tracing (DKT) Model

This module provides an implementation of the Deep Knowledge Tracing (DKT) algorithm for educational data, specifically designed to work with the 2009 Skill Builder dataset.

## Overview

Deep Knowledge Tracing (DKT) is a deep learning approach to knowledge tracing that uses recurrent neural networks (RNNs), specifically Long Short-Term Memory (LSTM) networks, to model student learning over time. Unlike traditional models like BKT and PFA, DKT can capture complex, non-linear learning patterns and long-term dependencies in student behavior.

### Key Features

- **Sequential Learning**: Models student responses as temporal sequences
- **Deep Learning**: Uses LSTM networks to capture complex patterns
- **Non-linear Modeling**: Can learn non-linear relationships between skills and performance
- **Long-term Dependencies**: Captures long-term learning effects and forgetting
- **Skill Interactions**: Can model interactions between different skills
- **Variable-length Sequences**: Handles students with different numbers of interactions

## File Structure

- `train_dkt.py`: Main script for training the DKT model and saving it to disk
- `dkt_demo.ipynb`: Comprehensive Jupyter notebook with training workflow and analysis
- `output/`: Directory where the trained model is saved (`dkt_model.joblib`)
- `example_dkt_usage.py`: Example script for loading the trained model and making predictions
- `compare_kt_models.py`: Script for comparing DKT with BKT and PFA models
- `readme_dkt.md`: This documentation file

## Dataset

The model is designed to work with the [2009 Skill Builder dataset](../../EduData/datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv). The dataset should have at least the following columns:
- `user_id`: Unique identifier for each student
- `problem_id`: Unique identifier for each problem
- `skill_name`: Name of the skill being assessed
- `correct`: 1 if the student answered correctly, 0 otherwise

## Model Architecture

The DKT model consists of:

1. **Embedding Layer**: Converts skill-correctness pairs into dense vectors
2. **LSTM Layers**: Process sequential information and maintain hidden states
3. **Output Layer**: Predicts probability of correct response for each skill
4. **Sigmoid Activation**: Ensures outputs are probabilities between 0 and 1

### Input Encoding

Each student interaction is encoded as:
```
encoded_interaction = skill_id + 1 + (correctness * num_skills)
```

This creates unique identifiers for (skill, correctness) pairs, allowing the model to distinguish between correct and incorrect attempts on each skill.

## Training the Model

### Using the Training Script
```bash
python train_dkt.py
# Choose option 1 for standard training
# Choose option 2 for hyperparameter tuning + training
```

### Using the Jupyter Notebook
```bash
jupyter notebook dkt_demo.ipynb
# Includes comprehensive hyperparameter tuning section
```

### Programmatic Training

#### Standard Training
```python
from train_dkt import DeepKnowledgeTracing, train_and_save_dkt_model

# Train with default configuration
dkt_model = train_and_save_dkt_model(
    data_path="path/to/dataset.csv",
    output_path="output/dkt_model.joblib"
)

# Train with custom configuration
dkt_model = train_and_save_dkt_model(
    data_path="path/to/dataset.csv",
    output_path="output/dkt_model.joblib",
    hidden_dim=256,
    num_layers=3,
    dropout=0.3,
    learning_rate=0.0005,
    batch_size=64,
    max_epochs=50,
    patience=10
)
```

#### Hyperparameter Tuning + Training
```python
from train_dkt import tune_and_train_dkt_model, compare_hyperparameter_results

# Automatic hyperparameter tuning and training
dkt_model, tuning_results = tune_and_train_dkt_model(
    data_path="path/to/dataset.csv",
    output_path="output/dkt_model_tuned.joblib",
    tuning_method='grid_search',  # or 'keras_tuner'
    max_trials=15
)

# Analyze tuning results
top_results = compare_hyperparameter_results(tuning_results, top_n=5)
print(f"Best validation accuracy: {tuning_results['best_score']:.4f}")
print(f"Best parameters: {tuning_results['best_params']}")
```

#### Manual Hyperparameter Tuning
```python
# Initialize model
dkt = DeepKnowledgeTracing()

# Perform hyperparameter tuning
tuning_results = dkt.tune_hyperparameters(
    df,
    method='grid_search',  # or 'keras_tuner'
    max_trials=20,
    tuning_epochs=15
)

# Train with best parameters
final_model = dkt.fit_with_best_params(df, tuning_results['best_params'])
```

## Hyperparameter Tuning

The DKT implementation includes comprehensive hyperparameter optimization capabilities to find the best model configuration for your specific dataset.

### Available Tuning Methods

#### 1. Grid Search (Built-in)
```python
tuning_results = dkt.tune_hyperparameters(
    df,
    method='grid_search',
    max_trials=15,
    tuning_epochs=10
)
```

#### 2. Keras Tuner (Advanced)
```python
# Requires: pip install keras-tuner
tuning_results = dkt.tune_hyperparameters(
    df,
    method='keras_tuner',
    max_trials=20,
    tuning_epochs=15
)
```

### Hyperparameters Optimized

The tuning process automatically searches for optimal values of:

- **hidden_dim** (64, 128, 256): LSTM hidden layer size
- **num_layers** (1, 2, 3): Number of LSTM layers
- **dropout** (0.1, 0.2, 0.3, 0.4, 0.5): Dropout rate for regularization
- **learning_rate** (0.0001, 0.001, 0.01): Adam optimizer learning rate
- **batch_size** (32, 64, 128): Training batch size
- **embedding_dim** (32, 64, 96, 128): Embedding layer dimension

### Tuning Results Analysis

```python
# Get detailed results
results_df = tuning_results['results']
best_params = tuning_results['best_params']
best_score = tuning_results['best_score']

# Analyze parameter importance
top_results = compare_hyperparameter_results(tuning_results, top_n=5)

# View parameter correlations
correlations = results_df.corr()['val_accuracy'].sort_values(ascending=False)
```

### Example Tuning Workflow

```python
# Complete hyperparameter tuning example
from train_dkt import DeepKnowledgeTracing

# 1. Initialize model
dkt = DeepKnowledgeTracing(max_seq_len=100)

# 2. Perform tuning
print("Starting hyperparameter optimization...")
tuning_results = dkt.tune_hyperparameters(
    df_train,
    method='grid_search',
    max_trials=12,
    tuning_epochs=10
)

# 3. Analyze results
print(f"Best accuracy: {tuning_results['best_score']:.4f}")
print(f"Best params: {tuning_results['best_params']}")

# 4. Train final model
final_dkt = DeepKnowledgeTracing()
final_model = final_dkt.fit_with_best_params(
    df_train,
    tuning_results['best_params']
)

# 5. Save optimized model
final_dkt.save("optimized_dkt_model.joblib")
```

## Model Parameters

### Architecture Parameters
- **hidden_dim** (int, default=128): LSTM hidden dimension
- **num_layers** (int, default=2): Number of LSTM layers
- **dropout** (float, default=0.2): Dropout rate for regularization
- **embedding_dim** (int, default=None): Embedding layer size (uses hidden_dim if None)

### Training Parameters
- **learning_rate** (float, default=0.001): Learning rate for Adam optimizer
- **batch_size** (int, default=32): Batch size for training
- **max_epochs** (int, default=50): Maximum number of training epochs
- **patience** (int, default=10): Early stopping patience
- **max_seq_len** (int, default=200): Maximum sequence length

### Tuning Parameters
- **max_trials** (int, default=10): Maximum hyperparameter combinations to test
- **tuning_epochs** (int, default=15): Epochs per trial during tuning

## Making Predictions

### Example 1: Using (problem_id, correct) pairs
```python
from train_dkt import DeepKnowledgeTracing

# Load model
dkt = DeepKnowledgeTracing.load("output/dkt_model.joblib")

# User history as (problem_id, correct) tuples
user_history = [(12345, 1), (12346, 0), (12347, 1)]
skill = 'Addition and Subtraction Integers'
user_id = 'user_123'

# Make predictions
predictions = dkt.predict_proba(user_history, skill, user_id)
print(f"Predicted probabilities: {predictions}")
```

### Example 2: Using correct values only
```python
# User history as correct values only
user_history = [1, 0, 1, 1]
skill = 'Equation Solving Two or Fewer Steps'

# Make predictions
predictions = dkt.predict_proba(user_history, skill)
print(f"Predicted probabilities: {predictions}")
```

### Example 3: Learning progression analysis
```python
# Simulate learning progression
learning_sequence = [0, 0, 1, 0, 1, 1, 1, 1]  # 0=incorrect, 1=correct
skill = 'Fractions'

predictions = dkt.predict_proba(learning_sequence, skill)

print("Learning progression:")
for i, (actual, pred) in enumerate(zip(learning_sequence, predictions)):
    print(f"Step {i+1}: Actual={actual}, Predicted={pred:.3f}")
```

## Model Evaluation

### Performance Metrics
The model can be evaluated using various metrics:
- **Accuracy**: Binary classification accuracy
- **AUC**: Area Under the ROC Curve
- **Log Loss**: Cross-entropy loss
- **Precision/Recall/F1**: Classification metrics

### Comparison with Other Models
```python
from compare_kt_models import KnowledgeTracingComparison

# Compare DKT with BKT and PFA
comparison = KnowledgeTracingComparison("path/to/dataset.csv")
comparison.load_and_prepare_data()
comparison.train_models()
comparison.evaluate_models()
comparison.plot_comparison()
comparison.print_summary()
```

## Model Statistics

```python
# Get comprehensive model statistics
stats = dkt.get_model_statistics()
for key, value in stats.items():
    print(f"{key}: {value}")
```

Example output:
```
model_type: Deep Knowledge Tracing (DKT)
num_skills: 124
hidden_dim: 128
num_layers: 2
total_parameters: 245,632
final_val_accuracy: 0.7234
```

## Advantages and Limitations

### Advantages
- **Complex Pattern Recognition**: Can capture non-linear learning patterns
- **Long-term Memory**: LSTM architecture handles long-term dependencies
- **No Parametric Assumptions**: Learns patterns directly from data
- **Skill Interactions**: Can model interactions between different skills
- **Flexible Architecture**: Can be adapted for different datasets and requirements

### Limitations
- **Computational Complexity**: Requires more computational resources than traditional models
- **Training Time**: Longer training time compared to BKT and PFA
- **Data Requirements**: Needs sufficient sequential data for effective training
- **Interpretability**: Less interpretable than traditional probabilistic models
- **Hyperparameter Sensitivity**: Performance depends on proper hyperparameter tuning

## Requirements

- Python 3.7+
- PyTorch 1.13+
- pandas
- numpy
- scikit-learn
- matplotlib
- seaborn
- joblib

Install dependencies with:
```bash
pip install torch pandas numpy scikit-learn matplotlib seaborn joblib
```

## GPU Support

The model automatically detects and uses GPU if available:
```python
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"Device: {torch.device('cuda' if torch.cuda.is_available() else 'cpu')}")
```

For optimal performance on large datasets, GPU usage is recommended.

## Best Practices

1. **Data Preprocessing**: Ensure temporal ordering of student interactions
2. **Sequence Length**: Adjust `max_seq_len` based on your dataset characteristics
3. **Hyperparameter Tuning**: Experiment with different architectures and learning rates
4. **Early Stopping**: Use validation data to prevent overfitting
5. **Regularization**: Apply dropout and other regularization techniques
6. **Evaluation**: Use proper train/validation/test splits for evaluation

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or sequence length
2. **Slow Training**: Use GPU, reduce model size, or sample data
3. **Poor Performance**: Increase model capacity, adjust learning rate, or add more data
4. **Overfitting**: Increase dropout, add regularization, or reduce model complexity

### Performance Tips

- Use GPU for training when available
- Adjust batch size based on available memory
- Use early stopping to prevent overfitting
- Monitor training curves for convergence
- Experiment with different architectures

## Citation

If you use this DKT implementation in your research, please cite:

```
Piech, C., Bassen, J., Huang, J., Ganguli, S., Sahami, M., Guibas, L. J., & Sohl-Dickstein, J. (2015). 
Deep knowledge tracing. In Advances in neural information processing systems (pp. 505-513).
```
