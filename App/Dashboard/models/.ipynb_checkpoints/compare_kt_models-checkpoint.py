import os
import pandas as pd
import numpy as np
import time
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, roc_auc_score, log_loss, precision_recall_fscore_support
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# Import all three models
from train_bkt import BayesianKnowledgeTracer, load_skill_builder_data
from train_pfa import PerformanceFactorsAnalysis
from train_dkt import DeepKnowledgeTracing


class KnowledgeTracingComparison:
    """
    Comprehensive comparison of BKT, PFA, and DKT models
    """
    
    def __init__(self, data_path):
        self.data_path = data_path
        self.df = None
        self.train_data = None
        self.test_data = None
        self.models = {}
        self.results = {}
        
    def load_and_prepare_data(self, test_size=0.2, random_state=42):
        """Load and prepare data for comparison"""
        print("Loading and preparing data...")
        
        # Load data
        self.df = load_skill_builder_data(self.data_path)
        
        # Clean data
        essential_cols = ['user_id', 'problem_id', 'skill_name', 'correct']
        df_clean = self.df.dropna(subset=essential_cols).copy()
        df_clean['correct'] = df_clean['correct'].astype(int)
        df_clean['user_id'] = df_clean['user_id'].astype(str)
        
        print(f"Dataset size: {len(df_clean):,} interactions")
        print(f"Unique students: {df_clean['user_id'].nunique():,}")
        print(f"Unique skills: {df_clean['skill_name'].nunique():,}")
        print(f"Overall accuracy: {df_clean['correct'].mean():.3f}")
        
        # Split data by students to avoid data leakage
        unique_users = df_clean['user_id'].unique()
        train_users, test_users = train_test_split(
            unique_users, test_size=test_size, random_state=random_state
        )
        
        self.train_data = df_clean[df_clean['user_id'].isin(train_users)].copy()
        self.test_data = df_clean[df_clean['user_id'].isin(test_users)].copy()
        
        print(f"Training data: {len(self.train_data):,} interactions ({len(train_users):,} students)")
        print(f"Test data: {len(self.test_data):,} interactions ({len(test_users):,} students)")
        
        return self
    
    def train_models(self):
        """Train all three models"""
        print("\n" + "="*60)
        print("TRAINING KNOWLEDGE TRACING MODELS")
        print("="*60)
        
        # 1. Train BKT Model
        print("\n1. Training BKT Model...")
        start_time = time.time()
        
        bkt_model = BayesianKnowledgeTracer(
            learning_rate=0.01,
            max_iterations=50,
            convergence_threshold=1e-6
        )
        bkt_model.fit(self.train_data)
        
        bkt_time = time.time() - start_time
        self.models['BKT'] = bkt_model
        
        print(f"BKT training completed in {bkt_time:.2f} seconds")
        
        # 2. Train PFA Model
        print("\n2. Training PFA Model...")
        start_time = time.time()
        
        pfa_model = PerformanceFactorsAnalysis(
            learning_rate=0.01,
            regularization=0.001,
            max_epochs=15,
            use_adaptive_lr=True,
            early_stopping_patience=5
        )
        pfa_model.fit(self.train_data, validation_split=0.2)
        
        pfa_time = time.time() - start_time
        self.models['PFA'] = pfa_model
        
        print(f"PFA training completed in {pfa_time:.2f} seconds")
        
        # 3. Train DKT Model
        print("\n3. Training DKT Model...")
        start_time = time.time()
        
        dkt_model = DeepKnowledgeTracing(
            hidden_dim=64,      # Smaller for faster training
            num_layers=2,
            dropout=0.2,
            learning_rate=0.001,
            batch_size=64,
            max_epochs=20,      # Fewer epochs for comparison
            patience=5,
            max_seq_len=50      # Shorter sequences for faster training
        )
        dkt_model.fit(self.train_data, validation_split=0.2)
        
        dkt_time = time.time() - start_time
        self.models['DKT'] = dkt_model
        
        print(f"DKT training completed in {dkt_time:.2f} seconds")
        
        # Store training times
        self.results['training_times'] = {
            'BKT': bkt_time,
            'PFA': pfa_time,
            'DKT': dkt_time
        }
        
        print(f"\n=== TRAINING TIME COMPARISON ===")
        for model_name, train_time in self.results['training_times'].items():
            print(f"{model_name}: {train_time:.2f} seconds ({train_time/60:.1f} minutes)")
        
        return self
    
    def evaluate_models(self, sample_size=5000):
        """Evaluate all models on test data"""
        print(f"\n" + "="*60)
        print("EVALUATING MODELS ON TEST DATA")
        print("="*60)
        
        # Sample test data for faster evaluation
        if len(self.test_data) > sample_size:
            test_sample = self.test_data.sample(n=sample_size, random_state=42)
            print(f"Using sample of {sample_size:,} test interactions for evaluation")
        else:
            test_sample = self.test_data
            print(f"Using all {len(test_sample):,} test interactions for evaluation")
        
        evaluation_results = {}
        
        for model_name, model in self.models.items():
            print(f"\nEvaluating {model_name} model...")
            start_time = time.time()
            
            predictions = []
            actuals = []
            
            # Group by user and skill for sequential prediction
            user_skill_groups = test_sample.groupby(['user_id', 'skill_name'])
            
            for (user_id, skill), group in user_skill_groups:
                if len(group) < 2:  # Need at least 2 interactions for prediction
                    continue
                
                # Sort by order if available
                if 'order_id' in group.columns:
                    group = group.sort_values('order_id')
                
                group_data = group.reset_index(drop=True)
                
                # Create history and predict each step
                for i in range(1, len(group_data)):
                    # History up to current point
                    history_data = group_data.iloc[:i]
                    current_actual = group_data.iloc[i]['correct']
                    
                    try:
                        if model_name == 'BKT':
                            # BKT expects list of correct values
                            history = history_data['correct'].tolist()
                            pred_probs = model.predict_proba(history, skill)
                            prediction = pred_probs[-1] if pred_probs else 0.5
                            
                        elif model_name == 'PFA':
                            # PFA expects (problem_id, correct) pairs
                            history = list(zip(history_data['problem_id'], history_data['correct']))
                            pred_probs = model.predict_proba(history, skill, user_id)
                            prediction = pred_probs[-1] if pred_probs else 0.5
                            
                        elif model_name == 'DKT':
                            # DKT expects (problem_id, correct) pairs
                            history = list(zip(history_data['problem_id'], history_data['correct']))
                            pred_probs = model.predict_proba(history, skill, user_id)
                            prediction = pred_probs[-1] if pred_probs else 0.5
                        
                        predictions.append(prediction)
                        actuals.append(current_actual)
                        
                    except Exception as e:
                        # Skip problematic predictions
                        continue
            
            eval_time = time.time() - start_time
            
            if len(predictions) > 0:
                # Calculate metrics
                predictions = np.array(predictions)
                actuals = np.array(actuals)
                
                # Clip predictions to avoid log(0) errors
                predictions_clipped = np.clip(predictions, 1e-8, 1 - 1e-8)
                
                accuracy = accuracy_score(actuals, predictions > 0.5)
                try:
                    auc = roc_auc_score(actuals, predictions)
                except:
                    auc = 0.5  # Default if AUC calculation fails
                
                try:
                    logloss = log_loss(actuals, predictions_clipped)
                except:
                    logloss = float('inf')
                
                precision, recall, f1, _ = precision_recall_fscore_support(
                    actuals, predictions > 0.5, average='binary', zero_division=0
                )
                
                evaluation_results[model_name] = {
                    'accuracy': accuracy,
                    'auc': auc,
                    'logloss': logloss,
                    'precision': precision,
                    'recall': recall,
                    'f1': f1,
                    'num_predictions': len(predictions),
                    'eval_time': eval_time
                }
                
                print(f"{model_name} evaluation completed:")
                print(f"  Predictions made: {len(predictions):,}")
                print(f"  Accuracy: {accuracy:.4f}")
                print(f"  AUC: {auc:.4f}")
                print(f"  Log Loss: {logloss:.4f}")
                print(f"  F1 Score: {f1:.4f}")
                print(f"  Evaluation time: {eval_time:.2f} seconds")
            else:
                print(f"No valid predictions for {model_name}")
                evaluation_results[model_name] = {
                    'accuracy': 0, 'auc': 0.5, 'logloss': float('inf'),
                    'precision': 0, 'recall': 0, 'f1': 0,
                    'num_predictions': 0, 'eval_time': eval_time
                }
        
        self.results['evaluation'] = evaluation_results
        return self
    
    def plot_comparison(self):
        """Plot comparison results"""
        if 'evaluation' not in self.results:
            print("No evaluation results to plot. Run evaluate_models() first.")
            return
        
        # Prepare data for plotting
        models = list(self.results['evaluation'].keys())
        metrics = ['accuracy', 'auc', 'f1', 'precision', 'recall']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        # Plot each metric
        for i, metric in enumerate(metrics):
            values = [self.results['evaluation'][model][metric] for model in models]
            
            bars = axes[i].bar(models, values, alpha=0.7, 
                              color=['skyblue', 'lightcoral', 'lightgreen'])
            axes[i].set_title(f'{metric.upper()}', fontsize=14, fontweight='bold')
            axes[i].set_ylabel(metric.capitalize())
            axes[i].set_ylim(0, 1)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # Plot training times
        train_times = [self.results['training_times'][model] for model in models]
        bars = axes[5].bar(models, train_times, alpha=0.7, 
                          color=['skyblue', 'lightcoral', 'lightgreen'])
        axes[5].set_title('TRAINING TIME (seconds)', fontsize=14, fontweight='bold')
        axes[5].set_ylabel('Time (seconds)')
        
        # Add value labels on bars
        for bar, value in zip(bars, train_times):
            axes[5].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(train_times)*0.01,
                       f'{value:.1f}s', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.suptitle('Knowledge Tracing Models Comparison', fontsize=16, fontweight='bold', y=1.02)
        plt.show()
        
        return self
    
    def print_summary(self):
        """Print comprehensive comparison summary"""
        print("\n" + "="*80)
        print("KNOWLEDGE TRACING MODELS COMPARISON SUMMARY")
        print("="*80)
        
        if 'evaluation' in self.results:
            # Create comparison table
            df_results = pd.DataFrame(self.results['evaluation']).T
            df_results['training_time'] = [self.results['training_times'][model] 
                                         for model in df_results.index]
            
            print("\n📊 PERFORMANCE METRICS:")
            print(df_results[['accuracy', 'auc', 'f1', 'precision', 'recall']].round(4))
            
            print("\n⏱️  COMPUTATIONAL EFFICIENCY:")
            print(df_results[['training_time', 'eval_time', 'num_predictions']].round(2))
            
            # Find best performing model for each metric
            print("\n🏆 BEST PERFORMING MODELS:")
            for metric in ['accuracy', 'auc', 'f1']:
                best_model = df_results[metric].idxmax()
                best_value = df_results[metric].max()
                print(f"  {metric.upper()}: {best_model} ({best_value:.4f})")
            
            # Model characteristics
            print("\n🔍 MODEL CHARACTERISTICS:")
            print("  BKT: Probabilistic, Markovian, interpretable parameters")
            print("  PFA: Logistic regression, linear learning, fast training")
            print("  DKT: Deep learning, non-linear, captures complex patterns")
            
            print("\n💡 RECOMMENDATIONS:")
            best_accuracy = df_results['accuracy'].idxmax()
            fastest_training = df_results['training_time'].idxmin()
            
            print(f"  • For highest accuracy: Use {best_accuracy}")
            print(f"  • For fastest training: Use {fastest_training}")
            print(f"  • For interpretability: Use BKT")
            print(f"  • For complex patterns: Use DKT")
            print(f"  • For balanced performance: Use PFA")
        
        return self


def main():
    """Main comparison function"""
    print("="*80)
    print("COMPREHENSIVE KNOWLEDGE TRACING MODELS COMPARISON")
    print("BKT vs PFA vs DKT")
    print("="*80)
    
    # Initialize comparison
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    
    comparison = KnowledgeTracingComparison(DATA_PATH)
    
    # Run complete comparison
    comparison.load_and_prepare_data(test_size=0.2)
    comparison.train_models()
    comparison.evaluate_models(sample_size=3000)  # Smaller sample for faster evaluation
    comparison.plot_comparison()
    comparison.print_summary()
    
    print(f"\n✅ Comparison completed successfully!")
    print(f"📈 All three models trained and evaluated")
    print(f"📊 Results visualized and summarized")


if __name__ == "__main__":
    main()
