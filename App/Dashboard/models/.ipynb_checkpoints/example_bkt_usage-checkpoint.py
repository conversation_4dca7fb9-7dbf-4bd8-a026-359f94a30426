import os
from train_bkt import BayesianKnowledgeTracer

MODEL_PATH = os.path.join("output", "bkt_model.joblib")

# Example user history: 1=correct, 0=incorrect
user_history = [1, 0, 1, 1]
skill = 'Addition'  # Replace with a skill present in your dataset

if os.path.exists(MODEL_PATH):
    bkt = BayesianKnowledgeTracer.load(MODEL_PATH)
    probas = bkt.predict_proba(user_history, skill)
    print(f"Predicted probabilities for user history {user_history} on skill '{skill}': {probas}")
else:
    print(f"Trained model not found at {MODEL_PATH}. Please train the model first.") 