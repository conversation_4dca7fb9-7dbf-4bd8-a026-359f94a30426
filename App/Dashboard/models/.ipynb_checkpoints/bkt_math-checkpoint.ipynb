{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Bayesian Knowledge Tracing (BKT) Model Training\n", "\n", "## ⚠️ **IMPORTANT: To see skill-specific parameters, please:**\n", "1. **<PERSON><PERSON>** (Kernel → Restart)\n", "2. **Run All Cells** (Cell → Run All)\n", "\n", "This ensures you get the latest BKT model with unique parameters for each skill!\n", "\n", "---\n", "\n", "This notebook demonstrates the complete workflow for training a BKT model with **skill-specific parameter estimation**:\n", "1. Load and explore the dataset\n", "2. Clean and preprocess the data\n", "3. Explore data statistics and skills\n", "4. Split data into train/test sets\n", "5. **Train the BKT model with skill-specific parameters**\n", "6. **Analyze parameter distributions and skill characteristics**\n", "7. Save the trained model\n", "8. Show training statistics\n", "9. Run prediction examples"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, roc_auc_score, classification_report\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BKT model imported successfully!\n", "✅ Using updated BKT model with skill-specific parameter estimation\n"]}], "source": ["# Import BKT model from the existing implementation\n", "import sys\n", "import os\n", "sys.path.append('App/Dashboard/models')\n", "\n", "# Force reload the module to get latest changes\n", "import importlib\n", "if 'train_bkt' in sys.modules:\n", "    importlib.reload(sys.modules['train_bkt'])\n", "\n", "from train_bkt import BayesianKnowledgeTracer, load_skill_builder_data\n", "from joblib import dump, load\n", "\n", "print(\"BKT model imported successfully!\")\n", "print(\"✅ Using updated BKT model with skill-specific parameter estimation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Dataset\n", "\n", "We'll load the 2009 Skill Builder dataset which contains student responses to educational problems."]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset...\n", "Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the dataset\n", "DATA_PATH = \"../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading dataset...\")\n", "df = load_skill_builder_data(DATA_PATH)\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 5 rows of the dataset:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>assignment_id</th>\n", "      <th>user_id</th>\n", "      <th>assistment_id</th>\n", "      <th>problem_id</th>\n", "      <th>original</th>\n", "      <th>correct</th>\n", "      <th>attempt_count</th>\n", "      <th>ms_first_response</th>\n", "      <th>tutor_mode</th>\n", "      <th>...</th>\n", "      <th>hint_count</th>\n", "      <th>hint_total</th>\n", "      <th>overlap_time</th>\n", "      <th>template_id</th>\n", "      <th>answer_id</th>\n", "      <th>answer_text</th>\n", "      <th>first_action</th>\n", "      <th>bottom_hint</th>\n", "      <th>opportunity</th>\n", "      <th>opportunity_original</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33022537</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33139</td>\n", "      <td>51424</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>32454</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>32454</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33022709</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33150</td>\n", "      <td>51435</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4922</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4922</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35450204</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33159</td>\n", "      <td>51444</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>25390</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>42000</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>88</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>35450295</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33110</td>\n", "      <td>51395</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4859</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4859</td>\n", "      <td>30059</td>\n", "      <td>NaN</td>\n", "      <td>41</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>35450311</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33196</td>\n", "      <td>51481</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>14</td>\n", "      <td>19813</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>124564</td>\n", "      <td>30060</td>\n", "      <td>NaN</td>\n", "      <td>65</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 30 columns</p>\n", "</div>"], "text/plain": ["   order_id  assignment_id  user_id  assistment_id  problem_id  original  \\\n", "0  33022537         277618    64525          33139       51424         1   \n", "1  33022709         277618    64525          33150       51435         1   \n", "2  35450204         220674    70363          33159       51444         1   \n", "3  35450295         220674    70363          33110       51395         1   \n", "4  35450311         220674    70363          33196       51481         1   \n", "\n", "   correct  attempt_count  ms_first_response tutor_mode  ... hint_count  \\\n", "0        1              1              32454      tutor  ...          0   \n", "1        1              1               4922      tutor  ...          0   \n", "2        0              2              25390      tutor  ...          0   \n", "3        1              1               4859      tutor  ...          0   \n", "4        0             14              19813      tutor  ...          3   \n", "\n", "   hint_total  overlap_time  template_id answer_id  answer_text  first_action  \\\n", "0           3         32454        30799       NaN           26             0   \n", "1           3          4922        30799       NaN           55             0   \n", "2           3         42000        30799       NaN           88             0   \n", "3           3          4859        30059       NaN           41             0   \n", "4           4        124564        30060       NaN           65             0   \n", "\n", "  bottom_hint  opportunity  opportunity_original  \n", "0         NaN            1                   1.0  \n", "1         NaN            2                   2.0  \n", "2         NaN            1                   1.0  \n", "3         NaN            2                   2.0  \n", "4         0.0            3                   3.0  \n", "\n", "[5 rows x 30 columns]"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display first few rows\n", "print(\"First 5 rows of the dataset:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Info:\n", "Total records: 401,756\n", "Memory usage: 200.29 MB\n", "\n", "Data types:\n", "order_id                  int64\n", "assignment_id             int64\n", "user_id                   int64\n", "assistment_id             int64\n", "problem_id                int64\n", "original                  int64\n", "correct                   int64\n", "attempt_count             int64\n", "ms_first_response         int64\n", "tutor_mode               object\n", "answer_type              object\n", "sequence_id               int64\n", "student_class_id          int64\n", "position                  int64\n", "type                     object\n", "base_sequence_id          int64\n", "skill_id                float64\n", "skill_name               object\n", "teacher_id                int64\n", "school_id                 int64\n", "hint_count                int64\n", "hint_total                int64\n", "overlap_time              int64\n", "template_id               int64\n", "answer_id               float64\n", "answer_text              object\n", "first_action              int64\n", "bottom_hint             float64\n", "opportunity               int64\n", "opportunity_original    float64\n", "dtype: object\n", "\n", "Missing values:\n", "order_id                     0\n", "assignment_id                0\n", "user_id                      0\n", "assistment_id                0\n", "problem_id                   0\n", "original                     0\n", "correct                      0\n", "attempt_count                0\n", "ms_first_response            0\n", "tutor_mode                   0\n", "answer_type                  0\n", "sequence_id                  0\n", "student_class_id             0\n", "position                     0\n", "type                         0\n", "base_sequence_id             0\n", "skill_id                 63755\n", "skill_name               76119\n", "teacher_id                   0\n", "school_id                    0\n", "hint_count                   0\n", "hint_total                   0\n", "overlap_time                 0\n", "template_id                  0\n", "answer_id               356302\n", "answer_text              89208\n", "first_action                 0\n", "bottom_hint             334712\n", "opportunity                  0\n", "opportunity_original     73465\n", "dtype: int64\n"]}], "source": ["# Basic dataset information\n", "print(\"Dataset Info:\")\n", "print(f\"Total records: {len(df):,}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "print(\"\\nData types:\")\n", "print(df.dtypes)\n", "print(\"\\nMissing values:\")\n", "print(df.isnull().sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Clean Dataset\n", "\n", "Clean the data by handling missing values, duplicates, and ensuring proper data types."]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df.columns:\n", "        missing = df[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df.columns]\n", "df_clean = df.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Explore Dataset\n", "\n", "Analyze the dataset to understand the distribution of skills, users, and performance."]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Statistics:\n", "Total interactions: 325,637\n", "Unique users: 4,151\n", "Unique problems: 16,891\n", "Unique skills: 110\n", "Overall accuracy: 0.658\n"]}], "source": ["# Basic statistics\n", "print(\"Dataset Statistics:\")\n", "print(f\"Total interactions: {len(df_clean):,}\")\n", "print(f\"Unique users: {df_clean['user_id'].nunique():,}\")\n", "print(f\"Unique problems: {df_clean['problem_id'].nunique():,}\")\n", "print(f\"Unique skills: {df_clean['skill_name'].nunique():,}\")\n", "print(f\"Overall accuracy: {df_clean['correct'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SKILLS ANALYSIS ===\n", "\n", "Top 15 skills by number of attempts:\n", "                                          Total_Attempts  Accuracy  \\\n", "skill_name                                                           \n", "Equation Solving Two or Fewer Steps                24253     0.679   \n", "Conversion of Fraction Decimals Percents           18742     0.637   \n", "Addition and Subtraction Integers                  12741     0.599   \n", "Addition and Subtraction Fractions                 11334     0.677   \n", "Percent Of                                          9497     0.595   \n", "Proportion                                          9054     0.641   \n", "Ordering Fractions                                  8539     0.792   \n", "Equation Solving More Than Two Steps                8115     0.758   \n", "Probability of Two Distinct Events                  7963     0.490   \n", "Finding Percents                                    7694     0.538   \n", "Subtraction Whole Numbers                           7669     0.641   \n", "Probability of a Single Event                       7438     0.742   \n", "Pattern Finding                                     7343     0.600   \n", "Absolute Value                                      7340     0.757   \n", "Ordering Positive Decimals                          7317     0.750   \n", "\n", "                                          Unique_Users  Unique_Problems  \n", "skill_name                                                               \n", "Equation Solving Two or Fewer Steps                961             1040  \n", "Conversion of Fraction Decimals Percents          1225              488  \n", "Addition and Subtraction Integers                 1226              413  \n", "Addition and Subtraction Fractions                1353              433  \n", "Percent Of                                        1115              465  \n", "Proportion                                         756              485  \n", "Ordering Fractions                                 882              464  \n", "Equation Solving More Than Two Steps               412              419  \n", "Probability of Two Distinct Events                 452              339  \n", "Finding Percents                                   771              371  \n", "Subtraction Whole Numbers                          903              242  \n", "Probability of a Single Event                      939              350  \n", "Pattern Finding                                    447              554  \n", "Absolute Value                                    1002              241  \n", "Ordering Positive Decimals                         942              543  \n"]}], "source": ["# Skills list and statistics\n", "print(\"\\n=== SKILLS ANALYSIS ===\")\n", "skill_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skill_stats.columns = ['Total_Attempts', 'Accuracy', 'Unique_Users', 'Unique_Problems']\n", "skill_stats = skill_stats.sort_values('Total_Attempts', ascending=False)\n", "\n", "print(f\"\\nTop 15 skills by number of attempts:\")\n", "print(skill_stats.head(15))"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAASmCAYAAADBBeLHAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3yN9///8cc5WdIgqJkaNSMqQUiJEa2iaGvWFi1F0aJW7CI2NT5ilRoVo4rUnh/6MVoNakTN2qGtGpFB9snvD7+crzQ5kdTI8LzfbrnJucb7/bqu1zntldd5X+/LkJCQkICIiIiIiIiIiIiIiCRjzOgAREREREREREREREQyKxXRRUREREREREREREQsUBFdRERERERERERERMQCFdFFRERERERERERERCxQEV1ERERERERERERExAIV0UVERERERERERERELFARXURERERERERERETEAhXRRUREREREREREREQsUBFdRERERERERERERMQCFdFFRCRLGDp0KM7Ozqn+eHt7P9cY/vjjDwYNGkTt2rWpUqUKLVu2ZOfOnUm2iYmJYcaMGbz11ltUrFiR9957j4CAgCe2HRcXx5o1a2jTpg01atTA1dWVunXrMmDAAM6fP2/eLjAwEGdnZ/bv32+xrXr16tG/f38Abty4gbOzM6tXrwYgICAAZ2dnLl269G9OAfAoF/Xq1fvX+6fFu+++i7OzMz/++ONz7UdERESyp39eO1asWJG33nqLfv36cejQoWTbP3799Cz5+fnh7OxMdHQ0AN7e3s/tmnXo0KHUqlXrubT9b+3atYu6devi5ubGsWPHLG73008/0b17d+rWrUvFihWpWbMmH3/8Mf/973+TbOfs7MxXX31lsZ1/noPUrotT8+uvv+Ls7EytWrWIi4t74vYikv2piC4iIlnCiBEjOHjwoPnnnXfeoXDhwkmW+fn5Pbf+Q0ND8fb25saNG/j5+bF+/XqqVq1K3759OXjwoHm7cePG8f333/Pll1+yY8cO2rVrx4gRI9i6dWuq7Y8ePZrJkyfTrFkzVqxYwfbt2xk/fjzBwcF06NCB4ODgNMe6bt06fH19//WxZrTDhw9z9epVXFxcWL9+fYrbjBo1KskfUHFxcVSuXPmpvhx41jJjTCIiIi+TfPnyma8Td+7cyeTJk8mdOzddu3Zl4sSJSbZN7/XTsWPH0lSw7tq1KwcPHsTOzi7d8ael7ccLwiNGjGDz5s3PvJ+n4efnh6OjI1u2bKFChQopbrNx40a6du1KyZIlmTdvHrt27WL+/PkUKFCAzz//nB07dqS5v2d1DtatW0f58uW5d+8e+/bte+r2RCTrs87oAERERNIiV65c5MqVy/zazs4OKysrChQo8EL637ZtGzdu3GD58uW89tprAAwfPpw9e/bwww8/ULt2bf744w/zH2CJI7W9vb05ceIEs2fP5r333kux7QcPHvDDDz/Qo0cPOnbsaF5etGhR3njjDT7++GNOnjxJsWLF0hRrvnz5nvJoM9batWupXLky7du3Z+TIkdy7dy/ZMZ04cYK6deuaX587d47IyMgXHWqqMmNMIiIiLxOj0ZjkWvG1116jRo0aVKtWDR8fH8qWLUvr1q2B9F8/nThxItX1cXFxWFlZ4eDggIODQ7pjfxKTycTJkydp0KCBednj18qZRUhICLVq1aJ48eIWt1mxYgVVq1Zl+PDh5mVOTk5UqlSJ0NBQgoKCaNSoUZr6exbnICIigh07djBkyBC2bdvG+vXreeedd5663WchNjYWGxubjA5D5KWkkegiIpKt/Pjjj7Rp0wY3NzcqV65Mhw4d+OWXX8zrDx48iLOzMwcPHuTzzz+nSpUqVK1alaFDh6Za8GzVqhX79+83F9ABDAYD+fLl4++//wYe3YZqMpnw8vJKsq+XlxdXr161OJrcZDIRHx9vvs33cfny5WPTpk28//77FmPbu3cvFSpUYNWqVUD6bkcODQ1lxIgR1KlTh4oVK1K3bl3Gjx9PVFTUE/f96aef+OCDD6hYsSL16tVj3bp15nicnZ0JDAxMsn1MTAzVqlVj8uTJFtsMDw9n165dNGvWjAYNGmBjY8PGjRuTbOPs7MyFCxdYtGgRzs7OBAQE0KpVKwCaNGmS5BbpFStW0LhxY/NtwV9++SXh4eHm9QMHDqRNmzb873//491338XV1ZXmzZtz9uxZDh8+TNOmTalUqRItW7bk3Llz5v3atWtHz5492bhxIw0aNDBP3ZM4UslSTOfOnaN79+7UqFEDNzc3mjRpgr+//xPPtYiIiDw7zZo1o1atWixcuNC87J/XT7t376ZVq1a4u7vj7u5Ou3bt+Pnnn4FHU4ZMmTKFO3fu4OzsjJ+fn3mqkLVr19KuXTvc3NwIDw9PNp1Los2bN9OwYUMqVqxI48aNk0xhl9I+0dHRSfpycXEhIiKCMWPG4OzsbI7r8dHxCQkJfPPNN7z77rtUrFiR6tWr07dv3yTXpDNnzqRatWpcvHiRDh06UKlSJby8vJgzZ84Tz2Nq192J5+P27dts2LAhxWvDRLGxscTExJCQkJBs3cKFC/Hx8bEYQ3BwMLVq1WLAgAGYTKZnMqXNli1biIuLo1GjRjRr1ox9+/Zx586dZNsdO3aMjh07UqlSJTw9PRk8eHCS7cLDwxk1ahSenp5UrlyZNm3a8NNPP5nXe3t706ZNmyRt7t+/P8m58vPzo1q1auzZs4fatWvzxRdfABAZGcn48eOpV68erq6ueHl5MXz4cEJCQtIU471793B1dU3xTt5PPvmEli1b/uvzJ5JdqYguIiLZxqFDh+jVqxflypXj+++/Z9WqVRQsWJBu3bqZC6DW1o9uwho/fjzvvvsumzZtYuTIkWzdupWpU6dabNvW1pZChQolWXbjxg3OnTtHlSpVALh8+XKK2yWOIL98+XKKbefKlQt3d3eWLl3KuHHjCAoKIj4+Pk3HfPLkSQYMGMBnn31Ghw4d0rTP48aPH09QUBCzZ89m165djB8/nj179jBp0qRU9wsLC2PevHmMHj2aDRs2UK1aNUaOHMnJkyd56623cHJySjYX/P79+wkPDzeP+ErJpk2bAHj//fdxcHCgUaNGyaZ0SZw+p1OnThw8eJBGjRqZRy6tXLnS/MfA119/zYQJE2jWrBmbN29m0qRJ7N+/n88++8zclo2NDX///TerVq3iP//5D8uXL+fOnTv4+Pgwb948Jk6ciL+/P3fu3Ely27e1tTWnT59m586d+Pn5sW7dOgoXLkyfPn34888/adKkSYox9ezZk5w5c+Lv78/WrVvp0qULU6ZMYdu2bamebxEREXm23n77ba5fv86ff/6ZbN2VK1f44osvePfdd9m4cSNr167F1dWVHj168OeffzJixAgaN25sni6ma9eu5n2XLl3Khx9+yM6dO8mZM2eKfV+5coUNGzbw1VdfsW7dOpycnOjbt2+KsaSkSJEirF27FoBBgwYlmVrwcX5+fsyaNYv27duzefNmZs+ezZUrV/joo4/Mg0esra2Ji4tj7Nix9O7dm61bt9KkSRP8/Pw4fPiwxRiedN1dpEgRDh48SL58+WjcuDEHDx40XzP/U926dTl16hTdunVj//79ab6TLyQkhG7duuHi4sKUKVMwGp9NiWvt2rU0bNiQPHny0LhxY+zs7JIN6rh06RIff/wxJUuWZN26dSxYsIDz58/To0cP85cBvXv35vDhw8yYMYONGzdSuXJlPv30U06dOpWueOLj41mxYgULFixgzJgxwKNr+I0bNzJmzBh27tzJ9OnT+eWXX/jyyy/TFGPevHlp2LAhP/zwQ5IvL0JCQvjll1/48MMP/+XZE8m+VEQXEZFs45tvvqFo0aKMGzeO8uXLU6FCBSZPnoy9vb15tK/BYAAeXax/8MEHFCtWjBYtWvDBBx+wefPmFEfApCQqKopBgwbh6OjIxx9/DDwabZLS7bqJf0CFhYVZbG/GjBl4eHiwYsUKWrdujYeHB926dWP16tUW/5C4evUqn376Ka1bt05SGE6P06dP4+7uTpUqVXBycqJOnTp8++23dOnSJdX9wsPDGTZsGNWqVaNMmTKMGzeOHDlysHnzZoxGo/mPx4iICPM+27dvp0qVKpQuXdpiu+vWraNhw4bkzp0bgA8//JDff/+doKAg8zaJt2Xb29tToEABXnnlFfOtu3nz5iVPnjzExsayaNEimjRpQs+ePSlZsiR169ZlxIgRBAYGcvLkSXN7f/75J6NHj6Z8+fJUqVKFBg0acOHCBfr160fFihVxc3Ojfv36nD171ryPwWDg3r17TJgwgfLly1O+fHnGjx9PdHQ0O3fuJEeOHMliunv3Ln/++ScNGjSgbNmyFCtWjNatW7N27Vo8PDzSmjIRERF5BooUKQJgvqPwcWfPniUuLo6WLVtSrFgxSpcuzbBhw/D39yd37tzkypWLHDlymKeLefz6r0yZMnz44YcUK1bMYlE3JCSEKVOm4ObmRvny5Rk3bhwxMTHJHlhviZWVlXn6mZw5c6Y4vWFMTAzLli3jgw8+MBdSq1evjq+vLzdv3mTXrl3mbSMjI+natSu1a9emaNGifPrppwBJrr/+6UnX3YnTLhqNRnLkyEGBAgWwtbVNsa3PPvuMNm3acOjQIbp3746Hhwdt27Zlzpw53Lp1K8V9oqKi6NmzJ3nz5sXPz++ZTXFy7tw5fvvtN3MR+ZVXXqFJkybJBnUsX74cR0dHfH19KVu2LJUqVWL06NGUKVOGO3fucPLkSQ4fPsyIESPw9PSkRIkSDB06lPfff58//vgjXTE9fPiQzp07U7FiRXOuBw4cyObNm/Hy8sLJyQkPDw/zlxWJf888Kcb27dtz8+bNJHft7tq1C2traz744IOnOY0i2ZKK6CIikm2cOnWKatWqmQvlADly5KB8+fJJCqAA7u7uSV67uLgQHh7O7du3n9hPREQE3bp14+LFi8yfP/+Jc2gmXsg+Htc/FSlSBH9/f7Zt28awYcOoXr06J06cYMyYMbz33ntcvXo1yfb37t2jW7du1KpVK8n8kenVoEEDvv/+e4YNG8bu3bsJCwujePHivP7666nuZ2dnR8WKFZO8LlmyJFeuXAGgdevWxMbGsn37duDRHzp79+5NdRT66dOnOXPmDC1btiQuLo64uDgqVapkHj2THpcvXyY8PJzq1asnWe7p6Qk8urU1Ub58+ZJM05M3b17g0XsiUZ48eZJ9CVKqVCnztvAoh3ny5LF4x0G+fPmoWrUqo0ePZvr06QQGBhITE4OLi8sLm9tfREREHomJiQFI8YGfVatW5dVXX6VTp04sXryYc+fOYTAYqFKlyhPnN3/8+siS4sWLkz9/fvNrJycnHB0dLV5D/BuXL1/mwYMHyb6or1SpElZWVsmujStXrmz+PU+ePMCjaf8sSc9195PY2toybtw49u/fz6RJk3j//fe5desWfn5+NGzYkP/+979Jto+Pj6d///6Eh4fz9ddfY29vn67+UrN27Vpee+01qlWrZr4ebd68OZcuXUoyD/6pU6dwcXFJ8kVJ1apVmTp1KgUKFDB/AfH4+8FoNDJ58mTefffddMf1z/dVbGwss2bN4p133qFq1apUqVKFb7/9locPH5rf20+KsVq1apQtWzbJFwTbtm2jUaNGmXJ+fZGMpiK6iIhkGxERETg6OiZb7ujomGRENCR/6FDixfeTbh+9ffs2HTt25OrVq/j7++Pm5pasn3+OZk+cgzul2P6pdOnSfPzxx8yfP5+ff/6ZMWPG8PfffyebambcuHHcvHmT27dvp3n0fEr69+/PpEmTuHnzJv3796dGjRp8/vnnFkf9JErpWHLkyMHDhw8BKFiwIPXq1TNflP/4448YDAYaN25ssc3vv/8egI8//pg33njD/HPlyhW2bduWpnnaEyWe8/Hjx1OlShXzT506dQCSfFnyzz+8Ev8YzJEjR7Jlj0vpFm17e3vzOfgng8HA4sWL6dq1K/v376dz587UqFGDKVOmmP/YERERkRfj+vXrGAyGZNPwARQqVIi1a9dSq1Yt/P39adasGW+99VaavtRPS/HxSddRz0Lite8/+zIajeTMmTPZtfErr7xi/j3xuie1a8z0XHenVf78+WnZsiWTJ0/mxx9/ZNmyZeTKlYsRI0Ykmerw+++/53//+x/3799/ptdQ0dHRbN68mZs3b1KxYkXztWjHjh0BkhSbw8PDk1wr/lPitWhKX9L8G4l3acKj5yn17t2bPXv20KdPH9asWcOGDRto165dshhSixGgbdu27N69m/DwcO7cucORI0dSHfQi8jKzzugAREREnpVcuXKlOGLm/v37yQqe/9wu8Y+W1EYXhYWF0bVrV+Lj4/n+++9xcnJKsr506dLExsbyxx9/JBnZfO3aNfN6S+7evcurr76aZJmtrS3t27fn4MGDSR5qCY8eftW2bVs++ugj5s+f/6+ncwFo3rw5zZs358GDB+zfv5+pU6cyaNCgVB94+fjDOROFhYWZb42GRxfl3bp14+bNm2zfvp33338/yR9oj4uKimLr1q107tyZpk2bJln34MEDunTpws6dO2nWrFmajinxj7pBgwZRt27dZOufxeialKbnefjwYarvIXt7e3r16kWvXr24ffs2W7ZsYcaMGTg4OPD5558/dUwiIiKSNrt27aJy5cpJ7ip73GuvvcaoUaMYNWoUFy9eZPny5YwYMYLixYvz5ptvPlXflq6jEq8hUvryPj2DCeD/rnX+ec0bHx9PeHj4U18Lpee6+0nCw8Oxs7NLMt2LwWDA09OT7t27M3HiRG7dumW+9i5atCgzZsygW7du+Pj4sGTJklTv+EyrnTt3EhYWxpIlS5IUreHRtIRr1qxh+PDh2NvbkytXrmQP8Xxc4vm9f//+E+9eeNw/H0CbkuDgYH777TfGjh1L8+bNzcv/+UylJ8UIj/4OmD59Onv27CEiIoLixYtTrVq1NMcr8jLRSHQREck2KlWqxNGjR5OMmnnw4AFnzpxJMmIc4Pjx40lenzlzhrx58ya5tfZxCQkJfPHFF8TGxrJixYpkBXSA2rVrY21tzd69e5Ms37t3L87OzinuA7BkyRJq1aqVrFCe2O8ff/yRbJTUBx98QLVq1ejbty9z587l6NGjKbadmsjISLZu3WouBjs4ONC4cWO6dOnC6dOnn7jv4/FGRERw/fp1ypYta15Wq1YtihUrZh4tlNqolu3btxMeHk6nTp1wdXVN8lOjRg3efPPNZHNRpjY6qmTJkuTOnZubN29SokQJ80/RokWJi4t74hQ8aXHt2jXu3btnfn3jxg1CQ0MpV65citvfunUryQNECxQoQJcuXahbty6//fbbU8cjIiIiabN8+XJOnz5N9+7dU1x/9uxZDh06ZH5dpkwZfH19yZMnT5JrpH97N+A/ryEuX75MZGSk+ToqsYD7+IjuM2fOpNiWpRhKlSpFrly5OHLkSJLlv/76KyaTCVdX138Ve6L0XHenJigoCA8PD/Mdif908+ZNbG1tzVPMANSpU4fSpUszbdo0AgMDWbhw4b8+jsetXbsWd3d3atWqlex6tFOnTjx48MA8b72rqyunT59OchdrUFAQ7du35+rVq+Zz8M9r9M8++4xvv/0WSHnUvqU8Py5xn8evZyMiIti9ezfwf++JJ8UIjwrtjRs3ZsuWLWzZskWj0EVSoSK6iIhkG4mjnkePHs2FCxc4deoUAwcOJD4+Hm9v7yTb/ve//2XdunVcu3aN9evXs2XLFlq0aGGx7e3bt/PTTz8xZMgQ4uPjuX37dpIfeDSFSYcOHfDz8+O///0vN27cYNGiRezZs4cvvvjCYtvNmzenePHi9OjRg7Vr13Lx4kVu3rzJkSNHGDBgAL///ju9evVKcd/Ehy8NGjSI+/fvp+t8WVtbM23aNAYPHkxQUBB//vknx44dY8OGDamOsEpISCBXrlyMHz+eX3/9lYsXLzJ69Gji4uKSjBQ3GAy0adOGxYsXU7JkyVT/WFu7di2urq6UKFEixfXvvfcehw8fJjg4GHj0x2VQUBBnz54lLCzM/Mfmvn37OHfuHNbW1nTr1o1Vq1bh7+/PtWvXOHfuHMOGDaNNmzYpPkQsvXLlyoWPjw+//fYb586dY+TIkbzyyivmeS7/GVNoaCiDBg3iq6++Mud4z549HD58+KlHtImIiEhyJpPJfK32119/cfToUUaMGMHEiRPp1asX77zzTor7nThxgt69e7N+/XqCg4O5fv06y5YtIzw8nKpVqwKP/j9///59fvnlF/Ndh2mRkJBAnjx5GDp0qPkawtfXN8k1ROI107p164iPj+fatWssWrQoydQgidcZR48e5cyZM8lGqtvY2NC1a1c2b97M8uXLuXr1Kj///DOjRo2iVKlS1K9fP+0nMgXpue5OjZubG/Xq1WPy5MnMmTOHM2fO8Mcff3D69Gn+85//sGLFCrp165bi3YzVqlWjd+/ezJ49O9kAmfS6fv06R44c4b333ktxvZOTE5UrVzYP6vD29iY2NpahQ4dy/vx5goKCGDt2LFFRURQvXpxKlSrh4eHB9OnT2b9/P9evX+err75i3759VKlSBXiU56tXr3L48GFMJhOHDh1iz549T4y1ZMmSODo6smrVKq5cucLRo0f59NNPqVevHgCBgYE8fPjwiTEmat++PYcOHeK3335LMrJdRJLSdC4iIpJtvPnmmyxYsIA5c+bQqlUrrK2tqVSpEsuXL082lcrAgQPZunUrEyZMwMrKihYtWtC/f3+LbR84cACAnj17prj+/PnzAAwZMoScOXPi6+vLvXv3KFmyJDNnzjRf1KYkX758fPfdd3z77bd8++23TJkyhcjISPLly4e7uzurVq2yOKLHaDQybdo0mjZtyvDhw5k3b16q5+hxNjY2LFmyhOnTp/Ppp58SHh5O/vz58fLyYsCAARb3i4uLo1ChQnz66aeMHj2aq1evUrhwYaZNm0b58uWTbNukSRO++uor2rRpY7G9K1eu8OuvvzJs2DCL2zRs2BBfX18CAgLo168fvXr1Ys6cOXzyySfMmzeP2rVr8+abbzJr1ix27drFd999x6effoqDgwMrV65kypQpvPLKK1SpUoWVK1dSsGDBNJ8nS8qUKUOjRo344osv+OuvvyhZsiTz5s0z382QUkzz589nwYIFfPfdd8TGxuLk5ES3bt3o0qXLU8cjIiIiSd27d4/atWsDj77cz58/PxUrVmTx4sXUqlXL4n7t27cnKiqKJUuWMG7cOKysrChTpgyzZ882X5O1bt2aH3/8kV69etGuXTvzvNlPEhcXR4UKFWjSpAn9+/fnzz//5PXXX2fevHnmB41XqVKFL774gpUrVzJv3jzKlSvHl19+SZcuXYiLiwMeFdE/+ugjvv/+e44cOZLiSO5evXqRI0cOVqxYwZQpU8iVKxd16tRh8ODBSaZO+TfSc939JLNnz2bNmjVs3ryZlStXEhYWRs6cOSlfvjxfffUVTZo0sbhvr169+OWXXxg4cCAbNmz418ezbt06rKysUn1+z/vvv8/48eO5fv06ZcqUYenSpcyYMYPWrVvzyiuvULt2bXx8fMwP8pw7dy5Tp07Fx8eHqKgoypQpw/z5883voU6dOvH777/Tp08f4uLi8PT0xMfHh08++STZ1CyPe+WVV/jqq6+YNGkSTZs25fXXX+eLL77A3d2d48ePM2jQIGbMmEGdOnWeGCM8+iKjUKFCVKxYMdn0kiLyfwwJT/M0MhERkSwmMDCQzp07s3z5cqpXr57R4WR7S5cuZd68efzvf/9L13yQmV3iCKvU5o0XEREREcnsTp8+TcuWLVm1apX5TgsRSU4j0UVEROSZ++OPPzh27BizZs1i0KBB2aqALiIiIiKS1d29e5crV64wfPhwGjZsqAK6yBOoiC4iIiLP3HvvvYetrS3du3dP17yYIiIiIiLy/A0fPpxffvmFevXqMW7cuIwORyTT03QuIiIiIiIiIiIiIiIWGJ+8iYiIiIiIiIiIiIjIy0lFdBERERERERERERERC1REFxERERERERERERGxQEV0ERERERERERERERELrDM6ABH5PwkJCdy79wCTSc/7zcqMRgP58jkol9mAcpl9KJfZh3KZfaQnlwUK5HpBUWVft2+Hv5B+9BnN2pS/rE35y9qUv6xN+cva0nKtqZHoIpmIwWDAaDRkdBjylIxGg3KZTSiX2YdymX0ol9mHcpk9Ka9Zm/KXtSl/WZvyl7Upf9mfiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigXVGByAi/ydqwFTsMzoIeSaiQLnMJpTL7EO5zD6Uy8whfFivjA5BREREROSFUBFdREREREREREQki7Bfu+2F9RXZuskL60skM9N0LiIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIWZPic6Ddu3KBRo0ZUqVIlyXIPDw/69u37TPvavHkzjRs3xtraGm9vbxYvXoytre1TtXn37l3Gjh3LvXv3AIiOjqZ///7UrFnT4j6BgYFMnz6d77//PsX1Z8+eZc2aNYwZM+apYuvfvz937twhLCyM4OBg3njjDQA+/vhj3nnnnadq+98aOnQox48fp2DBguZlJUuWxNfX94XGkVreIiMj2bdvH40aNXqhMYmIiIiIiIiIiEjmk+FFdABHR0f8/f2fez9+fn40bNgQa2vrZ9bfjBkzcHV1pXv37gCcOXOGUaNGUaNGDYzGfzfQ38XF5akL6AAzZ84E/q9o/yLO8T8lJCRgMBiSLPv4449p3759hsaQWt7OnDnDzp07VUQXERERERERERGRzFFET83evXuZPn06xYoVo3z58vzwww/s27ePoUOHUqlSJXMxtlatWqxZs4b8+fMzbNgwbt26RUxMDFWrVmXYsGHMnj2ba9eu0bVrV/z8/PD09CQoKAiTycTo0aMJDg4mPj4eLy8vPv/8cw4dOsQ333xD0aJFOXfuHLGxsXz99dcUKFAgSXzh4eFERESYX1eoUIH169cDEBkZmWLbif73v/+xZMkSli9fDjwq5Pr4+DBq1CjzSPUOHTpQt25dfv31V65cuUKHDh3o0qUL9+7do3///kRHR/PGG29w4cIFevfujaen5xPPab169Vi/fj158+ZlyJAhREdHM2vWLB48eMB7773H//73PxYuXMiuXbuwtbWlUKFCjBs3jpw5cyZp59SpU0yaNAmj0UhcXBxDhgyhSpUqDB06FFtbW27cuMGYMWMoXrx4mnL9888/M2fOHKytrbGysmLMmDFs2LCB3Llz06VLFw4dOkS3bt0IDAwkZ86cfPHFF7Rq1YpXX32VyZMnA2AymfDx8cHNzQ1vb2+cnZ25ePEiCxYsIEeOHE/MW1RUFCNGjCA0NJTJkyczZMgQpk6dSlBQEAkJCVSoUIERI0bwyy+/MGvWLIoWLcrNmzcxGo3MmjWLV199lVGjRnHp0iViY2NxdnZm3LhxWFtn+o+aiIiIiIiIiIiIpCDTz4k+duxYpk+fbi6CWllZpbr9nTt38PDwYNWqVaxdu5a9e/dy4cIF89QwS5YsIV++fObt/f39sbGxYfXq1axcuZLdu3fz66+/YjQaOXbsGL1792bNmjWULFmSHTt2JOuvZ8+ebNmyhcaNG+Pr68uPP/5IfHx8qm0nql27NpcvX+bu3bsAbN26laZNmyZp38rKirt377Jw4UIWLlzIggULAPj2228pW7Ys3333Hc2aNePYsWNpHvnu6elpjuPmzZvm/o8ePUqNGjU4efIkmzZtYtWqVaxatcrinQI+Pj4MGDCAFStWMHz4cIYMGWJeFxYWxpIlS9JcQI+KimLs2LHMmTOH5cuX8/HHHzNp0iRq1arFkSNHAPjll1+oXbu2OfagoCA8PDwYMmQIY8eOZfny5YwdO5YRI0aY27Wzs2PZsmVJCuhgOW85cuSgR48e1KhRg6FDh7Jjxw7u3LnDypUrWbVqFXfv3mX37t0YjUbzlx7fffcdbm5uLF++nPPnz3PkyBHWrFlDQEAAHh4ehISEpOkciIiIiGQl1tbGp/qxsnp07Wpl9eRtRUREREQyUqYYHhsaGoq3t3eSZb1796Z8+fJERERQvnx54FHR2dI84ony5MnDmTNnaN++PdbW1ty9ezfVIuaJEydo1qwZADY2NlSrVo1Tp07h4uJCyZIlKVSoEABFihQhNDQ02f4VKlRg165dHDt2zDxtyty5c1m9enWqbQNYW1vTsGFDdu3aRfv27dm5cycrVqzg2rVrSfqoUaMGAE5OTty/fx+A8+fP07JlSwDc3NwoUqRIquflcYmF6dKlS1O0aFGsra25dOkShw8fpnbt2hw/fpzq1aub54v39PRk48aNSdoIDw/nr7/+olq1auYYbt++TVhYGECyOe4ft2zZMrZt22Z+7eHhQYMGDbh9+zb9+vUDHo0oj4uLo3Llyly4cIGEhASOHz9Or169OHjwIEWLFqVkyZLExMRw5coVvvzyS3N7UVFRxMbGphpHanl73LFjxwgKCjK/PyMiIrhx4waOjo5J3h9Vq1Zl/fr1lCxZkly5cvHJJ59Qr149GjZsmOzuBREREZHsIG9eh2fSTu7c9s+kHRERERGR5yVTFNEtjXROfOhjIpPJZP79n3Ncx8XFAY8KtGFhYaxcuRKj0cj777//xP4TEhKS/J44ovufU3A8vl2iyMhI7O3t8fDwwMPDg549e9K4cWN+++23VNtO9MEHHzBz5kxcXFwoWrQohQsXTlZET2kqkH/O852e+ddr1qzJ0qVLef311/Hw8MDKyorDhw9z9OhRPvnkEzZt2vTEuFM6F4/HkdoDW1OaE/3cuXMULFgwxfdBuXLlOHHiBEajkapVqzJv3jycnJyoU6cORqMRo9Focb53S3E8KW+JDAYDrVq1okePHkmWBwYGJjkHJpMJg8GAvb0969ev59SpU/zvf/+jefPmLF++nNKlS1s8HyIiIiJZUUjIg6fa38rKSO7c9oSFRRIfb0p122dVsM8IBw4cYMiQIVSvXt38zCKAefPmMX/+/CTbJiQkULBgQfbu3ZusnSFDhrB58+Ykd+aWLFmSTZs2Pb/gRURERATI5NO55M2bF3t7e37//XcA9u3bZ17n6Ohonobk6tWr5hHa9+/fp1SpUhiNRo4ePcqff/5JTEwM8Kggmvh7Ind3d/bv3w9ATEwMhw8fpnLlymmKLz4+nkaNGvHzzz+bl4WGhhIVFYWTk1Oa2q5SpQp///0369evN49aT4tSpUpx/Phx4NFc6jdv3kzzvnny5MHKyoq9e/dSvXp1qlWrxr59+zAYDOTLlw93d3cCAwPN5+rAgQPJ4s6dOzdOTk4cPXoUeDQVjJOTU7J509Pq9ddfJywsjHPnzgFw6NAhVq5cCTwaOf/1119TtWpVbG1tsba2Zv/+/dSuXZucOXNSokQJ8x8aFy9e5D//+U+qfT0pb0aj0XzsVatWZc+ePeaR7bNmzeLSpUsAXLlyhb/++guA48ePU758eU6dOsUPP/yAm5sbffv2pXz58pw/f/5fnRMRERGRzCwuzvRUP4mF8/j4J2+bVS1atIjx48dTokSJZOt69+7NqVOnkvy88847fPDBBym2FRYWRt++fZNsrwK6iIiIyIuRKUaipzSdS968eZk9ezYjR46kX79+5M+fH1dXV/P6Vq1a4ePjQ3BwMIULF+b1118nISGB1q1bM2jQIA4fPoyHhwfdunVj8uTJvPHGG3h5edGhQwdmz55tbqdjx46MHj2a9u3bExcXR4sWLXBzcyMwMPCJcVtZWbFgwQKmTp2Kn58ftra2xMfHM3bsWAoVKpTmths3bsy3336bZE7xJ/noo4/o27cvHTp0oEKFCjg7Oz9xvvjH1ahRg82bN1O0aFEALly4YJ6P3c3NjebNm9OxY0esra0pUaIEnTp1StbGlClTmDhxIkajkYSEBKZMmZLm/v8pR44cTJ8+nVGjRmFnZ4fJZMLX1xeAOnXqMG7cOLp06QJApUqV2LhxI2XKlDHHMX78eBYvXkxsbCw+Pj6p9vWkvLm6uvLVV18xbNgwJk6cyMmTJ2nfvj0GgwEXFxdKlCjBnTt3KFOmDNOnTyc4OBgbGxv+85//YGNjw/z581m7di0Gg4FChQrx9ttv/+vzIiIiIiJZl52dHevWrWPChAlER0enuu3+/fs5deoUkydPTnF9WFgYjo6OzyNMEREREXkCQ4KleTkyoUuXLtG9e/cUb2982Vy+fJng4GDq1q1LREQEDRs2ZOPGjZp/+wVJnEf9SXP0p1fUgKnPtD0RERGR5yV8WK+n2t/a2kjevA6EhDx44mjzAgVyPVVfGW3o0KFER0cnmc7lcfHx8TRt2pTevXvz3nvvpbjNBx98QKFChbh69SqhoaG4ubkxatQoXn/99TTFcPduBEaj4ckbPqX0TNMjmY/yl7Upf1lbevJn+93WFxQVxLRL+f9LkpQ+f1lbWqYOzBQj0SX9cuXKxbJly5g3bx6xsbF89tlnKqCLiIiIiGRBW7duxWg00qRJE4vbvPbaa+TPn5+JEydiY2PDuHHj6N69O1u3bk31eUSJ8uVzSPZcqedJD4zN2pS/rE35y9rSkr8Ym7TPRPC0HLLwc0kygj5/2VeWKqKXLl1ao9D/vwIFCrB06dKMDuOlVb169Wc+Cl1EREREXk7Lli2jY8eOqRa5FyxYkOS1r68v1atX58iRI9SqVeuJfdy790Aj0eWJlL+sTfnL2tI1Ej02/gVFBQ+e8kHiLwt9/rI2jUQXERERERHJxIKDgzl37hyNGjVK1345c+bE0dGR27dvp2l7kykBk+nFzeSZ+MBYyZqUv6xN+cva0pI/mxc4M7PeS+mjz1/2ZczoAERERERERF5W+/fvp3z58uTJk8fiNhEREfj6+nLr1i3zspCQEEJCQihWrNgLiFJERETk5aYiuoiIiIiISAY5e/YsZcuWTbY8KCiIRo0aERMTQ86cOTlx4gQTJkwgNDSU0NBQxo4di4uLC1WqVMmAqEVEREReLprORSQTyTHDh5CQB7r1J4uztjaSN6+DcpkNKJfZh3KZfSiXkpW4uroCEBcXB8B///tfAE6dOmXe5vbt27z++uvJ9o2MjOTKlSsk/P9b9ufMmcPEiRNp0KAB1tbWeHh4MH/+fIxGjYsSERERed5URBcREREREXkOHi+WW/L111+nuLx69eqcP3/e/NrJyYk5c+Y8s9hEREREJO00bEFERERERERERERExAIV0UVERERERERERERELNB0LiKZSNSAqdhndBDyTESBcplNKJfZh3L5fIQP65XRIYiIiIiIiDxXGokuIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigXV6Nn748CHTp0/nyJEj5MqVi+joaNq2bUvr1q2fV3zp1r9/fwYPHoyTk9NzaT8sLIwOHTrg5OTEwoULzctv3LhBo0aNqFKlSpLtBwwYkGxZeh07doxXX32VEiVKMGHCBD744APc3Nyeqk0/Pz/Wr19PsWLFePjwIXZ2dnTp0oUGDRr8q/a8vb1ZvHgxtra2TxWXs7MzQUFB2NnZpanP+/fvkydPHkwmEzExMfTr14/atWs/VQz/xq1bt7h06RI1a9Z84X2LiIiIiIiIiIjI85OuIvqoUaMoUKAAGzZswGg0cvfuXTp37kzevHmpX7/+84oxGZPJhNGY8iD6mTNnPte+L1y4gK2tbZICeiJHR0f8/f1T3T+12C0JCAigYcOGlChRghEjRqRr39S8//77DBo0CIDr16/Tq1cvYmNjadKkSbrbetJxPy+DBw/Gy8sLgEuXLtG+fXt++uknbGxsnrjvv8mFJYGBgVy4cEFFdBERERERERERkWwmzUX069evc/jwYfbs2WMuPL766qusWrWK3LlzA7Bw4UJ27dqFra0thQoVYty4cdjb21OtWjX69evH3r17uXXrFr6+vkRGRrJkyRKWL18OwJkzZ/Dx8WHLli0sXbqUnTt3YmNjQ+HChfH19eXu3bt8+umnuLm5UahQId577z1GjRqFtbU1ERER9OjRgyZNmlCvXj0WLVpEyZIlmTx5MidPnsRgMFC+fHlGjRrFzZs36dWrF/Xr1+fIkSOEhoYya9YsypYtm+R4r127xujRo4mLiyM6OpqePXvy5ptvMm7cOIKDg+nVqxfz589P07nz8/Pjxo0b3L59m969e2NjY8PEiROxtbXl4cOHDBw4kJo1a/Lw4UNGjRrFrVu3iIqK4vPPPyc2NpYdO3Zw8uRJrKysWLBgAd27d8fLyytd57t69eqpxli8eHGGDRvGlClTaNKkCcHBwYwdO5aYmBhiYmLo1asXdevW5e7du4wYMYIHDx4QExPD8OHDqVSpknkE+fz58wkJCSE8PJyTJ0/StWtXLl68yMmTJ3F0dOSbb77BaDQybtw4Tp8+jcFg4LXXXmPKlClYW//f2/Hw4cNMmzYNW1tbIiMj8fHxoUaNGqkeQ+nSpbGysuLevXs4Ojri6+tLcHAwsbGx1KtXjx49ehAQEMCPP/5IdHQ0LVu2xMPDI8Xj+fnnn5kzZw7W1tZYWVkxZswYSpQoQd26denatSv79+/n2rVr9O/fHzc3N2bNmoXJZCJPnjy4ubmlO3YRERERERERERHJnNJcRP/9999xdnZONl2Ho6MjACdPnmTTpk0EBARga2vLmDFj8Pf3p1evXjx8+JCiRYuyfPly1qxZw4oVK5g5cyYjR47k7t27vPrqq2zdupWmTZsSFBTE3r17WbVqFUajkWnTpvHdd9/RoEEDLl++zMyZMylXrhzjx4/Hy8uL3r17ExERwcaNG5PEtWPHDi5evMjq1asxGAx8+umnbN26FXd3dy5fvsy7775L//79mTFjBuvXr2fo0KFJ9h83bhwtWrSgWbNm/PXXX7Rs2ZL//ve/DB8+nOnTp6e5gJ7o6tWrrF69GqPRyJ49e/Dx8aFq1aocOXKESZMmERAQwIoVK8iXLx/Tp0/nypUrzJw5k9mzZ7N8+XK6d+9OrVq1WLBgwb86308qogNUqlSJixcvYjKZGDduHJ988gmenp7cu3ePDz/8kB07djB79mxq1KjBxx9/zOHDh9m0aROVKlUyt2FlZcXvv//OihUrOH78ON7e3mzevJnRo0fz7rvv8ttvv/H666/j5OTEyJEjMRqNdO7cmQMHDvD222+b21m6dCmdOnWiWbNm3Lx5k+PHjz8x/sDAQHLmzEnBggVZvHgxBQsWZOLEiZhMJjp06GAeJf7bb7+xY8cO7OzsGD16dLLjcXZ2ZuzYsaxevZp8+fKxb98+Jk2axIIFC7CysgJg8eLFHDx4kBkzZhAQEECLFi2Ijo6mW7du9OrVK92xi4iIZFXW1i/2ETtWVsYk/0rWpVyKiIiISFaR5iJ6bGwsJpPJ4vrjx49TvXp1c5Hd09MzSWE7cSRukSJFuH//PtbW1jRs2JBdu3bRvn17du7cyYoVK9ixYwfBwcF89NFHAERGRuLu7g5Azpw5KVeuHADvvPMOgwcP5q+//sLLy4u2bdsmi6d27drmUfM1atTg1KlTuLu7kzt3bipUqGCOJygoKNnxnDhxgmnTpgFQuHBhXn31Va5du5bqOQoNDcXb2zvJsokTJwKPCtSJseTJk4d58+YRHR1NVFQU9+/fN/fZokULAEqWLMns2bMt9pXe850WBoMBa2trjEYjx44d48GDB8ybNw8AGxsbbt++zYkTJ8zn+s033+TNN99M1k7isRYqVIhcuXJRunRpAAoWLEhYWBgODg7cuXOHjh07Ym1tzcWLFwkJCUnSxrvvvsuMGTM4ffo0b731Fu+9916KMU+bNo1FixaRkJBAnjx5WLBgAQaDgWPHjhEcHMyvv/4KwIMHD7h+/ToAFSpUMM+5ntLxnD17ltu3b9OvXz/g0bQvcXFxaT63aY1dREQkO8ib1yFD+s2d2z5D+pVnT7kUERERkcwuzUV0FxcXzpw5Q1RUFDly5DAvDw4ONr9OSEgwL09ISEgy33RKc1R/8MEHzJw5ExcXF4oWLUrhwoUxGAx4eXnh6+ubZNsbN24kGQXv6enJjh07OHToEAEBAaxcuZKlS5cm2cdSPI9PG/LP7VLb32AwpLhdotTmRH88dh8fH4YPH84777zDqVOnzMXahISEVL+oeFJ8TzrfT3LkyBFcXV2BRwX1WbNmUaBAgWR9PinGxNHakPK53rJlCydOnGD58uXY2try6aefJmujefPmeHl58dNPPzFv3jy2bdvG+PHjk233+JzojzMYDPTs2TNZATtx5H5qx2MwGChYsKDFXP7zmP5t7CIiItlBSMiDF9qflZWR3LntCQuLJD4+7ddNkvmkJ5cZ9WWNiIiIiAhAmu+dLFGiBB4eHkyYMME8Kvfu3bv06dOH48eP4+7uTmBgIDExMQAcOHCAypUrp9pmlSpV+Pvvv1m/fj3NmjUDwN3dnYMHDxIREQHAt99+y9GjR5Pt6+/vz+3bt2nQoAFjx47l2LFjSYrKie2YTCZMJhM//fTTE+N5nLu7O/v37wcefVFw//59Xn/99TTvn5r79++b52DfsmWL+ZxVqVKFgwcPAvD333/Trl074FFRN3Gbx+NL7/lOzR9//MHUqVPp1asXAFWrVmXHjh0A3Llzx/ylxuMx/vbbb/Tp0yfdfd2/f59ixYpha2vLpUuXOH36dLLjmz17NgaDgQ8++IBBgwaZR5SnVdWqVdm5cyfwaCS5r68v9+7dS7ZdSsfz+uuvExYWxrlz5wA4dOgQK1euTLW/x3P0tLGLiIhkJXFxphf6k1hsjY9/sf3qJ2NzKSIiIiKSkdI8Eh0eTZ0xa9YsWrRoQe7cuTGZTPTs2ZOGDRsCj0bgJk7RUaJECTp16vTENhs3bsy3337LkCFDAHB1daVz58507twZW1tbChYsSKtWrZJNm1GmTBl8fHzIkSMHUVFRjBw5MslI8XfffZfjx4/Tvn174FGxtFGjRty8eTNNxzpy5Ei+/PJL1q1bR2xsLJMnT04yAv9p9OrVi169epEvXz569+7NwYMHmTx5Mn379mXUqFF06NCBmJgYPv/8cwBq167NxIkTeXxKETc3t391vh+3ZcsWTp48SXx8PCaTicGDB1OnTp0kx79jxw6ioqLo0aMHAH379mXEiBF06tSJuLg4hg8fnu7jb9y4MZs2baJ9+/aULVuW/v37M2/ePDw8PMzblC5dmm7duvHKK68QHR3NoEGD0tVHx44duXjxIm3atCE+Pp46deqQL1++ZNuldDw5cuRg+vTpjBo1Cjs7O3MRPjUeHh70798fe3t7ypUr91Sxi4iIiIiIiIiISOZhSLA0l4mIvHBRA6ZmdAgiIiLpEj6s1wvtz9raSN68DoSEPNAI5SwuPbksUCDXC4oq+7p9O/yF9KPPaNam/GVtyl/Wlp782a/d9oKigsjWTV5YX1mZPn9ZW1quNdM8nYuIiIiIiIiIiIiIyMtGRXQREREREREREREREQtURBcRERERERERERERsUBFdBERERERERERERERC6wzOgAR+T85ZvjoIRTZgB4okn0ol9mHcikiIiIiIiL/lkaii4iIiIiIiIiIiIhYoCK6iIiIiIiIiIiIiIgFKqKLiIiIiIiIiIiIiFigIrqIiIiIiIiIiIiIiAV6sKhIJhI1YCr2GR2EPBNRoFxmEy9zLsOH9croEEREREREREQynEaii4iIiIiIiIiIiIhYoCK6iIiIiIiIiIiIiIgFKqKLiIiIiIiIiIiIiFigIrqIiIiIiIiIiIiIiAXZuoh+7949KlasiJ+fn8VtLl26RL169VJcV6tWLW7cuMH+/fuZM2cOAMeOHePatWsATJgwgaCgoGcfeDqtXr2aoUOHJlseGRnJ8OHDadeuHd7e3rRs2ZItW7ak2taNGzeoVatWiuseP/antXnzZuLi4gDw9vYmJibmqdscOnQoq1evTnWbW7du8fPPPz91XyIiIiIiIiIiIvJyyNZF9A0bNlCyZEkCAgIwmUz/uh0vLy8+//xzAAICAsyF5BEjRuDm5vZMYn0eli5diq2tLd999x3+/v7MnTuXBQsWEBER8a/ae/zYEyUkJPyrtvz8/IiPjwfA398fW1vbf9VOegUGBj7XIvrTvM9EREREREREREQk87HO6ACep3Xr1jFkyBAmTpzIoUOHzCOsf/zxR6ZPn07RokUpVaqUeftr164xePBgHBwcKF++vHl5QEAABw4coEmTJuzYsYOTJ09iZWXFggUL6N69O15eXixcuJBdu3Zha2tLoUKFGDduHPb29lSrVo1+/fqxd+9ebt26ha+vL9WrV08S5/Xr1xk+fDgGg4EHDx7w8ccf07RpU9auXUtgYCAAly9fxsHBgUWLFpEjRw78/PzYvXs3xYsXJ1euXCkef3h4OA8ePCAhIQGDwUCRIkXMI9ETj2nmzJkA9O/fnzp16vDmm28CMGXKFM6dO0doaCjTpk3j8uXLyY7d2dmZixcvsmDBAjZv3syaNWuwt7cnISGBqVOn4uTkxNWrVxkzZgzx8fEkJCQwYcIENm7cyLVr1+jatSt+fn54enoSFBSEyWRi9OjRBAcHEx8fb/7y4tChQ3zzzTcULVqUc+fOERsby9dff02BAgUs5r5u3bp07dqV/fv3c+3aNfr374+bmxuzZs3CZDKRJ08eOnXqhK+vL8HBwcTGxlKvXj169OhBVFQUPj4+/Pnnn5QoUQKTyUTNmjX58MMP2bJlCytWrMDW1hYHBwfGjx/Pq6++SuXKlenUqRNhYWF8/vnnDBo0CJPJRHh4OC1btuSjjz5K79tXREREREREREREMoFsW0T/9ddfCQ8Pp3bt2rRq1Yp169aZi+hjxoxh0aJFlCtXjoULF5r38fPzo2nTpnTq1IkTJ06wZMmSJG02aNCA5cuX0717d2rVqsWCBQsAOHnyJJs2bSIgIABbW1vGjBmDv78/vXr14uHDhxQtWpTly5ezZs0aVqxYkayIfuvWLbp168Zbb73FH3/8QcuWLWnatClWVlYcPXqUrVu34uDgQLt27fj5558pVaoU69evZ+fOndja2tKjRw9effXVZOfA29ubnj178s477+Dp6UmtWrWoX7/+E0d937lzh/r16zNkyBD8/f2ZM2cOM2fOTHbsdnZ2LFu2DHg0dczcuXMpVKgQc+bMwd/fnyFDhjB+/Hg6depE/fr12bp1K9u3b6dv377MnTuXJUuWYGdnZ+7X398fGxsbVq9eTWxsLB9++CGenp4YjUaOHTvGxIkTKVSoEAMHDmTHjh14e3tbPAYrKysAFi9ezMGDB5kxYwYBAQG0aNGC6OhounXrxjfffEPBggWZOHEiJpOJDh06ULNmTc6ePUtsbCxr167l5s2bNG3alNq1a3Pr1i0WLFjAunXryJEjB6tWrWL+/PmMHDmSyMhIPDw8qFu3LsuWLaNYsWJMmDCB2NhY/P39Uz3fIiKZlbV19rlhzcrKmORfybqUy+xDuRQRERGRrCLbFtHXrVtHixYtsLKyomXLljRo0ID79++TkJBAREQE5cqVAx7Ne/7dd98BcP78ebp27QpA5cqVcXBwSFNfx48fp3r16ubitKenJxs3bjSvr1GjBgBFihTh/v37yfbPmzcvixcvZtGiRQCEhISY17m6uprjKFKkCKGhoZw/fx4XFxdzAbpmzZqcP38+WbtOTk5s3LiRU6dO8csvv/Dtt98yY8YM1q1bl+rxvPLKK1StWhUAd3d3vv/++xS3q1KlSpJ9BgwYgMFg4Pbt2+Z1J06cYPr06QC89957qfZ74sQJmjVrBoCNjQ3VqlXj1KlTuLi4ULJkSQoVKpTkPDzJk877sWPHCA4O5tdffwXgwYMHXL9+nXPnzplH5L/22mtUrFgRgFOnTnH37l26d+8OQGxsLAULFjS35+7uDjzKx5IlS/Dx8cHLy4sOHTo8MVYRkcwob960/X8wK8md2z6jQ5BnRLnMPpRLEREREcnssmURPSIigh07dlC4cGH27dsHgNFoZPPmzckKuYnzckPy+b3TM7/14/smJCRgNP7fiBobG5tU9/X19aV+/fp07tyZe/fu4enpaV5nbZ00RQkJCcnifPwYHhcdHY2trS1ubm64ubnRo0cPvL29OXjwIAaDwWIbj69Lbc7zxC8Nbty4weTJk9myZQuFCxdm8eLF/P777+btnsV5TOk8PMk/9/kng8FAz549k70njh49muQcPJ7LChUqsHjx4hTbSzwf5cqVY/fu3QQGBrJ9+3bmz5/Phg0bnvg+EBHJbEJCHmR0CM+MlZWR3LntCQuLJD5ez6/IypTL7CM9ucyOX+qJiIiISNaRLe+d3LJlC1WrVmX79u1s3LiRjRs38tVXX7F+/Xry5s2Lvb29uci7f/9+835lypTh+PHjABw5coTIyMhkbRsMBmJiYpIsc3d3JzAw0Lz8wIEDVK5cOc3x3r9/n7JlywKwceNGjEZjsj4eV7ZsWc6ePUtMTAwmk4mDBw+muF3nzp2TjDp/+PAh9+7do3jx4jg6OnL37l3g0Yjq06dPm7d78OABQUFBwKNpcRLnh0/p2AHCwsJwcHCgcOHCPHjwgD179pi3q1y5sjm+PXv2MH78eIttubu7m/MRExPD4cOH03Ue0+LxfqtWrcrOnTuBR4V+X19f7t27R6lSpczvg7/++ovffvsNeHRXwG+//cbff/8NPHqfJe7/uC1btnD69Gm8vLzw9fXlzz//TNPIeRGRzCYuzpRtfhILdPHxGR+LfpRL/aQ/lyIiIiIiGSlbjkRft24dvXv3TrKsbt26jBs3jjNnzjBy5Ei++OIL81QdiSOl+/Tpw+DBg9m9ezdlypShePHiyUY8165dm4kTJxIXF2de5ubmRvPmzenYsSPW1taUKFGCTp06pTne7t27M3bsWAoUKGCel3vQoEG89dZbKW5funRp3n//fT788EOcnJwoU6YMYWFhybabPn06EydOZN26ddjZ2REbG0vXrl1xc3MjKiqKpUuX0qdPH1599VVcXFwwmUyYTCacnJzYvn07M2fOJDw8nK+++srisQO4uLhQqVIlWrVqRf78+c3ncdu2bYwcOZLRo0ezZs0aACZMmABgnuZk9uzZ5nY6duzI6NGjad++PXFxcbRo0QI3Nzfzw1WfBQ8PD/r374+9vT29e/fm4sWLtGnThvj4eOrUqUO+fPlo3rw5e/fupXXr1pQpUwZ3d3esrKwoVKgQo0aNomfPntjb22NnZ8ekSZOS9VGmTBnGjBmDjY0NUVFRfPLJJ+TPn/+ZHYOIiIiIiIiIiIi8OIaEtMyLIfISuXXrFr/++itNmjQhLi6Opk2bMnnyZNzc3J5731EDpj73PkRE0ip8WK+MDuGZsbY2kjevAyEhDzSqNYtTLrOP9OSyQIFcLyiq7Ov27fAX0o8+o1mb8pe1KX9ZW3ryZ7922wuKCiJbN3lhfWVl+vxlbWm51syWI9FFnkauXLnYvn07S5YsISEhgQYNGryQArqIiIiIiIiIiIhkPiqii/zDK6+8gp+fX0aHISIiIiIiIiIiIplAtnywqIiIiIiIiIiIiIjIs6AiuoiIiIiIiIiIiIiIBSqii4iIiIiIiIiIiIhYoDnRRTKRHDN89CTnbEBP5c4+lEsRERERERER0Uh0ERERERERERERERELVEQXEREREREREREREbFARXQREREREREREREREQs0J7pIJhI1YCr2GR2EPBNRoFxmExmZy/BhvTKoZxERERERERFJpJHoIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFmSLIvq9e/eoWLEifn5+Fre5dOkS9erVS3FdrVq1uHHjBvv372fOnDkAHDt2jGvXrgEwYcIEgoKCnn3g6bR69WqGDh2abHlkZCTDhw+nXbt2eHt707JlS7Zs2ZJqWzdu3KBWrVoprnv82J/W5s2biYuLA8Db25uYmJinbtPb25sPPvgAb29v88/s2bOfut3IyEh27NgBkOS9ICIiIiIiIiIiIi8v64wO4FnYsGEDJUuWJCAggM8++wyj8d99N+Dl5YWXlxcAAQEBNGzYkBIlSjBixIhnGe4zt3TpUmxtbfnuu+8A+PPPP+nevTtvvfUWOXPmTHd7jx97ooSEBAwGQ7rb8vPzo2HDhlhbW+Pv75/u/S0ZPHiwOVcpMZlM6X4fnDlzhp07d9KoUaMk7wURERERERERERF5eWWLIvq6desYMmQIEydO5NChQ+YR1j/++CPTp0+naNGilCpVyrz9tWvXGDx4MA4ODpQvX968PCAggAMHDtCkSRN27NjByZMnsbKyYsGCBXTv3h0vLy8WLlzIrl27sLW1pVChQowbNw57e3uqVatGv3792Lt3L7du3cLX15fq1asnifP69esMHz4cg8HAgwcP+Pjjj2natClr164lMDAQgMuXL+Pg4MCiRYvIkSMHfn5+7N69m+LFi5MrV64Ujz88PJwHDx6YC91FihQxj0RPPKaZM2cC0L9/f+rUqcObb74JwJQpUzh37hyhoaFMmzaNy5cvJzt2Z2dnLl68yIIFC9i8eTNr1qzB3t6ehIQEpk6dipOTE1evXmXMmDHEx8eTkJDAhAkT2LhxI9euXaNr1674+fnh6elJUFAQJpOJ0aNHExwcTHx8PF5eXnz++eccOnSIb775hqJFi3Lu3DliY2P5+uuvKVCgQJreBzdu3ODTTz/Fzc2NQoUK0bNnT4YNG8atW7eIiYmhatWqDBs2DID169ezZs0aAFxdXRk4cCAjRowgNDSUyZMnU65cOfN5O3XqFJMmTcJoNBIXF8eQIUOoUqUKgwcPxsnJifPnz3P16lXq1q3LsGHD+P333xk1ahTW1tZERETQo0cPmjRpkqZjEBEREZHs5cCBAwwZMoTq1aubr8kBgoODqV+/Pra2tkm2nzp1Ko0bN07WTnR0NBMmTGDHjh3ExsZSp04dxowZQ758+Z77MYiIiIi87LJ8Ef3XX38lPDyc2rVr06pVK9atW2cuoo8ZM4ZFixZRrlw5Fi5caN7Hz8+Ppk2b0qlTJ06cOMGSJUuStNmgQQOWL19O9+7dqVWrFgsWLADg5MmTbNq0iYCAAGxtbRkzZgz+/v706tWLhw8fUrRoUZYvX86aNWtYsWJFsiL6rVu36NatG2+99RZ//PEHLVu2pGnTplhZWXH06FG2bt2Kg4MD7dq14+eff6ZUqVKsX7+enTt3YmtrS48ePXj11VeTnQNvb2969uzJO++8g6enJ7Vq1Urxgvyf7ty5Q/369RkyZAj+/v7MmTOHmTNnJjt2Ozs7li1bBjya8mTu3LkUKlSIOXPm4O/vz5AhQxg/fjydOnWifv36bN26le3bt9O3b1/mzp3LkiVLsLOzM/fr7++PjY0Nq1evJjY2lg8//BBPT0+MRiPHjh1j4sSJFCpUiIEDB7Jjxw68vb3T/H64fPkyM2fOpFy5cty4cQMPDw86dOhAQkICDRs2pFWrVuTKlYu5c+eydetW7O3t8fHx4erVq/To0YMDBw4wdOhQAgICzG36+Pgwbtw4qlWrRlBQEIMGDWLXrl1YWVlx6dIl5s+fz4MHD6hZsyZ9+vRhzZo1eHl50bt3byIiIti4cWOa4xcReZy1dbaYdS1TsLIyJvlXsi7lMvt4GXK5aNEi1q1bl+QOz0Th4eHY2Nhw6tSpNLU1bdo0jh07xvr168mZMyfDhg1j+PDh5r9VREREROT5yfJF9HXr1tGiRQusrKxo2bIlDRo04P79+yQkJBAREUG5cuWAR/OeJ053cv78ebp27QpA5cqVcXBwSFNfx48fp3r16ubitKenZ5ICaY0aNQAoUqQI9+/fT7Z/3rx5Wbx4MYsWLQIgJCTEvM7V1dUcR5EiRQgNDeX8+fO4uLiYC9A1a9bk/Pnzydp1cnJi48aNnDp1il9++YVvv/2WGTNmsG7dulSP55VXXqFq1aoAuLu78/3336e4XZUqVZLsM2DAAAwGA7dv3zavO3HiBNOnTwfgvffeS7XfEydO0KxZMwBsbGyoVq0ap06dwsXFhZIlS1KoUKEk5yEl06ZNM59HgMaNG+Pl5UXOnDnNOc+TJw9nzpyhffv2WFtbc/fuXUJCQrh+/TrOzs7Y29sDj0b7AJw7dy5ZP+Hh4fz1119Uq1YNADc3N27fvk1YWBgA1atXx2AwkDNnTnLlykVoaCjvvPMOgwcP5q+//sLLy4u2bdumej5ERCzJmzdt/3+StMud2z6jQ5BnRLnMPrJzLu3s7Fi3bh0TJkwgOjo6ybrQ0FAcHR3T1E5cXBw//PADU6ZMoVixYsCjgR6NGzfm1q1b5utnEREREXk+snQRPSIigh07dlC4cGH27dsHgNFoZPPmzckKufHx8ebfExISkqwzmUxp7vPxfRMSEpLMu21jY5Pqvr6+vtSvX5/OnTtz7949PD09zeusrZOmIiEhIVmcjx/D46Kjo7G1tcXNzQ03Nzd69OiBt7c3Bw8eTDaP+eNtPL7un309LvFLgxs3bjB58mS2bNlC4cKFWbx4Mb///rt5u2dxHlM6DylJaU70GzduJBl9v2zZMsLCwli5ciVGo5H333/f3GZqx5uW/lOL19PTkx07dnDo0CECAgJYuXIlS5cuTVN/IiKPCwl5kNEhZBtWVkZy57YnLCyS+Pi0//9KMh/lMvtITy6z6peKnTt3trguLCwMk8lEjx49OH78OHnz5qVNmzZ88sknya7hr1+/TkREBG+88YZ5WalSpbC3t+f06dNpKqIbjQaMxvQ/4yi9XoY7DLIz5S9rU/6ytvTk7988s+7f0t2xaaPPX/aXpYvoW7ZsoWrVqnzzzTfmZT/++CP/+c9/6NSpE/b29vz++++ULVuW/fv3m7cpU6YMx48fp0KFChw5coTIyMhkbRsMBmJiYpIsc3d3Z+3atcTExGBra8uBAweoXLlymuO9f/8+ZcuWBWDjxo0YjcZkfTyubNmynD17lpiYGKytrTl48CCFCxdOtl3nzp358MMPad26NQAPHz7k3r17FC9enDt37nD37l0AYmNjOX36NG+99RYADx48ICgoCDc3N3799Vfz/PApHTs8utB3cHCgcOHCPHjwgD179pjjqVy5MgcPHuSDDz5gz549HDp0iJEjR5rbenw6F3d3d/bv30+jRo2IiYnh8OHDNG/ePMU8PI379+9TqlQpjEYjR48e5c8//yQmJoaKFSsyduxYwsPDyZUrF0OGDKFVq1Yp5iN37tw4OTlx9OhRqlWrxtGjR3Fyckr1ga3+/v7Url2bBg0aULlyZerXr/+vH8wqIi+3uDgVCJ+1+HiTzms2oVxmHy9rLm1sbChevDjt2rVj9uzZ/Prrr/Tt2xdHR0fzdX2ixDtY/zlyPXfu3Ny7dy9N/eXL5/BCr0ez8x0GLwPlL2tT/rK2tOQvxsbqBUTyiEMW/SI7o+jzl31l6SL6unXr6N27d5JldevWZdy4cZw5c4aRI0fyxRdf8Nprr1GxYkXzSOk+ffowePBgdu/eTZkyZShevHiyEce1a9dm4sSJxMXFmZe5ubnRvHlzOnbsiLW1NSVKlKBTp05pjrd79+6MHTuWAgUK0KFDB2rWrMmgQYPMRe1/Kl26NO+//z4ffvghTk5OlClTxjyNyOOmT5/OxIkTWbduHXZ2dsTGxtK1a1fc3NyIiopi6dKl9OnTh1dffRUXFxdMJhMmkwknJye2b9/OzJkzCQ8P56uvvrJ47AAuLi5UqlSJVq1akT9/fvN53LZtGyNHjmT06NHmh3VOmDABAC8vLzp06MDs2bPN7XTs2JHRo0fTvn174uLiaNGiBW5ubuaHqz4rrVu3ZtCgQRw+fBgPDw+6devG5MmT8ff3p3///nTp0gUrKyveeOMNPDw8uHz5Ml999RXDhg3Dw8PD3M6UKVOYOHEiRqORhIQEpkyZkmq/ZcqUwcfHhxw5chAVFWX+MkFEREREJFG9evWoV6+e+XWtWrVo27Yt69evT1ZET01arzPv3Xvwwkai626RrEv5y9qUv6wtPfmzjU15poLn4YHujk0Tff6ytrTc9WhISOu8FiLy3EUNmJrRIYhIJhI+rFdGh5BtWFsbyZvXgZCQBy/liNfsRLnMPtKTywIFcr2gqJ6PoUOHEh0dzcyZM1PdbtWqVSxZsoT//ve/SZZfvXqVd999lx9//BEnJyfg0TSClSpVYvbs2RYH5Tzu9u3wfx1/eugzmrUpf1mb8pe1pSd/9mu3vaCoILJ1kxfWV1amz1/WlpZrTU3UIyIiIiIi8oLt3LmTlStXJll2+fJl84NDH1esWDHy5MnD6dOnzcvOnz9vnqpQRERERJ4vFdFFREREREReMBsbG6ZOncpPP/1EXFwcP//8M+vXr6djx44ABAUFmZ8hZGVlRZs2bZg1axbBwcHcvXuXSZMm0ahRI/Lnz5/BRyIiIiKS/WXpOdFFREREREQyK1dXVwDzs4YSp2k5deoU9erVY/jw4fj6+nLr1i2KFi3KqFGjqF+/PgCRkZFcuXLF/OymPn368ODBA1q2bEl8fDxvv/02Y8aMefEHJSIiIvISUhFdRERERETkOTh16lSq69u2bUvbtm1TXFe9enXOnz9vfm1ra8uXX37Jl19++UxjFBEREZEnUxFdJBPJMcNHD6HIBvRAkexDuRQRERERERERzYkuIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFujBoiKZSNSAqdhndBDyTESBcplJhQ/rldEhiIiIiIiIiEgWopHoIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFlhndAAi/3Tjxg0aNWpElSpVAIiNjSV//vz4+vqSL1++Fx7Pvn37cHV1TbHvHTt2sGTJEgDi4uJwcXFh0KBB5M2bF4AvvviCy5cvM3/+fF577bUXGreIiIiIiIiIiIg8PY1El0zJ0dERf39//P39+e677yhdujSzZ89O074JCQnPNJZly5YREhKSbPmhQ4eYO3cus2fP5vvvvycgIAAXFxe6dOlCbGwsADt37mTVqlUqoIuIiIiIiIiIiGRRGokuWYK7uzvLli0D4MyZM0yePBkAk8mEj48Pbm5ueHt74+zszMWLF1mwYAGBgYHMnz8fo9FIwYIFmTx5MnZ2dkydOpWgoCASEhKoUKECI0aM4JdffuGbb76haNGinDt3jtjYWL7++mt2797N0aNHGTJkCBMmTMDZ2dkc09y5cxk0aBCFCxc2L+vUqRNbtmzhwIED7N69G5PJxKeffsrkyZMpVqzYCz1nIiIiIiIiIiIi8vRURJdMz2QysWvXLqpWrQrAkCFDmD17NiVLluTSpUt88cUXbN68GQA7OzuWLVtGZGQkI0eOJCAggAIFCjB16lQCAwN5+PAhd+7cYeXKlQD079+f3bt34+joyLFjx5g4cSKFChVi4MCB7NixA29vb7755humTJlC6dKlk8R14cIF3njjjWTxurq6cv78eSZNmkRAQABLlizBzs7uOZ8lEUkra+u034RlZWVM8q9kXcpl9qFcZh/KpYiIiIhkFSqiS6YUGhqKt7c3APHx8bi7u9OjRw/CwsK4cuUKX375pXnbqKgo8/QpifOoX716lbx581KgQAEAfHx8AJgwYQJBQUHmtiMiIrhx4waOjo6ULFmSQoUKAVCkSBFCQ0NTjTE2NhaTyZRseUJCAkaj/hgUyazy5nVI9z65c9s/h0gkIyiX2YdymX0olyIiIiKS2amILplS4pzo/xQTE4PRaExxHYCtrS3wqJCd0tzoBoOBVq1a0aNHjyTLAwMDsbZO+nF40tzq5cuX5+TJkzRo0CDJ8lOnTtGnT59U9xWRjBMS8iDN21pZGcmd256wsEji45N/aSZZh3KZfSiX2Ud6cvlvvgAVEREREXlWVESXLCVnzpyUKFGCvXv3Uq9ePS5evMjWrVvp169fku1KlizJvXv3uHnzJq+99hozZ86kTJkyVK1alSVLltClSxdsbGyYNWsWH3zwQap9GgwG80j3x3Xv3p1p06ZRoUIF84NDV6xYAUDNmjWf0RGLyLMWF5f+olt8vOlf7SeZj3KZfSiX2YdyKSIiIiKZnYrokuVMmTKF8ePHs3jxYmJjY81TtTzO3t6eiRMn8sUXX2BjY0PBggXp3bs3tra2nDx5kvbt22MwGHBxcaFEiRLcuXPHYn916tShX79+TJgwgWrVqpmX16tXj/j4eAYOHAg8GiXv4uLCokWLNJ2LiIiIiIiIiIhINmFIeNKcFSLywkQNmJrRIYhke+HDeqV5W2trI3nzOhAS8kCjJLM45TL7UC6zj/TkskCBXC8oquzr9u3wF9KPPqNZm/KXtSl/WVt68me/dtsLigoiWzd5YX1lZfr8ZW1pudbUcFkREREREREREREREQs0nYuIiIiIiIiIiIgk86JGvWvEu2R2GokuIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhZoOheRTCTHDB89hCIb0ANFRERERERERESyD41EFxERERERERERERGxQEV0ERERERERERERERELVEQXEREREREREREREbFARXQREREREREREREREQv0YFGRTCRqwFTsMzoIeSaiQLl8wcKH9croEEREREREREQkG9JIdBERERERERERERERC1REFxERERERERERERGxQEV0ERERERERERERERELVEQXEREREREREREREbFADxaVTOvGjRs0atSIKlWqABAbG0v+/Pnx9fUlX758LySGW7ducenSJWrWrElAQACxsbG0bdv2hfQtIiIiIiIiIiIiGU9FdMnUHB0d8ff3N7+eOXMms2fPZsyYMQCYTCaMxudzQ4XJZCIwMJALFy5Qs2ZNWrZs+Vz6ERERERERERERkcxLRXTJUtzd3Vm2bBn16tXjvffe49KlS8ybN48NGzawYsUKcuTIwSuvvML48eMpWLAgdevWpXnz5hw9epSQkBD69evHu+++y7179xgxYgRhYWFER0fTpk0b2rRpQ0BAAD/++CPR0dG8+eabrFq1CpPJRJ48eYiMjCQ6OppBgwaxf/9+5syZg62tLQaDgTFjxlC6dGk6dOhA3bp1+fXXX7ly5QodOnSgS5cuGX3aRERERERERERE5F9SEV2yDJPJxK5du6hatSrXrl2jSJEiDBw4kD///JOvvvqKbdu2kTt3bhYtWsTs2bMZP348VlZWODo6snLlSs6fP88nn3zCu+++y3/+8x9cXV3p3bs3ERERNG7cmFq1agHw22+/sWPHDuzs7MyF827duuHn5wdAZGQkw4YN47vvvqNYsWJs27aN8ePHs3TpUqysrLh79y4LFy7kypUrtGvXTkV0kRfE2vrZ35ViZWVM8q9kXcpl9qFcZh/KpYiIiIhkFSqiS6YWGhqKt7c3APHx8bi7u9OjRw8CAgJwd3cH4PTp01SsWJHcuXMD4OnpyZYtW8xt1KhRAwBnZ2ciIyMJCQnhxIkTTJgwAYCcOXPyxhtvcPbsWQAqVKiAnZ2dxZiuXr1Kvnz5KFasmLm/L7/8Mll/Tk5O3L9//1mcBhFJg7x5HZ5b27lz2z+3tuXFUi6zD+Uy+1AuRURERCSzUxFdMrV/zon+OBsbG/PvCQkJSX5/fJ70x9c9Pof6P/cxGAwA2NraPjGu1PqzttbHSiQjhIQ8eOZtWlkZyZ3bnrCwSOLjTc+8fXlxlMvsQ7nMPtKTy+f5RamIiIiIyJOo2idZnqurK2PGjCE0NBRHR0cOHDhA5cqVzet//vln3njjDc6dO4ejoyOOjo64u7uzf/9+XF1dCQsL48yZM7zxxhv8/PPPSdo2GAzExMQkWVayZEnu379PcHAwxYoVS9afiGSMuLjnV0yLjzc91/blxVEusw/lMvtQLkVEREQks1MRXbK8QoUKMWjQID755BPs7OzIkycP48aNM6+/e/cuH330EXfv3mX06NEA9OnThxEjRtCxY0fzw0ILFy6crG0PDw/69++Pvb29eYR5jhw5mDRpEgMGDMDOzg5bW1vGjh37Yg5WREREREREREREXihDwuPzUohkM/Xq1WPRokWULl06o0NJk6gBUzM6BJEsK3xYr2feprW1kbx5HQgJeaBRklmccpl9KJfZR3pyWaBArhcUVfZ1+3b4C+lHn9GsTfnL2pS/rC09+bNfu+0FRfXiRLZuktEhPBV9/rK2tFxrGp+4hYiIiIiIiIiIiIjIS0rTuUi2tnfv3owOQURERERERERERLIwjUQXEREREREREREREbFARXQREREREREREREREQtURBcRERERERERERERsUBzootkIjlm+OhJztmAnsotIiIiIiIiIpJ9aCS6iIiIiIiIiIiIiIgFKqKLiIiIiIiIiIiIiFigIrqIiIiIiIiIiIiIiAWaE10kE4kaMBX7jA5CnokoUC6fUviwXhkdgoiIiIiIiIiIRqKLiIiIiIiIiIiIiFiiIrqIiIiIiIiIiIiIiAUqoouIiIiIiIiIiIiIWKAiuoiIiIiIiIiIiIiIBSqii4iIiIiIPCcHDhygZs2a9O/fP9m6wMBAWrduTZUqVXj77beZO3euxXaGDBlChQoVcHV1Nf80bdr0eYYuIiIiIv+fdUYHIBknJCSEqVOncubMGezt7TEYDHz88ce8++67T9x3//79LFq0CH9//zT3179/fwYPHoyTk9PThM3QoUM5fvw4BQsWNC8rWbIkvr6+T9UuwIYNG2jevDlnz55lzZo1jBkz5qnbFBEREZGX06JFi1i3bh0lSpRItu7PP/+kZ8+eDB06lFatWnHhwgU++ugjihYtSrNmzZJtHxYWRt++fenZs+eLCF1EREREHqMi+kusT58+NGzYkEmTJgFw69YtunfvTkJCAo0aNTJvl5CQgMFgeKq+EhISmDlz5lO18biPP/6Y9u3bW1xvMpkwGtN3o8WtW7dYtWoVzZs3x8XFRQV0EREREXkqdnZ2rFu3jgkTJhAdHZ1k3Z07d2jdujVt27YFoEKFCnh6enL06FGLRXRHR8cXEreIiIiIJKUi+kvql19+ITIyks6dO5uXFSpUCB8fH2bMmEGjRo3w9vbG2dmZixcvsmDBAjZu3Ii/vz8lSpSgUKFC5v2Cg4MZO3YsMTExxMTE0KtXL+rWrcvQoUOxtbXlxo0bjBkzho8//phFixZx7NgxAgMDAbh8+TIODg4sWrSIHDlyMGfOHHbu3EnhwoWpWLEiN2/eZOrUqWk+rsqVK9OpUyfCwsIYO3Ys48aN4/Tp0xgMBl577TWmTJmCtbU1+/btY/78+RiNRgoWLMjkyZMZOHAgFy9eZODAgbRp04bp06fz/fffc+3aNUaPHk1cXBzR0dH07NmTd955h1mzZhEREcGtW7e4evUqpUqVYtasWdy+fZtBgwZhMpkIDw+nZcuWfPTRR88ueSIiIiKSJTx+rf1PiVOyPO6PP/6gfPnyKW4fFhbGnj17WLx4MaGhobi5uTFq1Chef/31NMViNBowGp9uYExaWFkZk/wrWYvyl7Upf1lbevL3tAMdMyNr66z9vtXnL/tTEf0ldeHCBSpWrJhsuZubGxcuXCAhIQF4NHpm2bJlREREMGPGDLZv306+fPn48ssvzfuMGzeOTz75BE9PT+7du8eHH37Ijh07gEcX+0uWLEnSh5WVFUePHmXr1q04ODjQrl07fv75Z0qVKsWaNWvYtWsX1tbWtGvXjrJly6bruCIjI/Hw8KBu3bqEhobi5OTEyJEjMRqNdO7cmQMHDlCjRg1GjhxJQEAABQoUYOrUqQQGBtKnTx+mT5/O9OnTzUX+xONr0aIFzZo146+//qJly5Z4enpiZWXFiRMnWL16NQaDgfr163PhwgUOHTpEsWLFmDBhArGxsema8kZE/k9muIjShVD2oVxmH8pl9qFcJuXv709wcDCtWrVKcf1rr71G/vz5mThxIjY2NowbN47u3buzdetWbG1tn9h+vnwOL7Tokju3/QvrS5495S9rU/6ytrTkL8bG6gVE8mI55HXI6BCeCX3+si8V0V9SsbGx5kL540wmEwaDwXyBXaVKFQCuXr1KoUKFyJcvHwC1a9fmypUrABw7dowHDx4wb948AGxsbLh9+3aS/f/J1dUVB4dH/4EsUqQIoaGhnD9/njfeeAN7+0f/walXrx7BwcEp7r9s2TK2bdtmfu3h4UHfvn0BcHd3B8DBwYE7d+7QsWNHrK2tuXjxIiEhIVy9epW8efNSoEABAHx8fACSFM4fd+LECaZNmwZA4cKFefXVV7l27RoA1apVw8bGBoCCBQsSGhpKzZo1WbJkCT4+Pnh5edGhQ4cU2xWR1OXNRBdRuhDKPpTL7EO5zD6US1ixYgWzZ89m4cKFSe74fNyCBQuSvPb19aV69eocOXKEWrVqPbGPe/cevLCR6Llz2xMWFkl8vOm59yfPlvKXtSl/WVt68mcbG/+ConpxHoQ8yOgQnoo+f1lbWuoPKqK/pFxcXNi0aVOy5adOneKNN94wv04c1fLPgnt8/P/9B9tgMDBr1ixzUfpxlkbFWFsnfeslJCQkm3s9tTnNU5sTPbHPLVu2cOLECZYvX46trS2ffvppkr7S4/HtH48zpeMoV64cu3fvJjAwkO3btzN//nw2bNhgLraLSNqEZIKLKF0IZR/KZfahXGYf6cllZvpi9VmbOXMmP/zwAytWrMDZ2TnN++XMmRNHR0fz4JUnMZkSMJnSdw38NOLjTcTF6TOaVSl/WZvyl7WlJX826axpZAXZ5T2rz1/2pSL6S6p69erY2tqycOFCevToATx6sOa0adMYMGBAsu1LlCjBn3/+SUhICHnz5mX//v3mdVWrVmXHjh14e3tz584d5s2bl2S6l7QqVaoUZ8+eJSYmBqPRyI8//kipUqX+9THev3+fYsWKYWtry6VLlzh9+jRvv/02JUuW5N69e9y8eZPXXnuNmTNnUqZMGQoXLkxMTEyydtzd3dm/fz/NmzcnODiY+/fvpzr35JYtW3BycsLLywtPT0+qV69OaGgo+fPn/9fHIvIyykwXHroQyj6Uy+xDucw+XuZcLl26lG3btvHdd9/h5ORkcbvEqRU//fRT80j1kJAQQkJCKFas2IsKV0REROSlpSL6S8rKyopvvvmGadOm0aJFC+zs7DAYDPTt25e33nor2fa5c+emb9++eHt789prr1GmTBmuX78OwMiRI/nyyy/ZsWMHUVFR5qJ8epUrV44GDRrQsmVLSpQogZubG5GRkf/6GBs3bsymTZto3749ZcuWpX///sybNw8PDw8mTpzIF198gY2NDQULFqR37948fPiQsLAwunTpQs+ePc3tJB7funXriI2NZfLkyeTIkcNiv2XKlGHMmDHY2NgQFRXFJ598ogK6iIiIiCQRHBzM7NmzWb16dYoF9KCgIHx8fNi0aRM5c+bkxIkTTJgwgXHjxgEwduxYXFxcLE6fKCIiIiLPjiEhvfNaiDwncXFx/PDDDzRt2hQ7OztGjRpFiRIl6NatW0aH9sJEDZia0SGIZBrhw3pldAhYWxvJm9eBkJAHL+0oyexCucw+lMvsIz25LFAg1wuK6tlydXUFHl3nwv9NBXjq1Cnmzp2Ln59fsin/nJyc2LlzJ4GBgXTu3JmgoCDs7Oz4448/mDhxIocPH8ba2hoPDw+GDx9ucQ71f7p9O/wZHpll+oxmbcpf1qb8ZW3pyZ/92m2prs+KIls3yegQnoo+f1lbWq41NRJdMg1ra2v++OMPWrdujYODA6+++ipDhw7N6LBERERERP6VU6dOWVz32Wef8dlnn1lcX716dc6fP29+7eTkxJw5c55pfCIiIiKSNiqiS6bSr18/+vXrl9FhiIiIiIiIiIiIiABgzOgAREREREREREREREQyKxXRRUREREREREREREQs0HQuIplIjhk+eghFNqAHioiIiIiIiIiIZB8aiS4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIW6MGiIplI1ICp2Gd0EPJMRIFymYrwYb0yOgQRERERERERkTTRSHQREREREREREREREQtURBcRERERERERERERsUBFdBERERERERERERERC1REFxERERERERERERGx4Lk+WPTGjRs0atSIKlWqJFnu4eFB3759n2lfmzdvpnHjxlhbW+Pt7c3ixYuxtbV9qjbv3r3L2LFjuXfvHgDR0dH079+fmjVrWtwnMDCQ6dOn8/3336e4/uzZs6xZs4YxY8Y8VWwAzs7OfPrppwwYMMC87K+//uLtt99mwoQJtGzZ8qn7SBQQEMAPP/wAwIkTJ3B2dsbe3p7SpUvTuHHjVI/537h9+7b5uG7evInJZKJYsWIATJkyBScnp6fuIygoiGnTpmEwGIiJicHBwQFfX19ee+01Ll68SGRkJK6urk/dj4iIiIiIiIiIiGRdz7WIDuDo6Ii/v//z7gY/Pz8aNmyItbX1M+tvxowZuLq60r17dwDOnDnDqFGjqFGjBkbjvxvE7+Li8kwK6AAFChRgz5499O/fH4PBAMCmTZsoXrx4mttISEgw75uali1bmovy9erVY8qUKZQuXRp49MXBs1agQAFzHv38/IiOjmbQoEHPtI+BAwcyffp03NzcAFixYgVLlixh1KhR7N69mzx58qiILiIiIiIiIiIi8pJ77kX01Ozdu5fp06dTrFgxypcvzw8//MC+ffsYOnQolSpVon379gDUqlWLNWvWkD9/foYNG8atW7eIiYmhatWqDBs2jNmzZ3Pt2jW6du2Kn58fnp6eBAUFYTKZGD16NMHBwcTHx+Pl5cXnn3/OoUOH+OabbyhatCjnzp0jNjaWr7/+mgIFCiSJLzw8nIiICPPrChUqsH79egAiIyNTbDvR//73P5YsWcLy5cuBRwV4Hx8fRo0aZR613aFDB+rWrcuvv/7KlStX6NChA126dOHevXv079+f6Oho3njjDS5cuEDv3r3x9PRMEp+trS3Ozs788ssv5nU7duygTp065m3279/PnDlzsLW1xWAwMGbMGEqXLo23tzfOzs5cvHiRBQsWcOzYMebMmYO1tTVWVlaMGTOGEiVKpDmX8fHxjBs3jtOnTxMdHc3ChQspUKAAa9euZc2aNdjb25OQkMDUqVNxcnKyeOxPEhwczGeffcamTZuARwX9Tz75hI4dO7Jv3z42btzIhAkTUs1NorCwMMLDw82vO3XqBMDx48dZsWIFOXPmxN7enrp16/Lll18SGhpKTEwMbdu2pUWLFsyaNYubN29y9+5dQkJCKFeuHJMmTeLOnTsMGjQIk8lEeHg4LVu25KOPPkrzuRQREREREREREZHMI0OL6GPHjuXrr7+mfPnyLFiwACsrq1S3v3PnDh4eHnTo0IGEhAQaNmxIq1at6Nu3L3PnzmXJkiXY2dmZt/f398fGxobVq1cTGxvLhx9+iKenJ0ajkWPHjjFx4kQKFSrEwIED2bFjB97e3kn669mzJ3369GHXrl14enpSp04dvLy8sLKysth2otq1azNy5Eju3r3Lq6++ytatW2natGmS9q2srLh79y4LFy7kypUrtGvXji5duvDtt99StmxZRo4cSVBQEO3bt7c48r1p06Zs2rQJT09Pzp07R+HChXF0dAQeFfqHDRvGd999R7Fixdi2bRvjx49n6dKlANjZ2bFs2TKioqIYO3Ysq1evJl++fOzbt49JkyaxYMGCNOfy8uXLzJ07l8KFCyc5n5GRkcydO5dChQoxZ84c/P39GTJkiMVjf5JixYoRHR3N/fv3CQ0NpUyZMhw+fJiOHTty+PBhateubTE3VatWTdLW8OHD6devH2XLluXNN9+kfv36uLq6UqVKFerUqUOlSpVo3rw5Y8eOxcvLi9atWxMdHU3Tpk2pVasWVlZW/P7776xbtw6j0UibNm04ePAgly9fplixYkyYMIHY2NgXcieGSFZjbZ01HslhZWVM8q9kXcpl9qFcZh/KpYiIiIhkFc+9iB4aGpqsON27d2/Kly9PREQE5cuXBx4VnZ80p3aePHk4c+YM7du3x9ra2jwC2JITJ07QrFkzAGxsbKhWrRqnTp3CxcWFkiVLUqhQIQCKFClCaGhosv0rVKjArl27OHbsmHmu87lz57J69epU2wawtramYcOG7Nq1i/bt27Nz505WrFjBtWvXkvRRo0YNAJycnLh//z4A58+fN0+d4ubmRpEiRSweY+3atZkwYQKRkZFs3LiRpk2b8vvvvwNw9epV8uXLZ55L3NPTky+//NK8b+Jc9VeuXOH27dv069cPAJPJRFxcnMU+U1KyZEkKFy4MJD2fr7zyCgMGDMBgMHD79u0k8+OndOxp4enpya+//sqdO3do1KgRa9euBeDIkSN07tyZsWPHppibfxbRmzVrRv369Tl06BCBgYF8+umnvP/++wwfPjzJdseOHePUqVPm0e9WVlYEBwcDUK1aNaytH32MKleuzMWLF6lduzZLlizBx8cHLy8vOnTokOZjE3lZ5M3rkNEhpEvu3PYZHYI8I8pl9qFcZh/KpYiIiIhkdhk2J3riwzoTmUwm8+//nKM7saC7bNkywsLCWLlyJUajkffff/+J/SckJCT5PXFEd2LhM6XtEkVGRmJvb4+HhwceHh707NmTxo0b89tvv6XadqIPPviAmTNn4uLiQtGiRSlcuHCyIvo/40hs6/FzkNr869bW1tStW5fdu3ezb98+vvjiC3MR/UkxJj541WAwULBgwacaMZ3S+bxx4waTJ09my5YtFC5cmMWLFyeJLaVjT4vatWtz9OhR/vrrLwYMGMCRI0cICgoiLi7O/MXIk3IDj/Lr4OBA/fr1qV+/Pp07d6ZJkyYMGzYsyXYGg4GRI0dSuXLlJMt//vnnZMdsNBopV64cu3fvJjAwkO3btzN//nw2bNiAjY3NvzpekewoJORBRoeQJlZWRnLnticsLJL4eNOTd5BMS7nMPpTL7CM9ucxqX76KiIiISPaSYdO55M2bF3t7e37//XfKli3Lvn37zOscHR25e/cu8Gg0deIo5fv371OqVCmMRiNHjx7lzz//JCYmBnhU6IyJiUkynYu7uzv79++nUaNGxMTEcPjwYZo3b05kZOQT44uPj6dRo0ZMmjSJmjVrAo9G1UdFReHk5JSmtqtUqcLff//N+vXrzSOj06JUqVIcP36cBg0acObMGW7evJnq9s2aNWPYsGFUrlw5yfGXLFmS+/fvExwcTLFixThw4ECyQjDA66+/TlhYGOfOnaN8+fIcOnSIy5cv07FjxzTHnJKwsDAcHBwoXLgwDx48YM+ePebR6k+jRo0aLF68mNjYWIoVK4aHhwdff/21eWS7pdw87tKlS3z00UesX7/eXHj/448/KF68OAaDwfx+AqhatSo7duygcuXKREZGMmHCBPOI/iNHjhAbG4uVlRUnTpygfv36bNmyBScnJ7y8vPD09KR69eqEhoaSP3/+pz52kewiLi5rFb7i401ZLmZJmXKZfSiX2YdyKSIiIiKZXYZM55I3b15mz57NyJEj6devH/nz58fV1dW8vlWrVvj4+BAcHEzhwoV5/fXXSUhIoHXr1gwaNIjDhw/j4eFBt27dmDx5Mm+88YZ52ozZs2eb2+nYsSOjR4+mffv2xMXF0aJFC9zc3AgMDHxi3FZWVixYsICpU6fi5+eHra0t8fHxjB07lkKFCqW57caNG/Ptt98yZMiQNJ+zjz76iL59+9KhQwcqVKiAs7NzqvPFu7q6YjKZks25niNHDiZNmsSAAQOws7PD1taWsWPHJts/R44cTJ8+nVGjRmFnZ4fJZMLX1zfN8Vri4uJCpUqVaNWqFfnz56dPnz4MHjyYbdu2PVW7OXPmxMrKyvzg02rVqjF8+HDzXO+WcvO40qVLM3z4cPr06YOtrS1WVlbkyJHD/P7x9PRk6tSpWFlZ8fnnnzNq1Cjat29PdHQ0rVq1Mo/iL1GiBJ999hm3b9+mYsWK1KhRg/PnzzNmzBhsbGyIiorik08+UQFdREREREREREQkizIkpDSPSQa4dOkS3bt3Z+/evRkdSoa7fPkywcHB1K1bl4iICBo2bMjGjRspUKBARocmj/Hz8yM6OppBgwY9szajBkx9Zm2JZGbhw3pldAhpYm1tJG9eB0JCHmiUZBanXGYfymX2kZ5cFiiQ6wVFlX3dvh3+QvrRZzRrU/6yNuUva0tP/uzXPt3gxMwosnWTjA7hqejzl7Wl5Vozw6ZzEcty5crFsmXLmDdvHrGxsXz22WcqoIuIiIiIvCAHDx6kdu3aAJw/f56AgABKlSpF27ZtMzgyEREREckImaaIXrp0aY1C//8KFChgnppEMq8+ffpkdAgiIiIi8owtWrSIb775hsDAQO7fv89HH31E2bJl+fHHH7l37x69emWNu6lERERE5NkxZnQAIiIiIiIimcXatWv5+uuvAdi6dStFihTB39+fb775hg0bNmRscCIiIiKSIVREFxERERER+f9u375N5cqVAfjll1+oX78+AMWLF+fvv//OwMhEREREJKOoiC4iIiIiIvL/OTg4EB4eTkxMDIcPH8bT0xOA8PBwbG1tMzg6EREREckImWZOdBGBHDN89CTnbEBP5RYREcm6atasyRdffIG1tTX58uXD3d2duLg45syZQ6VKlTI6PBERERHJABqJLiIiIiIi8v+NHDmSokWLYmNjw9y5cwGIjIzkf//7H0OHDs3g6EREREQkI2gkuoiIiIiIyP+XO3duxo4dm2RZrly52LlzZwZFJCIiIiIZTUV0ERERERF5qc2YMSNN2xkMBvr37/+coxERERGRzEZFdJFMJGrAVOwzOgh5JqJAuXxM+LBeGR2CiIiIRVu2bEnTdiqii4iIiLycVEQXEREREZGX2t69ezM6BBERERHJxFREFxERERGRl9qVK1fSvG3JkiWfYyQiIiIikhmpiC4iIiIiIi+1xo0bYzAYUt0mISEBg8HA2bNnX1BUIiIi8jzYr932zNs0GAzE2FhhGxuPTUKCeXlk6ybPvC/JGCqii4iIiIjIS+3bb799YhFdRERERF5eKqKLiIiIiMhLrXr16hkdgoiIiIhkYtm2iH7jxg0aNWpElSpVAIiNjSV//vz4+vqSL1++NLdTr149Fi1aROnSpdO0fUBAAAcOHGDmzJlJlt++fZuxY8cyZ84chg4dSqVKlahfv7552a1bt7h06RI1a9ZM+0Gm4Pr163zyySfUqFGDcePGmfseMGAAADdv3sRkMlGsWDEApkyZgpOT01P1CeDs7Ey1atUAiIiIoFKlSvTv35+8efNy9uxZ1qxZw5gxYyzuv2HDBpo3b57kPKXXxYsXiYyMxNXVNcnygIAApk2bRpkyZZIs79y5Mw0aNEh3P5YkHoOIiIiIZC1Dhw5l8uTJAAwcODDVbadPn/4iQhIRERGRTCTbFtEBHB0d8ff3N7+eOXMms2fPTlLMNZlMGI3G5x5LgQIFkhWGH18WGBjIhQsXnrqIfuzYMcqXL28uoCf2k3ge/Pz8iI6OZtCgQU/VT0qWLFmCnZ0dCQkJfPPNN3z00UesX78eFxeXVAvo8fHx+Pn50bx58xTPU1rt3r2bPHnyJCuiA9SoUSPZFxvP0uPHICIiIiJZy+3bt82///333xkYiYiIiIhkRtm6iP5P7u7uLFu2DHg0wvy9997j0qVLzJs3jw0bNrBixQpy5MjBK6+8wvjx4ylYsCDwaCTzpUuXuH79On379qVRo0Zcv36d4cOHYzAYePDgAR9//DFNmzYFICwsjIEDB3Lz5k0cHR2ZMWMGISEhtG3blp9++skcz40bN2jbti3fffcds2bNwmQykSNHDlavXs2uXbtwcHAgPj6et99+m3Xr1pnjATh16hSTJk3CaDQSFxfHkCFDcHR0ZMGCBYSGhvLll1/i6+ub6vkIDg7ms88+Y9OmTeZz8sknn9CxY0f27dvHxo0bmTBhAqNHjyY4OJj4+Hi8vLz4/PPPU23XYDDQvXt39uzZw48//oijoyPTp0/n+++/Z8WKFfzwww9YW1tjZ2fHtGnTmDFjBrdu3eKjjz5iwoQJ5vM0ePBgnJycOH/+PFevXqVu3boMGzYMk8nExIkTOXPmDNHR0bRv357SpUuzYsUKcubMib29fZqL2X379qVevXrm7cePH0+RIkVo2bIlX375JaGhocTExNC2bVtatGjBrFmziIiI4NatW1y9epVSpUoxa9Yshg8fbj6GefPmMXjwYO7fv8/Dhw+pVasWgwcPTlM8IiIiIvLiLV682Pz7t99+a3GQTdj/Y+/O42M62z+Of2cmq0S2lpRW7aWIXe1bqnZqJyGeWktri11riaXWWoqqUi1VFKWqqC7qQUutVUpRSy1dNMhCJLLM/P7wmJ8R0aRiJpl83q+XVzLn3Oe+r3OuTEyuuec+sbH2CgkAAABZSI4popvNZn311VeqVKmSdVu+fPk0ZMgQ/fnnn3rzzTe1ZcsW+fj4aPHixZo7d64mTZokSfL29tbChQt18uRJ9ejRQ40aNdLly5fVs2dP1atXT3/88YfatGljLaIfO3ZMX3/9tXLnzq3+/ftr48aNql27dpqxFShQQK1bt9atW7fUr18//fnnn/riiy/Url077du3T6VLl7YpoEvS8OHDNXHiRFWuXFlHjhzR0KFD9dVXX6l3797atWvXPxbQ74x769YtRUdHKyYmRsWKFdO+ffvUuXNn7du3T7Vq1dLy5cvl6uqqVatWKSkpSe3atVP16tVtrmNaypYtq1OnTqlKlSrWbbNnz9bmzZv1xBNPaO/evfrzzz/Vv39/fffdd1q2bJkuXbpkbWsymXTmzBm98847iouLU40aNdS/f3/t3LlTf/zxh1auXKmYmBiFh4dr4cKFql27tsqVK5eh2eAtW7bUJ598olatWslsNuvrr7/WmjVrNHfuXNWpU0ft27fXrVu31LJlS9WsWVMmk0mHDx/WqlWrZDAY1KBBA506dcrmHL7++mvdvHlTK1eulCR9+OGHSkhIkIeHR7rjApyNi8uj/8TPo2AyGW2+Ivsil86DXDqPrJrLIUOGaObMmakK6UeOHFF4eLi2bdvmoMgAAADgKE5dRI+JiVFYWJik28ttVKxYUb1797bur1ixoqTbRe8yZcrIx8dHklS9enVt2rTJ2q5WrVqSbq/7ffPmTUVHR8vf319LlizR4sWLJUlRUVHW9uXKlVPu3LmtY5w+ffqBRfR7hYSE6I033lC7du20efNmtW/f3mb/9evX9ddff1nXIC9btqwiIyP/1cyY6tWr6+DBg7py5YoaN26stWvXSpL279+vrl27avz48XrxxRclSa6urqpcubKOHj2ariK60WiUm5ubzbbmzZurV69eaty4sRo0aKASJUrYFM7vVbVqVRkMBnl7eyt37tyKiYnRjz/+qOeee07S7SV73n///X+M5YcffrD+LNwxb9481alTRxEREbp+/bqOHTumokWLKjAwUIcOHdLRo0ets/RNJpMuXrwoSapcubJcXV0lSXnz5lVMTIy8vLys/ZYrV04zZszQgAEDVKdOHbVq1YoCOnI8f3+vf26Uhfn4eDo6BGQScuk8yKXzyGq5PHPmjF599VW99dZb1teyy5cv17Rp09SiRQsHRwcAAABHcOoi+r1rot/rTiFUkiwWi833d888uft7i8Uig8GgCRMmqEGDBuratauuXbum6tWrW9sYDIZU7TOiTJkySkxM1JkzZ/TTTz9p/PjxNvvvjvVu/2Zt91q1aunAgQP666+/NHjwYO3fv19HjhxRcnKyAgMDU41377V5kH379qVaxmT8+PG6cOGCdu7cqVdffVX9+vWzvhlwPy4utj+iFotFFotFZrM5vaco6cFrogcHB2vbtm06dOiQ9Q0Dg8Gg0aNHq3z58jZtd+/efd+Y7pY3b15t2bJFhw4d0rZt2zR37lxt2LAhQze0BZxNVFSco0P4V0wmo3x8PBUbG6+UlIz93kHWQi6dB7l0HhnJpT3fjF25cqVefvll9e3bV1OnTtXEiRO1a9cuTZo0ifvfAAAA5FBOXURPr6CgIEVERCgmJka+vr7atWuXTfF09+7dKl26tE6cOCEfHx/5+fkpOjpaxYsXlyR99tlnMhqNSkxMlHR7vfIbN27I29tbBw8eVL169f4xBoPBYD1ekjp16qQxY8YoODhYJpPJpq2Pj4/y58+vAwcOqHLlyjpw4IDy588vb2/vDJ97tWrVtGTJEiUlJalAgQKqUqWK3n33XVWrVk3S7Zn0O3fuVOPGjZWYmKh9+/b94x8PFotFS5YskYeHh6pWrar9+/dLuv3JgA8//FCvvvqqunTpops3b1pnld997v+kYsWKWrdunbp166aEhAR17dpVS5cuTXUN06tly5ZatmyZfvnlF40cOVKSVKlSJW3dulXly5dXfHy83njjDY0dOzbNPu7O/3fffafExEQFBwfrueee086dO3Xx4kWK6MjRkpOzd6ErJcWc7c8Bt5FL50EunUdWy6W3t7c++OADDRw4UPXr11fhwoX1ySefqGjRoo4ODQAAAA5CEV1SYGCghg4dqh49esjd3V1+fn6aOHGipNvLwNy8eVN9+vTRxYsXrYXUXr16afz48cqTJ49CQ0NVo0YNDR06VLVr11ZQUJDGjRun33//XQEBAWrRooWuXLnywBiqVKmi8PBweXp6Kjw8XM2bN9ekSZM0bdq0+7afNm2aJk+eLKPRKIvFkma7f+Lt7S2TyaSCBQtKur1UyWuvvaYPPvhAktS5c2eNGzdOISEhSk5OVuvWrVW2bNn79tW9e3cZjUbFxcWpdOnSevfdd21mrfv6+iohIUEdOnSQp+ftj+1OmDBBefPmVf78+dWuXTvrOvQP0rhxYx06dEghISFKSkpSSEiIvLy8VL16dU2fPl0mk0ldunSxOeZ+y7nUqFFDffv2VaVKlTRq1ChVrFhRuXLlkiT169dPY8aMUUhIiG7duqW2bdumWprmbnefw9y5czVq1CgtWbJEycnJql69uoKCgv7xvAAAAOAY95uIMWvWLI0ePVpXrlzRU089ZW3zoNeEAAAAcE4GS1prg8ChvvnmG23YsEHz5893dCiwo4TB0x0dAvBIXB/V19Eh/CsuLkb5+3spKiouS82SRMaRS+dBLp1HRnKZJ0/uRxpLyZIl77sE450/le7e98svvzzSWB6VyMjrdhmH52j2Rv6yN/KXvWUkf55rt9gpKvuJb9/UbmM9iutnMBjk6mpSUlKKzbK/9jwv/Hvpea3JTPQsaODAgbp27Zpmzpzp6FAAAAAApzd58uQM38cIAAAAOQdF9CzorbfecnQIAAAAQI7Rpk0bR4cAAACALMz4z00AAAAAwPnt3LlTp0+ftj7+8ccfFRISokaNGmnBggUOjAwAAACORBEdAAAAQI73zTffqE+fPvr1118lSXFxcerbt6/i4+NVu3ZtLV26VJs2bXJwlAAAAHAElnMBshCPWcO5CYwT4IY+AABkP0uXLtWQIUPUpEkTSbeL6rdu3dKHH34oHx8fVapUSStXrlTz5s0dHCkAAADsjZnoAAAAAHK8M2fOqEOHDtbHP/zwg2rUqCEfHx9JUt26dXXu3DlHhQcAAAAHoogOAAAAIMdLSEhQ7ty5rY9//PFHVahQwfo4V65cunHjhiNCAwAAgINRRAcAAACQ4/n6+ioyMlKSFBkZqd9++00VK1a07r927Zq8vLwcFR4AAAAciCI6AAAAgByvYsWKevfddxUZGalZs2bp8ccfV/ny5a37N2zYoGeffdZxAQIAAMBhuLEokIUkDJ4uT0cHgUyRIOXIXF4f1dfRIQAA8K/07t1bISEhWrFihYxGo6ZMmSKj8facow8++ECzZs3S/PnzHRwlAAAAHIEiOgAAAIAcr2TJktq6dasOHjyoZ555RsWKFbPu8/b21sSJE1WvXj3HBQgAAACHYTkXAAAAAJAUGBiopk2b2hTQJal9+/Zq3br1v+pz165dqlGjhsLDw1Pt27Nnj1q2bKmgoCC98MIL2rhxY5r93Lp1S2PHjtVzzz2nChUqaMCAAbp27dq/igkAAAAZQxEdAAAAAB6BxYsXa9KkSSpYsGCqfZcvX1bfvn3Vrl077du3T6+//rrGjBmjI0eO3LevGTNm6NChQ1q3bp2+/fZbJSYm6rXXXnvUpwAAAABRRAcAAACAR8Ld3V2ffPLJfYvon3/+uQoWLKiuXbvK09NT9erV0/PPP6+1a9emapucnKxPP/1UgwYNUoECBeTv76/hw4dr+/btunz5sj1OBQAAIEdjTXTc16VLl9S4cWNVqFDBZvvgwYN17tw5JSUlqWPHjunq69atWypbtqxOnjyp9evXZ+jYtOzdu1d9+/ZV6dKlJUnx8fEqXry4IiIi5O7u/lB9/xuff/65mjRpIhcXnlIAAAC4rWvXrmnuO378uPW17B3PPvusvvjii1RtL1y4oBs3bti0L1KkiDw9PXXs2DEFBgb+YyxGo0FGoyED0f87JpPR5iuyF/KXvZG/7C0j+TMYHv3vc3tzcbHfz+2juH53urz99f/7t+d54dGi4oc0+fr6avny5am231tYz4g2bdo8TEg2ihUrZo3PYrFo2LBh+vDDD9WrV69/PNZsNstozLxfZPPmzVPDhg0pogMAAGRDR44cUdmyZSVJhw8fVvny5R/5mFFRUSpZsqTNNj8/v/uucx4VFSXp9uvzu/n4+KR7XfSAAC+7Fl18fDztNhYyH/nL3sifYyS+t+7h+5DkkZ6GrqaHHiurcV231Y6DPbrr5+Ji27eXv9cjGwv2RcUPGTZv3jzdunVL4eHhqly5sgYOHKhvv/1Wly9f1oQJE1S1alX99NNPGjt2rPLkyaNy5cpl6NgjR45ozJgx8vHxUZMmTTRx4kQdO3bsgUVvg8GgChUq6JdffpEk7d69W/Pnz5eLi4tMJpMiIiJUsGBBBQcHq1mzZjpz5owWLFigdevWafXq1ZKkoKAgvf7660pMTNSECRN08eJFJSUlKTg4WL1799batWu1d+9eSdLZs2fl5eWlxYsXa9GiRTp//ry6d++ut956S7NmzdKZM2eUlJSkEiVKaOLEiRTXAQAAsrD//Oc/OnDggEwmk/7zn//op59+clgsGS10p7f9tWtxdpuJ7uPjqdjYeKWkmB/5eMhc5C97I3+O5ZaU8lDHGwy3C7DJySmyWDIpKNhNWvmLi4pzXFBIN/90vNlBZQ//mslk0s2bN/XUU0/pww8/1OrVq/XRRx+patWqmjp1qgYNGqT69etr8+bNGTp2ypQp6tevn1544QUtXbo0XbPGExMTtX37djVr1kwJCQkaP368Vq1apYCAAO3YsUNTpkzRwoULJUn58uXTkCFD9Oeff+rtt9/W5s2b5enpqeHDh+vEiRPavXu38ubNq8mTJ8tsNis0NFQ1atSQyWTSgQMHtHnzZnl5ealTp07avXu3BgwYoLffflvvv/++zpw5o/379+vrr7+WJK1fv15RUVHKkydP5icAyIKc7aNqfCTWeZBL50EunUdWymWePHnUvn17FS5cWElJSRoyZEiabWfOnJkpYwYEBCg6OtpmW1RUlAICAlK1feyxxyRJ0dHRypUrl6Tbn8SMjo627vsnZrNFZrP9qjIpKWYlJ1PEy67IX/ZG/hzD9aEr37ff6LRYbv+OR3Zz//zxXHQeFNGRppiYGIWFhdlsmzx5cqp21apVk3S7OH3nD4ETJ06oYsWKkqSaNWumOUZax1apUkWS1KhRI02ZMuW+x54+fdoan9lsVr169dSqVSudOHFCkZGRGjhwoHVfcnKy9bg7cR07dkwlSpSQp+ftj7pNnz5dkjR//nxdvHhRBw8elCTFxcXpwoULkm7PVvfy8rLGHBMTYxNT4cKFlTt3bvXo0UPBwcFq2LAhBXTkKOl59zY74iOxzoNcOg9y6TyyQi7ffPNNLVmyRJGRkZKkv//++5GPGRQUpPXr19tsu3tZmbsVKFBAfn5+OnbsmPLnzy9JOnnypBITE1WmTJlHHisAAEBORxEdaUprTfR7ubq6PnC/2Zz2u25pHXvnY6kPmoF+95ro9x6bN2/eNGO/M6bFYrnvu7sGg0F9+vRRs2bNbLavX78+1bIs9x7v6empdevW6ejRo/rvf/+rVq1a6cMPP1TRokXTPA/AmUQ52UfV+Eis8yCXzoNcOo+M5PJRv0lbtmxZvfXWW5Kkxo0bp+s18MNq0aKF5s2bp6VLl6pTp07673//q507d2rNmjWSbhfUhw8fro0bN8rNzU0dOnTQnDlzVLJkSeXKlUtTpkxR48aN9fjjjz/yWAEAAHI6iuh4JIoVK6bDhw+rbt262rlzZ4aOLVy4sPXYO8uiZEShQoUUGxurEydOqGTJktqzZ4/Onj2rzp0727QrU6aMxo8fr+vXryt37twaMWKE2rZtq0qVKunLL79Us2bNZDabNWnSJPXr1++BYxoMBiUmJurUqVP69ddf1aZNG5UtW1Y//fSTTp48SREdOYazflSNj8Q6D3LpPMil88hqudy69faNzaKionTx4kVJt19f+vj4ZLivoKAgSbJ+KvKbb76RJB09elSPPfaY3n33XU2cOFEzZ85U/vz5NXPmTOvNRuPj43Xu3DnrpI3+/fsrLi5Obdq0UUpKiurXr6+IiIiHOlcAAACkD0V0PBIjRozQ+PHj9eGHH6py5cpycXF54Iz0uw0bNkwTJkzQsmXLVKdOHZlMGbtrsoeHh2bOnKkxY8bI3d1dZrNZEyZMSNUuX758Cg8PV7du3WQymVS6dGlVqVJF5cqV0+nTp9WhQwelpKSodu3a912b8m516tRRaGioZsyYoW+++UZr166VwWBQYGCg6tevn6H4AQAA4Dg3b97U0KFD9d///tf6yUWTyaQmTZpo8uTJcnNzS3dfR48efeD+ypUr67PPPrvvvqpVq+rkyZPWx25ubho7dqzGjh2b7vEBAACQOQwW7laALOaHH36Qn5+fSpYsqUOHDmnMmDH3vTmpM0oYPN3RIQAP5fqovo4OIVO5uBjl7++lqKi4LDVLEhlHLp0HuXQeGcllnjy57RSVNGnSJO3du1cvv/yyChUqJIvFotOnT+vdd99Vo0aNFB4ebrdYMlNk5HW7jMNzNHsjf9kb+XMsz7VbHup4g8EgV1eTkpJSuLFoNpRW/uLbN3VgVEiv9LzWZCY6shwXFxe99tpr8vDwUFJSksaPH+/okAAAAJBD7Ny5U4sWLVKhQoWs24KCglS6dGn1798/2xbRAQAA8O9RREeWU7lyZa1fv97RYQAAACAHunr1qp566qlU24sUKaK//vrLAREBAADA0YyODgAAAAAAsoqnnnpK33//fart33//vQIDAx0QEQAAAByNmegAAAAA8D9dunTRoEGD1LRpUxUrVkwGg0G//vqrtmzZosGDBzs6PAAAADgARXQAAAAA+J/27dvL1dVVH330kb7++mtJUqFChTRp0iQ1a9bMwdEBAADAESiiA1mIx6zh3EndCbi4GOXv70UuAQDIplq1aqVWrVo5OgwAAABkEayJDgAAAAAAAABAGiiiAwAAAAAAAACQBoroAAAAAAAAAACkgSI6AAAAAAAAAABp4MaiQBaSMHi6PB0dBDJFgpTjcnl9VF9HhwAAwENJTk5W69at9fnnnzs6FAAAAGQhzEQHAAAAAEkuLi6yWCz69ddfHR0KAAAAshBmogMAAADA/7Rt21aDBg1SrVq19NRTT8nNzc26z2AwqEOHDg6MDgAAAI5AER0AAAAA/mfatGmSpDNnzqTaRxEdAAAgZ6KIDgAAAAD/c+LECUeHAAAAgCyGNdEfwo4dOxQSEqKwsDC1bdtWAwcOVHR09AOPGTlypFatWnXffRs2bMiUuC5fvqzdu3dLktavX6/Vq1c/VH9ms1lVq1bVxYsXrdvmz5+vLl262LSrV6+eTpw4oeDg4PvO3EnL+vXrFR4enu728+bNU7Vq1Wyu9aVLlxQWFpbuPtJy6dIl1axZ86H7AQAAQPb2+++/a+/evY4OAwAAAFkARfR/KTExUUOGDNGcOXO0fPlyrVu3Tk899ZQ++eSTf9Xf5cuXtXLlylTbzWZzhvvau3evtYjepk0bdezY8V/FdIfRaFT16tX1/fffW7ft3r1bV65c0c2bNyVJZ8+eVXJyskqWLPlQY6VX/vz5NXPmTLuMlV4Wi8XRIQAAAOAhRUdHq0ePHnr++efVo0cPSVJkZKRatGihy5cvOzg6AAAAOALLufxLiYmJSkhI0I0bNxQYGChJGjZsmHV/cHCwFi9erKJFi+rMmTPq1auXvv32W0nSzz//rFdffVUXL15U7dq1NWzYMA0ZMkSnT5/WkCFD1KFDB7399tvy9fVV1apV1bRpUw0fPlwJCQm6efOmmjdvru7du0uS3nnnHe3YsUPJycl6/vnn1bx5c82ZM0dms1l+fn6Kj4/XrVu3NHToUO3cuVPz58+Xm5ubDAaDIiIiVLRoUYWGhqpu3bo6ePCgzp07p9DQUHXr1s3mfGvXrq0dO3aoU6dOunHjhq5fv67q1atr//79qlu3rvbs2aPatWtb22/dulU//vijzp07p8GDB6tZs2a6du2aXn/9dcXGxurWrVvq0KFDqjUljx8/rqlTp0q6/QbC8OHDVbZs2VTXPyQkRCtXrtRPP/2kcuXK2ewbOXKkypUrp5CQEElSzZo1tXr1au3Zs0d79uyRm5ubfvzxRzVu3Fgmk0m7d+9WYmKili1bZu1j2rRpOnHihGJiYjRjxgwVLVo0zdjCwsJUokQJnT59WgsWLNCkSZN05swZJSUlqUSJEpo4caJcXHiqAQAAZAdvvvmmzGaz1q5dq86dO0uScufOrWeffVZvvPGG5s6d6+AIAQAAYG9U9v4lb29vDRw4UO3atVO5cuX03HPPqVGjRipatOg/HvvXX3/pvffeU2Jiol544QW1adNG/fv318yZMzVz5kzt3btXR48e1bZt2xQQEKATJ07oxRdfVIsWLXTr1i1Vq1ZNHTp00JkzZ/T1119r7dq1MpvNevXVV9W2bVu1bt1at27dUs+ePTVv3jxJUnx8vEaNGqWPP/5YBQoU0JYtWzRp0iR98MEHMplMunr1qhYtWqRz586pU6dO9y2iT5s2TSkpKdq7d68qVaqkqlWr6vvvv1fdunW1e/duNWvWzNrey8tL7733nr777jvNmjVLzZo101tvvaWgoCC98sorunHjhpo0aZJq6ZQRI0Zo7ty5Kly4sM6cOaNBgwbp888/T3UNTSaTxo4dq/Hjx2vt2rXpypnJZNJPP/2kLVu2KDo6WvXq1dOiRYs0YMAAdevWTbt27VLZsmV15coVNWjQQCNGjNDy5cs1f/58zZ49+4Gxubu7a+nSpTp+/Lj279+vr7/+WtLtpWqioqKUJ0+edMUIZGcuLs734SaTyWjzFdkXuXQe5NJ5ZNVc7t69W6tWrVJgYKAMBoMkycPDQ6NGjVKTJk0cHB0AAAAcgSL6Q+jVq5fatWun3bt3a+/everYsaP69eunl1566YHH1ahRQwaDQe7u7ipVqpROnz4tPz8/mzYFCxZUQECAJMnf31/bt2/XqlWrZDKZlJSUpOjoaP3444+qWLGiTCaTTCaTFi1alOaYv/32mwICAlSgQAFJUvXq1TV27Fjr/mrVqkm6vUzK/dZ1z5s3r5544gn9/PPP2r17t6pXr67nnntOCxYsUEpKig4cOKA33njD5hwlKV++fNb+Dh8+bG3j7e2t0qVL65dffrEeExsbq3PnztnElZCQoKSkJLm6uqaKqUKFCipZsqQ++ugjPf/882me+91Kly4td3d3BQYGymw2q2LFipKkwMBAxcbGSpJy5cqlSpUqSZIqVqyoNWvWPDC2O7FIUuHChZU7d2716NFDwcHBatiwIQV05Bj+/l6ODuGR8fHxdHQIyCTk0nmQS+eR1XJ59erV+75+8/T0VEJCggMiAgAAgKNRRH8I8fHx8vf3V7NmzdSsWTM1bdpU48aNS1VET0lJsXlsNNrOtrkzw+Vubm5u1u/nzJmjPHnyaNasWTKbzapcubJ1X0bW4b67rcVisYkjPcuN1K5dW3v37tW+ffvUv39/+fn5yWQyadeuXSpUqJDNGwEmkyldMdx97kajUUajUcuXL0/3OQ0dOlTt27e3WfLl3uuZnJycZlx3n/ed2O4+/s62f4rtTr48PT21bt06HT16VP/973/VqlUrffjhh+n6hAKQ3UVFxTk6hExnMhnl4+Op2Nh4paRk/B4VyDrIpfMgl84jI7m05xu1hQsX1n//+18FBwfbbP/4449VuHBhu8UBAACArIMi+r+0a9cuTZs2TatWrVLu3LklSX/88YcKFiwoSfLz89O1a9dUtGhR/fTTTzbH7tmzRy+99JJu3bql48ePa+TIkYqMjFRiYuJ9x4qOjrbOmN68ebPMZrMSExNVvnx5ffLJJ0pMTJSLi4t69eqlcePGyWAwpOqrcOHCio6O1sWLF1WgQAHt2rVL5cuXz9A5165dW3PnzpW7u7u1YF6tWjUtXrzYZj30tFSsWFE7d+5UUFCQYmNjdfz4cZUuXdp6E1Rvb28VLFhQ3377rYKDg3X69Glt3rxZAwcOTLPPgIAA9e7dW9OmTbPOVvf19dXVq1cl3Z6Bf7+Z9Q8SFxenI0eOqGzZsjp48KBKliyZ7tiOHj2qX3/9VW3atFHZsmX1008/6eTJkxTRkSMkJztvMSslxezU55eTkEvnQS6dR1bL5SuvvKJBgwYpODhYKSkpmjBhgo4dO6ajR49qzpw5jg4PAAAADkAR/V+qXbu2zp8/r5deekm5cuWSJD322GOaOHGiJKlHjx6aPHmygoKClC9fPpnNt/8wSElJUf78+dW/f39duHBB7dq1U6FCheTr66vY2Fh169ZNffr0sRmra9eumjBhgjZs2KAmTZqoZcuWGjZsmNatW6fmzZurS5cuMpvNev755/X000+rSpUqCg8Pl6enp3WmtYeHh6ZMmaLBgwfL3d1dbm5uGj9+fIbOuVKlSjpx4oT1hp3S7SL6+++/r+HDh//j8f3799frr7+uzp07W292+sQTT9i0mTZtmiZNmqQlS5YoKSkpXf126NBB69atsz5u27athg8frosXL+qJJ55QoUKF0j1j32w2K3/+/Priiy80e/ZsXb9+XW+++Wa6YytUqJDeeecdrV27VgaDQYGBgapfv366xgYAAIDjNWzYUB999JGWLl2qYsWK6fDhwypcuLBef/31+97wHgAAAM7PYMnIeiAAHqmEwdMdHQLwr10f1dfRIWQ6Fxej/P29FBUVl6VmSSLjyKXzIJfOIyO5zJMnt52icl6RkdftMg7P0eyN/GVv5M+xPNdueajjDQaDXF1NSkpKydDSvcga0spffPumDowK6ZWe15rMRAcAAACA/0lJSdHSpUv17bff6s8//5S7u7vy58+vZs2aqU2bNo4ODwAAAA5AER0AAAAA/mf69Olas2aN6tatq/Lly8tisejSpUuaMGGCzp8/r/DwcEeHCAAAADujiA4AAAAA/7NlyxYtXrxYlStXttm+d+9eDR06lCI6AABADmR0dAAAAAAAkFUkJCSofPnyqbZXrFhRN2/etH9AAAAAcDiK6AAAAADwPw0aNNC3336bavvOnTsVHBzsgIgAAADgaCznAmQhHrOGcyd1J+DiYpS/vxe5BAAgm5g/f771+8cff1xjx47V+vXrVaRIERmNRp07d04HDx5Uly5dHBglAAAAHIUiOgAAAIAcbf369TaPc+XKpVOnTunUqVM229auXat+/frZOzwAAAA4GEV0AAAAADna/ZZvAQAAAO6giA4AAAAA97h27ZoSEhJSbc+fP78DogEAAIAjUUQHspCEwdPl6eggkCkSJKfO5fVRfR0dAgAAj8SWLVs0fvx4xcbG2my3WCwyGAz65ZdfHBQZAAAAHIUiOgAAAAD8z/Tp09WoUSM9//zz8vR05rfEAQAAkF4U0QEAAADgf2JiYjRu3DiZTCZHhwIAAIAswujoAAAAAAAgq6hdu7aOHj3q6DAAAACQhTATHQAAAAD+JyIiQv/5z39UpkwZ5c+fXwaDwWZ/v379HBQZAAAAHIUiOgAAAAD8z8SJE/Xrr7/q6tWr8vDwsNlnMBgoogMAAORAFNH/pUuXLqlx48aqUKGCJCkpKUmPP/64JkyYoICAgHT3ExwcrMWLF6to0aLpar9+/Xrt2rVLs2fPttkeGRmp8ePHa/78+Ro5cqTKlSunBg0aWLddvnxZZ86cUY0aNdJ/kvdx4cIF9ejRQ9WqVdPEiRMzfPxvv/2mSZMm6datW0pJSZEkjR49WqVKldKiRYtUtGhRPf/88xnu9845h4SEpKt9WFiYoqOj5efnZ7N98eLFqf5Y+rcy65oDAADAfrZv366PPvpIlStXdnQoAAAAyCIooj8EX19fLV++3Pp49uzZmjt3riIiIqzbzGazjMZHv/R8njx5NH/+/DS37d27V6dOnXrogu6hQ4dUsmTJf1VAl25/PLZDhw5q2rSpJGnbtm2aP3++FixYoN69ez9UbBk1bNgw1alT55H1n1nXHAAAAPbj7++vsmXLOjoMAAAAZCHcWDQTVaxYUefPn5d0e4b5zJkzrR/33LBhg9q1a6cuXbqod+/e+vvvv63HrV+/Xn369FHTpk21detWSbdnfHfp0kVhYWFq06aNNm7caG0fGxurIUOGqFOnTnr55ZcVFxenS5cuqWbNmjbx3Nl28eJFzZkzR5s2bdL8+fNVs2ZNxcXFSZJSUlJUp04dm3gk6ejRowoNDVWXLl3UqVMn/fjjjzp79qwWLlyoAwcOaOzYsTbtExISFB4ertDQULVr105Tpky57zWKjY3VjRs3rI+ff/55LViwQNLt2eSrVq3ShQsX1KxZM82ePVuhoaFq1qyZfv31V0nSf//7XzVv3lwvvfSSli1bpnr16qUaY9OmTerUqZO6du2qvn376urVq2lkLLWYmBhVrVpVCQkJkqTk5GTVrFlTkZGR2r17t0JDQ9W1a1d169bNmuu6detq2bJl6tGjhxo0aKDNmzfbXPP33nsv3eMDAADAscLDwzVnzhzFx8c7OhQAAABkEcxEzyRms1lfffWVKlWqZN2WL18+DRkyRH/++afefPNNbdmyRT4+Plq8eLHmzp2rSZMmSZK8vb21cOFCnTx5Uj169FCjRo10+fJl9ezZU/Xq1dMff/yhNm3aqGXLlpKkY8eO6euvv1bu3LnVv39/bdy4UbVr104ztgIFCqh169a6deuW+vXrpz///FNffPGF2rVrp3379ql06dLKmzevzTHDhw/XxIkTVblyZR05ckRDhw7VV199pd69e2vXrl2aMGGCTfsrV66oSpUqCg0NlcViUcOGDdW2bVs988wzNu2GDBmiYcOG6eOPP9Zzzz2n+vXrq2rVqjZtjEajzp49q0aNGik8PFyzZs3SunXrNHLkSI0bN07vvPOOSpUqpTfeeCPVLP/Lly9r4cKF+uSTT+Th4aGVK1fqnXfe0ejRo9OVR19fX1WuXFk7duxQo0aN9P333+vZZ59V7ty5NX78eK1atUoBAQHasWOHpkyZooULF8pkMkmSlixZou+++06zZs3S+vXrrde8Z8+e6RobyE5cXHLGe7Amk9HmK7Ivcuk8yKXzyKq5XLJkiX7//XctW7ZMfn5+qW4s+t133zkoMgAAADgKRfSHEBMTo7CwMEm3Z3RXrFjRZkmSihUrSrpd9C5Tpox8fHwkSdWrV9emTZus7WrVqiVJKlGihG7evKno6Gj5+/tryZIlWrx4sSQpKirK2r5cuXLKnTu3dYzTp08/sIh+r5CQEL3xxhtq166dNm/erPbt29vsv379uv766y/rOpBly5ZVZGSkYmNj0+zTz89Px48fV0hIiFxcXHT16lWbmO+oWbOm/vvf/2rv3r3at2+fRo0apWeffVZvv/22TTsfHx+VKlVK0u03I44cOaKoqChdv37dur1Ro0batm2bzXFHjx7V1atX1atXL0m316q/9w2CO2bMmGG9vpIUFBSk4cOHq0WLFtq6dasaNWqkLVu26MUXX9S5c+cUGRmpgQMHSrr9pklycrL12GrVqlljjY6OTvM6Ac7C39/L0SHYlY+Pp6NDQCYhl86DXDqPrJbLevXqydXV1dFhAAAAIAuhiP4Q7l0T/V53v/i2WCw23989g/ru7y0WiwwGgyZMmKAGDRqoa9euunbtmqpXr25tc/dsmDvtM6JMmTJKTEzUmTNn9NNPP2n8+PE2+++O9W4PWtt96dKlio2N1YoVK2Q0GtW8efP7touPj5enp6dq166t2rVrq0+fPqpVq1aq5WRcXGx/NC0WS6pzTeu8S5UqpSVLlqQZ6x1prYlev359TZ48WTExMfrhhx8UERGh8+fPK2/evGnm+954AWcXFRXn6BDswmQyysfHU7Gx8UpJMTs6HDwEcuk8yKXzyEgu7fnmbXh4uN3GAgAAQPZA5c8OgoKCFBERoZiYGPn6+mrXrl0qX768df/u3btVunRpnThxQj4+PvLz81N0dLSKFy8uSfrss89kNBqVmJgo6fZs6xs3bsjb21sHDx6877rg9zIYDNbjJalTp04aM2aMgoODrcuR3OHj46P8+fPrwIEDqly5sg4cOKD8+fPL29s7zf6jo6NVpEgRGY1GHThwQH/++afNeNLtmfsvvPCCVqxYYT23yMhIubu7y9/f/x/Pwd/fX25ubjp9+rSKFSumb775JlWboKAgvf766/r777+VN29ebdq0Sa6urmrUqNE/9n+Hu7u7atWqpdmzZ6tGjRry9PRUoUKFFBsbqxMnTqhkyZLas2ePzp49q86dO6fZz73XHHAmyck5q3CVkmLOcefsrMil8yCXziOr5XL+/Plp7ktJSbF+MhEAAAA5B0V0OwgMDNTQoUPVo0cPubu7y8/PTxMnTpR0+4X4zZs31adPH128eNF6w85evXpp/PjxypMnj0JDQ1WjRg0NHTpUtWvXVlBQkMaNG6fff/9dAQEBatGiha5cufLAGKpUqaLw8HB5enoqPDxczZs316RJkzRt2rT7tp82bZomT54so9Eoi8WSZrs72rdvr6FDh2rfvn2qUqWKevbsqalTp6p06dIKCAiQdHvm/ltvvaVx48bJYDDIxcVFBoNBCxYsSNdHZg0Gg1577TW9+uqrKlCggCpVqpTqDYDAwECNGTNGffr0kaenp9zd3dO8yem9y7lI0ogRI1SmTBm1aNFCPXr0sM5o9/Dw0MyZMzVmzBi5u7vLbDanWhf+XvdecwAAAGR9H3/8sc1js9ms6Ohoubu76+mnn6aIDgAAkAMZLGmt3QGn9s0332jDhg0PnGmTFX3zzTcqUaKEChQooM8//1yff/65Fi1a5OiwMk3C4OmODgFIl+uj+jo6BLtwcTHK399LUVFxWWqWJDKOXDoPcuk8MpLLPHly2ymq+7t586bmzZun0qVLp7lsYVYXGXndLuPwHM3eyF/2Rv7uz3PtFkeHkC4Gg0GuriYlJaWkucwusi5H5y++fVO7j+lM0vNak5noOdDAgQN17do1zZw509GhZJjZbFb//v3l5eWVrtngAAAAwMPKlSuXhg8frjZt2mTbIjoAAAD+PYroOdBbb73l6BD+tYYNG6phw4aODgMAAAA5jMFgUFRUlKPDAAAAgANQRAcAAACA/1m9enWqbUlJSdqxY4f8/f0dEBEAAAAcjSI6AAAAAPzPuHHjUm1zd3dXsWLFFBERYf+AAAAA4HAU0YEsxGPWcG4C4wS4oQ8AANnXiRMnHB0CAAAAshijowMAAAAAAAAAACCrYiY6AAAAgBwvLCxMBoPhgW0MBoOWLVtmp4gAAACQVVBEBwAAAJDjVa1aNc19ZrNZ69ev1+XLl+0YEQAAALIKiugAAAAAcrx+/frdd/vp06f12muvSZIWLlyYaePt379f3bt3T7U9MTFR3377rZ588knrtnXr1un111+Xq6urTdvt27fr8ccfz7SYAAAAcH8U0YEsJGHwdHk6OghkigTJKXN5fVRfR4cAAIBdpKSkaOHChVq0aJFat26t999/X97e3pnWf5UqVXT06FGbbStWrNCmTZuUP39+m+3Xr19XjRo19P7772fa+AAAAEg/iugAAAAAcJeff/5Zr732mm7duqX33ntPVapUeeRjXr16VXPnztWyZctSrc0eExMjX1/fRx4DAAAA7o8iOgAAAADo9lIqc+bM0fLlyxUWFqaBAwfK3d3dLmO/9dZbatSokUqWLJlqX2xsrM6fP682bdro/PnzevrppzVo0CDVrVs33f0bjQYZjQ++cWpmMJmMNl+RvZC/7I383d8/3TQ6q7gT5u2v2SNm/D9H58/Fhef9o0YRHQAAAECOt3//fo0ePVqenp76+OOPVbp0abuNfenSJX3++efaunXrfff7+fkpICBAQ4cO1dNPP601a9bo1Vdf1WeffaaiRYuma4yAAC+7FpJ8fJxxYbucg/xlb+TPVqKrydEhZIiLS/aKF7YclT8vfy+HjJuTUEQHAAAAkON17dpVAQEBatKkibZv367t27fft11aNyB9GCtWrFD9+vUVGBh43/39+/e3efzSSy9p06ZN2rhxo8LDw9M1xrVrcXabie7j46nY2HilpJgf+XjIXOQveyN/9+eWlOLoENLFYLhdgE1OTpHF4uhokFGOzl9cVJz9B3Ui/ul4E4IiOtLt0qVLaty4sSpUqCBJSkpK0uOPP64JEyYoICAgzeMuX76sM2fOqEaNGoqPj9eOHTvUuHHjTI+vRIkSqly5sozG//8IS9euXeXu7q4jR45k6A+emjVravXq1Tp79myGjwUAAED2U7lyZUnSwYMH02zzqGZyf/HFFxo9enSGjnnqqacUGRmZ7vZms0Vms/3+qk9JMSs5mSJedkX+sjfyZ8s121Skb/8fY7FIlmwTM/6fY/PHc/7Ro4iODPH19dXy5cutj2fPnq25c+cqIiIizWP27t2rU6dOqUaNGjp+/Li+/PLLdBfRzWazTVH8n7z//vv3XbeyTp066e7j3uP+7bEAAADIPu5+jWtPp0+f1l9//aVq1aql2ebdd99VUFCQatSoYd127ty5RzIxBQAAAKlRRMdDqVixopYuXSpJ2r59uxYsWCAPDw8lJiYqIiJC3t7emjNnjsxmszw9PfX5558rJiZGU6dO1YgRIzR9+nQdOXJEFotFpUqV0uuvv659+/bp7bfflq+vr6pWraorV67oxo0bunz5sn777TcVKVJEc+bMSfdMoPXr12vXrl2aPXu26tatq+7du2vnzp06f/68wsPD1axZM50/f17Dhg2Tl5eXzc2c0nPshQsXNHToULm6uqp69er69NNPtXTpUhUoUOBRXHIAAAA4kePHjytfvnzy9va22d64cWNNmjRJlStXVlRUlCZMmKCFCxcqf/78WrFihS5cuKA2bdo4KGoAAICchSI6/jWz2ayvvvpKlSpVkiTFxMRo8uTJKl68uDZs2KB33nlHc+fOVevWrXXr1i29+uqrypcvn3bt2qWRI0fqiy++0JUrV7RixQpJUnh4uL7++mv5+vrq6NGj2rZtmwICAjRv3jwdPnxYq1atksFgUIMGDXTq1CmVKFEiwzGbTLdv8LBkyRJ99913mjVrlpo1a6Z58+apZcuW6tKliw4fPqz3338/3cfOnTtXTZs21UsvvaSvvvpKv//+e7a5+zeQUTntjt8mk9HmK7Ivcuk8yKXzIJe3RUZGys/PL9X2c+fO6ebNm5KkwYMHy2w2q0uXLoqPj1eJEiW0dOnSNNdQBwAAQOaiiI4MiYmJUVhYmCQpJSVFFStWVO/evSVJuXPn1oQJE2SxWBQTE3PfPwbudujQIR05csTa340bN3Tp0iX5+vqqYMGCNuusV65cWa6urpKkvHnzKiYm5r59du/e3Wb5l8GDB6dqc+ejsvny5VN0dLQk6eTJk+revbskqXz58vLyuv8NBdI6tlu3bpKk559/Xi4uPK3gvNJzsw1n5OPj6egQkEnIpfMgl84jp+eyR48e6tGjR6rtJ0+etH7v5uam1157Ta+99po9QwMAAMD/UO1Dhty7JvodiYmJGjx4sFavXq2SJUtq69at1hnmaTEYDGrbtq21CH/H3r175ebmZrPt3sJ0WjdpuN+a6OfOnXtgX/frz2y+/w0Z0jr2zsxzg8HALHQ4tagcdsdvk8koHx9PxcbGKyWFG7VkZ+TSeZBL55GRXObUN3EBAACQNVBER6aIj4+X2WxWwYIFlZycrC1btigxMVHS7cLyne+NRqP1+0qVKun9999Xt27d5Orqqjlz5qhFixYOib9YsWL68ccfVapUKe3fv1/x8fHpPrZIkSLWY7dv367k5ORHGCngWDn1jt8pKeYce+7Ohlw6D3LpPMglAAAAsrqcvQAhMo2vr686duyoTp06qWfPnuratav++usvLV26VFWqVNGmTZs0e/ZsBQUF6aefftKoUaPUsGFDVapUSSEhIWrfvr2uXbumggULOiT+/v37a926dXrppZf05Zdf6umnn05ztvu9XnnlFa1evVphYWE6ceKE/P39WdIFAAAAAAAAcBIGS3orhQDu6+jRo7p165YqV66sP//8Uy1bttQPP/xgvRFpRiQMnv4IIgQyz/VRfR0dgl25uBjl7++lqKg4Zklmc+TSeZBL55GRXObJk9tOUTmvyMjrdhmH52j2Rv6yN/J3f55rtzg6hHQxGAxydTUpKSkl3ZP6kHU4On/x7ZvafUxnkp7XmkyXBR5Srly59MYbb0i6vTb8G2+88a8K6AAAAAAAAACyHorowEMqWrSoPv74Y0eHAQAAAAAAAOARYE10AAAAAAAAAADSwEx0AAAAAAAA2E12WaccAO5gJjoAAAAAAAAAAGlgJjqQhXjMGs6d1J2Ai4tR/v5e5BIAAAAAAMAJMBMdAAAAAAAAAIA0UEQHAAAAAAAAACANFNEBAAAAAAAAAEgDa6IDWUjC4OnydHQQyBQJklPm8vqovo4OAQAAAAAAwK6YiQ4AAAAAAAAAQBooogMAAAAAAAAAkAaK6AAAAAAAAAAApIEiOgAAAAAAAAAAaaCIDgAAAAAAAABAGiiiI1Ncu3ZNZcqU0bx582y2BwcH68yZMw/df3r6+fzzz5WcnJyu/vbs2aMWLVqk2v7RRx9p0KBBaR5XokQJ3bp1K11jAAAAAAAAAMj+KKIjU2zYsEGFCxfW+vXrZTabHRLDvHnzlJKSkq621apVU3x8vH7++Web7Z9++qnatWv3KMIDAAAAAAAAkA1RREem+OSTTzR06FC5ublpz549NvvWr1+vPn36qGnTptq6dask6YsvvlDbtm0VEhKijh076tSpU5JuF+PbtWunLl26qHfv3vr7779T9RUeHm59HB4ervXr12vu3Lk6f/68unfvrmvXrmn37t0KDQ1V165d1a1bN50/f96mH4PBoLZt2+rTTz+1bjt9+rSioqJUvXp1TZgwQR07dlSnTp00ZMiQVDPc582bpzfffNP6uEOHDtq7d68k6YMPPlCnTp0UFhamYcOGKT4+/t9eVgAAAAAAAAAO5uLoAJD9HTx4UNevX1etWrXUtm1bffLJJ6pZs6Z1v7e3txYuXKiTJ0+qR48eatSokd555x2NHTtWlStX1i+//KK//vpLuXPn1ptvvqktW7bIx8dHixcv1ty5czVp0qR/jGHAgAF6++239f7778tisWj8+PFatWqVAgICtGPHDk2ZMkULFy60OaZNmzZq3bq1RowYITc3N61fv15t2rTRjRs3lD9/fo0ePVpGo1Fdu3bVrl27VL9+/X+M48iRI/r222+1cuVKGY1GzZgxQx9//LG6deuW8QsLZEEuLjnrvVeTyWjzFdkXuXQe5NJ5kEsAAABkFxTR8dA++eQTtW7dWiaTSW3atNELL7yg6Oho+fn5SZJq1aol6fZ64jdv3lR0dLSaNWumUaNGqUmTJgoODladOnX0zTffqEyZMvLx8ZEkVa9eXZs2bcpwPOfOnVNkZKQGDhwoSTKbzfddKz0wMFBly5bV9u3b1aBBA23evFmrVq2Sl5eXrly5os6dO8vFxcU6Qz09Dh06pIsXL+o///mPJCk+Pl4VK1bM8DkAWZW/v5ejQ3AIHx9PR4eATEIunQe5dB7kEgAAAFkdRXQ8lBs3bmjr1q164okntGPHDkmS0WjU559/rrCwMOvjOywWiwwGg15++WW1atVKO3fu1NixY9WkSRMVL15cFovFpu3dx0q3l2G52/3WQDcYDMqbN6+WL1/+j/G3a9dOn3zyiXLlyqVixYopf/782rBhgw4fPqwPP/xQbm5uevnll+87xt3uFOkNBoPq1KmjCRMm/OPYQHYUFRXn6BDsymQyysfHU7Gx8UpJccz9HpA5yKXzIJfOIyO5zKlv4gIAACBroIiOh7Jp0yZVqlRJ7733nnXb9u3b9dZbb1mL6Lt371bp0qV14sQJ+fj4WJdtGTBggNq3b69cuXLp008/VZs2bRQREaGYmBj5+vpq165dKl++vM14vr6+unr1qiQpKSlJx44dU7169STdLmAnJiaqUKFCio2N1YkTJ1SyZEnt2bNHZ8+eVefOnVPFX69ePU2ePFlr165V+/btJUnR0dEqUKCA3NzcdObMGR07dizVUi6+vr76/fffJUkxMTH67bffJEkVK1bUsmXLdOPGDXl7e2vZsmUqXbq0Kleu/NDXGsgKkpNzZsEqJcWcY8/d2ZBL50EunQe5BAAAQFZHER0P5ZNPPtErr7xis61u3bqaOHGijh07ppSUFN28eVN9+vTRxYsXNXbsWJlMJj3++OMKCQlRrly5lJSUpBEjRigwMFBDhw5Vjx495O7uLj8/P02cONGm7xo1auiDDz5Q//799dhjj+nZZ5+V2Xz7j646deooNDRUc+fO1cyZMzVmzBi5u7vLbDanOTPcxcVFzZs315o1a6w3Cm3SpIk2btyokJAQFS9eXOHh4VqwYIGqVKliPa5x48bauHGjhgwZooCAAJUqVUpms1lBQUHq2rWrunbtKjc3N+XNm1dt27bNzEsOAAAAAAAAwI4MlrvXzwDgUAmDpzs6BOCBro/q6+gQ7MrFxSh/fy9FRcUxSzKbI5fOg1w6j4zkMk+e3HaKynlFRl63yzg8R7M38pe9Zaf8ea7d4ugQshyDwSBXV5OSklJEqS77cXT+4ts3tfuYziQ9rzWN/9gCAAAAAAAAAIAciiI6AAAAAAAAAABpoIgOAAAAAAAAAEAaKKIDAAAAAAAAAJAGF0cHAOD/ecwani1uAoMHy0439AEAAAAAAMCDMRMdAAAAAAAAAIA0MBMdAAAAAAAAALIpz7Vb7DZWfPumdhsrK2EmOgAAAAAAAAAAaaCIDgAAAAAAAABAGljOBchCEgZPl6ejg0CmSJCcLpfXR/V1dAgAAAAAAAB2x0x0AAAAAAAAAADSQBEdAAAAAAAAAIA0UEQHAAAAAAAAACANFNEBAAAAAAAAAEgDRfRHICoqSqNGjdKLL76oTp06KSQkRF9++WW6jt25c6fCwsIyNF54eLj++OOPfxOqjZEjR6pRo0YKCwtTly5d1KZNG23cuPFf9bVo0SJt27ZNkvT5558rOTlZkhQWFqbExMRMi7NNmzbq1q2b9u3b96/6ioyMVL9+/R4qHkm6dOmSatas+dD9AAAAAAAAAMhaXBwdgDPq37+/GjZsqClTpkiSLl++rF69eslisahx48bWdhaLRQaD4aHGslgsmj179kP1cbeXXnpJISEhkqSrV6+qefPmqlq1qgIDAzPUT+/eva3fz5s3Tw0bNpSLi4uWL1+e6XEeO3ZMr776qmbNmqWKFStmqJ88efJo/vz5mRITAAAAAAAAAOdDET2T/fDDD4qPj1fXrl2t2wIDAzV8+HDNmjVLjRs3VlhYmEqUKKHTp09r4cKF+uyzz7R8+XIVLFjQplh98eJFjR8/XomJiUpMTFTfvn1Vt25djRw5Um5ubrp06ZIiIiL00ksvafHixTp06JD27t0rSTp79qy8vLy0ePFieXh4aP78+fryyy/1xBNPqEyZMvr99981ffr0B57LY489pieffFKXLl1Snjx5NHXqVP30008yGAwqWbKkxowZo4SEBA0bNkzR0dG6efOmatasqWHDhmnkyJEqV66cIiMjdf78eXXv3l3z5s1T9erVtWvXLrVo0UI7duyQh4eHkpOTVbduXW3YsEG//vqr5s+fLxcXF5lMJkVERKhgwYIPjLN06dLq27evFi1apIULF+r48eOaOnWqJMlsNmv48OEqW7asfvvtN0VERCglJUUWi0VvvPGGTCaTOnbsqO+//17Dhg3T448/rt9//10///yzBg8erB07dujEiRMqVaqUpk2bpoSEBI0aNUqXL19WYmKiKlWqpFGjRtnE88UXX+i9996Tm5ubzGazJk6cqGeeeSZDP0cAAAAAAAAAsgaK6Jns1KlTKlOmTKrtZcuW1alTp2SxWCRJ7u7uWrp0qW7cuKFZs2bpiy++UEBAgMaOHWs9ZuLEierRo4eqV6+ua9euqV27dtq6daskKTY2Vu+//77NGCaTSQcOHNDmzZvl5eWlTp06affu3SpSpIhWr16tr776Si4uLurUqZOKFy/+j+dy5swZ/f333ypevLi2bt2q06dPa9WqVTIYDHr55Ze1efNmeXp66ubNm1q5cqUk6cMPP1RCQoK1jwEDBujtt9/W+++/L3d3d0mSr6+vKleurB07dqhRo0b6/vvv9eyzzyp37twaP368Vq1apYCAAO3YsUNTpkzRwoUL/zHWcuXK6d1335UkjRgxQnPnzlXhwoV15swZDRo0SJ9//rkmTZqkLl26qEGDBtq8ebO++OILNW/e3Ob6/f3335o7d642btyocePG6ZtvvpGXl5eqVq2qYcOGKSEhQVWqVFFoaKgsFosaNmyotm3bKleuXNZ+3nnnHY0dO1aVK1fWL7/8or/++osiOpyCi0vOWwHMZDLafEX2RS6dB7l0HuQSAAAA2QVF9EyWlJRkLZTfzWw2y2AwWJdvqVChgiTpt99+U2BgoAICAiRJtWrV0rlz5yRJhw4dUlxcnBYsWCBJcnV1VWRkpM3x9woKCpKXl5ckKV++fIqJidHJkydVunRpeXp6SpKCg4N18eLF+x6/dOlSbdmyRRaLRR4eHnrrrbfk4+OjH3/8UbVq1ZLRePuPnGrVquno0aPq1auXZsyYoQEDBqhOnTpq1aqVPDw8/vE6tWjRQlu3blWjRo20ZcsWvfjiizp37pwiIyM1cOBA6zW7s5b6PzEYDHJzc1NsbKzOnTtn82ZEQkKCkpKSdPjwYc2cOVOS1KxZM0m31zK/253rGhgYqIIFC8rf31+S5O/vr9jYWOXNm1fHjx9XSEiIXFxcdPXqVUVFRdkU0Zs1a6ZRo0apSZMmCg4OVp06ddJ1DkBW5+/v5egQHMbHx9PRISCTkEvnQS6dB7kEAABAVkcRPZM9++yz970Z59GjR1W6dGnrYzc3N0lKVXBPSUmxfm8wGDRnzhzlyZMnVX93jr+Xi4ttSi0WS6q11+8Uwu/n7rXG73V3rBaLRUajUXnz5tWWLVt06NAhbdu2TXPnztWGDRvS7P+O+vXra/LkyYqJidEPP/ygiIgInT9/Xnnz5v1X66bv379fQUFBMhqNMhqNafZhNpsf2I/JZLJ+f79ruXTpUsXGxmrFihUyGo02M9nvePnll9WqVSvt3LlTY8eOVZMmTdS3b98MnxOQ1URFxTk6BLszmYzy8fFUbGy8UlIe/PsDWRu5dB7k0nlkJJc5+Y1cAAAAOB5F9ExWtWpVubm5adGiRdaba16+fFkzZszQ4MGDU7UvWLCg/vzzT0VFRcnf3187d+607qtUqZK2bt2qsLAwXblyRQsWLLCZYZ1eRYoU0S+//KLExEQZjUZt375dRYoUyVAfFStW1Jo1a9StWzdJ0vfff6/27dvru+++U2JiooKDg/Xcc89p586dqWa5GwwGJSYmWpdzkW4vZ1OrVi3Nnj1bNWrUkKenpwoVKqTY2FidOHFCJUuW1J49e3T27Fl17tz5gbGdPHlS7733nt599115e3urYMGC+vbbbxUcHKzTp09r8+bNGjhwoMqXL6/vvvtOLVq00LZt27Rnzx699NJLGboO0dHRKlKkiIxGow4cOKA///xTiYmJ1v0pKSmaPXu2BgwYoPbt2ytXrlz69NNPMzQGkFUlJ+fcYlVKijlHn78zIZfOg1w6D3IJAACArI4ieiYzmUx67733NGPGDLVu3Vru7u4yGAwaMGCA6tWrl6q9j4+PBgwYoLCwMD355JMqVqyYLly4IEkaPXq0xo4dq61btyohIcFalM+oZ555Ri+88ILatGmjggULqmzZsoqPj89QH40aNdKPP/5onaVeoUIFNW7cWJcvX9aIESO0ZMkSJScnq3r16goKCtKqVausx9apU0ehoaGaO3euTZ8tWrRQjx49tGTJEkmSh4eHZs6cqTFjxsjd3V1ms1kTJky4bzx3lp1JTk6Wi4uLZs2apRIlSkiSpk2bpkmTJmnJkiVKSkrS8OHDJd2+nuPGjdPq1aslSW+88UaGroEktW/fXkOHDtW+fftUpUoV9ezZU1OnTrXeyNRkMunxxx9XSEiIcuXKpaSkJI0YMSLD4wAAAAAAAADIGgyW+y3gDaeSnJysTz/9VC1btpS7u7vGjBmjggULqmfPno4ODfdIGDzd0SEAabo+KuctS+TiYpS/v5eiouKYJZnNkUvnQS6dR0ZymSdPbjtF5bwiI6/bZRyeo9kb+cveslP+PNducXQIWY7BYJCrq0lJSSn3vdcesraclL/49k0dHUKmS89rTWai5wAuLi76448/1L59e3l5eemxxx7TyJEjHR0WAAAAAAAAAGR5FNFziIEDB2rgwIGODgMAAAAAAAAAshWjowMAAAAAAAAAACCroogOAAAAAAAAAEAaKKIDAAAAAAAAAJAG1kQHshCPWcOzxZ3U8WAuLkb5+3uRSwAAAAAAACfATHQAAAAAcID69eurTJkyCgoKsv6bOHHifdsuW7ZM9evXV9myZdW+fXsdO3bMztECAADkXMxEBwAAAAAHiI2N1YcffqiKFSs+sN3XX3+tOXPm6J133lG5cuW0ZMkSvfzyy/rqq6+UK1cuO0ULAACQczETHQAAAADsLCUlRXFxcfL19f3HtmvXrlW7du1UrVo1eXp66tVXX5XBYNC2bdvsECkAAACYiQ4AAAAAdhYbGyuLxaJ58+bpwIEDkqR69epp5MiR8vb2tml7/PhxNW3a1PrYYDCoZMmSOnbsmFq0aJGu8YxGg4xGQ+adQBpMJqPNV2Qv5C97y075Mxge/e+j7ObOJbn9leuT3eSk/Lm4ZP3fMY8CRXQgC0kYPF2ejg4CmSJByra5vD6qr6NDAADA6SUmJqp06dIKCgrSpEmT9Pfff2vgwIGKiIjQm2++adM2KipKfn5+Ntt8fX117dq1dI8XEOBl16KVj092fSUEifylR+J76+w2llvPthlqnx3yl+hqcnQIWZaLC9cmO8sJ+fPy93J0CA5BER0AAAAA7CwwMFDr16+3Pvb29tbQoUPVp08fTZ48WW5ubtZ9aRW/M1IUv3Ytzm4z0X18PBUbG6+UFPMjHw+Zi/yln1tSit3GiouKS1e77JQ/e16/7MJguF2ATU5OkcXi6GiQUTkpf+n9nZSd+KfjjQGK6AAAAACQBTz11FMym826evWq8uXLZ93u7++v6Ohom7ZRUVF65pln0t232WyR2Wy/v+pTUsxKTs7aRTykjfz9M1c7VskymovskD97Xr/s4/YbnRaLZOH6ZEM5J39Z/ffLo5IzF7EBAAAAAAc6ceKEpk6darPt7NmzcnNzU968eW22BwUF6eeff7Y+TklJ0fHjx1W2bFm7xAoAAJDTUUQHAAAAADvz9/fX6tWrtWjRIiUmJuq3337TW2+9pZCQEJlMJjVu3Nh6w9FOnTpp3bp1+uGHHxQXF6dZs2bJw8NDwcHBDj4LAACAnCHbFtEvXbqkEiVKaPXq1TbbDx48qBIlSmjv3r0PPD44OFhnzpyRJG3YsEGS9MsvvygiIuKBx82bN896o5+wsDAlJiZmOPbLly9r9+7dkqT169enOgdHOHPmzH1fhK9fv17Vq1dXWFiYOnTooPbt22vNmjXW/YsWLdK2bdvS7Pdh96fX3r171aFDh39sdyfXAAAAgCMFBgZaXwtXrVpV3bt3V7169TR06FBJ0rlz53Tz5k1JUp06dTR8+HCNGjVK1atX148//qhFixbJ3d3dkacAAACQY2TrNdELFSqkjRs3qmPHjtZtGzduVKFChdLdx+XLl7Vy5Uq1atVKzz777D8W0e+2fPnyDET7//bu3atTp06pRo0aatOmzb/qw56qVaum2bNnS5KuXLmiQYMGKSYmRr169VLv3r0feOzD7s9MKSkpmjdvnlq1avXIxrBYLBm6wRMAAAByripVqqQ5oebkyZM2j0NCQhQSEmKPsAAAAHCPbF1Ez5s3r5KSknTx4kUVKFBASUlJOnDggMqVKyfp9mz1jh076vvvv5ckrVq1Sj/99JPN2oNDhgzR6dOnNWTIEHXo0EEzZ87UmjVrFBoaqnLlyunnn3/W1atXFRISorCwMJvxS5QooSNHjiglJUVjxozR5cuXlZCQoH79+qlevXravn27FixYIA8PDyUmJioiIkLe3t6aM2eOzGaz/Pz8FB8fr1u3bmno0KHauXOn5s+fLzc3NxkMBkVERKho0aIKDQ1V3bp1dfDgQZ07d06hoaHq1q2bTSwJCQkaNWqULl++rMTERFWqVEmjRo3ShQsX1LdvXzVo0ED79+9XTEyM5syZo+LFi2v79u2aOXOmnnrqKRUpUiRd1/zxxx/XxIkT1bFjR3Xv3l2vv/66ypUrpz179ig4ONhaoJ40aZLy5cunX3/9VeXKlVPLli01bNgwRUdH6+bNm6pZs6aGDRumkSNHqly5cgoJCdGGDRv00UcfycPDQ7ly5dKkSZOUN29e1a1bV927d9fOnTt1/vx5hYeHq1mzZmnGOGfOHN24cUOXL1/Wb7/9piJFimjOnDl67bXXdPnyZf3nP//RsmXLtGnTJn300Udyc3OTl5eXJk2apMcee0xr167V0qVLFRgYqGrVqmnHjh1asWKFLl68qPHjxysxMVGJiYnq27ev6tatq5EjR8rNzU2XLl1SRESEPvnkE+3evVsGg0GPP/64ZsyYIW9v73RdXwAAAAAAAABZS7YuoktSixYt9Nlnn6lfv37asWOHatSooevXr6f7+P79+2vmzJmaOXOmzRIwJpNJiYmJWr58ua5du6ZGjRrpxRdfvG8fH330kQICAjRz5kydO3dOs2fPVr169RQTE6PJkyerePHi2rBhg9555x3NnTtXrVu31q1bt9SzZ0/NmzdPkhQfH69Ro0bp448/VoECBbRlyxZNmjRJH3zwgUwmk65evapFixbp3Llz6tSpU6oi+pUrV1SlShWFhobKYrGoYcOGatu2rXLlyqWzZ8+qUaNGCg8P16xZs7Ru3TqNHDlSERERWrx4sZ555hktWrQo3descOHCslgs+uuvv6zbWrZsqU8++UStWrWS2WzW119/rTVr1lhnsO/evVs3b97UypUrJUkffvihEhISrMf/+eefevPNN7Vlyxb5+Pho8eLFmjt3riZNmiSTySRJWrJkib777jvNmjXrgUV0k8mkw4cPa9WqVTIYDGrQoIFOnTql/v3767vvvtOyZct0+fJlLVy4UJ988ok8PDy0cuVKvfPOOxo0aJBmzJihzZs3K0+ePHrllVdkNN5e9WjixInq0aOHqlevrmvXrqldu3baunWrJCk2Nlbvv/++YmJitHTpUh06dEguLi765ptvdPnyZYroyFZcXLLtSl+ZzmQy2nxF9kUunQe5dB7kEgAAANlFti+iN23aVJ07d1a/fv20ceNG9erVSytWrMiUvqtVqyZJCggIUL58+XThwoX7tjt8+LBat24t6XaBee7cuZKk3Llza8KECbJYLIqJiZGfn1+aY/32228KCAhQgQIFJEnVq1fX2LFjU8WSP39+RUdHpzrez89Px48fV0hIiFxcXHT16lVFRUUpV65c8vHxUalSpSRJ+fLl05EjRxQVFaUbN27omWeekSTVrFlTH3/8cbqvjdFolJubm/VxnTp1FBERoevXr+vYsWMqWrSoAgMDrfvLlSunGTNmaMCAAapTp45atWolDw8P6/5jx46pTJky8vHxsZ7/pk2bUp1/vnz57nv+96pcubJcXV0l3f7EQkxMjLy8vKz7jx49qqtXr6pXr16SpKSkJOXNm1e//fabAgMDlSdPHklSw4YNtW7dOknSoUOHFBcXpwULFkiSXF1dFRkZKUmqUKGCJMnX11fPPfecOnfurBdeeEGNGjWy5hTILvz9vf65UQ7j4+Pp6BCQScil8yCXzoNcAgAAIKvL9kV0f39/FSxY0LrUR1BQkHXfvWtTp6SkZKhvi8Vi8/2dGcn3a2c2m222JSYmavDgwVq9erVKliyprVu3/mNx/0Hjubg8OFVLly5VbGysVqxYIaPRqObNm6d5rMVisRlLyti1OXXqlDw9PfX4449bt7m5uSk4OFjbtm3ToUOHUs3az5s3r7Zs2aJDhw5p27Ztmjt3bqqbfD7M+d/rfud8r1KlSmnJkiU2244ePWrzc3N3DAaDQXPmzLEW2O929xsK7733nk6ePKmdO3cqNDRUM2bMsL4JAGQHUVFxjg4hyzCZjPLx8VRsbLxSUsz/fACyLHLpPMil88hILnmDFwAAAI6U7YvokvTiiy9q+vTpatKkic12X19fxcXFKTExUW5ubjp8+HCq4qrRaFRiYuJ9+929e7caNmyoa9eu6e+//9bTTz9933YVKlTQd999p0aNGunvv//WgAED9O6778psNqtgwYJKTk7Wli1brOMYDIZUYxYuXFjR0dHW9d137dql8uXLp/saREdHq0iRIjIajTpw4ID+/PPPNM9Luv3mg6enp3799VcVL15cO3fuTPc448aNU9++fVO9SdGyZUstW7ZMv/zyi0aOHGmz77vvvlNiYqKCg4P13HPPaefOnbp48aJ1f1BQkCIiIhQTEyNfX98Mn3963J3roKAgvf766/r777+VN29ebdq0Sa6urqpatap+//13RUVFyd/fX9988431+EqVKmnr1q0KCwvTlStXtGDBAptPC0jSxYsXtW3bNr300ksqUaKELl68qCNHjlBER7aSnExR6l4pKWaui5Mgl86DXDoPcgkAAICszimK6MHBwRo7dqxatmxps93b21sdO3ZUt27dVLx4cfn5+Sk2NtamTbFixRQbG6tu3bqpT58+qfru1q2bIiMjNWTIkDTXte7SpYvGjBmj0NBQJSYmql+/fvL19VXHjh3VqVMn+fv7q1+/fhoyZIiWLl2qKlWqKDw8XJ6entaivoeHh6ZMmaLBgwfL3d1dbm5uGj9+fLqvQfv27TV06FDt27dPVapUUc+ePTV16lSbm6jezWAwaPTo0Ro0aJCefPJJlSlTJtVs+jt++OEHhYWFyWw269atWwoJCVHbtm1TtbtzM9OKFSsqV65cNvuKFSumESNGaMmSJUpOTlb16tUVFBSkVatWSZICAwM1dOhQ9ejRQ+7u7vLz89PEiRPTff7pkTdvXuXPn1/t2rXT0qVLNWbMGPXp00eenp5yd3fXlClT5Ofnp1deeUWhoaF6+umnVbJkSevPzOjRozV27Fht3bpVCQkJ6t27d6oxnnjiCZ08eVIdO3aUq6urPDw8NGjQoEw9DwAAAAAAAAD2Y7Dcb50LKCwsTL169VKdOnUcHQrsbMOGDapbt678/f21cOFCXb58WePGjbPL2AmDp9tlHOBBro/q6+gQsgwXF6P8/b0UFRXHLMlsjlw6D3LpPDKSyzx5ctspKucVGXndLuPwHM3eyF/6ea7dYrex4ts3TVe77JQ/e16/7MJgMMjV1aSkpJT7LkmLrC0n5S+9v5Oyk/S81nSKmehAZrp586b+85//KHfu3HJzc0tzNj8AAAAAAAAA50cRPQ3Lly93dAhwkNDQUIWGhjo6DAAAAAAAAABZgNHRAQAAAAAAAAAAkFVRRAcAAAAAAAAAIA0s5wJkIR6zhmeLm8DgwbLTDX0AAAAAAADwYMxEBwAAAAAAAAAgDRTRAQAAAAAAAABIA0V0AAAAAAAAAADSQBEdAAAAAAAAAIA0cGNRIAtJGDxdno4OApkiQcoWubw+qq+jQwAAAAAAAMjSmIkOAAAAAAAAAEAaKKIDAAAAAAAAAJAGiugAAAAAAAAAAKSBIjoAAAAAAAAAAGmgiA4AAAAAAAAAQBpcHB0A4EhvvPGGTpw4oVu3bun48eOqUKGCJKl58+bq2LGjTdt58+bp1q1bGjp0qM32vXv3aubMmVqzZo3d4gYAAAAAAABgHxTRkaO9/vrrkqRLly6pY8eOWr58uYMjAgAAAAAAAJCVUEQH7vHTTz9p8uTJcnNz082bNzVkyBDVqFFD0u1i+4ABA3Tp0iUVK1ZMU6dOtTn24sWLGj9+vBITE5WYmKi+ffuqbt26jjgNAAAAAAAAAJmAIjpwjytXrmj48OGqVKmS9u/frylTpmj9+vWSpNOnT2v9+vVycXFRhw4d9N1338nd3d167MSJE9WjRw9Vr15d165dU7t27bR161a5ubk56nSAB3Jx4dYYD2IyGW2+Ivsil86DXDoPcgkAAIDsgiI6cA8/Pz8tWLBAt27dUkJCgqKjo637nnvuOWtBvHz58jp9+rRKly5t3X/o0CHFxcVpwYIFkiRXV1dFRkbqySeftOs5AOnl7+/l6BCyBR8fT0eHgExCLp0HuXQe5BIAAABZHUV04B7Dhw/Xa6+9pueff15Hjx7VwIEDrfuMxv+fKWWxWGQwGGyONRgMmjNnjvLkyWO3eIGHERUV5+gQsjSTySgfH0/FxsYrJcXs6HDwEMil8yCXziMjueRNXwAAADgSRXTgHtHR0SpevLgkadOmTUpMTLTu279/v5KSkmQymfTjjz+qQYMGNsdWqlRJW7duVVhYmK5cuaIFCxZo7Nixdo0fyIjkZApQ6ZGSYuZaOQly6TzIpfMglwAAAMjqWIAQuEffvn3Vt29fhYWFqV69evL19dXUqVOVkpKiEiVKaMiQIWrXrp2effZZVatWzebY0aNHa/v27ercubNefvllVa1a1UFnAQAAAAAAACAzMBMdkPTUU0/p+++/lyT17NlTPXv2tO7bvHnzA4+tWrWq1qxZY+3n/ffff3SBAgAAAAAAALArZqIDAAAAAAAAAJAGiugAAAAAAAAAAKSBIjoAAAAAAAAAAGmgiA4AAAAAAAAAQBq4sSiQhXjMGq6oqDglJ5sdHQoegouLUf7+XuQSAAAAAADACTATHQAAAAAAAACANFBEBwAAAAAAAAAgDRTRAQAAAAAAAABIA0V0AAAAAAAAAADSwI1FgSwkYfB0eTo6CGSKBCnL5fL6qL6ODgEAAAAAACDbYSY6AAAAAAAAAABpoIgOAAAAAAAAAEAaKKIDAAAAAAAAAJAGiugAAAAAAAAAAKSBG4tmM5cuXVLjxo1VoUIFSVJSUpIef/xxTZgwQQEBAWket2HDBrVq1Up79+7VzJkztWbNGnuF/EisX79en376qSTp8OHDKlGihDw9PVW0aFE1adLEKc4RAAAAAAAAgONRRM+GfH19tXz5cuvj2bNna+7cuYqIiLhv+8uXL2vlypVq1aqVfQK8i9lsltGY+R94aNOmjdq0aSNJCg4O1rRp01S0aFFJ0t69ezN9PEmyWCwyGAyPpG8AAAAAAAAAWRNFdCdQsWJFLV26VGvXrtXBgwc1depUSdL27du1atUq3bx5U6dPn9aQIUPUoUMHpaSkaOLEiTp27Jhu3bqlRYsWKU+ePFq7dq1Wr14tT09PWSwWTZ8+Xfnz59eRI0c0ZswY+fj4qEmTJjbHjhs3ThcvXlRKSorq1Kmjfv36ae/evXr77bfl6+urqlWrql27dpowYYIuXryopKQkBQcHq3fv3kpISNCoUaN0+fJlJSYmqlKlSho1apQuXLigvn37qkGDBtq/f79iYmI0Z84cFS9ePN3XJK1z3L17t+bPny8XFxeZTCZFRESoYMGCOnr0qKZMmSKj0ajk5GSNGDFCFSpU0MiRI+Xm5qZLly4pIiJCN27csF5fs9ms4cOHq2zZsgoNDVW1atV04sQJnTx5UqNGjdLGjRt16tQpNWrUSOHh4Y8k9wAAAAAAAAAeLYro2ZzZbNZXX32lSpUqqVmzZpo7d65u3Lghb29vbdmyRe3bt5ePj49mzpypmTNnau/evTp79qzefvttPfHEExoyZIi2bt2qsLAwxcfH6+2331ZgYKDmz5+v5cuXa8SIEZoyZYr69eunF154QUuXLrXOLl++fLlcXV21atUqJSUlqV27dqpevbok6ejRo9q2bZsCAgL03nvvKW/evJo8ebLMZrNCQ0NVo0YN+fn5qUqVKgoNDZXFYlHDhg3Vtm1b5cqVS2fPnrUWn2fNmqV169Zp5MiR6b4u9zvH9u3ba/z48Vq1apUCAgK0Y8cOTZkyRQsXLtTw4cM1ceJEVa5cWUeOHNHQoUP11VdfSZJiY2P1/vvvS5JatGihuXPnqnDhwjpz5owGDRqkzz//XCaTScnJyVqwYIHeeecdTZ06VVu2bFFUVJQaNmxIER1ZgosLt8HIKJPJaPMV2Re5dB7k0nmQS8DxPNdusXlsMBiU6GqSW1KKXC2WTBsnvn3TTOsLAABHoIieDcXExCgsLEzS7RnXFStWVO/eveXm5qYXXnhBX3zxhVq2bKkjR45oypQpOnjwoM3xhQsX1hNPPCFJypcvn2JiYiRJuXLl0uDBg2UwGBQZGWldd/3EiROqUqWKJKlRo0aaMmWKpNtrkb/44ouSJFdXV1WuXFlHjx7Vs88+q4IFC1rXaD906JAuXrxojSMuLk4XLlxQoUKFdPz4cYWEhMjFxUVXr15VVFSUcuXKJR8fH5UqVcoa45EjRzJ0je53jufOnVNkZKQGDhwo6fYbEMnJybp+/br++usvVa5cWZJUtmxZRUZGKjY2VpKs1yE2Nlbnzp3T2LFjreMkJCQoKSnJpl1gYKBKlSolNzc3BQYGKiEhQYmJiXJzc8vQOQCZzd/fy9EhZFs+Pp6ODgGZhFw6D3LpPMglAAAAsjqK6NnQvWui361Tp04aP368fH191aBBA7m4pE7xvdssFosuXbqkqVOnatOmTXriiSe0ZMkS/frrr9Y2d9YCv3d9c8tdsxMsFot1/90FY4PBoD59+qhZs2Y2x86fP1+xsbFasWKFjEajmjdv/sAYM+J+xxsMBuXNmzfVtbtTLL/XvediNBqtM/Dvx2Qy3ff7fxM/8ChERcU5OoRsx2QyysfHU7Gx8UpJMTs6HDwEcuk8yKXzyEgueSMYAAAAjkQR3ck888wzMhgM+uCDDzR58mRJt4u/iYmJDzwuNjZWXl5eeuKJJxQXF6dt27ZZZ3IXLlxYhw8fVt26dfX1119bj6lYsaJ27typxo0bKzExUfv27VOrVq0UHx9v03elSpX05ZdfqlmzZjKbzZo0aZL69eun6OhoFSlSREajUQcOHNCff/75j3E+jEKFCik2NlYnTpxQyZIltWfPHp09e1adO3dW/vz5deDAAVWuXFkHDhxQ/vz55e3tbXO8t7e3ChYsqG+//VbBwcE6ffq0Nm/ebJ3ZDmR1yckUm/6tlBQz189JkEvnQS6dB7kEAABAVscChE7oxRdflIuLiwoXLixJKlasmGJjY9WtW7c0j3n22WdVrlw5tW3bVoMHD1b//v21b98+bdmyRcOGDdPUqVPVvXt3JScnW2dZd+7cWcnJyQoJCVHnzp3VunVrlS1bNlXfnTt3lre3tzp06GBdoz0gIEDt27fXtm3bFBoaql27dqlnz56aOnWqoqKiHsl18fDw0MyZMzVmzBh16dJF8+bNU9WqVSVJ06ZN06xZs9SlSxfNnj1b06ZNu28f06ZN03vvvafOnTvrtddeU82aNR9JrAAAAAAAAACyBoOFdSacisViUf/+/dW6dWs9//zzmdLnDz/8ID8/P5UsWVKHDh3SmDFjtHnz5kzpG7YSBk93dAhwYtdH9XV0CNmOi4tR/v5eioqKY5ZkNkcunQe5dB4ZyWWePLntFJXzioy8bpdxeI5mL/e7sairq0lJSSmZuiSlM95Y9N5r9yil9/plp+efPa9fdvGonn+wj5yUP2f8nZ6e15os5+JEjh8/rrFjx6patWqZVkCXbq8v/tprr8nDw0NJSUkaP358pvUNAAAAAAAAAFkZRXQnUqpUKX3yySeZ3m/lypW1fv36TO8XAAAAAAAAALI61kQHAAAAAAAAACANFNEBAAAAwAEuXbqkvn376rnnnlP16tU1fPhwxcTEpGq3bt06lSxZUkFBQTb/rly54oCoAQAAch6K6AAAAADgAH379pWfn5+2b9+ujRs36ty5c5o+PfWN5q9fv64aNWro6NGjNv8ef/xxB0QNAACQ87AmOpCFeMwani3upI4Hc3Exyt/fi1wCAIA0Xb9+XWXKlNHQoUPl5eUlLy8vtWrVSh9++GGqtjExMfL19XVAlAAAAJAoogMAAACA3eXOnVtTpkyx2fb7778rX758qdrGxsbq/PnzatOmjc6fP6+nn35agwYNUt26ddM9ntFokNFoeOi4/4nJZLT5iqzNYDDc8/jur5n38+Li4nw/D/deu0cpvdcvOz3/7Hn9sotH9fyDfeSk/Dnj7/T0oIgOAAAAAA529OhRrVixQnPnzk21z8/PTwEBARo6dKiefvpprVmzRq+++qo+++wzFS1aNF39BwR42bVo5ePjabex8O8lupruu93F5f7b/y0vf69M7S8rSOvaPQoZvX7Z4flnz+uX3WT28w/2lRPy54y/09ODIjoAAAAAONDBgwfVt29fDR8+/L6zy/v372/z+KWXXtKmTZu0ceNGhYeHp2uMa9fi7DYT3cfHU7Gx8UpJYVm7rM4tKcXmscFwuwCUnJwiiyXzxomLisu8zrKIe6/do5Te65ednn/2vH7ZxaN6/sE+clL+nPF3un863higiA5kIQmDpyvrzxlAeiRIds3l9VF97TgaAADILNu3b9ewYcM0fvx4NWvWLN3HPfXUU4qMjEx3e7PZIrPZfn/Vp6SYuTdMNuCaqtJz+40Wi0WyZGIVyBl/FlJfu0cno9cvOzz/7Hn9so9H8/yDveSc/GX13y+PSs5cxAYAAAAAHOzQoUMaMWKE5s6d+8AC+rvvvqvdu3fbbDt37pwKFCjwqEMEAACAKKIDAAAAgN0lJydr9OjRGjhwoGrUqJFqf+PGjXXgwAFJUlRUlCZMmKDffvtNiYmJ+uCDD3ThwgW1adPG3mEDAADkSCznAgAAAAB2dvjwYZ05c0ZTp07V1KlTbfZt3bpV586d082bNyVJgwcPltlsVpcuXRQfH68SJUpo6dKlCgwMdEToAAAAOQ5FdAAAAACws8qVK+vkyZNp7r97n5ubm1577TW99tpr9ggNAAAA92A5FwAAAAAAAAAA0kARHXZ38+ZNTZw4US1btlTnzp3Vrl07rV27VpL0yy+/KCIiIt19rV+/XuHh4Rkaf8+ePQoLC1OHDh3UsWNHvfTSS/r5558z1MeDhIWFaefOnZnWHwAAAAAAAADHYTkX2N2YMWOUJ08ebdiwQUajUVevXlXXrl3l7++vBg0apCqiWywWGQyGTBn79OnTGjNmjBYtWqQiRYpIknbs2KFXXnlFW7duVa5cuaxtzWazjEbeZwIAAAAAAAByMorosKsLFy5o37592rZtm7VA/dhjj2nlypXy8fHR3r17NXPmTK1Zs0ZhYWEqUaKETp8+rYULF2rv3r165513ZDQalTdv3lQ3YDp+/Lh1m9ls1vDhw1W2bFmbNu+++6569OhhLaBLUt26dfXNN9/Izc1NklS+fHl16dJFsbGxGj9+vCZOnKhjx47JYDDoySef1LRp02QwGFS6dGn1799f3333nW7cuKFx48apcuXKkqT9+/drxYoVOnPmjDp37qxu3bo9smsKAAAAAAAA4NGhiA67+vXXX1WiRAlrwfoOX1/f+7Z3d3fX0qVLFR8fr9GjR2v9+vXKkyePpk+frr1799q0HTFihObOnavChQvrzJkzGjRokD7//HObNqdPn1aXLl1SjXN3PPHx8apSpYrq1q2rmJgY5c+fX6NHj5bRaFTXrl21a9cu1a9fXxaLRcWKFdOrr76qb7/9VtOnT9eaNWskSQkJCXr33Xd17tw5derUiSI6HjkXFz418SiYTEabr8i+yKXzIJfOg1wCAAAgu6CIDrtKSkqS2WxOd/sKFSpIkn777Tf5+/srT548kqThw4dLur0muiTFxsbq3LlzGjt2rPXYhIQEJSUlydXV1botOTlZKSkp1sf9+vVTTEyMoqKi1Lt3b7Vs2VKSVLFiRUmSl5eXrly5os6dO8vFxUWnT59WVFSU9fhq1apJkipVqqRff/3Vur1GjRqSpPz58ys6Ojrd5wv8W/7+Xo4Owan5+Hg6OgRkEnLpPMil8yCXAAAAyOooosOunn32WR0/flwJCQny8PCwbr948aLN4zvuzBC3WCyyWCxp9ms0GmU0GrV8+fJ/HP/w4cPWIvn8+fMlSSNHjlRcXFyqcTdt2qTDhw/rww8/lJubm15++WWb/u7EdO/66SaT6YFxAJktKirunxshw0wmo3x8PBUbG6+UlPS/AYish1w6D3LpPDKSS94sBgAAgCNRRIddFSxYUFWqVNEbb7yhcePGycXFRVevXlX//v31yiuvpLmsS+HChXXt2jX9/vvvevLJJzV79mwVK1bMut/b21sFCxbUt99+q+DgYJ0+fVqbN2/WwIEDbfrp3r27Xn75ZVWtWlWlS5eWJF2+fFm//PKLdVb53aKjo1WgQAG5ubnpzJkzOnbsmOrXr2/dv3v3bjVt2lQ//vijSpYsmRmXCPhXkpMpJD1KKSlmrrGTIJfOg1w6D3KJ7MBz7Ra7jRXfvqndxkL2kN6fP4PBoERXk9ySUuT6gEloAPAw7PV/Ylb7/5AiOuxuxowZmjNnjlq3bi0fHx+ZzWb16dNHDRs2TLXO+R2enp6aPHmyBg0aJFdXV+XNm1evvPKKNm/ebG0zbdo0TZo0SUuWLFFSUpJ1yZe7lSxZUm+99ZZmzpypuLg4JSUlycPDQ127dlWrVq1StW/SpIk2btyokJAQFS9eXOHh4VqwYIGqVKkiSTp16pRWrVqlmJgYTZo0KXMuEAAAAAAAAIAsw2B50BoZANJUokQJHTlyRO7u7pnWZ8Lg6ZnWF3KW66P6OjoEp+TiYpS/v5eiouKYJZnNkUvnQS6dR0ZymSdPbjtF5bwiI6/bZRxnfY4660z0e8/LYDDI1dWkpKSUBy6nmVFZbTZhZrDnz0R6Par8wT7IX/ZG/jKfPf/vSM9rTeM/tgAAAAAAAAAAIIdiORfgXzp58qSjQwAAAAAAAADwiDETHQAAAAAAAACANFBEBwAAAAAAAAAgDSznAmQhHrOGO91NmHIiZ72hFgAAAAAAQE7ETHQAAAAAAAAAANJAER0AAAAAAAAAgDRQRAcAAAAAAAAAIA0U0QEAAAAAAAAASAM3FgWykITB0+Xp6CCQKRKkTMvl9VF9M6knAAAAAAAAZBQz0QEAAAAAAAAASANFdAAAAAAAAAAA0kARHQAAAAAAAACANFBEBwAAAAAAAAAgDRTRkalCQ0P15Zdf2mxLSUlRrVq1dPr06Ucy5siRI9WoUSOFhYWpTZs26tatm/bt22fdHx4erj/++OORjA0AAAAAAADAubk4OgA4l/bt2+vTTz9Vo0aNrNt27dqlp556SsWKFXtk47700ksKCQmRJB07dkyvvvqqZs2apYoVK2r27NkP3b/ZbJbRyHtOAAAAAAAAQE5DER2ZqkmTJpo6daquXLmixx9/XJL06aefql27dkpISNCECRN08eJFJSUlKTg4WL1799batWu1d+9eSdLZs2fl5eWlxYsX6++//1bfvn3VoEED7d+/XzExMZozZ46KFy/+wBhKly6tvn37atGiRVq4cKGCg4O1ePFiPfnkkxo1apQuX76sxMREVapUSaNGjZIkzZ8/X19++aWeeOIJlSlTRr///rumT5+u4OBgNWvWTGfOnNGCBQv0zjvv6Ntvv5W7u7s8PDw0Z84ceXt7q27dugoLC9O+fft04cIFjRgxQqtXr9bp06fVs2dPderU6dFeeAAAAAAAAACPBEV0ZCoPDw81btxYGzduVPfu3RUTE6O9e/dq8uTJ+uijj5Q3b15NnjxZZrNZoaGhqlGjhkwmkw4cOKDNmzfLy8tLnTp10u7du/XMM8/o7NmzatSokcLDwzVr1iytW7dOI0eO/Mc4ypUrp3fffddm25UrV1SlShWFhobKYrGoYcOGatu2rdzc3LR69Wp99dVXcnFxUadOnWwK9fny5dOQIUOUnJwso9GopUuXysvLS6NGjdKGDRvUpUsXmUwm5c6dW4sWLdLo0aO1ePFiffTRR/rxxx81evRoiuh4KC4ufArCUUwmo81XZF/k0nmQS+dBLgEAAJBdUERHpuvQoYNGjhyp7t27a/PmzWrQoIG8vLx06NAhXbx4UQcPHpQkxcXF6cKFC5KkoKAgeXl5SbpdtI6JiZEk+fj4qFSpUtbtR44cSVcMBoNBbm5uNtv8/Px0/PhxhYSEyMXFRVevXlVUVJSio6NVunRpeXp6SpKCg4N18eJF63EVK1aUJLm43H669OzZUyaTSZcuXVL+/Pmt7SpUqCBJCgwMVO7cuWU0GhUYGKjr169n4OoBqfn7ezk6hBzPx8fT0SEgk5BL50EunQe5BAAAQFZHER2ZrnTp0nJxcdGxY8f02WefWZdMMRgM6tOnj5o1a2bTfv369dYC9R0Wi0WS0tz+T/bv36+goCCbbUuXLlVsbKxWrFgho9Go5s2bW/s0GAzWdveufe7q6mrtc+3atVq/fr18fHw0YcIEm3Ymk+m+36c3ZiAtUVFxjg4hxzKZjPLx8VRsbLxSUsyODgcPgVw6D3LpPDKSS95QBgAAgCNRRMcj0a5dO73//vu6efOmypcvL0mqVKmSvvzySzVr1kxms1mTJk1Sv379Mn3skydP6r333ku1nEt0dLSKFCkio9GoAwcO6M8//1RiYqKKFCmiX375RYmJiTIajdq+fbuKFCmSqt/o6GjlzZtXPj4+ioyM1A8//KDnn38+0+MH7pWcTJHI0VJSzOTBSZBL50EunQe5BAAAQFZHER2PRIsWLTR9+nSFh4dbt3Xu3FmnT59Whw4dlJKSotq1aysgICBTxlu6dKm2bNmi5ORkubi4aNasWSpRooRNm/bt22vo0KHat2+fqlSpop49e2rq1Klavny5XnjhBbVp00YFCxZU2bJlFR8fn2qMWrVq6cMPP1THjh2t66RHRESoTp06mXIOAAAAAAAAALIeg4W1JpDDJScn69NPP1XLli3l7u6uMWPGqGDBgurZs6fdY0kYPN3uYyLruz6qr6NDyLFcXIzy9/dSVFQcsySzOXLpPMil88hILvPkyW2nqJxXZKR97tPjrM9Rz7Vb7DZWfPumdhvr3vMyGAxydTUpKSklU5eltOc52Ys9fybS61HlD/ZB/rI38pf57Pl/R3peazITHTmei4uL/vjjD7Vv315eXl567LHHNHLkSEeHBQAAAAAAACALoIgOSBo4cKAGDhzo6DAAAACAbC/xvXVyS0qRqx1m4jnjDGc8nKw4QxwAkP0ZHR0AAAAAAAAAAABZFUV0AAAAAAAAAADSQBEdAAAAAAAAAIA0sCY6kIV4zBquqKg4JSebHR0KHoKLi1H+/l7kEgAAAAAAwAkwEx0AAAAAAAAAgDRQRAcAAAAAAAAAIA0U0QEAAAAAAAAASANFdAAAAAAAAAAA0kARHQAAAAAAAACANFBEBwAAAAAAAAAgDRTRAQAAAAAAAABIA0V0AAAAAAAAAADSQBEdAAAAAAAAAIA0UEQHAAAAAAAAACANFNEBAAAAAAAAAEgDRXQAAAAAAAAAANJAER0AAAAAAAAAgDRQRAcAAAAAAAAAIA0U0QEAAAAAAAAASANFdAAAAAAAAAAA0kARHQAAAAAAAACANFBEBwAAAAAAAAAgDRTRAQAAAMABLl26pB49eqh8+fKqXr26ZsyYIbPZfN+2y5YtU/369VW2bFm1b99ex44ds3O0AAAAORdFdAAAAACwM4vFon79+snf3187duzQihUr9MUXX2jp0qWp2n799deaM2eOpkyZor1796pu3bp6+eWXdfPmTfsHDgAAkANRRAcAAAAAOzt69KhOnjyp0aNHy9fXV0WKFFHv3r21evXqVG3Xrl2rdu3aqVq1avL09NSrr74qg8Ggbdu2OSByAACAnMfF0QEAAAAAQE5z/PhxPfnkk/Lz87NuK1WqlH777TfduHFD3t7eNm2bNm1qfWwwGFSyZEkdO3ZMLVq0SNd4RqNBRqMh0+JPi8l0e56WwSBJj348Fxf7zAszGB79udxhr3OSUp/XnYeZnT9HnlNO8qjyB/sgf9kb+ct89vy/Iz0oogNZjL+/l6NDQCYhl86DXDoPcuk8yKXzyKm5jIqKkq+vr822O4+joqJsiuhRUVE2xfY7ba9du5bu8R57zPufG2WWnm3lZqeh7PbT07eDvUay3zlJaZ5XZucvK5xTTmKv5x8eDfKXvZG/zJPVXiFmrZI+AAAAAMBGWjNrc/KMWwAAAHuiiA4AAAAAdvbYY48pOjraZltUVJQkKSAgwGa7v7//fdve2w4AAACPBkV0AAAAALCzoKAg/fHHH9bCuSQdOXJExYoVk5eXV6q2P//8s/VxSkqKjh8/rrJly9otXgAAgJyMIjoAAAAA2Nmzzz6rsmXLatKkSYqNjdXJkye1aNEide7cWZLUuHFjHThwQJLUqVMnrVu3Tj/88IPi4uI0a9YseXh4KDg42JGnAAAAkGNwY1EAAAAAcIC33npLY8eOVe3ateXl5aXQ0FCFhoZKks6dO6ebN29KkurUqaPhw4dr1KhRunr1qsqUKaNFixbJ3d3dkeEDAADkGAaLxWJxdBAAAAAAAAAAAGRFLOcCAAAAAAAAAEAaKKIDAAAAAAAAAJAGiugAAAAAAAAAAKSBIjoAAAAAAAAAAGmgiA5kAZcuXVKPHj1Uvnx5Va9eXTNmzJDZbHZ0WJBUv359lSlTRkFBQdZ/EydOlCTt2bNHLVu2VFBQkF544QVt3LjR5thly5apfv36Klu2rNq3b69jx45Z9926dUtjx47Vc889pwoVKmjAgAG6du2aXc8tJ9i1a5dq1Kih8PDwVPseZf54TmeutPJ48eJFlShRwub5GRQUpC+++MLahjxmLZcuXVLfvn313HPPqXr16ho+fLhiYmIk8ZzMbtLKJc/LnCEjeXhQvuEYGcnfypUr1bBhQ1WoUEEtWrTQN998Y+doca9/83vw8uXLqlChgubNm2enKJGWjOTvzJkz6ty5s8qVK6d69epp6dKl9g0WqaQ3f2azWW+99Zbq169v/f25ZcsWB0SMTGUB4FBms9ny4osvWoYMGWKJjo62nDlzxlK/fn3LkiVLHB0aLBZLxYoVLQcPHky1/a+//rKUK1fOsmzZMsvNmzct27dvt5QtW9by008/WSwWi+Wrr76ylC9f3rJnzx7LzZs3LfPmzbPUrFnTEhcXZ7FYLJaJEydamjVrZrlw4YLl2rVrlpdfftny8ssv2/XcnN2iRYssDRs2tHTq1MkyaNAgm32PMn88pzPXg/J47NgxS+nSpdM8ljxmPc2bN7eMHDnScuPGDcvff/9tadeuneW1117jOZkNpZVLnpfOLyN5+Kd8w/4ykr8vv/zSUqlSJcuhQ4csSUlJlvXr11tKly5tOX/+vAMih8Xy738P9uvXz1KhQgXL3Llz7RQp7icj+YuPj7cEBwdb1qxZY0lISLD88MMPlsaNG1tOnz7tgMhhsWQsf8uXL7fUqlXLcvbsWUtKSopl+/btllKlSll++eUXB0SOzEIRHXCwn376yVKyZElLVFSUdduqVassDRs2dFxQsFgsFktycrKlRIkS932hsnjxYkvLli1ttoWHh1tGjx5tsVgsll69elkmTZpk3Wc2my21atWybNy40ZKUlGSpWLGi5euvv7buP3PmjOWZZ56x/PXXX4/obHKeZcuWWWJjYy0jRoxIVXx9lPnjOZ25HpTH3bt3W2rUqJHmseQxa4mNjbWMHDnScuXKFeu2jz76yNKwYUOek9nMg3LJ89L5ZSQPD8o3HCMj+fvss88sK1eutNlWtWpV8udA/+b34H//+19LkyZNLEOGDKGI7mAZyd/69estvXr1smN0+CcZyd9rr71mGTBggM22atWqWTZs2PCow8QjxHIugIMdP35cTz75pPz8/KzbSpUqpd9++003btxwXGBQbGysLBaL5s2bp1q1aqlWrVoaPXq0bty4oePHj6t06dI27Z999lnrR5Tv3W8wGFSyZEkdO3ZMFy5c0I0bN2z2FylSRJ6ennzEORN17dpVuXPnvu++R5k/ntOZ60F5jI2NldlsVu/evVWlShU1bNhQ7733niwWiyTymNXkzp1bU6ZM0WOPPWbd9vvvvytfvnw8J7OZB+WS56Xzy0geHpRvOEZG8teyZUuFhIRYH8fGxurGjRvKly+fvcLFPTL6ezAhIUETJkxQRESEXFxc7Bgp7icj+Ttw4IAKFSqkAQMGqFKlSvo/9u48Lqqy///4e9hxYUtvy9wqE0zANEzTEiXL1DQ1EKnUu03zNrc0yswltVzKJbXFpULNLJHcbvfMMs2ltBIhtVJLqdtSwAVRZJjfH/6YryMcYXRgGHw9Hw8fOueaOedzrosZr3nP4Zr27duzHIiT2TN+rVq10nfffad9+/YpNzdXX3zxhc6fP6+77767lKuGIxGiA06WkZEhf39/m235tzMyMpxREv6/nJwcNWjQQGFhYVq7dq3mz5+vn376SaNHjy503AICAqzrtmZkZNj85ypdHNf09HTruF7+eD8/P9ZFLyUlOX48p0uPp6enatWqpe7du+ubb77RqFGj9N5772nJkiWSGMeyLjk5WQsXLtSTTz7Jc9LFXTqWPC/LP3vG4UrjDee42ueRxWLRq6++qtDQUDVu3LhEa4Qxe8fvnXfeUZMmTQjuygh7xu/YsWNatmyZoqOjtXXrVj399NMaMmSI9u3bV2r1wpY94/fAAw8oLi5OjzzyiBo0aKAhQ4bojTfe4ENIF8dHkQBgoFq1avr888+ttytVqqShQ4fqueeeU0RERKGPMZlMNn8btRspqh0li/FzLVFRUYqKirLebtGihWJjY5WUlKSYmBjGsQzbtWuX+vbtq/j4eEVGRhp+URbPybLv8rGUxPMSVlc73ihbLly4oJdfflmHDh1SQkKC3Ny4Fs8V/Prrr1q6dGmBL+qGa8jNzVWrVq3UsmVLSdKjjz6qxYsXa9WqVQoJCXFydSjKsmXLtHTpUi1dulR169bVtm3b9MILL6h69eoKDw93dnm4SvzvBzjZDTfcoMzMTJtt+Z9iBgUFOaEiXEmNGjWUl5cnNze3Qsctf8wCAwMN2/N//f3SdovFoszMTJtfjUfJCQoKKrHx4zntXDVq1NDx48clMY5l1aZNm9SnTx+NGjVKjz/+uCSek66qsLEsDM/L8sWecbjSeMM57H0enTt3Tn369NGxY8e0cOFC5qpOZs/4jR49WoMGDeL5VobYM37+/v4FljS8+eabrf+fovTZM34LFixQt27ddMcdd8jLy0uRkZFq2rSpli1bVkrVoiQQogNOFhYWpj///NPm13/27NmjunXrqmLFik6sDPv27dOECRNsth08eND6n+Dl63nu2bPH+qlyWFiY9u7da20zm81KTU1VeHi4atasqYCAAJvH79+/Xzk5OQoNDS3BM0K+sLCwEhs/ntOlZ926dVq4cKHNtoMHD6pmzZqSGMeyaPfu3XrppZc0ffp0dejQwbqd56TrMRpLnpflnz3jcKXxhnPYM34Wi0WDBw+Wl5eXPvzwQ/n5+ZV2ubhMcccvLS1N3333nd588001bdpUTZs21apVqzR37lx16dLFGaVD9j3/GjRoUGBulJaWpptvvrlUakVB9r5+5uXl2WzLzc3lN3lcHKMHOFn9+vUVHh6ucePG6dSpU9q/f79mz559xSu6UDoCAwP12Wefafbs2crJydHhw4f19ttvW9c2S0tLU0JCgs6dO6e1a9dq8+bNio2NlSR1795dSUlJ2r59u7KysjRlyhT5+PgoKipK7u7u6tatm6ZNm6YjR47oxIkTGj9+vB566CFVqVLFyWd9fejYsWOJjR/P6dLj6empSZMmaevWrcrNzdW3336rpKQka18zjmVLbm6uXn31VQ0cOFDNmze3aeM56VquNJY8L8u/osbhoYce0vfffy/pyuMN57Bn/FauXKn9+/dr6tSp8vLycmbZ+P+KO3433nijvv76ay1fvtz6JyoqSt27d9fs2bOdfBbXL3uef507d9b+/fv16aefKicnRytWrFBKSoo6derkzFO4rtkzfq1bt9aSJUv0yy+/yGw2a9u2bdq2bZtatWrlxDPANbMAcLq//vrL8uyzz1rCw8Mt99xzj2XGjBnOLgn/386dOy3dunWz3HnnnZbWrVtb3nzzTcv58+ctFovF8t1331k6depkCQ0NtTz44IOW9evX2zz2k08+sbRq1coSFhZmiYuLsxw4cMDadv78ectrr71miYiIsDRq1Mjywgsvpvk6nAAAlRFJREFUWE6dOlWq51behYaGWkJDQy0hISGWkJAQ6+18JTl+PKcdp6hx/PTTTy0PPvigpWHDhpYOHTpYkpKSbB7POJYd3333naVevXrWMbz0z9GjR3lOupCixpLnZfl3pXGoV6+e5euvv7bevtJ4wzmKO349e/a01K9fv8DzfPjw4c4qHRb7nn+XeumllyzTp08vrTJhwJ7x27lzp+WRRx6xhIeHWx5++GHDsUXpKe745eTkWKZMmWJp3bq1pWHDhpZ27dpZlixZ4qyy4SAmi8VicXaQDwAAAAAAAABAWcRyLgAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAJQhUVFRmjFjhrPLkCT99NNPevDBB9WwYUP99NNPzi4HAAAAAACnIEQHAOASPXr0UEhIiHbs2FGgbcaMGerRo4cTqnKODz74QBUrVtR3332n8PBww/t98803Cg4OVvfu3Qtt37Rpk5KTk623Dxw4oLVr1zq8XntdXhcAAMD1Kjc3V5GRkQoJCdGhQ4ecXQ4AlDmE6AAAXCYwMFAjRozQuXPnnF3KNbtw4cJVPzYjI0N16tSRl5eXTCaT4f0WLFighx9+WHv27FFqamqB9hkzZmjv3r3W259//rnWrVt31XU5yuV1AQAAXK82bNignJwctWjRQgsXLnRaHdcydwWAkkSIDgDAZbp16yYfHx+9/fbbV7xfcHCwFi1aZLOtRYsW1uVYpk6dqk6dOmn58uVq1aqVGjZsqIEDB+rEiRMaMGCAGjVqpMjISK1evdpmH+fOndOwYcMUERGhe++9V2+88YbNG4r169frkUceUXh4uFq1aqXhw4crIyPDpq4PP/xQHTp0UJcuXQzrT0xMVKdOndSoUSO1adNGY8eO1ZkzZyRJbdu21Xfffae1a9cqLCxMu3fvLnQff/zxhzZv3qxnn31WLVq00Mcff1ygP1JSUjRu3DhFRUVp4MCBSkhIsO43/0qnTz/9VG3btlV4eLjatGmjiRMnKjs7W5J0+PBhBQcHa8OGDYqLi1N4eLjatWunvXv3asGCBbrvvvt011136cUXX5TZbLb2ffv27bV06VJFRUWpUaNG6tmzp/74449C65Kkbdu2KTY2VnfddZcaN26sxx57TD/88INh/wEAAJQXH3/8sTp27KhHH31US5cuVVZWlrUtNzdXkydP1n333ac777xT0dHR2rZtm7X98OHDevbZZ9WoUSM1bdpUQ4cOtc5NZ8yYoRYtWtgca9GiRQoODrbeLmzueuLECb3wwgu65557dOedd6pDhw5asWKFzX42b96sLl26KDw8XJGRkZo+fbry8vI0depU3XfffdZ5Yb6ePXtq0KBBDukvANcfQnQAAC7j4eGh119/XQsWLNCePXuuaT9paWnat2+f1q1bp7lz52rt2rXq1auXevXqpZ07d6ply5YaPXq0LBaL9XFLlixRy5Yt9e2332rq1KlavHixEhISJEl79uzRkCFD1L9/f+3atUuLFi3S0aNHNXToUJtjJyYmavLkyQXebORbtmyZXn/9dQ0ePFg7duzQ7NmztXPnTut+1q1bpyZNmuihhx5ScnKyGjduXOh+FixYoNDQUIWEhCgmJkarVq1SZmamtX3r1q2SpFdffVVffvml3n77bZv93nLLLVq7dq2mTJmicePG6YcfftCsWbO0ZcsWjR8/3tqP0sXlZSZNmqStW7fK29tb/fv3199//60vvvhC7733nlasWKFNmzZZH/Pnn39q9+7dWrlypTZu3Ciz2ax+/foVWldubq769eunRx55RDt37tTWrVvVtGlTDR48uMAbMAAAgPLk559/1vfff6+YmBi1adNGnp6eWr58ubV99uzZWr16tT788EN9//336tChg/r06aOjR4/KbDbrueee07/+9S998803WrNmjf73v/8VmJsW5fK56/Dhw3X48GH997//1a5du9SjRw+99NJL+uWXXyRJR44c0X/+8x899thj+v777zV37lwtWrRIc+fOVbdu3XT8+HF99dVX1v0fO3ZM3333nbp163btHQbgukSIDgBAIcLCwtSjRw8NHz78mn6t9OzZs3r++efl7e2tJk2aKCgoSKGhobrrrrvk6empNm3a6OTJkzpx4oT1MQ0bNlS7du3k5eWlJk2aKDIyUl9++aUk6aOPPlLr1q2tb3BuuukmDR06VFu2bNGRI0es+2jRooVCQkLk5lb4f/ULFixQu3bt1Lp1a3l5eenWW29V79699dVXXyk9Pb1Y55aVlaWlS5da34xERUWpcuXKWrJkiV199OGHH+rRRx9VkyZN5O7urttuu039+vVTUlKSTd937NhRNWvWVOXKldW8eXP9888/6t+/v7y9vXX33XcrKChIBw8etN7/3LlzGjx4sCpWrKigoCA9++yzOnDggA4fPlzouWRnZ6tChQpyd3eXr6+vBgwYoE2bNsnd3d2u8wEAAHAl8+fPV6NGjXT77bfLy8tLXbp00SeffGJtX7BggR577DHdfvvt8vDwUK9evfT666/Lw8NDW7Zs0aFDhzRkyBBVqlRJQUFBGjt2rGJjY+2q4fK569tvv60FCxbohhtukLu7u7p27aq8vDzr99ksWrRIderUUUxMjLy8vHT77bdrxowZaty4sW6++Wbdd999SkxMtO5/9erVql69uu655x4H9BiA6xEhOgAABgYOHKjz589r1qxZV72PgIAAVaxY0Xrb19dXN910k/W2j4+PJNmsv37bbbfZ7OPmm2/W//73P0nSwYMH9cUXXygsLMz657HHHpO7u7uOHj1qfUzNmjWvWNcff/yhunXr2myrU6eOLBaLdcmToixbtkxms1nt27eXdPHq765du2rRokXKy8sr1j7yz2n+/Pk25/Tiiy/KYrHor7/+st6vRo0a1n/7+vqqSpUq8vLystl2/vx56+0bbrhBQUFBBR5/6T7z+fv769VXX9WoUaPUtm1bvfbaa9ar2gEAAMqrjIwMrVq1yuYK7W7duumXX37R9u3bdfr0aaWnp9vMLd3c3NSxY0fdeOONOnz4sPWChXy33HKLHnzwQbvquHzuum/fPvXv318tWrSwXoAiyTrX+/33323mhpIUERGhiIgISVJsbKw2b96sY8eOSZJWrlypmJiYK37PDwBciYezCwAAoKzy8fHRuHHj9PTTT6tt27bFeszl4XFhV4IbXR2e7/LJvcVisYbFbm5u6tatm0aPHn3FfVwaLttznMK2F8Zisejjjz/W+fPn1apVK+t2s9mss2fP6quvvrKuNV4UNzc3DRo0SM8++2yh7fkfDlxeV1H9ePlY5J9f/gcXl3v88cfVsWNHbdmyRdu3b9ewYcPUoEEDzZkzh6vRAQBAubR48WKdP39e48aN0xtvvGHdbjKZtHDhQus2owskLBaLzbKExVHYvi6du2ZlZal3795q0qSJli9fripVqigvL0/169cv9nFbtWqlqlWravny5XrggQe0f/9+vf/++3bVCQCXIkQHAOAK7r77bnXp0kWvvPKK7r33Xps2b29vm+VGzp49a7Me+NXK/7LNfGlpaapevbqki1f2pKSk2LRnZ2fr9OnT+te//lXsY9SpU8e6pmS+3377TW5ubqpdu3aRj9+6dasOHjyojz76SLVq1bJpGzFihBYuXFjsEL2wczp58qSki1eIX62MjAxlZGQoMDBQkqzL3Vz6mwCXSk9PV1BQkNq3b6/27dvr2WefVZs2bXTgwAGbN20AAADlgdls1qJFi9S9e/cCFzNs2bJFY8aM0ZkzZxQUFKTffvvNpn3evHlq1qyZbrnlFp09e1Z//fWXdY516NAhffXVV+rZs2eB+bIk/fnnn1es6+DBg8rMzNQzzzyjKlWqSJJ1GZd8derU0YYNG2y2bdu2TSdOnNDDDz8sd3d3xcTEaOXKlcrOzlZkZKRdc2UAuBzLuQAAUIT4+Hj9/fff+vzzz222161bV19//bX1SpgZM2YYXuVsj927d+uLL75QTk6OduzYoa+//tq6ZMq///1vJScn66OPPlJ2drYyMjI0atQoPfnkk3YtodKrVy+tWbNGX3/9tS5cuKADBw7o/fffV7t27RQQEFDk4xcsWKDGjRurefPmqlGjhs2fHj16aOvWrdYPA3x8fHT48GFlZmbKYrHIx8dHaWlpOnnypM6fP68nn3xSGzZs0MqVK5WTk6Njx45p8ODBGjJkyFX1Xz4vLy+99dZbOnXqlE6cOKEPPvhAd911l2688cYCde3atUv333+/Nm/erNzcXOXm5mr37t3y8fGx3h8AAKA82bBhg/73v//pySefLDCfe/TRRxUUFKRPP/1UTzzxhBYtWqS9e/cqNzdXixYt0uTJk+Xr66sWLVqoTp06mjhxok6ePKnMzEyNGzdOmzdvlru7u26//XadPHlSu3fvlnQxYL88/L7cjTfeKA8PD3333XfKy8vT3r17NX36dPn7+1uX5evevbv++usvffTRRzp//rx+++03vfzyyzbfERQTE6PffvtNCxcu5AtFAVwzQnQAAIpQqVIlvfbaa9Z1yfONHDlSx48fV2RkpDp37qz69eurRo0ays3NvepjXbhwQU888YTWrl2rZs2aaciQIerRo4ceffRRSVJ4eLimTZumZcuWqWnTpnrooYeUnZ2tuXPnFrm8yaUefvhhDR48WOPGjVPjxo3Vr18/PfDAAza/xmvkjz/+0ObNm/X4448X2t6qVStVr15dCxculHQxsP/000/18MMP68KFC4qNjdXhw4f1wAMPKDk5WQ899JBeeeUVzZw5U3fddZc6d+6satWqafLkycU+n8JUqVJFd955px555BHrF7G+9dZb1vZL6woLC9Mrr7yiN998U02aNFGzZs302WefadasWdYr2QEAAMqTjz/+2BqCX87T01MxMTFavHixnn32WXXq1EnPPfecIiIilJiYqPfff1+1atWSh4eH3nvvPZ0+fVqtWrVS27ZtFRAQYJ3HtWrVSo899pj69++vtm3basqUKXrmmWckyXDOXLVqVY0cOVLz58/XXXfdpbfeekujR49WbGys5s+fr6lTp6pWrVqaOXOmli9froiICD355JPq3Lmz+vTpY91PtWrV1KpVK/n4+Oi+++5zfAcCuK6YLPYuXgUAAFDGzZgxQ0uXLtWXX37p7FIAAADgJLGxsWrdurWee+45Z5cCwMWxJjoAAAAAAADKjQsXLmj27NlKS0vTE0884exyAJQDLOcCAAAAAACAcmHXrl266667tGbNGr333nuqVKmSs0sCUA6wnAsAAAAAAAAAAAa4Eh0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAQKl6+eWXFRwcbP0TGhqqVq1aaeDAgdq2bVuB+0dFRWnw4MEOr2PGjBkKDg7W+fPnJUk9evRQjx49HH4c6eI5t2jRokT2fbXWr1+vyMhIhYeHa/fu3YXep6i+b9GihV5++eWSKrHYXn31VQUHB2vixInOLgUAADgQ88ayoTjzxnzvvfeegoODNWDAgFKqDpcq6udn8ODBioqKKsWKgPKDEB0AUOqCgoK0ZcsWbdmyRevWrdOECRPk5+enp556Sm+88YbNfZcsWaIxY8YUe9+7d+8u1huPp556Slu2bJG3t7fd9Rdn34sWLbLeHj58uFauXOnw41yLGTNmyN/fX//97391xx13OLucq3b27FmtXr1ad9xxh5YvX64LFy44uyQAAOBAzBudr7jzRovFos8//1x33HGHvvzyS6Wnpxe4T2F9PmLECL311lsOr/talMWaADgXIToAoNS5ubmpatWqqlq1qm6++WY1a9ZMY8eO1YQJEzRv3jwlJiZa7xsUFKTKlSsXe98//vjjFdtzc3NlsVhUsWJFVa1a9WpPwVBeXp5++uknm22VK1dWUFCQw491LTIyMlS/fn3VqlVLPj4+zi7HUFGh+KpVq3ThwgWNHz9e6enp+vrrr0upsiuzWCzKzc11dhkAALg85o3OV9x54/bt2/XHH39o3Lhx8vT01IoVKwrcp7A+L2ocnKEs1pQvLy9PZrPZ2WUYyn/eAOUNIToAoMx45JFH1KJFC82ePdu67fJfy92wYYMeffRRNW7cWI0bN1b37t317bffSrr464sTJ07U8ePHFRwcrBkzZujo0aMKDg5WYmKiunfvrvDwcJ0+fbrAr+XmW7lypR588EGFhoaqXbt22rRpk7WtsMecP3/e5lj169fXmTNnNHr0aAUHB1vruvSKG4vForlz56pt27YKDQ1V06ZNNWDAAB05csR6n6lTpyoiIkK//vqrHnvsMTVs2FAtW7bUzJkzi+zHTZs2qVu3bgoPD9edd96pxx57TNu3b5cka3/8888/WrZsmYKDg7Vjx45ijU9Rdu7cqSeeeEJNmjTRnXfeqS5dumjVqlU29/n222/VvXt3NWzYUI0bN1bv3r3122+/Wds///xzBQcHa/PmzWrTpo1iYmKueMwlS5bo/vvvV0hIiCIiIrRkyZIC97FYLJo9e7batGmjsLAwtW3bVvPnz7e5z+7du/X444+rYcOGuueee/Tiiy/q+PHjkv6vzy69Skwq+OuwUVFRGjdunIYPH66GDRvqq6++kiQlJyfr6aefVtOmTdWwYUO1b99en376abFrXLBggUJCQmx+PiTp2LFjql+/vhYsWHDFPgIAoDxi3lj25o2JiYkKDw9XgwYN9OCDDyopKcmmvbA+Dw4O1oEDBzRnzhwFBwfr6NGjkqSUlBQ9/fTTatSokRo2bKgnnnjCZimZrVu3Kjg4WN9995369Omjhg0bqkWLFvrwww916tQpDR48WI0aNdJ9992nhIQE6+M+++wzBQcHKzk5Wb169VLDhg3VrFkzTZgwwRpMF1ZTTk6OJkyYoKioKIWFhalFixZ66aWXCr3aPt+UKVPUuHFjpaSkKDo6WmFhYbrvvvtsfmYl6cSJExo2bJjuuecehYaGqkOHDgXmtMHBwZo9e7aee+45hYeH68CBA1cY1eJLS0vToEGD1KJFC4WFhalNmzaaMWOGTUj/xx9/qH///mrSpInCwsLUtWtXm591o+cNUN4QogMAypTWrVvrjz/+0F9//VWg7dChQxo0aJDatm2r5cuXKzExUWFhYerdu7f++usvDR8+XO3atbP+2u9TTz1lfexHH32k6OhorVu3TpUqVSr02IcOHdKyZcv01ltvacmSJapevboGDBhQaC2Fuemmm6xXQw0dOlRbtmwp9H4zZszQtGnTFBcXp5UrV2r69Ok6dOiQevXqpezsbEmSh4eHcnNz9dprr+k///mPVq1apfbt22vGjBnauXOnYQ3btm1T3759Va9ePS1evFiffPKJ/vWvf+mZZ57Rvn37dNNNN2nLli0KCgpSu3bttGXLFjVq1KhY53clp0+fVp8+fVS/fn0tXrxYK1asULt27TRkyBDrlTzff/+9nnnmGVWvXl2LFy9WQkKCsrOz9cQTTxR4AzJnzhy9/vrrmjVrluExf/31V/3444+Kjo6WJEVHR+ubb77RP//8Y3O/mTNn6r333tPzzz+vVatWqU+fPpo4caI1fP7tt9/073//W7fccouWLFmi999/X/v371fv3r3tvormm2++UYUKFbRy5Urdc889ysrK0pNPPik3NzfNnz9fq1atUmxsrEaNGqUvv/yyWDV27txZPj4+Wrp0qc2x1q5dK09PT3Xq1MmuGgEAKC+YN5adeWNmZqY2bNhgMy87cOCAkpOTrfcprM/zz/uJJ57Qli1bdNNNN+n333/XE088odzcXM2fP1+LFy9WlSpV9OSTT+rgwYPWc5akSZMmqWvXrlq+fLmaNWumSZMmacCAAWrVqpWWLVume+65RxMnTrR+6JD/uFGjRumpp57SypUr1bt3byUkJOijjz6SpEJrevfdd7Vq1Sq98cYbWrdund5++23t27dP8fHxhv3r4eGhs2fPauLEiYqPj9eKFSvUsWNHTZ48WWvWrJEk5eTk6N///re2b9+uCRMmaOXKlerYsaOGDx+uZcuW2ewvKSlJjRs31po1a3TbbbcZHtceL774otLT0zVnzhytW7dOQ4cO1fz58/XBBx9Iujiujz32mP744w+99957WrZsmRo3bqz//Oc/1g9b8hXneQO4MkJ0AECZctNNN0mS/v777wJtP//8s3Jzc9W1a1fVrFlTt912m4YNG6YFCxbIz89PlStXlo+Pj/XXfitWrGh9bN26dRUdHa2aNWvKza3w//4yMjI0ceJEhYeHKyQkRGPHjlVOTo7WrVtXrNrd3d2tv35bqVKlQn/tNycnRwkJCerYsaM1uG3atKnGjBmjtLQ0rV+/3nrf7OxsPfXUU7r33ntVo0YN9enTR5K0Z88ewxrmzp2rGjVqaOzYsQoJCdEdd9yhCRMmyNfXVwsWLJC7u7uqVq0qNzc3+fj4qGrVqvLy8irW+V3JoUOHdPbsWT388MO65ZZbVKtWLfXu3VuLFy9WnTp1JEmzZ89WtWrVNHHiRAUHBys8PFxTpkzR6dOnC1xt07ZtWzVt2lTVqlUzPObixYt18803q3nz5tbH+Pj42LzhuHDhghISEvT444+rc+fOqlWrlrp27ar+/fvrzJkzkqT58+fL399fY8aM0e23366GDRtq1KhRqlu3rvVq9OI6c+aMXn75ZdWuXVsVK1aUj4+PVq5cqalTpyo4OFg1atRQr169dMMNN+ibb74pVo2VK1dW+/bttWzZMptQf82aNXrggQfk7+9vV40AAJQXzBvLzrxx+fLlcnd3V4cOHSRJTZo0UZ06dWyuRi+sz/PP29fXV1WrVpW7u7v1yvG3335bYWFh1i+Qr1SpkubNm2dz3MjISLVt21Z16tRRr169ZLFYVKtWLT3yyCOqXbu2evTooby8PO3fv1+SZDKZJEmdO3dWZGSkatWqpaeeekpNmza1Lj9TWE0pKSkKDg5Ws2bNVL16dUVERGj27Nl68cUXDfvXZDLJYrGoV69euvvuu3XLLbcoPj5etWrVsh5r48aNOnDggMaOHavIyEjdcssteu655xQVFVXgYpKKFSuqd+/eqlmzpkPm79LFK/7vvfde3XHHHapevboeeughLVq0yDqOS5Ys0fHjxzVlyhRFRETotttu0/Dhw1WvXj3NnTvXZl/Fed4AroyfagBAmZKTkyNJhX5x01133aUbbrhBTzzxhD744APt27dPJpNJjRo1snnjU5jQ0NAij12rVi1VqVLFert69ery9/e3XvHiCAcPHlRWVpaaNGlis71hw4Zyd3fXzz//bLP9zjvvtP47ICBAknTy5EnD/ScnJysiIsL6BkGSfHx8FBISUmDfjnT77berTp066t+/v9555x39+OOPMpvNCg8Pt9b9008/qXHjxvL09LQ+rmrVqrr99tttfj1XKnq8cnJytGLFCj3yyCPKy8tTbm6uPD091b59e5s3awcPHtSZM2fUoEEDm8c/99xz6tu3r6SLfVa/fn2byf5dd92lSZMm2b3+aUhIiNzd3a233d3dtX//fv3nP/9R8+bN1ahRIzVq1Ejp6enKzMwsdo3du3dXWlqatm3bJkn6888/9eOPPxa53A0AAOUZ88ayM29csmSJ9YKG3Nxc5ebmqnPnzlq1alWBZXCK8tNPPyk4ONh6DtLFMW7UqFGBOWNYWJj134GBgZIuzsfy5e/j8uVFGjdubHO7fv36Vxy7Bx54QN9884369++vlStX6vjx46pWrZp1GZ4rufxYISEh1mP99NNPMplMuvvuu23uc8899+jgwYPW+aJUvJ9Le7Vp00YzZ87UuHHj9PXXXys7O1t169bVzTffbK2vatWqNle+m0wmNWvWzO75O+DqPJxdAAAAl/rjjz9kMpkKvQK5WrVqSkxM1IcffqgFCxZo0qRJuvHGG9W/f3/rr44aKc6XTBV2Ra+Pj4/Onj1b/BMoQv7Vz5cfy83NTZUqVbK256tQoYL13/lvcK60xMiZM2cKPQ9/f38dO3bMrlrd3d2v+KVFFy5csAbivr6+WrRokT766COtWLFC06dPV0BAgJ5++mk9++yzMplMOn36tNatW2ezjIl0cX3QS4Nnqejx+uKLL5SRkaF3331X7777boH23bt3q3HjxtY3TFf6EqzTp0+rRo0aVzxecfn5+dncTk1NVb9+/dS4cWO9/fbbqlKlitzc3NSjRw+b4xdVY/76oklJSWrevLlWr16tmjVrqmnTpg6pGwAAV8S8sWzMG3/66ScdOHBABw4cKLAEiSStX79eHTt2LPb+Tp8+rbS0tAJLx+Tk5BSYa106f8o/58K2Xd4Pl4+xr6+vLly4oNzcXOuSL5fq1q2b/vWvf+nTTz/Vq6++qvPnz+vuu+/WqFGjilxapbBj5S/Fc/r0aVkslgJzuvwvqD9+/Lj1g4DLz70w9szfpYvL4XzyySdatWqVFi5cKA8PD3Xs2FHDhg1T5cqVdfr0aR0/frzAWFy4cEEXLlywnkdh5wmUN4ToAIAyZf369brzzjutV5Jc7uabb9aIESM0YsQI/frrr5o/f76GDx+uWrVqFbiCw16FfQHOqVOnrFcrXXqVTr5z587ZdYz8yeXlVwWZzWadPn36mieflStXLvSKo8zMTLvXJqxatarhup6nTp3SyZMndeONN1q3BQUFaciQIRoyZIiOHj2qJUuWaMqUKapataq6dOkiPz8/tWjRQgMGDCiwP3t/JTUxMVF33XWXhg0bVqAtPj7eumZkfn9eehXP5SpXrqyMjAzD9sLGXSre2K9bt04mk0nvv/++9ecoLy/P5k1vcWqUpNjYWE2YMEHZ2dlavXq1YmJiDGsDAOB6wLyxbMwbExMTVatWLU2ZMqVA21tvvaWkpCS7QnQ/Pz/deOONGjduXIE2Ry0Tcvl5nz17Vt7e3oUG6PlatWqlVq1aKScnR9u2bdNbb72l3r1764svvrjinOzUqVPWpXvyj5X/c+Ln5ydvb+9CP3yQ/m/JouKqWrWqMjIylJ2dLV9f3wLtR44csZm/u7u7q0ePHurRo4dOnjyp9evX680335TFYtH48ePl5+enGjVqFFi6JV9hvwUClFcs5wIAKDPmz5+vlJQUPfvss4W2//zzz9blLKSL6+6NGTNGAQEBSklJsW6398sg8/3+++82X3B58OBBZWdn6/bbb5f0f1d/XBqApqamFrovoxpuvfVWVa5cWd99953N9l27dikvL8/mV1KvRsOGDfX999/bHD8rK0upqakKDw+3a1+RkZFKTU3Vb7/9VqDt008/lSQ9+OCDkqTDhw/bXGFeo0YNDRo0SPXr17eOzZ133qlDhw6pdu3aNn9yc3PtWjbl6NGj2rZtm7p06aKwsLACfx5++GGtWbNGZ8+e1a233qpKlSrp+++/t9nHzJkz9fLLL0u6+GvAKSkpNlfS7NmzR3FxcTp8+LD1Cq1Lx/3StTWvJCsrS15eXja/Nr5u3TplZWVZx6g4NUpSx44d5ebmpo8++kj79+9Xly5dittlAACUO8wby8a88ezZs1q9erU6dOhQ6LysS5cu2r59u44ePWp9TGHne+m2/DnjTTfdZDNntFgs+te//nUNZ/x/fvjhB5vbqamp1rG7vKa8vDxt2LBBf/75p6SLF39ERkZq0KBBOnr06BWXzJFks+yJxWKxOdadd96p8+fPKzs72+ZcfXx85OfnZ/eFJpGRkZKk//73vwXafvzxR+3fv986f8/MzNTy5cutV737+/srJiZGXbp00d69e631/e9//1OlSpVs6nN3d9cNN9zA2ue4rvDTDgAodXl5efrnn3/0zz//6H//+5++//57DR8+XG+88Yb69u2r+++/v9DH/fjjj/rPf/6jpKQkHTlyRH/88YcSEhJ0+vRp3XXXXZIuvmHJzMzU9u3b9fvvvxe7JovFooCAAL388svau3ev9u3bpzFjxqhChQpq27atpP9bc3HJkiUym836/fffNWfOHJsrMPLfMH3//fdKTU0tcMWRp6ennnrqKa1cuVLz58/X4cOH9e2332rEiBG69dZb1aZNm+J3ZCGeeeYZpaWladSoUTpw4ICSk5M1ZMgQmc1mmyVEiqNHjx6qW7eunn32Wa1atUqHDx9WcnKypk6dqunTp+u5556zvgH4448/9Pzzz+vDDz/U4cOHdfToUS1btky//vqrdR3PZ555Rvv27dPo0aO1f/9+a/917NhRW7ZsKXZdS5YskYeHh3VcLtehQwdlZWVp7dq18vT0VM+ePbV8+XItWrRIf/zxh1asWKFZs2ZZ18vs0aOHLly4oJdffln79+/Xnj179Nprr+ncuXOqVauWKlWqpDp16mjdunXKzMxUVlaWpkyZory8vCJrbdiwobKyspSQkKAjR44oKSlJn3zyiRo1aqRffvlFR44cKVaN0sVf0e7YsaPeffddRUZG2r1eOwAAroh5Y9meN65atUpZWVl6+OGHC21v06aNvLy8tHTpUkmF97mfn5/27Nmjn3/+WadOnVLPnj2VlZWlIUOGaO/evTpy5IgWL16szp07F/gy+qu1ePFirV+/XocPH9bcuXO1c+dOde3a1dp+aU1nzpzRnDlzNGjQIH3//ff666+/tHfvXi1atEj16tWzWbu9MHPmzNHWrVt16NAhTZw4UWlpadZjtW7dWvXq1dOLL76ob7/9Vmlpafr666/1xBNPaNSoUXafV6NGjRQTE6M33nhDH3zwgfbv369ff/1VixcvVt++fdWkSRPFxsZKuvjcGj16tEaMGKF9+/bpr7/+0rZt2/TFF19Yf1Oja9eu8vf318CBA7V7924dPXpUa9asUUxMjN555x276wNcGcu5AABKXXp6uu69915JF3/VtUqVKgoNDdUHH3ygFi1aGD4uLi5O586d04cffqixY8fK3d1ddevW1fTp061Xy8TExGjTpk3q27evunfvrscff7xYNeXm5uqOO+5Q+/btNXjwYP3111+qU6eO3n33XWtY2ahRIw0aNEgLFy7Uu+++q3r16mnkyJF68sknrVdw+Pn5qVevXlq8eLG+++47LV68uMCx+vbtKx8fH3388ceaOHGiKleurPvuu08vvvii3VebXO7uu+/W+++/r5kzZ+rRRx+Vh4eHGjZsqPnz5xe5XuPlKlSooE8++USzZ8/WjBkz9Oeff8rb21shISF688031a5dO+t9W7ZsqTfeeEPz58/XjBkzJF38wq2RI0da30xGRERo7ty5mjFjhrp16yY3NzfVrVtXU6dOVatWrYpVU15enpYuXaqWLVsargtZp04d6/rhXbt21YABA+Tj46O5c+fq9ddfV/Xq1TVkyBD9+9//lnTxyrSPPvpIU6ZMUUxMjCpUqKB7771X8fHx1qtrJk6cqDFjxigyMlJBQUF6/PHH9cADD2jdunVXrLdDhw5KTk7WrFmzNH36dDVr1kyTJ0/W7t279eqrr+rZZ5/V2rVri6wxX/v27bVo0SJ169atWP0FAICrY95YtueNSUlJCgkJUd26dQttr1Spklq1aqWlS5eqX79+Bfr8pZdeUt++fTVz5kw9/fTTevfdd3XnnXdqwYIFmjp1qnr06KG8vDzVrl1bL730kuLi4q7pnPMNGzZMs2bN0p49e1ShQgU999xzeuyxx6ztl9f0zjvvaNKkSRo0aJAyMzMVEBCgpk2b6rXXXivWscaOHasDBw4oMDBQw4cPt859vby8lJCQoLfeektDhgzR6dOn9a9//Utt27bVwIEDr+rcxo4dq7CwMC1ZskTvvPOO8vLyVKtWLT355JN68sknrUvWBAUF6aOPPtL06dPVq1cvnT17VtWqVdNDDz1kXX4xICBAn3zyid566y316dNH586dU/Xq1dWzZ0/17t37quoDXJXJcrW/uwQAAIBSNW7cOH3zzTdau3Yt66EDAADY6fPPP9ewYcO0ceNGh32xvJEZM2Zo5syZxVoCEEDZx5XoAAAAZVhubq6OHTumL7/8UgsXLtTMmTMJ0AEAAACgFBGiAwAAlGH//POP2rdvr8qVK2vUqFGGa78CAAAAAEoGy7kAAAAAAAAAAGDAzdkFAAAAAAAAAABQVhGiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABjwcHYBAP7PP/+cLpXjuLmZFBRUUenpWcrL47uFyyrGyXUwVq6BcXINjJPrKM2xqlq1conu/3pgsVh4XtmB1yL70F/2o8/sQ3/Zjz6zD/1lv/LUZ8WZa3IlOnAdcnMzyWQyyc3N5OxScAWMk+tgrFwD4+QaGCfXwVi5FsbKPvx824f+sh99Zh/6y370mX3oL/tdb31GiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAgIezCwAAAABQunwTV1/zPkwmk9S3mwOqQWlyxNiXlOyY9s4uAQAAoFBciQ4AAAAAAAAAgAFCdMDAG2+8oeDgYOvtbdu2qVOnTgoLC9MDDzygFStW2Nx/3rx5at26tcLDwxUTE6OUlJTSLhkAAAAAAACAgxGiA4X4+eeftWzZMuvtY8eOqW/fvoqOjtbOnTs1fPhwjRgxQnv27JEkbdiwQdOmTdP48eO1Y8cORUZGqk+fPjp79qyTzgAAAAAAAACAIxCiA5fJy8vTqFGj9OSTT1q3rVy5UrVr11bPnj3l6+urVq1a6f7771diYqIkKTExUdHR0WrWrJl8fX3Vr18/mUwmbdy40VmnAQAAAAAAAMAB+GJR4DKffvqpfHx81LFjR02bNk2SlJqaqgYNGtjcr379+lqzZo21vX37//siJJPJpJCQEKWkpKhjx47FPrabm0lubqZrP4kiuLu72fyNsolxch2MlWtgnFwD41Q6TKZrn2/k74KxAgAAQHlHiA5c4vjx43rnnXe0YMECm+0ZGRkKCQmx2RYQEKD09HRre0BAgE27v7+/tb24goIqOuRNbXH5+fmW2rFw9Rgn18FYuQbGyTUwTiUrx9PdYftirAAAAFDeEaIDlxg/fry6deumW2+9VUePHi3y/vmBt1HwbW8gnp6eVWpXovv5+erUqWyZzXklfjxcHcbJdTBWroFxcg2MU+nwumC+5n2YTJKXVCpjFRhYsUT3DwAAAFwJITrw/23btk179+7VG2+8UaAtKChImZmZNtsyMjIUFBQkSQoMDCy0vV69enbVkJdnUV6exa7HXAuzOU+5uQQUZR3j5DoYK9fAOLkGxqlkeVocMd+4+ME/YwUAAIDyjgUMgf9vxYoV+t///qeWLVuqadOm6tq1qySpadOmCg4OVkpKis399+zZo/DwcElSWFiY9u7da20zm81KTU21tgMAAAAAAABwTYTowP/38ssva926dVq+fLmWL1+u2bNnS5KWL1+uhx9+WGlpaUpISNC5c+e0du1abd68WbGxsZKk7t27KykpSdu3b1dWVpamTJkiHx8fRUVFOfOUAAAAAAAAAFwjlnMB/j9/f3/5+/tbb+fm5kqSbrzxRknSrFmzNHbsWE2ePFnVq1fX5MmTrV822rJlS8XHx2vYsGE6ceKEQkNDNXv2bHl7e5f+iQAAAAAAAABwGEJ0wECNGjW0f/9+6+2IiAgtX77c8P5xcXGKi4srjdIAAAAAAAAAlBKWcwEAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAHCQo0ePqm/fvrr77rt1zz33KD4+XidPnpQkbdu2TZ06dVJYWJgeeOABrVixwuax8+bNU+vWrRUeHq6YmBilpKRY286fP6+RI0fq7rvvVqNGjTRgwAClp6eX6rkBAABcrwjRAQAAAMBB+vbtq4CAAG3atEkrVqzQoUOHNGnSJB07dkx9+/ZVdHS0du7cqeHDh2vEiBHas2ePJGnDhg2aNm2axo8frx07digyMlJ9+vTR2bNnJUlvvvmmdu/eraSkJH355ZfKycnRK6+84sxTBQAAuG4QogMAAACAA5w+fVqhoaEaOnSoKlasqKpVq6pz5876/vvvtXLlStWuXVs9e/aUr6+vWrVqpfvvv1+JiYmSpMTEREVHR6tZs2by9fVVv379ZDKZtHHjRuXm5mrp0qUaNGiQatasqcDAQMXHx2vTpk06duyYk88aAACg/PNwdgEAAAAAUB5UrlxZ48ePt9mWlpamm266SampqWrQoIFNW/369bVmzRpJUmpqqtq3b29tM5lMCgkJUUpKiho0aKAzZ87YPP7WW2+Vr6+vUlJSVK1atWLV5+7uJpPJdLWnV+I8PMrONV7u7m42f+PK6C/70Wf2ob/sR5/Zh/6y3/XWZ4ToAAAAAFACkpOTtXDhQk2fPl0JCQkKCQmxaQ8ICLCua56RkaGAgACbdn9/f6WnpysjI8N6+1J+fn52rYvu5+erHE/3qziT0lExsKKzSyjAz8/X2SW4FPrLfvSZfegv+9Fn9qG/7He99BkhOgAAAAA42K5du9S3b1/Fx8crMjJSCQkJhd4v/8pwoyvEi7py3J4ry0+dypb7BXOx71/asjKynF2Clbu7m/z8fHXqVLbM5jxnl1Pm0V/2o8/sQ3/Zjz6zD/1lv/LUZ4HF+CCfEB0AAAAAHGjTpk168cUX9dprr6lDhw6SpKCgIGVmZtrcLyMjQ0FBQZKkwMDAQtvr1aunG264QZKUmZmpChUqSJIsFosyMzOtbcVhNufJzWK5yrMqebm5Ze8NuNmcVybrKqvoL/vRZ/ahv+xHn9mH/rLf9dJn18eiNQAAAABQCnbv3q2XXnpJ06dPtwbokhQWFqaUlBSb++7Zs0fh4eHW9r1791rbzGazUlNTFR4erpo1ayogIMDm8fv371dOTo5CQ0NL+IwAAABAiA4AAAAADpCbm6tXX31VAwcOVPPmzW3aOnbsqLS0NCUkJOjcuXNau3atNm/erNjYWElS9+7dlZSUpO3btysrK0tTpkyRj4+PoqKi5O7urm7dumnatGk6cuSITpw4ofHjx+uhhx5SlSpVnHGqAAAA1xWWcwEAAAAAB/jxxx/122+/acKECZowYYJN29q1azVr1iyNHTtWkydPVvXq1TV58mTrl422bNlS8fHxGjZsmE6cOKHQ0FDNnj1b3t7ekqT+/fsrKytLXbt2ldlsVuvWrTV69OjSPkUAAIDrEiE6AAAAADhARESE9u/fb9h+8803a/ny5YbtcXFxiouLK7TNy8tLI0eO1MiRI6+5TgAAANiH5VwAAAAAAAAAADBAiA4AAAAAAAAAgAFCdOAyP//8s/79738rIiJCzZo108CBA/X333/ryJEjCg4OVlhYmM2fNWvWWB87b948tW7dWuHh4YqJiVFKSooTzwQAAAAAAADAtSJEBy5x/vx5Pf3002rSpIm+/fZb/fe//9Xx48c1evRonT59Wp6enkpOTrb5065dO0nShg0bNG3aNI0fP147duxQZGSk+vTpo7Nnzzr5rAAAAAAAAABcLUJ04BLnzp3T4MGD1adPH3l5ealKlSp66KGH9Ouvv+rkyZPy9/c3fGxiYqKio6PVrFkz+fr6ql+/fjKZTNq4cWMpngEAAAAAAAAARyJEBy7h7++vmJgYeXh4yGKx6ODBg1q6dKnatWunU6dOKS8vT71791aTJk304IMPau7cubJYLJKk1NRUNWjQwLovk8mkkJAQlnQBAAAAAAAAXJiHswsAyqK0tDQ9+OCDMpvNio2N1cCBA/XVV1+pVq1a6t69u6ZPn65du3ZpwIAB1uA9IyNDAQEBNvvx9/dXenp6sY/r5maSm5vJwWdTkLu7m83fKJsYJ9fBWLkGxsk1ME6lw2S69vlG/i4YKwAAAJR3hOhAIW6++Wbt3btXv//+u0aMGKEXX3xRkydPVlRUlPU+LVq0UGxsrJKSkhQTE2P4ZtSeN6lBQRUd8qa2uPz8fEvtWLh6jJPrYKxcA+PkGhinkpXj6e6wfTFWAAAAKO8I0QEDJpNJderUUXx8vKKjozV8+HAFBQXZ3KdGjRpav369JCkwMFCZmZk27RkZGapXr16xj5menlVqV6L7+fnq1Klsmc15JX48XB3GyXUwVq6BcXINjFPp8LpgvuZ9mEySl1QqYxUYWLFE9w8AAABcCSE6cIlt27ZpxIgRWrt2rTw8Lj498vIuvin87rvvdPz4cT3++OPW+x88eFA1a9aUJIWFhWnv3r3q3LmzJMlsNis1NVXR0dHFPn5enkV5eRYHnU3RzOY85eYSUJR1jJPrYKxcA+PkGhinkuVpccR84+IH/4wVAAAAyjsWMAQuERoaquzsbE2ePFnZ2dlKT0/XjBkzFBERIU9PT02aNElbt25Vbm6uvv32WyUlJVlD9e7duyspKUnbt29XVlaWpkyZIh8fH5slYAAAAAAAAAC4Fq5EBy5RuXJlzZ07VxMnTtR9990nDw8PNW3aVK+//rqqVaumV155RWPGjNGxY8dUo0YNjRgxQm3atJEktWzZUvHx8Ro2bJhOnDih0NBQzZ49W97e3k4+KwAAAAAAAABXixAduEz9+vWVkJBQaFtsbKxiY2MNHxsXF6e4uLgSqgwAAAAAAABAaWM5FwAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAduMzPP/+sf//734qIiFCzZs00cOBA/f3335Kkbdu2qVOnTgoLC9MDDzygFStW2Dx23rx5at26tcLDwxUTE6OUlBRnnAIAAAAAAAAAByFEBy5x/vx5Pf3002rSpIm+/fZb/fe//9Xx48c1evRoHTt2TH379lV0dLR27typ4cOHa8SIEdqzZ48kacOGDZo2bZrGjx+vHTt2KDIyUn369NHZs2edfFYAAAAAAAAArhYhOnCJc+fOafDgwerTp4+8vLxUpUoVPfTQQ/r111+1cuVK1a5dWz179pSvr69atWql+++/X4mJiZKkxMRERUdHq1mzZvL19VW/fv1kMpm0ceNGJ58VAAAAAAAAgKvl4ewCgLLE399fMTExkiSLxaJDhw5p6dKlateunVJTU9WgQQOb+9evX19r1qyRJKWmpqp9+/bWNpPJpJCQEKWkpKhjx47FOr6bm0lubiYHnY0xd3c3m79RNjFOroOxcg2Mk2tgnEqHyXTt8438XTBWAAAAKO8I0YFCpKWl6cEHH5TZbFZsbKwGDhyop59+WiEhITb3CwgIUHp6uiQpIyNDAQEBNu3+/v7W9uIICqrokDe1xeXn51tqx8LVY5xcB2PlGhgn18A4lawcT3eH7YuxAgAAQHlHiA4U4uabb9bevXv1+++/a8SIEXrxxRcN75sfehuF3/aE4unpWaV2Jbqfn69OncqW2ZxX4sfD1WGcXAdj5RoYJ9fAOJUOrwvma96HySR5SaUyVoGBFUt0/wAAAMCVEKIDBkwmk+rUqaP4+HhFR0crMjJSmZmZNvfJyMhQUFCQJCkwMLDQ9nr16hX7mHl5FuXlWa619GIzm/OUm0tAUdYxTq6DsXINjJNrYJxKlqfFEfONix/8M1YAAAAo71jAELjEtm3b1KZNG+Xm5lq35eVdfFPYvHlzpaSk2Nx/z549Cg8PlySFhYVp79691jaz2azU1FRrOwAAAAAAAADXQ4gOXCI0NFTZ2dmaPHmysrOzlZ6erhkzZigiIkIdO3ZUWlqaEhISdO7cOa1du1abN29WbGysJKl79+5KSkrS9u3blZWVpSlTpsjHx0dRUVFOPisAAAAAAAAAV4sQHbhE5cqVNXfuXP3888+677771L59e1WsWFFTpkzRDTfcoFmzZmnp0qVq0qSJpk6dqsmTJ1u/bLRly5aKj4/XsGHDdM899+iHH37Q7Nmz5e3t7eSzAgAAAAAAAHC1WBMduEz9+vWVkJBQaFtERISWL19u+Ni4uDjFxcWVUGUAAAAAAAAAShtXogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAACAg3zzzTdq3ry5Bg8ebLP9yJEjCg4OVlhYmM2fNWvWWO8zb948tW7dWuHh4YqJiVFKSoq17fz58xo5cqTuvvtuNWrUSAMGDFB6enqpnRcAAMD1zMPZBQAAAODq+Saudsh+smPaO2Q/wPVszpw5WrJkiWrXrl2g7fTp0/L09FRycnKhj92wYYOmTZum9957Tw0bNtQHH3ygPn36aP369apQoYLefPNN7d69W0lJSapUqZKGDRumV155Re+//35JnxYAAMB1jyvRAQAAAMABvL29DUP0kydPyt/f3/CxiYmJio6OVrNmzeTr66t+/frJZDJp48aNys3N1dKlSzVo0CDVrFlTgYGBio+P16ZNm3Ts2LGSPCUAAACIK9EBAAAAwCF69uxp2Hbq1Cnl5eWpd+/e+uGHHxQYGKhu3brp6aeflslkUmpqqtq3/7/fCDGZTAoJCVFKSooaNGigM2fOqEGDBtb2W2+9Vb6+vkpJSVG1atWKVZ+7u5tMJtPVn2AJ8/AoO9d4ubu72fyNK6O/7Eef2Yf+sh99Zh/6y37XW58RogMAAABACfP09FStWrXUvXt3TZ8+Xbt27dKAAQPk7++vmJgYZWRkKCAgwOYx/v7+Sk9PV0ZGhvX2pfz8/OxaF93Pz1c5nu7XfC4lpWJgRWeXUICfn6+zS3Ap9Jf96DP70F/2o8/sQ3/Z73rpM0J0AAAAAChhUVFRioqKst5u0aKFYmNjlZSUpJiYGMMrxIu6ctyeK8tPncqW+wVzse9f2rIyspxdgpW7u5v8/Hx16lS2zOY8Z5dT5tFf9qPP7EN/2Y8+sw/9Zb/y1GeBxfggnxAdAAAAAJygRo0aWr9+vSQpMDBQmZmZNu0ZGRmqV6+ebrjhBklSZmamKlSoIEmyWCzKzMy0thWH2ZwnN4vFMcWXgNzcsvcG3GzOK5N1lVX0l/3oM/vQX/ajz+xDf9nveumz62PRGgAAAABwonXr1mnhwoU22w4ePKiaNWtKksLCwrR3715rm9lsVmpqqsLDw1WzZk0FBAQoJSXF2r5//37l5OQoNDS0dE4AAADgOkaIDgAAAAAlzNPTU5MmTdLWrVuVm5urb7/9VklJSXr88cclSd27d1dSUpK2b9+urKwsTZkyRT4+PoqKipK7u7u6deumadOm6ciRIzpx4oTGjx+vhx56SFWqVHHymQEAAJR/LOcCAAAA+Saudsh+smPaO2Q/gCsKCwuTJOXm5kqSvvjiC0lScnKyoqKi9Morr2jMmDE6duyYatSooREjRqhNmzaSpJYtWyo+Pl7Dhg3TiRMnFBoaqtmzZ8vb21uS1L9/f2VlZalr164ym81q3bq1Ro8eXfonCQAAcB0iRAcAAAAAB0hOTr5ie2xsrGJjYw3b4+LiFBcXV2ibl5eXRo4cqZEjR15TjQAAALAfy7kAAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdLi8rKwsTZ061Xp76dKl6tKli4YMGaKTJ086sTIAAACUdcwlAQAAUBRCdLi8N954Q1u3bpUkHTp0SCNHjlRUVJRycnI0YcIEJ1cHAACAsoy5JAAAAIri4ewCgGv19ddfKzExUZK0Zs0aNW/eXP3791dmZqY6derk5OoAACicb+JqZ5cAQMwlAQAAUDSuRIfLy8rK0k033SRJ2r59u1q0aCFJCggI0KlTp5xZGgAAAMo45pIAAAAoCiE6XF5QUJCOHDmi48eP64cfftB9990nSfrzzz9VuXJlJ1cHAACAsoy5JAAAAIrCci5weZ07d1ZcXJzc3d3VokUL3XLLLcrKytJLL72k1q1bO7s8AAAAlGHMJQEAAFAUQnS4vP79++u2227T8ePH9eijj0qSPD09VadOHb300ktOrg4AAABlGXNJAAAAFIUQHS4tNzdXCQkJeuaZZ2y2e3l5aezYsVe1z6NHj+r111/Xrl275O7urvvuu0/Dhw/XqVOn1KZNG3l5edncf9KkSWrXrp0kad68eUpISNCJEycUHBys0aNHq0GDBld3cgAAAChRJTGXBAAAQPnDmuhwaR4eHpozZ47OnTvnsH327dtXAQEB2rRpk1asWKFDhw5p0qRJOn36tDw9PZWcnGzzJz9A37Bhg6ZNm6bx48drx44dioyMVJ8+fXT27FmH1QYAAADHKYm5JAAAAMofQnS4vBdffFFjxozRvn37dObMGeXk5Nj8scfp06cVGhqqoUOHqmLFiqpatao6d+6s77//XidPnpS/v7/hYxMTExUdHa1mzZrJ19dX/fr1k8lk0saNG6/1FAEAAFBCHDmXBAAAQPnEci5wea+//rpyc3O1dOnSQtt//vnnYu+rcuXKGj9+vM22tLQ03XTTTTp16pTy8vLUu3dv/fDDDwoMDFS3bt309NNPy2QyKTU1Ve3bt7c+zmQyKSQkRCkpKerYsePVnRwAAABKlCPnkgAAACifCNHh8kaOHFli+05OTtbChQs1ffp0mc1m1apVS927d9f06dO1a9cuDRgwQP7+/oqJiVFGRoYCAgJsHu/v76/09PRiH8/NzSQ3N5ODz6Igd3c3m79RNjFOroOxcg1lbZxMppJ/vXcGD49r69+yNk7llSN+/vJ34epjVZJzSQAAAJQPhOhweV26dCmR/e7atUt9+/ZVfHy8IiMjJUlRUVHW9hYtWig2NlZJSUmKiYkxfDNqz5vUoKCKpRqq+Pn5ltqxcPUYJ9fBWLmGsjJOOZ7uzi6hRFQMrOiQ/ZSVcSqvHPnz5+pjVVJzSQAAAJQfhOgoF1avXq1Fixbpr7/+0hdffKGcnBzNnz9fzzzzzFXtb9OmTXrxxRf12muvqUOHDob3q1GjhtavXy9JCgwMVGZmpk17RkaG6tWrV+zjpqdnldqV6H5+vjp1Kltmc16JHw9Xh3FyHYyVayhr4+R1wezsEkpEVkbWNT2+rI1TeeWInz+TSfKSSmWsAh304YwRR88lAQAAUL4QosPlJSYmavz48erSpYt++uknSRfD608++cS6hrk9du/erZdeeknTp09X8+bNrdvXrVun48eP6/HHH7duO3jwoGrWrClJCgsL0969e9W5c2dJktlsVmpqqqKjo4t97Lw8i/LyLHbVey3M5jzl5hJQlHWMk+tgrFxDWRknT0vpvd6XJkf1bVkZp/LKMT9/Fz/4d/WxcvRcEgAAAOWPay9gCEj6+OOP9e6772rEiBHWbdWqVdP06dOVmJho175yc3P16quvauDAgTYBuiR5enpq0qRJ2rp1q3Jzc/Xtt98qKSnJGqp3795dSUlJ2r59u7KysjRlyhT5+PjYLAEDAACAssWRc0kAAACUT1yJDpd35MgRNW3aVJLt+uMNGjTQ33//bde+fvzxR/3222+aMGGCJkyYYNO2du1avfLKKxozZoyOHTumGjVqaMSIEWrTpo0kqWXLloqPj9ewYcN04sQJhYaGavbs2fL29r7GMwQAAEBJceRcEgAAAOUTITpcXsWKFXX06FHrsir5kpOTVblyZbv2FRERof379xu2x8bGKjY21rA9Li5OcXFxdh0TAAAAzuPIuSQAAADKJ0J0uLx27drphRde0PPPPy+LxaK9e/dq7969mjVr1hW/FBQAAABgLgkAAICiEKLD5Q0dOlQTJkxQv379lJubq+joaHl4eCg2NlYvvPCCs8sDAABAGcZcEgAAAEUhRIfL8/Ly0siRIzV06FD98ccfMplMqlWrlnx9fZ1dGgAAuAo5c5PkdcEsT4vlmvaTHdPeQRWhPGMuCQAAgKIQoqNc2LJli+69916FhIRo//79mjZtmm699dYrrl8OALj+EM4CKAxzSQAAAFwJITpc3pw5czR37lzt2LFDmZmZ6tWrl26//XZt2rRJ6enp6tu3r7NLLJMcESQRIgEAAFfHXBIAAABFcXN2AcC1SkxM1KxZsyRJq1at0k033aQFCxZo7ty5WrZsmXOLAwAAQJnGXBIAAABFIUSHy/vnn3905513SpK2b9+uNm3aSJJq1aqlv//+24mVAQAAoKxjLgkAAICisJwLXF7FihV1+vRpeXt7a+fOnXryySclSadPn5aXl5eTqwMA1+WbuPqa98GyTwDKOuaSAAAAKAohOlxe8+bNNWjQIHl4eCgoKEiNGzdWbm6uZs6cqYYNGzq7PAAAAJRhzCUBAABQFJZzgct79dVXVaNGDXl6euqdd96RJGVnZ+urr77Syy+/7OTqAAAAUJYxlwQAAEBRuBIdLs/Pz0+vvfaazbbKlStr3bp1TqoIAAAAroK5JAAAAIpCiA6XtWXLlkK3V6hQQbfffrsqV65cyhUBAADAVTCXBAAAQHERosNlPfPMMzKZTLJYLAXafHx81LdvX/Xp08cJlQEAAKCsYy4JAACA4iJEh8vauHFjodvPnDmj7du3a/r06apRo4Y6dOhQypUBAACgrGMuCQAAgOIiRIfLuvnmmw3bgoOD5e/vr4ULF/LGBwAAAAUwlwQAAEBxuTm7AKCktGnTRgcOHHB2GQAAAHBBzCUBAACQjxAd5ZaXl5fMZrOzywAAAIALYi4JAACAfIToKLd27dqlmjVrOrsMAAAAuCDmkgAAAMjHmuhwWTk5OYVuP3funH744QeNHTtWTzzxRClXBQAAAFfAXBIAAADFRYgOlxUeHi6TyWTY/vDDD6tnz56lWBEAAABcBXNJAAAAFBchOlzWG2+8UegbH29vbwUHB+u2225zQlUAAABwBcwlAQAAUFyE6HBZXbt2dXYJAAAAcFHMJQEAAFBcfLEoAAAAAAAAAAAGCNEBAAAAAAAAADDAci4AAAAol3wTVztkP9kx7R2yHwAAAACuiSvR4ZL27Nlj/fePP/7ovEIAAADgcphLAgAAwB6E6HBJvXr1ktlstv4bAAAAKC7mkgAAALAHy7nAJVWtWlUxMTG65ZZbdOHCBQ0ZMsTwvpMnTy7FygAAAFDWMZcEAACAPQjR4ZLeeustffDBB/rnn38kSX///beTKwIAAICrYC4JAAAAexCiwyWFh4fr7bffliQ99NBDWrBggZMrAgAAgKtgLgkAAAB7EKLD5a1du1aSlJGRoSNHjkiS6tSpIz8/P2eWBQAAABfAXBIAAABFIUSHyzt79qyGDh2qr776ShaLRRaLRe7u7mrXrp3eeOMNeXl5ObtEAAAAlFHMJQEAAFAUQnS4vClTpujIkSOaNGmS6tSpI4vFol9//VWzZs3SO++8o8GDBzu7RAAAAJRRzCUBAABQFEJ0uLzNmzdr9uzZqlOnjnVbWFiYGjRooP79+/PGBwAAAIaYSwIAAKAobs4uALhWJ06cUI0aNQpsv/XWW/W///3PCRUBAADAVTCXBAAAQFEI0eHyatSooa1btxbYvnXrVlWrVs0JFQEAAMBVMJcEAABAUVjOBS7viSee0KBBg9S+fXvVrVtXJpNJv/zyi1avXq0XXnjB2eUBAACgDGMuCQAAgKIQosPlxcTEyNPTUx9//LE2bNggSapTp47GjRunDh06OLk6AAAAlGXMJQEAAFAUQnSUC507d1bnzp2dXQYAAABcEHNJAAAAXAlrogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAITpcWm5urjp27OjsMgAAAOCCmEsCAACgOAjR4dI8PDxksVj0yy+/OLsUAAAAuBjmkgAAACgOD2cXAFyrRx99VIMGDdK9996rGjVqyMvLy9pmMpnUrVs3J1YHAACAsoy5JAAAAIpCiA6XN3HiREnSb7/9VqCNNz4AAAC4EuaSAAAAKAohOlzevn37nF0CAAAAXBRzSQAAABSFNdFRbqSlpWnHjh3OLgMAAAAuiLkkAAAAjHAlOlxeZmamhgwZoq1bt8rDw0N79+7VP//8o6eeekpz585VtWrVnF0iAADXDd/E1df0eJPJJHm6O6gaoGjMJQEAAFAUrkSHy3vrrbeUl5enxMREubld/JGuXLmy6tevr9dff93u/R09elR9+/bV3XffrXvuuUfx8fE6efKkJGnbtm3q1KmTwsLC9MADD2jFihU2j503b55at26t8PBwxcTEKCUl5dpPEAAAACXG0XNJAAAAlD+E6HB53377rSZMmKCwsLCLV69J8vHx0bBhw7Rz506799e3b18FBARo06ZNWrFihQ4dOqRJkybp2LFj6tu3r6Kjo7Vz504NHz5cI0aM0J49eyRJGzZs0LRp0zR+/Hjt2LFDkZGR6tOnj86ePevQ8wUAAIDjOHouCQAAgPKHEB0u78SJE6patWqB7b6+vjp37pxd+zp9+rRCQ0M1dOhQVaxYUVWrVlXnzp31/fffa+XKlapdu7Z69uwpX19ftWrVSvfff78SExMlSYmJiYqOjlazZs3k6+urfv36yWQyaePGjQ45TwAAADieI+eSAAAAKJ9YEx0u75ZbbtFXX32lqKgom+2ffvqpbrnlFrv2VblyZY0fP95mW1pamm666SalpqaqQYMGNm3169fXmjVrJEmpqalq3769tc1kMikkJEQpKSnq2LFjsY7v5maSm5vJrpqvhrv7xc/PLl5sdfXH8/Dgc7iSlD9O+X+j7CqvY5V/Rea1KEuvE4567ZMcc16O6N/yKL9bHDFOjlKWfo4dxRE/f/m7cPXXPkfOJQEAAFA+EaLD5f3nP//RoEGDFBUVJbPZrDFjxiglJUXJycmaNm3aNe07OTlZCxcu1PTp05WQkKCQkBCb9oCAAKWnp0uSMjIyFBAQYNPu7+9vbS+OoKCKpRaq5Ejy8Li2L26rGFjRMcXgivz8fJ1dAoqpvI1VjgO+3LGsvU444rVPcsx5OaJ/yzNHjJOjlLWfY0dw5M+fq7/2leRcEgAAAOUDITpc3oMPPqiPP/5YCQkJqlu3rn788UfdcsstGj58uMLDw696v7t27VLfvn0VHx+vyMhIJSQkFHq//NDbKPy2JxRPT88qtSvRfSTl5pplsVz9frIyshxWEwpyd3eTn5+vTp3Kltmc5+xycAXlday8LpiveR9l6XXCUa99kmPOyxH9Wx6ZTBcDdEeMk6OUpZ9jR3HEz5/JJHlJpfLaF1iCH2SU1FwSAAAA5QchOsqF8PBwTZkyxWH727Rpk1588UW99tpr6tChgyQpKChImZmZNvfLyMhQUFCQJCkwMLDQ9nr16hX7uHl5FuXllV5iYLFIlmtIKHJzy09YWJaZzXn0tYsob2Pl6YAEsyz2x7W+9kmOOS9H9G/5dPHDZEeMk6OUxZ/ja+WYn7+LY1UeXvscPZcEAABA+UKIDpdnNpuVkJCgL7/8Un/99Ze8vb1VvXp1dejQQV27drV7f7t379ZLL72k6dOnq3nz5tbtYWFh+vzzz23uu2fPHusVSmFhYdq7d686d+5srSs1NVXR0dFXf3IAAAAoUY6eSwIAAKD8ce1vAQIkTZo0STNnzlTVqlXVrl07tW7dWhUrVtSYMWM0depUu/aVm5urV199VQMHDrQJ0CWpY8eOSktLU0JCgs6dO6e1a9dq8+bNio2NlSR1795dSUlJ2r59u7KysjRlyhT5+PgU+JIqAAAAlB2OnEsCAACgfOJKdLi81atXa86cOYqIiLDZvmPHDg0dOlSDBw8u9r5+/PFH/fbbb5owYYImTJhg07Z27VrNmjVLY8eO1eTJk1W9enVNnjzZ+mWjLVu2VHx8vIYNG6YTJ04oNDRUs2fPlre397WfJAAAAEqEI+eSAAAAKJ8I0eHyzp07pzvvvLPA9saNG+vs2bN27SsiIkL79+83bL/55pu1fPlyw/a4uDjFxcXZdUwAAAA4jyPnkgAAACifWM4FLq9Nmzb68ssvC2zfvHkzS6kAAADgiphLAgAAoChciQ6XNHPmTOu/q1SpopEjR+rzzz/XrbfeKjc3Nx06dEi7du3SE0884cQqAQAAUBYxlwQAAIA9CNHhkj7//HOb2xUqVNCBAwd04MABm22JiYl6/vnnS7s8AAAAlGHMJQEAAGAPQnS4pMJ+5RYAAAAoDuaSAAAAsAchOsqN9PR0nTt3rsD26tWrO6EaAAAAuBLmkgAAADBCiA6Xt3r1ar322ms6deqUzXaLxSKTyaSff/7ZSZUBAACgrGMuCQAAgKIQosPlTZo0SW3bttX9998vX19fZ5cDAAAAF8JcEgAAAEUhRIfLO3nypEaNGiV3d3dnlwIAAAAXw1wSAAAARXFzdgHAtbrvvvuUnJzs7DIAAADggphLAgAAoChciQ6XN3r0aPXq1UuhoaGqXr26TCaTTfvzzz/vpMoAAABQ1jGXBAAAQFEI0eHyxo4dq19++UUnTpyQj4+PTZvJZOKNDwAAAAwxlwQAAEBRCNHh8jZt2qSPP/5YERERzi4FAAAALoa5JAAAAIrCmuhweYGBgQoPD3d2GQAAAHBBjp5LfvPNN2revLkGDx5coG3btm3q1KmTwsLC9MADD2jFihU27fPmzVPr1q0VHh6umJgYpaSkWNvOnz+vkSNH6u6771ajRo00YMAApaenO6xuAAAAGCNEh8sbPHiwpk2bpuzsbGeXAgAAABfjyLnknDlzNG7cONWuXbtA27Fjx9S3b19FR0dr586dGj58uEaMGKE9e/ZIkjZs2KBp06Zp/Pjx2rFjhyIjI9WnTx+dPXtWkvTmm29q9+7dSkpK0pdffqmcnBy98sor11wzAAAAisZyLnB5H3zwgdLS0jRv3jwFBAQU+DKoLVu2OKkyAAAAlHWOnEt6e3tryZIlev3113X+/HmbtpUrV6p27drq2bOnJKlVq1a6//77lZiYqPDwcCUmJio6OlrNmjWTJPXr10+fffaZNm7cqHbt2mnp0qWaOHGiatasKUmKj49Xu3btdOzYMVWrVu1augAAAABFIESHy2vVqpU8PT2dXQYAAABckCPnkvkBeWFSU1PVoEEDm23169fXmjVrrO3t27e3tplMJoWEhCglJUUNGjTQmTNnbB5/6623ytfXVykpKYToAAAAJYwQHS6vsPUmAQAAgOIorblkRkaGQkJCbLYFBARY1zXPyMhQQECATbu/v7/S09OVkZFhvX0pPz8/u9ZFd3d3K3ClfVni4VF2Vht1d3ez+RtXRn/Zjz6zD/1lP/rMPvSX/a63PiNEh8ubOXOmYZvZbNbAgQNLsRoAAAC4EmfPJfNDbaNwu6jQ255Q3M/PVzme7sUvrpRVDKzo7BIK8PPzdXYJLoX+sh99Zh/6y370mX3oL/tdL31GiA6X9+mnn9rczsvLU2Zmpry9vVWrVi1CdAAAABgqrblkUFCQMjMzbbZlZGQoKChIkhQYGFhoe7169XTDDTdIkjIzM1WhQgVJksViUWZmprWtOE6dypb7BfPVn0QJy8rIcnYJVu7ubvLz89WpU9kym/OcXU6ZR3/Zjz6zD/1lP/rMPvSX/cpTnwUW44N8QnS4vMK+7Ons2bOaMWNGgXUnAQAAgEuV1lwyLCxMn3/+uc22PXv2KDw83Nq+d+9ede7cWdLFq+BTU1MVHR2tmjVrKiAgQCkpKapevbokaf/+/crJyVFoaGixazCb8+RmsTjmhEpAbm7ZewNuNueVybrKKvrLfvSZfegv+9Fn9qG/7He99Nn1sWgNrjsVKlRQfHy8PvjgA2eXAgAAABdTEnPJjh07Ki0tTQkJCTp37pzWrl2rzZs3KzY2VpLUvXt3JSUlafv27crKytKUKVPk4+OjqKgoubu7q1u3bpo2bZqOHDmiEydOaPz48XrooYdUpUoVh9UIAACAwnElOsotk8lk/RImAAAAwB5XM5cMCwuTJOXm5kqSvvjiC0lScnKybrjhBs2aNUtjx47V5MmTVb16dU2ePNn6ZaMtW7ZUfHy8hg0bphMnTig0NFSzZ8+Wt7e3JKl///7KyspS165dZTab1bp1a40ePdpBZwsAAIArIUSHy/vss88KbLtw4YK+/vprBQYGOqEiAAAAuApHziWTk5Ov2B4REaHly5cbtsfFxSkuLq7QNi8vL40cOVIjR460qyYAAABcO0J0uLxRo0YV2Obt7a26detydQ4AAACuiLkkAAAAikKIDpe3b98+Z5cAAAAAF8VcEgAAAEXhi0UBAAAAAAAAADDAlehwWT169JDJZLrifUwmk+bNm1dKFQEAAMBVMJcEAABAcRGiw2U1bdrUsC0vL0+ff/65jh07VooVAQAAwFUwlwQAAEBxEaLDZT3//POFbv/111/1yiuvSJLef//90iwJAAAALoK5JAAAAIqLNdFRbpjNZr3zzjt69NFHdccdd+i///2vIiMjnV0WAAAAXABzSQAAABjhSnSUC3v37tUrr7yi8+fPa+7cuWrSpImzSwIAAICLYC4JAACAK+FKdLi0nJwcTZo0SXFxcbr33nu1YsUK3vQAAACgWJhLAgAAoDi4Eh0u67vvvtOrr74qX19fffrpp2rQoIGzSwIAAICLYC4JAACA4iJEh8vq2bOngoKC1K5dO23atEmbNm0q9H5GXxoFAACA6xdzSQAAABQXITpcVkREhCRp165dhvcxmUylVQ4AAABcCHNJAAAAFBchOlzWggULnF0CAKAIvomrHbKf7Jj2DtkPAORjLgkAAIDi4otFAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6MBlvvnmGzVv3lyDBw+22X7kyBEFBwcrLCzM5s+aNWus95k3b55at26t8PBwxcTEKCUlpbTLBwAAAAAAAOBAHs4uAChL5syZoyVLlqh27doF2k6fPi1PT08lJycX+tgNGzZo2rRpeu+999SwYUN98MEH6tOnj9avX68KFSqUdOkAUK75Jq6+5n2YTCbJ090B1QAAAAAAridciQ5cwtvb2zBEP3nypPz9/Q0fm5iYqOjoaDVr1ky+vr7q16+fTCaTNm7cWJIlAwAAAAAAAChBXIkOXKJnz56GbadOnVJeXp569+6tH374QYGBgerWrZuefvppmUwmpaamqn379tb7m0wmhYSEKCUlRR07dizW8d3cTHJzM13zeRTF3f3i52cmkyRd/fE8PPgcriTlj1P+3yi7yutYmUwl/3pUmvJP51pf+yTHvP6Vt/51FEeOk6OUx//vHPHzl7+L8vbaBwAAAFyOEB0oJk9PT9WqVUvdu3fX9OnTtWvXLg0YMED+/v6KiYlRRkaGAgICbB7j7++v9PT0Yh8jKKhiqYUqOZI8PK5tWYOKgRUdUwyuyM/P19kloJjK21jllNOlT671tU9yzOtfee1fR3HEODlKefz/zpE/f+XttQ8AAAC4HCE6UExRUVGKioqy3m7RooViY2OVlJSkmJgYw/DbnlA8PT2r1K5E95GUm2uWxXL1+8nKyHJYTSjI3d1Nfn6+OnUqW2ZznrPLwRWU17HyumB2dgkOZTJdDGav9bVPcszrX3nrX0dx5Dg5Snn8/84RP38mk+QllcprX2A5/CADAAAAroMQHbgGNWrU0Pr16yVJgYGByszMtGnPyMhQvXr1ir2/vDyL8vJKLzGwWCTLNSQUubnlJywsy8zmPPraRZS3sfIsKwmmw1z8kPJaX/skyXPRf6+5mvLWu47juHFylPL0vM7nmOf3xbEqb699AAAAwOVYwBAopnXr1mnhwoU22w4ePKiaNWtKksLCwrR3715rm9lsVmpqqsLDw0u1TgAAAAAAAACOQ4gOFJOnp6cmTZqkrVu3Kjc3V99++62SkpL0+OOPS5K6d++upKQkbd++XVlZWZoyZYp8fHxsloABAAAAAAAA4FpYzgW4RFhYmCQpNzdXkvTFF19IkpKTkxUVFaVXXnlFY8aM0bFjx1SjRg2NGDFCbdq0kSS1bNlS8fHxGjZsmE6cOKHQ0FDNnj1b3t7ezjkZAAAAAAAAANeMEB24RHJy8hXbY2NjFRsba9geFxenuLg4R5cFAAAAAAAAwElYzgUAAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYMDD2QUAAFAe5MxNktcFszwtlqveR3ZMewdWBAAAAAAAHIEr0QEAAAAAAAAAMMCV6AAAAMAV+Caudsh++G0TAAAAwDVxJToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGPJxdAAAAAAAAvomrnV2ClclkUo6nu7wumJUb3c7Z5QAAACfjSnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABjwcHYBQFnzzTff6KWXXlLTpk01depUm7Zt27Zp/PjxOnTokG688Ub1799fnTp1srbPmzdPCQkJOnHihIKDgzV69Gg1aNCgtE8BAACUQb6Jq695H9kx7R1QCQAAAAB7cCU6cIk5c+Zo3Lhxql27doG2Y8eOqW/fvoqOjtbOnTs1fPhwjRgxQnv27JEkbdiwQdOmTdP48eO1Y8cORUZGqk+fPjp79mxpnwYAAAAAAAAAByFEBy7h7e2tJUuWFBqir1y5UrVr11bPnj3l6+urVq1a6f7771diYqIkKTExUdHR0WrWrJl8fX3Vr18/mUwmbdy4sbRPAwAAAAAAAICDEKIDl+jZs6cqV65caFtqamqBpVnq16+vlJSUQttNJpNCQkKs7QAAAAAAAABcD2uiA8WUkZGhkJAQm20BAQFKT0+3tgcEBNi0+/v7W9uLw83NJDc30zXXWhR394ufn5lMknT1x/Pw4HO4kpQ/Tvl/o+wqr88pk6nkX49KU/7pXOs4oWQxTlfmqNcJRzy/83fB/1MAAAAo7wjRgWuU/ybU6M2oPW9Sg4IqllpolSPJw8P9mvZRMbCiY4rBFfn5+Tq7BBRDeXxO5Xhe2/mUVdc6TigdjFPhHPU64cjnN/9PAQAAoLwjRAeKKSgoSJmZmTbbMjIyFBQUJEkKDAwstL1evXrFPkZ6elapXYnuIyk31yyL5er3k5WR5bCaUJC7u5v8/Hx16lS2zOY8Z5eDKyivzymvC2Znl+BQJtPFYPZaxwkli3G6Mke9Tjji+W0ySV5Sqfw/FVjGPmQEAADA9YUQHSimsLAwff755zbb9uzZo/DwcGv73r171blzZ0mS2WxWamqqoqOji32MvDyL8vJKLzGwWCTLNSQUubkEu6XBbM6jr11EeXtOeZa7BPPih5TXOk4oaYzTlTjqdcIxz++LY8X/UwAAACjvWMAQKKaOHTsqLS1NCQkJOnfunNauXavNmzcrNjZWktS9e3clJSVp+/btysrK0pQpU+Tj46OoqCgnVw4AAAAAAADganElOnCJsLAwSVJubq4k6YsvvpAkJScn64YbbtCsWbM0duxYTZ48WdWrV9fkyZOtXzbasmVLxcfHa9iwYTpx4oRCQ0M1e/ZseXt7O+dkgHLON3G1Q/aTHdPeIfsBAAAAAADlEyE6cInk5OQrtkdERGj58uWG7XFxcYqLi3N0WQCuE3wwAAAAAABA2cNyLgAAAAAAAAAAGCBEBwAAAIBS0Lp1a4WGhiosLMz6Z+zYsZKkbdu2qVOnTgoLC9MDDzygFStW2Dx23rx5at26tcLDwxUTE6OUlBRnnAIAAMB1ieVcAAAAAKAUnDp1SvPnz1fjxo1tth87dkx9+/bVCy+8oJiYGO3YsUMDBw5UnTp1FB4erg0bNmjatGl677331LBhQ33wwQfq06eP1q9frwoVKjjpbAAAAK4fXIkOAAAAACXMbDYrKytL/v7+BdpWrlyp2rVrq2fPnvL19VWrVq10//33KzExUZKUmJio6OhoNWvWTL6+vurXr59MJpM2btxY2qcBAABwXSJEBwAAAIASdurUKVksFs2YMUP33nuv7r33Xr366qs6c+aMUlNT1aBBA5v7169f37pky+XtJpNJISEhLOkCAABQSljOBQAAAABKWE5Ojho0aKCwsDCNGzdOf//9twYOHKjRo0crIyNDISEhNvcPCAhQenq6JCkjI0MBAQE27f7+/tb24nJ3d5PJZLqm87he5HeTySR5eHDtWVHc3d1s/kbR6DP70F/2o8/sQ3/Z73rrM0J0AAAAAChh1apV0+eff269XalSJQ0dOlTPPfecIiIiCn1MfuBtFHzbG4j7+fkqx9Pdrsdc7zw83FUhsKKzy3AZfn6+zi7B5dBn9qG/7Eef2Yf+st/10meE6AAAAADgBDVq1FBeXp7c3NyUmZlp05aRkaGgoCBJUmBgYKHt9erVs+t4p05ly/2C+VpKvm5cvALdXbm5ZmVlZDm7nDLP3d1Nfn6+OnUqW2ZznrPLcQn0mX3oL/vRZ/ahv+xXnvossBgfmBOiAwAAAEAJ27dvn5YtW6aXX37Zuu3gwYPy8vJSZGSkli1bZnP/PXv2KDw8XJIUFhamvXv3qnPnzpIufklpamqqoqOj7arBbM6Tm8VyTedx/bh4lb/FIuXmunYwUJrM5jz6y070mX3oL/vRZ/ahv+x3vfTZ9bFoDQAAAAA4UWBgoD777DPNnj1bOTk5Onz4sN5++23FxcXpkUceUVpamhISEnTu3DmtXbtWmzdvVmxsrCSpe/fuSkpK0vbt25WVlaUpU6bIx8dHUVFRTj4rAACA6wNXogMAAABACatWrZpmz56tt956S++9954CAwPVvn17DRgwQF5eXpo1a5bGjh2ryZMnq3r16po8ebL1y0Zbtmyp+Ph4DRs2TCdOnFBoaKhmz54tb29vJ58VAADA9YEQHQAAAABKQZMmTfTZZ58V2hYREaHly5cbPjYuLk5xcXElVRoAAACugOVcAAAAAAAAAAAwQIgOAAAAAAAAAIABlnMBAKCc8U1c7ewSAAAAAAAoN7gSHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGDAw9kFAADgTL6Jq695HyaTSfJ0d0A1AAAAAACgrOFKdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAwP9r787jqq7zPY6/D5sSrmjXTCvMum6AgiK5A7mnoimuqVOOZg9N4kJm6k0bl2nR8Rr1cCRTG8dr5kaaiY3luOWCMrmmpkiOG06KMioIHH73Dx6cG8oPOIoc8Lyej4cPH+e3fs73+z0/vr/P+f6+B4AJkugAAAAAAAAAAJggiQ4AAAAAAAAAgAmS6AAAAAAAAAAAmCCJDgAAAAAAAACACZLoAAAAAAAAAACYIIkOAAAAAAAAAIAJkugAAAAAAAAAAJggiQ4AAAAAAAAAgAmS6AAAAAAAAAAAmCCJDgAAAAAAAACACZLoAAAAAAAAAACYIIkOAAAAAAAAAIAJkugAAAAAAAAAAJggiQ4AAAAAAAAAgAmS6AAAAAAAAAAAmCCJDgAAAAAAAACACZLoAAAAAAAAAACYIIkOAAAAAAAAAIAJN0cHAAAwl7VojTyyrXI3jPs6TkZEz1KKCAAAAAAAwLkwEh0AAAAAAAAAABMk0QEAAAAAAAAAMEESHQAAAAAAAAAAE8yJDgAoMc9V39z3MZifHQAAAAAAVCSMRAfsEBoaKl9fX/n5+dn+zZgxQ5K0e/du9enTR35+furSpYvWr1/v4GgBAAAAAAAA3C9GogN2SE9P11/+8hcFBgYWWJ6amqrXXntN//Vf/6WIiAjt3btXkZGR8vHxkb+/v4OiBQAAAAAAAHC/GIkOlJDVatXNmzdVvXr1u9Zt2LBBTz31lEaMGCFPT0+FhITo+eef16pVqxwQKQAAAAAAAIDSwkh0oITS09NlGIZiY2O1f/9+SVJISIgmTZqkY8eOqVmzZgW2b9KkiTZt2mTXOVxcLHJxsZRazGZcXfO+P7NYJOnez+fmxvdwD1Jp1ZNUenVlsdx/+yxPsZSW/FBKo67w4FBPFQP1VLTydA3NP0T+3ysAAADgYUUSHSihrKwsNWvWTH5+fpo5c6YuX76syMhITZ8+XWlpaWrcuHGB7WvUqKGrV6/adQ5vb68ySwxmSXJzc72vY3jV9CqdYGCqNOpJKr26ynJ/uGIpbaVRV3jwqKeKgXoqXHm8hlar5llqxwIAAADKI5LoQAnVqVNHa9eutb2uUqWKYmJiNHbsWLVq1arQfexNiF+9erPMRqJXlpSTY5Vh3PtxbqbdLLWYcLfSqiep9OrKI9t638coT7GUFoslL+FXGnWFB4d6qhiop6KVp2uoxSJ5SEpPz5DVmnv/QRWhJl/cAwAAwIFIogP3oX79+srNzZWLi4uuXbtWYF1aWpq8vb3tOl5urqHc3LLLGBiGZNxHhiIn58HeMCPP/daTVHp15V4KGa3yFEvpyfvyqzTqCg8S9VQxUE9FKV/X0Ly6slpz6RMAAADgoUYSHSih48ePKz4+XpMmTbItS05OloeHhzp16qT4+PgC2x86dEj+/v5lHCUAAACA0uS56htHh2AqI6Kno0MAAMAp8CtAQAnVrFlTK1euVFxcnLKyspSSkqL58+dryJAhCg8P1/nz57V06VJlZmYqISFB27dv16BBgxwdNgAAAAAAAID7QBIdKKE6deooLi5O3333nYKDg/XKK68oJCREMTExqlWrlhYuXKh169YpKChI8+bN09y5c+/6sVEAAAAAAAAAFQvTuQB2CAoK0sqVKwtd16pVK3311VdlHBFQMuX5MWQAAAAAAIDyjJHoAAAAAAAAAACYYCQ6AKBMMSoeAAAAAABUJIxEBwAAAAAAAADABCPRAQAAgAqCp3kAAACAssdIdAAAAAAAAAAATJBEBwAAAAAAAADABEl0AAAAAAAAAABMkEQHAAAAAAAAAMAESXQAAAAAAAAAAEyQRAcAAAAAAAAAwISbowMAAAAAAAD281z1jaNDkCRZLBZlubvKI9sqd8NQRkRPR4cEAECpIokOAA9AadzQWCwWyd21FKIBAAAAAADAvWI6FwAAAAAAAAAATJBEBwAAAAAAAADABEl0AAAAAAAAAABMkEQHAAAAAAAAAMAESXQAAAAAAAAAAEyQRAcAAAAAAAAAwARJdAAAAAAAAAAATJBEBwAAAAAAAADABEl0AAAAAAAAAABMuDk6AAAobzxXfePoEAAAAAAAAFBOkEQH8NAg+Q0AAAAAAIDSxnQuAAAAAAAAAACYIIkOAAAAAAAAAIAJpnMB4HBMwwIAAAAAAIDyipHoAAAAAAAAAACYYCQ6AAAAAAAoNeX5SdOMiJ6ODgEAUAExEh0AAAAAAAAAABMk0QEAAAAAAAAAMEESHQAAAAAAAAAAE8yJDgAAAAAAnEJ5ma/dYrEoy91VHtlWuRsGc7UDQDnHSHQAAAAAAAAAAEwwEh0AAAAAAMCByssI+cIwSh4AGIkOAAAAAAAAAIApkugAAAAAAAAAAJggiQ4AAAAAAAAAgAnmRAdwz8rzvH0AAAAAAABAaSCJDgAAAAAAgEKVh8FTFotFWe6u8si2yt0wJPGDpwDKFtO5AAAAAAAAAABggiQ6AAAAAAAAAAAmSKIDAAAAAAAAAGCCJDoAAAAAAAAAACZIogMAAAAAAAAAYMLN0QEAAAAAAAAA9vBc9Y2jQzCVEdHT0SEAKGUk0QEAAAAAAIBSUh4S/BaLRVnurvLItsrdMCSR3AfuB9O5AAAAAAAAAABggiQ6AAAAAAAAAAAmSKIDAAAAAAAAAGCCOdGBUnTu3DlNmzZNBw4ckKenp1588UVFR0fLxYXvqwAAAHB/6GsCAO5HeZir3Ux5na+dMkM+kuhAKTEMQ+PHj9czzzyjbdu26cqVK/r973+vWrVq6ZVXXnF0eAAAAKjA6GsCAAA4Dkl0oJQcPnxYJ06c0NKlS1W9enVVr15dY8aM0ZIlS7ixAQAAwH2hrwkAeJg5esS3xWJRlrurPLKtcjcMh8ZSUuW1zB7WEfIk0YFScuzYMdWrV081atSwLWvatKlSUlJ048YNValSxXHBAQAAoEKjrwkAACoCRyf3i3I/CX6S6EApSUtLU/Xq1Qssy3+dlpZWohsbFxeLXFwsDyS+33J1zZs302KRpAd/Ptwbi+W3/1NP5Rl1VTFQTxUD9VRx5NdVfr8CD1Zp9DVdXV1ksfC5KgmuRfahvOxHmdmH8rIfZWYfyst+FbHMHlm9qfAVrw0sdl+S6EA5UqtWGY4g+n1/eZTd2XAfqKeKg7qqGKinioF6qjiqVfN0dAgooWrVPEt0k4j/x7XIPpSX/Sgz+1Be9qPM7EN52c9ZyoxhI0ApqVWrlq5du1ZgWVpamiTJ29vbAREBAADgYUFfEwAAwHFIogOlxM/PTxcuXLDdzEjSoUOH9Mwzz8jLy8uBkQEAAKCio68JAADgOCTRgVLSpEkT+fv7a+bMmUpPT9eJEycUFxenYcOGOTo0AAAAVHD0NQEAABzHYhiG4egggIfFpUuX9M4772jv3r3y8vLS0KFDNX78eEeHBQAAgIcAfU0AAADHIIkOAAAAAAAAAIAJpnMBAAAAAAAAAMAESXQAAAAAAAAAAEyQRAcAAAAAAAAAwARJdAAAAAAAAAAATJBEBx5C586d06hRo9SiRQu1adNGH374oXJzcwvd9vPPP1doaKj8/f0VERGho0ePlnG0zs2euvrf//1fde3aVQEBAerdu7e2bNlSxtE6L3vqKV9qaqoCAgIUGxtbRlFCsq+uTp8+rWHDhql58+YKCQnR0qVLyzZYJ1bSesrNzdX8+fMVGhpqu/Z98803DojYee3YsUNt27ZVVFRUsdvSpyif7uVvmLMJDQ2Vr6+v/Pz8bP9mzJghSdq9e7f69OkjPz8/denSRevXr3dwtI5R1LWguDJyxmuDWXn985//VKNGjQq0NT8/P23atMm2jTOW17lz5/Taa6+pdevWatOmjSZOnKjr169Lon2ZMSsz2ljhfvrpJ/3ud79Tq1at9NxzzykyMlKXL1+WRBszY1ZmTt3GDAAPldzcXCM8PNyIjo42rl27Zpw+fdoIDQ01Pvvss7u2/fbbb40WLVoYu3fvNm7dumXExsYa7dq1M27evOmAyJ2PPXW1efNmo2XLlkZSUpKRnZ1trF271mjWrJnxyy+/OCBy52JPPf3W+PHjjYCAAOOjjz4qo0hhT11lZGQYYWFhxpdffmlkZmYae/bsMbp3726cOnXKAZE7F3vqadmyZUb79u2N5ORkw2q1Glu3bjWaNm1q/PTTTw6I3PnExcUZXbt2NQYPHmy88cYbRW5Ln6J8ute/Yc4mMDDQOHDgwF3LL126ZDRv3tz4/PPPjVu3bhlbt241/P39jYMHDzogSscp6lpQXBk547WhqPI6evSo0axZM9N9nbG8DMMwevXqZUyaNMm4ceOGcfnyZWPAgAHG5MmTaV9FMCsz2tjdMjMzjTZt2hgff/yxcfv2beNf//qXMXToUOO1116jjZkoqsycuY0xEh14yBw+fFgnTpzQ1KlTVb16dT399NMaM2aMVq5cede2q1at0oABA/Tcc8/J09NT48aNk8Vi0XfffeeAyJ2PPXWVmZmp6OhoBQQEyM3NTf369VOVKlV08OBBB0TuXOypp3zbtm3T6dOnFRYWVoaRwp662rRpkxo2bKiIiAhVqlRJwcHBtmV4sOypp59++kmBgYFq0KCBXFxcFBISomrVqunEiRMOiNz5VKpUSatXr9ZTTz1V7Lb0Kcqne/kb5mysVqtu3ryp6tWr37Vuw4YNeuqppzRixAh5enoqJCREzz//vFatWuWASB2nqGtBcWXkjNeGosrr+vXrhba1fM5YXv/+97/l6+urmJgYeXl56dFHH1Xfvn21f/9+2peJosqMNna3zMxMRUVF6dVXX5WHh4dq166t7t2769SpU7QxE0WVmTO3MZLowEPm2LFjqlevnmrUqGFb1rRpU6WkpOjGjRt3bdusWTPba4vFosaNGz88j9qUc/bUVZ8+fTRkyBDb6/T0dN24cUN169Ytq3Cdlj31JOV1OP7whz9o+vTpcnNzK8NIYU9d7d+/Xz4+PpowYYJatmypnj17Mk1IGbGnnkJCQpSYmKjjx48rJydHW7Zs0e3bt9W6desyjto5jRgxQlWrVi3RtvQpyid7/4Y5o/T0dBmGodjYWLVv317t27fX1KlTdePGjbvatSQ1adLE6dp1UdeC4srIGa8NRZVXenq6cnNzNWbMGAUFBalr165atGiRDMOQ5JzlVbVqVf3xj39UrVq1bMvOnz+vunXr0r5MFFVmtLG7Va9eXREREXJzc5NhGEpOTta6devUo0cP2piJosrMmdsYSXTgIZOWlnbXt4L5r9PS0u7a9rc3VfnbXr169YHGiDz21NVvGYahqVOnytfXV4GBgQ80RthfT5988omCgoJI8jmAPXWVmpqq+Ph4DRgwQLt27dKoUaMUHR2t48ePl1m8zsqeeurSpYuGDBmi8PBwNWvWTNHR0Zo9ezZfIJZD9CnKp3vtaziTrKwsNWvWTH5+fkpISNBf/vIXHTx4UNOnTy+0/GrUqEG7/o3iyohrQ0Hu7u568sknNXjwYO3YsUPTpk3TggULtHr1akmUl5T3BM3y5cv18ssv075K6LdlRhszd/78efn6+qpnz57y8/NTZGQkbawYhZWZM7cxkuiAE7NYLHYth+NlZ2crJiZGZ86c0SeffCIXFy7j5cmpU6e0bt06TZw40dGhoBg5OTkKCQlRx44dVblyZfXv31/+/v7auHGjo0PDb8THx2vdunVat26dDh8+rI8++khTpkzRoUOHHB0a7kCfAhVVnTp1tHbtWo0aNUpVqlTR008/rZiYGG3cuFE5OTmF7kO7Ll5+GXFtKCgsLEwrV65UWFiYKleurHbt2mnQoEFas2aNJMrrwIEDGjVqlCZOnKhOnTqZbkf7+n93lhltzFy9evV05MgRJSQkKDk5WW+++abptrSxPIWVmTO3MbIvwEOmVq1aunbtWoFl+SONvL29CyyvWbNmodveuR0eDHvqSsqbJuTVV19Vamqqli9fXuDxPTw49tTT9OnT9cYbb/AZchB76qp69ep3PWpdr149/frrrw80RthXT8uWLdPAgQPVtGlTeXh4qFOnTgoODlZ8fHwZRYuSok9RPtnb10Ce+vXrKzc3Vy4uLrTrYnh7exdZRlwbile/fn1b/8OZy2vr1q169dVXNW3aNA0bNkwS7as4hZVZYWhj/89iscjHx0cTJ07U119/LXd3d9pYMe4ss8JGlDtLGyOJDjxk/Pz8dOHChQKP6B46dEjPPPOMvLy87tr2yJEjttdWq1XHjh2Tv79/mcXrzOypK8MwFBUVJQ8PDy1evFjVqlUr63CdVknr6fz580pMTNSHH36o4OBgBQcHa+PGjVq0aJH69evniNCdjj2fqWbNmt01L9/58+dVr169MonVmdl77cvNzS2wLCcnh6dwyiH6FOWTPZ83Z3X8+HG99957BZYlJyfbvri782/FoUOHaNe/4efnV2QZcW0oaPPmzVq+fHmBZcnJyXriiSckOW95JSUl6a233tJHH32kF154wbac9mXOrMxoY3fbvXu3OnfuXODpovz+Zdu2bWljhSiqzBITE522jXEHAjxkmjRpIn9/f82cOVPp6ek6ceKE4uLibN9Md+/eXfv375ckDR48WGvWrNGePXt08+ZN/elPf1LlypUVFhbmyLfgNOypqw0bNujEiROaN2+ePDw8HBm20ylpPT322GPatm2bvvrqK9u/sLAwDR48WHFxcQ5+F87Bns9U3759deLECX3xxRfKysrS+vXrdfToUfXp08eRb8Ep2FNPoaGhWr16tX7++WdZrVbt3r1bu3fvVkhIiAPfAfLRpyj/ivu8IW/E3MqVKxUXF6esrCylpKRo/vz5tt9jOH/+vJYuXarMzEwlJCRo+/btGjRokKPDLjd69+5dZBlxbSjI3d1dH3zwgXbt2qWcnBz98MMPWrNmje0z6YzllZOTo6lTpyoyMlJt27YtsI72Vbiiyow2djdfX19lZGRo7ty5ysjI0NWrVxUbG6tWrVrRxkwUVWbO3MYsRv7PpwJ4aFy6dEnvvPOO9u7dKy8vLw0dOlTjx4+XJDVq1EiffvqpOnbsKElasWKF4uLidOXKFfn6+urdd9/Vs88+68jwnUpJ62rkyJFKTEyUq6trgf3Dw8M1c+ZMR4TuVOz5TP3WpEmTVK9ePb3++utlHbLTsqeuEhMTNWvWLJ05c0ZPPvmk3nzzzULrEaWvpPWUnZ2tjz/+WBs2bNDVq1f1+OOPa9SoUerfv7+D34Fz8PPzkyTbKCQ3NzdJeT9gJtGnqCiK+rwhT2JioubMmaOTJ0+qZs2a6tmzpyZMmCAPDw/t379fM2bMUHJysh5//HHFxMSoS5cujg65TBV3LSiujJzt2lBcea1cuVKLFy9Wamqq6tevr1deeUUvvviibX9nK6/9+/dr2LBhhQ4USkhI0MWLF2lfdyiuzHbu3Ekbu8NPP/2k999/X0eOHJGbm5uCg4M1efJk1alTh2uYiaLKzFmvYyTRAQAAAAAAAAAwwXQuAAAAAAAAAACYIIkOAAAAAAAAAIAJkugAAAAAAAAAAJggiQ4AAAAAAAAAgAmS6AAAAAAAAAAAmCCJDgAAAAAAAACACZLoAAAAAAAAAACYIIkOAAAAAAAAAIAJkugAAAAAAABOICwsTLGxsY4OQ5J08OBBde3aVc2bN9fBgwcdHQ4AFIkkOgAAD0BYWJjmzJlT6LpJkyZp4MCBZRxRnh07dqhRo0YaPHiwQ84PAADgzIYPH67GjRtr7969d62LjY3V8OHDHRCVY3z22Wfy8vJSYmKi/P39Tbcrrv+6detWHT582Pb65MmTSkhIKPV47XVnXAAqNpLoAAA8RLKzs4tcv2zZMvXq1UuHDh3SsWPHyiiqwhUXKwAAwMOoZs2a+u///m9lZmY6OpT7dj/9ubS0NPn4+MjDw0MWi8V0u+L6r7GxsTpy5Ijt9dq1a7V58+Z7jqu03BkXgIqNJDoAAA62adMmhYeHKyAgQEFBQRo1apSSk5Nt6xMTEzV48GA1b95c7du314QJE3ThwgXb+rCwMM2bN0+DBw9W69atTc9z9uxZbd++XaNHj1a7du3017/+9a5tUlJSNHr0aAUEBCg4OFgxMTFKS0uzrY+Pj1ePHj3k7++vLl26FDhGYaPvBw4cqEmTJknKu6EJCAjQqlWr1Lp1ay1ZskSStG3bNkVERKhly5Zq3bq1Ro8erV9++cV2jJycHM2dO1cdOnRQixYtNGDAAO3evVu5ubkKDQ3Vhx9+WOCcFy5cUOPGjbVt27Yiyx0AAMARBg4cqMqVK2v+/PlFbteoUSOtWLGiwLJ27drZpmOZN2+e+vTpo6+++kohISFq3ry5IiMjdeXKFU2YMEEBAQHq1KmTvvnmmwLHyMzM1Ntvv61WrVqpffv2mj17doFk+Lfffqvw8HD5+/srJCREU6ZMKdAfbNSokRYvXqwXXnhB/fr1M41/1apV6tOnjwICAtS5c2fNmDFDN27ckCR169ZNiYmJSkhIkJ+fn5KSkgo9RnH913bt2uno0aOaOXOmwsLCFBkZqaVLl9qOe+bMGUnSF198oW7dusnf31+dO3fW+++/r4yMDEl5/d9GjRrpb3/7m4YMGSJ/f3/16NFDR44c0bJly9ShQwe1bNlSb775pqxWq63se/bsqXXr1iksLEwBAQEaMWKEzp49W2hckrR7924NGjRILVu2VGBgoIYOHap//OMfpuUHoHwhiQ4AgAOlpqYqOjpaUVFRSkpK0vfff6/HHntMkydPliRdvHhRo0ePVnh4uBITE7V+/Xq5u7tr9OjRtk68lJfcjoyM1P79+03PtWzZMvn6+qpx48aKiIjQxo0bde3aNdt6q9WqsWPH6j/+4z+0Y8cObdq0SZcuXVJMTIwkaf/+/Zo6daomTpyopKQkzZ49Wx988IE2btxY4vebnZ2tpKQkbd26Vb///e/166+/aty4cerUqZP27NmjLVu2yGq16s0337TtExcXp2+++UaLFy/W/v379cILL+jVV1/VhQsXNGDAAK1bt67Ajd/XX3+tunXrqkOHDiWOCwAAoKy4ublp1qxZWrZsmQ4dOnRfxzl//ryOHz+uzZs3a9GiRUpISNDIkSM1cuRI7du3Tx07dtT06dNlGIZtv9WrV6tjx4764YcfNG/ePH355ZdaunSpJOnQoUOKjo7W66+/rgMHDmjFihU6d+6crT+Yb9WqVZo7d67Wr19faGzx8fGaNWuWoqKitHfvXsXFxWnfvn2242zevFlBQUHq3r27Dh8+rMDAwEKPU1z/ddeuXZKkqVOn6vvvv9f8+fMLHLdBgwZKSEjQn/70J82cOVP/+Mc/tHDhQu3cuVN//OMfbeUo5U0v88EHH2jXrl2qVKmSXn/9dV2+fFlbtmzRggULtH79em3dutW2z4ULF5SUlKQNGzbou+++k9Vq1bhx4wqNKycnR+PGjVN4eLj27dunXbt2KTg4WFFRUQX69ADKL5LoAAA4UFpamqxWq7y8vGSxWFS1alXNmDFDX3zxhSRp+fLlatiwoYYMGSIPDw95e3trypQpOnXqlBITE23Hadq0qdq0aSNXV9dCz3Pz5k2tW7fONhd7WFiYqlatqtWrV9u22blzp86cOaPo6GhVqVJF3t7emjFjhgYNGiQp7yamXbt2Cg0NlZubm4KCgvTJJ5+oQYMGJX6/2dnZGj58uLy8vOTi4qLatWtr9+7dGjt2rNzd3VWtWjV169ZNR44cUU5Oju28Q4cO1bPPPis3NzeNHDlSs2bNkpubmyIiInT9+nXbDY0kbdiwQf3795eLC90cAABQPvn5+Wn48OGaMmXKfU2JcuvWLY0fP16VKlVSUFCQvL295evrq5YtW8rd3V2dO3fW9evXdeXKFds+zZs3V48ePeTh4aGgoCB16tRJ33//vSRpyZIlCg0NVefOneXu7q66desqJiZGO3fu1D//+U/bMdq1a6fGjRub9reWLVumHj16KDQ0VB4eHnr66ac1ZswY/f3vf9fVq1dL9N5K0n8ticWLF6t///4KCgqSq6urGjZsqHHjxmnNmjUFyr5379564oknVLVqVbVt21b/+te/9Prrr6tSpUpq3bq1vL29CzwtmpmZqaioKHl5ecnb21ujR4/WyZMnlZKSUuh7ycjI0COPPCJXV1d5enpqwoQJ2rp1q2n/HUD54uboAAAAcGaNGzfW2LFj9fLLL6thw4Zq06aNQkNDFRwcLElKTk7WsWPH5OfnV2A/Nzc3nTt3zvb6iSeeKPI88fHxslqt6tmzp23/F198UStWrNArr7wiFxcXpaSk2G4C8jVo0MCWJP/ll1/UqlWrAse9l9Hed8a6Zs0arV69WhcuXFB2drZyc3NltVpltVqVkZGhq1evFtjHxcVFvXv3tr0ODQ3Vl19+qa5du+rkyZM6deqU4uLi7I4LAACgLEVGRuq7777TwoULNX78+Hs6Ro0aNeTl5WV77enpqbp169peV65cWZIKzL/esGHDAseoV6+ebUR8cnKyfv7557v6nq6urjp37pytT1Zc3/Ps2bO2fmc+Hx8fGYahs2fPFuhvmilJ/7UkkpOTdfTo0bumgjEMQxcvXrQdp379+rZ1np6eql27tjw8PAosu337tu11rVq1CryP/P0vXrwoHx+fAueqXr26pk6dqmnTpmnBggVq27atOnTooNDQ0BK9BwCORxIdAIAHwMPDw/THom7cuKFHHnnE9joqKkrDhw/Xjh07tGfPHo0dO1Y9evTQ7Nmz5eLiovbt2+vTTz8t9nxmDMPQX//6V92+fVshISG25VarVbdu3dLf//53hYWFyTCMAo/6Fnac3NzcIuO4U2Hb/zbWr7/+Wu+9957ee+899ejRQ5UqVdLq1as1ZcqUYo+Tb/DgwRo9erRSU1O1YcMGdejQocDNIwAAQHlUuXJlzZw5U6NGjVK3bt1KtM+dfaLCEsnFJZfv/BFPwzBs/TMXFxcNHDhQ06dPL/IYRfU9izpPYcsLU9L+a0m4uLjojTfe0OjRowtdnz8w5c64iivHO+si//3lf3Fxp2HDhql3797auXOn9uzZo7ffflvNmjXTp59+ymh0oALgOWcAAB6Axo0b68cff7xreWZmpg4ePChfX19JeZ3va9euqXbt2urXr5/ef/99LViwQGvWrFF6eroaNGigEydO2KY2yd/nt6PQi7Nr1y4lJydr0aJFio+Pt/3bsGGD2rZtq+XLl0vKG3V+69YtXbx40bbvmTNntGTJElmtVvn4+Oj06dMFjv3tt9/afsCzUqVKBR6JNQxDly5dKjK2pKQk+fj4qG/fvqpUqZIk6fDhw7b1VatWlbe3913n/fzzz3XixAlJeY8T16tXT19//bU2btxoe+QXAACgvGvdurX69eunyZMn35WUvbNvdevWrQLzgd+r/B/bzHf+/Hk9/vjjkvL6g0ePHi2wPiMjQ5cvX7brHD4+Pvr5558LLDt9+rRcXFz01FNPFbt/SfuvJVHYe7p+/bquX79e4mMUJi0trcAPruZPd2M2mOPq1auqVq2aevbsqT/84Q9avXq1du3apZMnT95XHADKBkl0AAAegLFjx+rUqVOaNWuWLl++LKvVqtOnT+uNN96QxWLRyJEjJeWNxO7Vq5d+/PFH5ebm6vbt2zp48KAeffRRValSRUOHDtW1a9c0Z84c3bhxQzdv3tTcuXMVERGhmzdvliiWZcuWKTAwUG3btlX9+vUL/Bs+fLh27dqlM2fOqF27dvLx8dH777+v69ev69q1a5o5c6a2b98uV1dXvfTSS9q3b582btyo7Oxs/fjjj5o0aZLtBuTZZ5/VDz/8oKysLEl5ie6MjIwiY6tXr54uX76slJQUZWVlaenSpbYbiQsXLkiSXnrpJa1YscI2T/qKFSs0d+5ceXp6SsobNTRw4EAtXLhQWVlZBUYrAQAAlHcTJ07U5cuXtXbt2gLLn3nmGW3bts32tGBsbKzpKGd7JCUlacuWLcrKytLevXu1bds225Qpv/vd73T48GEtWbJEGRkZSktL07Rp0/Tyyy/b9UTiyJEjtWnTJm3btk3Z2dk6efKk/vznP6tHjx6qUaNGsfuXtP8q5Y38TklJ0bVr12QYhipXrqzz58/r+vXrun37tl5++WX97W9/04YNG5SVlaXU1FRFRUUpOjr6nsovn4eHh+bMmaP09HRduXJFn332mVq2bKnHHnvsrrgOHDig559/Xtu3b1dOTo5ycnKUlJSkypUr27YHUL6RRAcA4AFo3LixVq5cqdTUVIWHh6tFixYaNWqUHn30Ua1atUqPPvqopLwfMBoxYoTeeustBQYGqmPHjtq3b58+/fRTubi4qG7duoqLi9OPP/6odu3aqWPHjjp+/LiWLl1aYP5LM2fPntX27ds1bNiwQteHhITo8ccf1/Lly+Xm5qYFCxbo3//+t0JCQtStWzfVqFFDc+fOlSQFBQVp1qxZ+uijjxQYGKiYmBhFRkaqT58+kmT7YaWwsDD17t1bubm5CgoKKjCK/k5DhgxR+/bt1a9fP4WGhurXX3/VggUL9J//+Z8aNGiQjh07pjFjxqhPnz4aO3asWrVqpVWrVunPf/6znnzySdtx+vfvr1u3bunFF1+Umxuz1QEAgIqjSpUqevfdd+96gu+dd97Rr7/+qk6dOqlv375q0qSJ6tevX2TfqjjZ2dl66aWXlJCQoOeee07R0dEaPny4+vfvL0ny9/fX//zP/yg+Pl7BwcHq3r27MjIytGjRIrt+tL1Xr16KiorSzJkzFRgYqHHjxqlLly6aPXt2sfva03+V8hL2X3zxhXr16qXs7GwNGjRIKSkp6tKliw4fPqzu3btr8uTJ+vjjj9WyZUv17dtXderUsfVx71Xt2rXVokULhYeH236Idc6cObb1v43Lz89PkydP1ocffqigoCA999xzWrlypRYuXKiaNWveVxwAyobFKGryUwAAgArg9OnTCg8P17fffmt7HBkAAAB4EGJjY7Vu3Tp9//33jg4FQBlhJDoAAKjQUlNT9fbbbysiIoIEOgAAAACg1JFEBwAAFda7776rrl276oknntBbb73l6HAAAAAAAA8hpnMBAAAAAAAAAMAEI9EBAAAAAAAAADBBEh0AAAAAAAAAABMk0QEAAAAAAAAAMEESHQAAAAAAAAAAEyTRAQAAAAAAAAAwQRIdAAAAAAAAAAATJNEBAAAAAAAAADBBEh0AAAAAAAAAABMk0QEAAAAAAAAAMPF/b9RuBw//exMAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize skill distribution\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Top 20 skills by attempts\n", "top_skills = skill_stats.head(20)\n", "axes[0,0].barh(range(len(top_skills)), top_skills['Total_Attempts'])\n", "axes[0,0].set_yticks(range(len(top_skills)))\n", "axes[0,0].set_yticklabels(top_skills.index, fontsize=8)\n", "axes[0,0].set_xlabel('Number of Attempts')\n", "axes[0,0].set_title('Top 20 Skills by Attempts')\n", "axes[0,0].invert_yaxis()\n", "\n", "# Accuracy distribution\n", "axes[0,1].hist(skill_stats['Accuracy'], bins=20, alpha=0.7)\n", "axes[0,1].set_xlabel('Accuracy')\n", "axes[0,1].set_ylabel('Number of Skills')\n", "axes[0,1].set_title('Distribution of Skill Accuracy')\n", "\n", "# User performance distribution\n", "user_accuracy = df_clean.groupby('user_id')['correct'].mean()\n", "axes[1,0].hist(user_accuracy, bins=30, alpha=0.7)\n", "axes[1,0].set_xlabel('User Accuracy')\n", "axes[1,0].set_ylabel('Number of Users')\n", "axes[1,0].set_title('Distribution of User Accuracy')\n", "\n", "# Attempts per user\n", "user_attempts = df_clean.groupby('user_id').size()\n", "axes[1,1].hist(user_attempts, bins=50, alpha=0.7)\n", "axes[1,1].set_xlabel('Number of Attempts')\n", "axes[1,1].set_ylabel('Number of Users')\n", "axes[1,1].set_title('Distribution of Attempts per User')\n", "axes[1,1].set_xlim(0, user_attempts.quantile(0.95))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Split Data into Train/Test Sets\n", "\n", "Split the data for training and evaluation while maintaining temporal order."]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Splitting data into train/test sets...\n", "Training set: 255,487 interactions from 3,320 users\n", "Test set: 70,150 interactions from 831 users\n", "Train accuracy: 0.655\n", "Test accuracy: 0.670\n"]}], "source": ["# Split data by users to avoid data leakage\n", "print(\"Splitting data into train/test sets...\")\n", "\n", "# Get unique users\n", "unique_users = df_clean['user_id'].unique()\n", "train_users, test_users = train_test_split(unique_users, test_size=0.2, random_state=42)\n", "\n", "# Split data based on users\n", "train_data = df_clean[df_clean['user_id'].isin(train_users)].copy()\n", "test_data = df_clean[df_clean['user_id'].isin(test_users)].copy()\n", "\n", "print(f\"Training set: {len(train_data):,} interactions from {len(train_users):,} users\")\n", "print(f\"Test set: {len(test_data):,} interactions from {len(test_users):,} users\")\n", "print(f\"Train accuracy: {train_data['correct'].mean():.3f}\")\n", "print(f\"Test accuracy: {test_data['correct'].mean():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Train BKT Model with Skill-Specific Parameters\n", "\n", "Initialize and train the Bayesian Knowledge Tracing model. The improved model now estimates unique parameters for each skill based on student performance data."]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing BKT model with skill-specific parameter estimation...\n", "Default parameters (used as fallback):\n", "  P(Init): 0.1\n", "  P(Learn): 0.15\n", "  P(Guess): 0.25\n", "  P(Slip): 0.1\n", "\n", "🎯 Note: The model will estimate UNIQUE parameters for each skill during training.\n", "📊 Each skill will have different P(Init), P(Learn), P(Guess), and P(Slip) values!\n"]}], "source": ["# Initialize BKT model with default parameters (used as fallback)\n", "print(\"Initializing BKT model with skill-specific parameter estimation...\")\n", "bkt_model = BayesianKnowledgeTracer(\n", "    p_init=0.1,   # Default initial probability (fallback)\n", "    p_learn=0.15, # Default learning rate (fallback)\n", "    p_guess=0.25, # Default guessing probability (fallback)\n", "    p_slip=0.1    # Default slip probability (fallback)\n", ")\n", "\n", "print(f\"Default parameters (used as fallback):\")\n", "print(f\"  P(Init): {bkt_model.p_init}\")\n", "print(f\"  P(Learn): {bkt_model.p_learn}\")\n", "print(f\"  P(Guess): {bkt_model.p_guess}\")\n", "print(f\"  P(Slip): {bkt_model.p_slip}\")\n", "print(\"\\n🎯 Note: The model will estimate UNIQUE parameters for each skill during training.\")\n", "print(\"📊 Each skill will have different P(Init), P(Learn), P(Guess), and P(Slip) values!\")"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Training BKT model with skill-specific parameter estimation...\n", "Training completed in 3.85 seconds\n", "Model trained on 110 skills\n", "Each skill now has unique parameters estimated from student data!\n"]}], "source": ["# Train the model with skill-specific parameter estimation\n", "print(\"\\nTraining BKT model with skill-specific parameter estimation...\")\n", "import time\n", "start_time = time.time()\n", "\n", "bkt_model.fit(train_data, skill_col='skill_name', user_col='user_id', correct_col='correct')\n", "\n", "training_time = time.time() - start_time\n", "print(f\"Training completed in {training_time:.2f} seconds\")\n", "print(f\"Model trained on {len(bkt_model.skill_params)} skills\")\n", "print(f\"Each skill now has unique parameters estimated from student data!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Trained Model\n", "\n", "Save the trained model for future use."]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model saved to: models/bkt_model_trained.joblib\n", "Model file size: 10.68 KB\n"]}], "source": ["# Save the trained model\n", "MODEL_PATH = \"models/bkt_model_trained.joblib\"\n", "os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "\n", "bkt_model.save(MODEL_PATH)\n", "print(f\"Model saved to: {MODEL_PATH}\")\n", "print(f\"Model file size: {os.path.getsize(MODEL_PATH) / 1024:.2f} KB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Show Training Statistics\n", "\n", "Display model parameters and performance metrics."]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== TRAINING STATISTICS ===\n", "Training time: 3.85 seconds\n", "Number of skills trained: 110\n", "Training data size: 255,487 interactions\n", "Test data size: 70,150 interactions\n", "\n", "=== MODEL PARAMETERS FOR TOP 10 SKILLS ===\n", "\n", "Equation Solving Two or Fewer Steps:\n", "  P(Init): 0.530\n", "  P(Learn): 0.135\n", "  P(Guess): 0.104\n", "  P(Slip): 0.099\n", "\n", "Conversion of Fraction Decimals Percents:\n", "  P(Init): 0.619\n", "  P(Learn): 0.143\n", "  P(Guess): 0.138\n", "  P(Slip): 0.073\n", "\n", "Addition and Subtraction Integers:\n", "  P(Init): 0.622\n", "  P(Learn): 0.189\n", "  P(Guess): 0.134\n", "  P(Slip): 0.050\n", "\n", "Addition and Subtraction Fractions:\n", "  P(Init): 0.501\n", "  P(Learn): 0.205\n", "  P(Guess): 0.100\n", "  P(Slip): 0.066\n", "\n", "Percent Of:\n", "  P(Init): 0.375\n", "  P(Learn): 0.236\n", "  P(Guess): 0.100\n", "  P(Slip): 0.076\n", "\n", "Proportion:\n", "  P(Init): 0.542\n", "  P(Learn): 0.156\n", "  P(Guess): 0.100\n", "  P(Slip): 0.067\n", "\n", "Ordering Fractions:\n", "  P(Init): 0.718\n", "  P(Learn): 0.127\n", "  P(Guess): 0.100\n", "  P(Slip): 0.050\n", "\n", "Equation Solving More Than Two Steps:\n", "  P(Init): 0.531\n", "  P(Learn): 0.124\n", "  P(Guess): 0.100\n", "  P(Slip): 0.087\n", "\n", "Probability of Two Distinct Events:\n", "  P(Init): 0.419\n", "  P(Learn): 0.147\n", "  P(Guess): 0.117\n", "  P(Slip): 0.052\n", "\n", "Finding Percents:\n", "  P(Init): 0.351\n", "  P(Learn): 0.225\n", "  P(Guess): 0.100\n", "  P(Slip): 0.069\n"]}], "source": ["# Display training statistics\n", "print(\"=== TRAINING STATISTICS ===\")\n", "print(f\"Training time: {training_time:.2f} seconds\")\n", "print(f\"Number of skills trained: {len(bkt_model.skill_params)}\")\n", "print(f\"Training data size: {len(train_data):,} interactions\")\n", "print(f\"Test data size: {len(test_data):,} interactions\")\n", "\n", "# Show parameters for top skills\n", "print(\"\\n=== MODEL PARAMETERS FOR TOP 10 SKILLS ===\")\n", "top_skills_list = skill_stats.head(10).index.tolist()\n", "for skill in top_skills_list:\n", "    if skill in bkt_model.skill_params:\n", "        params = bkt_model.skill_params[skill]\n", "        print(f\"\\n{skill}:\")\n", "        print(f\"  P(Init): {params['p_init']:.3f}\")\n", "        print(f\"  P(Learn): {params['p_learn']:.3f}\")\n", "        print(f\"  P(Guess): {params['p_guess']:.3f}\")\n", "        print(f\"  P(Slip): {params['p_slip']:.3f}\")"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SKILL-SPECIFIC PARAMETERS TABLE ===\n", "Skill                               Attempts Accuracy P(Init)  P(Learn) P(Guess) P(Slip) \n", "---------------------------------------------------------------------------------------------------------\n", "Equation Solving Two or Fewer Step  24253    0.679    0.530    0.135    0.104    0.099   \n", "Conversion of Fraction Decimals Pe  18742    0.637    0.619    0.143    0.138    0.073   \n", "Addition and Subtraction Integers   12741    0.599    0.622    0.189    0.134    0.050   \n", "Addition and Subtraction Fractions  11334    0.677    0.501    0.205    0.100    0.066   \n", "Percent Of                          9497     0.595    0.375    0.236    0.100    0.076   \n", "Proportion                          9054     0.641    0.542    0.156    0.100    0.067   \n", "Ordering Fractions                  8539     0.792    0.718    0.127    0.100    0.050   \n", "Equation Solving More Than Two Ste  8115     0.758    0.531    0.124    0.100    0.087   \n", "Probability of Two Distinct Events  7963     0.490    0.419    0.147    0.117    0.052   \n", "Finding Percents                    7694     0.538    0.351    0.225    0.100    0.069   \n", "Subtraction Whole Numbers           7669     0.641    0.566    0.225    0.100    0.051   \n", "Probability of a Single Event       7438     0.742    0.658    0.171    0.100    0.050   \n", "Pattern Finding                     7343     0.600    0.480    0.175    0.100    0.109   \n", "Absolute Value                      7340     0.757    0.618    0.178    0.148    0.050   \n", "Ordering Positive Decimals          7317     0.750    0.677    0.178    0.138    0.050   \n"]}], "source": ["# Show skill-specific parameters in a formatted table\n", "print(\"\\n=== SKILL-SPECIFIC PARAMETERS TABLE ===\")\n", "print(f\"{'Skill':<35} {'Attempts':<8} {'Accuracy':<8} {'P(Init)':<8} {'P(Learn)':<8} {'P(Guess)':<8} {'P(Slip)':<8}\")\n", "print(\"-\" * 105)\n", "\n", "top_skills = skill_stats.head(15)\n", "for skill in top_skills.index:\n", "    if skill in bkt_model.skill_params:\n", "        params = bkt_model.skill_params[skill]\n", "        stats = skill_stats.loc[skill]\n", "        print(f\"{skill[:34]:<35} {stats['Total_Attempts']:<8.0f} {stats['Accuracy']:<8.3f} \"\n", "              f\"{params['p_init']:<8.3f} {params['p_learn']:<8.3f} \"\n", "              f\"{params['p_guess']:<8.3f} {params['p_slip']:<8.3f}\")"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PARAMETER DISTRIBUTION ANALYSIS ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Parameter Summary Statistics:\n", "p_init: mean=0.473, std=0.228, min=0.010, max=0.800\n", "p_learn: mean=0.220, std=0.098, min=0.050, max=0.500\n", "p_guess: mean=0.131, std=0.055, min=0.100, max=0.250\n", "p_slip: mean=0.065, std=0.023, min=0.050, max=0.167\n"]}], "source": ["# Visualize parameter distributions across all skills\n", "print(\"\\n=== PARAMETER DISTRIBUTION ANALYSIS ===\")\n", "\n", "# Extract parameters for all skills\n", "params_data = {\n", "    'p_init': [params['p_init'] for params in bkt_model.skill_params.values()],\n", "    'p_learn': [params['p_learn'] for params in bkt_model.skill_params.values()],\n", "    'p_guess': [params['p_guess'] for params in bkt_model.skill_params.values()],\n", "    'p_slip': [params['p_slip'] for params in bkt_model.skill_params.values()]\n", "}\n", "\n", "# Create visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Plot distributions\n", "axes[0,0].hist(params_data['p_init'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0,0].set_title('Distribution of P(Init) - Initial Knowledge')\n", "axes[0,0].set_xlabel('P(Init)')\n", "axes[0,0].set_ylabel('Number of Skills')\n", "axes[0,0].axvline(np.mean(params_data['p_init']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_init\"]):.3f}')\n", "axes[0,0].legend()\n", "\n", "axes[0,1].hist(params_data['p_learn'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')\n", "axes[0,1].set_title('Distribution of P(Learn) - Learning Rate')\n", "axes[0,1].set_xlabel('P(Learn)')\n", "axes[0,1].set_ylabel('Number of Skills')\n", "axes[0,1].axvline(np.mean(params_data['p_learn']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_learn\"]):.3f}')\n", "axes[0,1].legend()\n", "\n", "axes[1,0].hist(params_data['p_guess'], bins=20, alpha=0.7, color='orange', edgecolor='black')\n", "axes[1,0].set_title('Distribution of P(Guess) - Guessing Probability')\n", "axes[1,0].set_xlabel('<PERSON>(Guess)')\n", "axes[1,0].set_ylabel('Number of Skills')\n", "axes[1,0].axvline(np.mean(params_data['p_guess']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_guess\"]):.3f}')\n", "axes[1,0].legend()\n", "\n", "axes[1,1].hist(params_data['p_slip'], bins=20, alpha=0.7, color='pink', edgecolor='black')\n", "axes[1,1].set_title('Distribution of P(Slip) - Slip Probability')\n", "axes[1,1].set_xlabel('P(Slip)')\n", "axes[1,1].set_ylabel('Number of Skills')\n", "axes[1,1].axvline(np.mean(params_data['p_slip']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_slip\"]):.3f}')\n", "axes[1,1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print summary statistics\n", "print(\"\\nParameter Summary Statistics:\")\n", "for param_name, values in params_data.items():\n", "    print(f\"{param_name}: mean={np.mean(values):.3f}, std={np.std(values):.3f}, \"\n", "          f\"min={np.min(values):.3f}, max={np.max(values):.3f}\")"]}, {"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SKILL COMPARISON BY CHARACTERISTICS ===\n", "\n", "🟢 EASIEST SKILLS (Highest P(Init) - Students often know these initially):\n", "  1. Mode: P(Init)=0.800, P(Learn)=0.117\n", "  2. Ordering Integers: P(Init)=0.800, P(Learn)=0.138\n", "  3. Area Parallelogram: P(Init)=0.800, P(Learn)=0.167\n", "  4. Distributive Property: P(Init)=0.800, P(Learn)=0.083\n", "  5. Recognize Quadratic Pattern: P(Init)=0.800, P(Learn)=0.050\n", "\n", "🔴 HARDEST SKILLS (Lowest P(Init) - Students rarely know these initially):\n", "  1. Finding Slope From Situation: P(Init)=0.010, P(Learn)=0.050\n", "  2. Midpoint: P(Init)=0.010, P(Learn)=0.150\n", "  3. Simplifying Expressions positive exponents: P(Init)=0.010, P(Learn)=0.050\n", "  4. Rotations: P(Init)=0.035, P(Learn)=0.156\n", "  5. Solving Systems of Linear Equations: P(Init)=0.077, P(Learn)=0.251\n", "\n", "🚀 FASTEST LEARNING SKILLS (Highest P(Learn) - Quick to master):\n", "  1. Effect of Changing Dimensions of a Shape Prportionally: P(Learn)=0.500, P(Init)=0.095\n", "  2. Scale Factor: P(Learn)=0.475, P(Init)=0.150\n", "  3. Fraction Of: P(Learn)=0.439, P(Init)=0.698\n", "  4. Prime Number: P(Learn)=0.438, P(Init)=0.250\n", "  5. Surface Area Cylinder: P(Learn)=0.434, P(Init)=0.082\n", "\n", "🐌 SLOWEST LEARNING SKILLS (Lowest P(Learn) - Take time to master):\n", "  1. <PERSON><PERSON> on Parallel Lines Cut by a Transversal: P(Learn)=0.050, P(Init)=0.704\n", "  2. Percent Discount: P(Learn)=0.050, P(Init)=0.250\n", "  3. Finding Slope From Situation: P(Learn)=0.050, P(Init)=0.010\n", "  4. Recognize Quadratic Pattern: P(Learn)=0.050, P(Init)=0.800\n", "  5. Quadratic Formula to Solve Quadratic Equation: P(Learn)=0.050, P(Init)=0.125\n"]}], "source": ["# Compare skills with different characteristics\n", "print(\"\\n=== SKILL COMPARISON BY CHARACTERISTICS ===\")\n", "\n", "# Find skills with different parameter patterns\n", "all_skills_params = [(skill, params) for skill, params in bkt_model.skill_params.items()]\n", "\n", "# Sort by different criteria\n", "easiest_skills = sorted(all_skills_params, key=lambda x: x[1]['p_init'], reverse=True)[:5]\n", "hardest_skills = sorted(all_skills_params, key=lambda x: x[1]['p_init'])[:5]\n", "fastest_learning = sorted(all_skills_params, key=lambda x: x[1]['p_learn'], reverse=True)[:5]\n", "slowest_learning = sorted(all_skills_params, key=lambda x: x[1]['p_learn'])[:5]\n", "\n", "print(\"\\n🟢 EASIEST SKILLS (Highest P(Init) - Students often know these initially):\")\n", "for i, (skill, params) in enumerate(easiest_skills, 1):\n", "    print(f\"  {i}. {skill}: P(Init)={params['p_init']:.3f}, P(Learn)={params['p_learn']:.3f}\")\n", "\n", "print(\"\\n🔴 HARDEST SKILLS (Lowest P(Init) - Students rarely know these initially):\")\n", "for i, (skill, params) in enumerate(hardest_skills, 1):\n", "    print(f\"  {i}. {skill}: P(Init)={params['p_init']:.3f}, P(Learn)={params['p_learn']:.3f}\")\n", "\n", "print(\"\\n🚀 FASTEST LEARNING SKILLS (Highest P(Learn) - Quick to master):\")\n", "for i, (skill, params) in enumerate(fastest_learning, 1):\n", "    print(f\"  {i}. {skill}: P(Learn)={params['p_learn']:.3f}, P(Init)={params['p_init']:.3f}\")\n", "\n", "print(\"\\n🐌 SLOWEST LEARNING SKILLS (Lowest P(Learn) - Take time to master):\")\n", "for i, (skill, params) in enumerate(slowest_learning, 1):\n", "    print(f\"  {i}. {skill}: P(Learn)={params['p_learn']:.3f}, P(Init)={params['p_init']:.3f}\")"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== MODEL EVALUATION ===\n", "Test Accuracy: 0.831\n", "Test AUC: 0.746\n", "Number of predictions: 6907\n", "Mean predicted probability: 0.780\n", "Actual success rate: 0.832\n"]}], "source": ["# Evaluate model performance on test data\n", "print(\"\\n=== MODEL EVALUATION ===\")\n", "\n", "# Simple evaluation: predict probability for each test interaction\n", "test_predictions = []\n", "test_actuals = []\n", "\n", "# Group test data by user and skill for sequential prediction\n", "for (user_id, skill), group in test_data.groupby(['user_id', 'skill_name']):\n", "    if skill in bkt_model.skill_params:\n", "        user_history = group['correct'].tolist()\n", "        if len(user_history) > 1:\n", "            # Use first n-1 responses to predict the last one\n", "            history = user_history[:-1]\n", "            actual = user_history[-1]\n", "            \n", "            probas = bkt_model.predict_proba(history, skill)\n", "            if probas:\n", "                predicted_prob = probas[-1]  # Last probability\n", "                test_predictions.append(predicted_prob)\n", "                test_actuals.append(actual)\n", "\n", "if test_predictions:\n", "    test_predictions = np.array(test_predictions)\n", "    test_actuals = np.array(test_actuals)\n", "    \n", "    # Calculate metrics\n", "    binary_predictions = (test_predictions > 0.5).astype(int)\n", "    accuracy = accuracy_score(test_actuals, binary_predictions)\n", "    auc = roc_auc_score(test_actuals, test_predictions)\n", "    \n", "    print(f\"Test Accuracy: {accuracy:.3f}\")\n", "    print(f\"Test AUC: {auc:.3f}\")\n", "    print(f\"Number of predictions: {len(test_predictions)}\")\n", "    print(f\"Mean predicted probability: {test_predictions.mean():.3f}\")\n", "    print(f\"Actual success rate: {test_actuals.mean():.3f}\")\n", "else:\n", "    print(\"No valid predictions could be made on test data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Run Prediction Examples\n", "\n", "Demonstrate the model with various prediction scenarios."]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PREDICTION EXAMPLES ===\n", "\n", "Example 1: Struggling Student\n", "Skill: <PERSON><PERSON> Finding \n", "Response history: [0, 0, 0, 1, 0, 1]\n", "Predicted probabilities: ['0.480', '0.304', '0.265', '0.259', '0.689', '0.409']\n", "Final knowledge probability: 0.409\n"]}], "source": ["# Example 1: Prediction for a struggling student\n", "print(\"=== PREDICTION EXAMPLES ===\")\n", "print(\"\\nExample 1: Struggling Student\")\n", "skill_example = list(bkt_model.skill_params.keys())[55]  # Use first available skill\n", "struggling_history = [0, 0, 0, 1, 0, 1]  # Mostly incorrect responses\n", "\n", "probas = bkt_model.predict_proba(struggling_history, skill_example)\n", "print(f\"Skill: {skill_example}\")\n", "print(f\"Response history: {struggling_history}\")\n", "print(f\"Predicted probabilities: {[f'{p:.3f}' for p in probas]}\")\n", "print(f\"Final knowledge probability: {probas[-1]:.3f}\")"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 2: Successful Student\n", "Skill: <PERSON><PERSON> Finding \n", "Response history: [1, 1, 0, 1, 1, 1]\n", "Predicted probabilities: ['0.480', '0.820', '0.884', '0.846', '0.887', '0.891']\n", "Final knowledge probability: 0.891\n"]}], "source": ["# Example 2: Prediction for a successful student\n", "print(\"\\nExample 2: Successful Student\")\n", "successful_history = [1, 1, 0, 1, 1, 1]  # Mostly correct responses\n", "\n", "probas = bkt_model.predict_proba(successful_history, skill_example)\n", "print(f\"Skill: {skill_example}\")\n", "print(f\"Response history: {successful_history}\")\n", "print(f\"Predicted probabilities: {[f'{p:.3f}' for p in probas]}\")\n", "print(f\"Final knowledge probability: {probas[-1]:.3f}\")"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 3: Learning Progression Visualization\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Example 3: Learning progression visualization\n", "print(\"\\nExample 3: Learning Progression Visualization\")\n", "\n", "# Create different learning scenarios\n", "scenarios = {\n", "    'Quick Learner': [0, 0, 1, 1, 1, 1, 1, 1],\n", "    'Slow Learner': [0, 0, 0, 0, 1, 0, 1, 1],\n", "    'Inconsistent': [1, 0, 1, 0, 1, 0, 1, 1],\n", "    'Expert': [1, 1, 1, 1, 1, 1, 1, 1]\n", "}\n", "\n", "plt.figure(figsize=(12, 8))\n", "for scenario_name, history in scenarios.items():\n", "    probas = bkt_model.predict_proba(history, skill_example)\n", "    plt.plot(range(1, len(probas) + 1), probas, marker='o', label=scenario_name, linewidth=2)\n", "\n", "plt.xlabel('Attempt Number')\n", "plt.ylabel('Probability of Knowing Skill')\n", "plt.title(f'Learning Progression for Skill: {skill_example}')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.ylim(0, 1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 4: Interactive Prediction Function\n", "Prediction result:\n", "  skill: Range\n", "  history: [0, 1, 0, 1, 1]\n", "  knowledge_progression: ['0.602', '0.327', '0.800', '0.416', '0.848']\n", "  current_knowledge_probability: 0.848\n", "  next_response_probability: 0.848\n", "  recommendation: <PERSON><PERSON> mastered\n"]}], "source": ["# Example 4: Interactive prediction function\n", "def predict_next_response(user_history, skill_name, model=bkt_model):\n", "    \"\"\"\n", "    Predict the probability of correct response for the next attempt\n", "    \n", "    Args:\n", "        user_history: List of 0s and 1s representing incorrect/correct responses\n", "        skill_name: Name of the skill\n", "        model: Trained BKT model\n", "    \n", "    Returns:\n", "        Dictionary with prediction details\n", "    \"\"\"\n", "    if skill_name not in model.skill_params:\n", "        return {\"error\": f\"Skill '{skill_name}' not found in trained model\"}\n", "    \n", "    probas = model.predict_proba(user_history, skill_name)\n", "    \n", "    if not probas:\n", "        return {\"error\": \"Could not generate predictions\"}\n", "    \n", "    current_knowledge = probas[-1]\n", "    \n", "    return {\n", "        \"skill\": skill_name,\n", "        \"history\": user_history,\n", "        \"knowledge_progression\": probas,\n", "        \"current_knowledge_probability\": current_knowledge,\n", "        \"next_response_probability\": current_knowledge,\n", "        \"recommendation\": \"Continue practicing\" if current_knowledge < 0.8 else \"Skill mastered\"\n", "    }\n", "\n", "# Test the function\n", "print(\"\\nExample 4: Interactive Prediction Function\")\n", "test_history = [0, 1, 0, 1, 1]\n", "skill_example = list(bkt_model.skill_params.keys())[11]\n", "result = predict_next_response(test_history, skill_example)\n", "\n", "print(f\"Prediction result:\")\n", "for key, value in result.items():\n", "    if key == 'knowledge_progression':\n", "        print(f\"  {key}: {[f'{p:.3f}' for p in value]}\")\n", "    elif isinstance(value, float):\n", "        print(f\"  {key}: {value:.3f}\")\n", "    else:\n", "        print(f\"  {key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated a complete BKT model training pipeline with **skill-specific parameter estimation**:\n", "\n", "1. **Data Loading**: Successfully loaded the 2009 Skill Builder dataset\n", "2. **Data Cleaning**: Handled missing values and ensured data quality\n", "3. **Data Exploration**: Analyzed skills distribution and user performance\n", "4. **Train/Test Split**: Split data by users to avoid leakage\n", "5. **Model Training**: Trained BKT model with **skill-specific parameters** estimated from data\n", "6. **Parameter Analysis**: Visualized parameter distributions and identified skill characteristics\n", "7. **Model Saving**: Saved trained model for future use\n", "8. **Performance Evaluation**: Evaluated model on test data\n", "9. **Prediction Examples**: Demonstrated various prediction scenarios\n", "\n", "### 🎯 **Key Improvements:**\n", "- **Unique Parameters**: Each skill now has different P(Init), P(Learn), P(Guess), and P(Slip) values\n", "- **Data-Driven**: Parameters are estimated from actual student performance patterns\n", "- **Skill Insights**: Can identify easy vs hard skills, fast vs slow learning skills\n", "- **Better Accuracy**: More realistic modeling of different skill characteristics\n", "\n", "### 🚀 **The BKT model can now be used to:**\n", "- Track student knowledge over time with skill-specific accuracy\n", "- Predict probability of correct responses based on skill difficulty\n", "- Identify when students have mastered different types of skills\n", "- Provide personalized learning recommendations based on skill characteristics\n", "- Understand which skills are naturally easier/harder for students\n", "- Optimize learning sequences based on skill learning rates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}