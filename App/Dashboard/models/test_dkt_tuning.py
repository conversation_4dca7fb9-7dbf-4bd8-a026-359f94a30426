#!/usr/bin/env python3
"""
Test script for DKT hyperparameter tuning
"""

import os
import sys
import pandas as pd
import numpy as np
import tensorflow as tf

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_dkt import DeepKnowledgeTracing, load_skill_builder_data

def test_hyperparameter_tuning():
    """Test hyperparameter tuning with a small dataset"""
    
    print("="*60)
    print("TESTING DKT HYPERPARAMETER TUNING")
    print("="*60)
    
    # Load data
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    
    if not os.path.exists(DATA_PATH):
        print(f"Data file not found: {DATA_PATH}")
        print("Please check the path and try again.")
        return
    
    print("Loading dataset...")
    df = load_skill_builder_data(DATA_PATH)
    print(f"Original dataset size: {len(df):,}")
    
    # Use a smaller subset for testing
    print("Creating test subset...")
    df_sample = df.sample(n=min(5000, len(df)), random_state=42)
    print(f"Test subset size: {len(df_sample):,}")
    
    # Initialize DKT model
    print("\nInitializing DKT model...")
    dkt = DeepKnowledgeTracing(
        hidden_dim=64,  # Smaller for testing
        num_layers=1,   # Fewer layers for testing
        dropout=0.2,
        learning_rate=0.001,
        batch_size=32,
        max_epochs=5,   # Fewer epochs for testing
        patience=2,
        max_seq_len=50  # Shorter sequences for testing
    )
    
    print(f"GPU available: {dkt.gpu_available}")
    
    # Test hyperparameter tuning
    print("\nStarting hyperparameter tuning test...")
    try:
        tuning_results = dkt.tune_hyperparameters(
            df_sample,
            method='grid_search',
            max_trials=4,  # Very limited for testing
            tuning_epochs=3  # Very few epochs for testing
        )
        
        print("\n" + "="*50)
        print("TUNING RESULTS:")
        print("="*50)
        print(f"Best parameters: {tuning_results['best_params']}")
        print(f"Best score: {tuning_results['best_score']}")
        
        if 'results' in tuning_results and not tuning_results['results'].empty:
            print(f"Number of trials: {len(tuning_results['results'])}")
            print("\nAll results:")
            print(tuning_results['results'])
        else:
            print("No detailed results available")
            
        # Test training with best parameters
        if tuning_results['best_params'] is not None:
            print("\nTesting training with best parameters...")
            final_dkt = DeepKnowledgeTracing(
                max_epochs=3,  # Very few epochs for testing
                patience=2,
                max_seq_len=50
            )
            
            final_dkt.fit_with_best_params(
                df_sample, 
                tuning_results['best_params'],
                validation_split=0.2
            )
            
            print("✅ Training with best parameters completed successfully!")
        else:
            print("❌ No best parameters found - tuning failed")
            
    except Exception as e:
        print(f"❌ Hyperparameter tuning failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "="*60)
    print("✅ HYPERPARAMETER TUNING TEST COMPLETED")
    print("="*60)
    return True

def test_simple_training():
    """Test simple DKT training without tuning"""
    
    print("\n" + "="*60)
    print("TESTING SIMPLE DKT TRAINING")
    print("="*60)
    
    # Create synthetic data for testing
    print("Creating synthetic test data...")
    
    # Simple synthetic dataset
    np.random.seed(42)
    n_students = 100
    n_interactions = 500
    n_skills = 5
    
    data = []
    for i in range(n_interactions):
        student_id = np.random.randint(1, n_students + 1)
        skill_id = np.random.randint(1, n_skills + 1)
        problem_id = np.random.randint(1, 100)
        correct = np.random.choice([0, 1], p=[0.3, 0.7])  # 70% correct
        
        data.append({
            'user_id': f'student_{student_id}',
            'problem_id': problem_id,
            'skill_name': f'skill_{skill_id}',
            'correct': correct
        })
    
    df_synthetic = pd.DataFrame(data)
    print(f"Synthetic dataset created: {len(df_synthetic)} interactions")
    print(f"Students: {df_synthetic['user_id'].nunique()}")
    print(f"Skills: {df_synthetic['skill_name'].nunique()}")
    
    # Test simple training
    try:
        print("\nTesting simple DKT training...")
        dkt = DeepKnowledgeTracing(
            hidden_dim=32,
            num_layers=1,
            dropout=0.1,
            learning_rate=0.01,
            batch_size=16,
            max_epochs=3,
            patience=2,
            max_seq_len=20
        )
        
        dkt.fit(df_synthetic, validation_split=0.2)
        
        print("✅ Simple training completed successfully!")
        
        # Test prediction
        print("\nTesting prediction...")
        test_history = [(1, 1), (2, 0), (3, 1)]
        test_skill = 'skill_1'
        
        predictions = dkt.predict_proba(test_history, test_skill)
        print(f"Test predictions: {predictions}")
        
        print("✅ Prediction test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Simple training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("DKT Hyperparameter Tuning Test")
    print("This script tests the hyperparameter tuning functionality")
    print("with a small dataset to identify and fix issues.\n")
    
    # Test simple training first
    simple_success = test_simple_training()
    
    if simple_success:
        print("\n" + "="*60)
        print("Simple training successful, proceeding to tuning test...")
        
        # Test hyperparameter tuning
        tuning_success = test_hyperparameter_tuning()
        
        if tuning_success:
            print("\n🎉 ALL TESTS PASSED!")
            print("Hyperparameter tuning is working correctly.")
        else:
            print("\n❌ TUNING TEST FAILED")
            print("There are issues with hyperparameter tuning.")
    else:
        print("\n❌ SIMPLE TRAINING FAILED")
        print("Basic DKT training has issues that need to be fixed first.")
