#!/usr/bin/env python3
"""
Example script demonstrating DKT hyperparameter tuning
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_dkt import DeepKnowledgeTracing, load_skill_builder_data, compare_hyperparameter_results

def run_dkt_hyperparameter_example():
    """
    Complete example of DKT hyperparameter tuning workflow
    """
    print("="*70)
    print("DKT HYPERPARAMETER TUNING EXAMPLE")
    print("="*70)
    
    # Load data
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    
    print("1. Loading dataset...")
    df = load_skill_builder_data(DATA_PATH)
    print(f"   Dataset size: {len(df):,} interactions")
    print(f"   Unique students: {df['user_id'].nunique():,}")
    print(f"   Unique skills: {df['skill_name'].nunique():,}")
    
    # Use a manageable subset for demonstration
    print("\n2. Creating sample for hyperparameter tuning...")
    df_sample = df.sample(n=min(10000, len(df)), random_state=42)
    print(f"   Sample size: {len(df_sample):,} interactions")
    print(f"   Sample students: {df_sample['user_id'].nunique():,}")
    print(f"   Sample skills: {df_sample['skill_name'].nunique():,}")
    
    # Initialize DKT model for tuning
    print("\n3. Initializing DKT model...")
    dkt = DeepKnowledgeTracing(
        hidden_dim=128,     # Will be optimized
        num_layers=2,       # Will be optimized  
        dropout=0.2,        # Will be optimized
        learning_rate=0.001, # Will be optimized
        batch_size=64,      # Will be optimized
        max_epochs=20,      # Reasonable for final training
        patience=5,         # Early stopping
        max_seq_len=100     # Sequence length
    )
    
    print(f"   GPU available: {dkt.gpu_available}")
    
    # Perform hyperparameter tuning
    print("\n4. Starting hyperparameter tuning...")
    print("   This will test different combinations of:")
    print("   - Hidden dimensions: [64, 128]")
    print("   - Number of layers: [1, 2]") 
    print("   - Dropout rates: [0.2, 0.3]")
    print("   - Learning rates: [0.001, 0.01]")
    print("   - Batch sizes: [32, 64]")
    
    tuning_results = dkt.tune_hyperparameters(
        df_sample,
        method='grid_search',
        max_trials=8,       # Limited for demonstration
        tuning_epochs=10    # Fewer epochs for faster tuning
    )
    
    # Display results
    print("\n" + "="*50)
    print("5. HYPERPARAMETER TUNING RESULTS")
    print("="*50)
    
    best_params = tuning_results['best_params']
    best_score = tuning_results['best_score']
    
    print(f"✅ Best validation accuracy: {best_score:.4f}")
    print(f"✅ Best parameters found:")
    for param, value in best_params.items():
        print(f"   • {param}: {value}")
    
    # Analyze results
    if 'results' in tuning_results and not tuning_results['results'].empty:
        results_df = tuning_results['results']
        print(f"\n📊 Analysis of {len(results_df)} trials:")
        
        # Show top 3 results
        top_3 = results_df.nlargest(3, 'val_accuracy')
        print("\nTop 3 parameter combinations:")
        for i, (_, row) in enumerate(top_3.iterrows(), 1):
            print(f"\n{i}. Validation Accuracy: {row['val_accuracy']:.4f}")
            print(f"   Hidden Dim: {row.get('hidden_dim', 'N/A')}")
            print(f"   Layers: {row.get('num_layers', 'N/A')}")
            print(f"   Dropout: {row.get('dropout', 'N/A')}")
            print(f"   Learning Rate: {row.get('learning_rate', 'N/A')}")
            print(f"   Batch Size: {row.get('batch_size', 'N/A')}")
        
        # Performance improvement
        worst_score = results_df['val_accuracy'].min()
        improvement = ((best_score - worst_score) / worst_score) * 100
        print(f"\n📈 Performance improvement from tuning: {improvement:.1f}%")
        
        # Parameter correlations
        print(f"\n🔍 Parameter impact analysis:")
        numeric_cols = ['hidden_dim', 'num_layers', 'dropout', 'learning_rate', 'batch_size', 'val_accuracy']
        numeric_cols = [col for col in numeric_cols if col in results_df.columns]
        
        if len(numeric_cols) > 1:
            correlations = results_df[numeric_cols].corr()['val_accuracy'].sort_values(ascending=False)
            for param, corr in correlations.items():
                if param != 'val_accuracy':
                    impact = "Strong" if abs(corr) > 0.5 else "Moderate" if abs(corr) > 0.3 else "Weak"
                    direction = "positive" if corr > 0 else "negative"
                    print(f"   • {param}: {corr:.3f} ({impact} {direction} impact)")
    
    # Train final model with best parameters
    print(f"\n6. Training final model with optimized parameters...")
    final_dkt = DeepKnowledgeTracing(
        max_epochs=15,      # Reasonable for final training
        patience=5,
        max_seq_len=100
    )
    
    final_dkt.fit_with_best_params(
        df_sample, 
        best_params,
        validation_split=0.2
    )
    
    # Get final model statistics
    final_stats = final_dkt.get_model_statistics()
    print(f"\n📊 Final model statistics:")
    print(f"   • Total parameters: {final_stats['total_parameters']:,}")
    print(f"   • Final validation accuracy: {final_stats['final_val_acc']:.4f}")
    print(f"   • Training epochs: {final_stats['training_epochs']}")
    print(f"   • Best epoch: {final_stats['best_epoch']}")
    
    # Test predictions
    print(f"\n7. Testing predictions with optimized model...")
    test_cases = [
        {
            'history': [(1, 0), (2, 0), (3, 1), (4, 1)],
            'skill': df_sample['skill_name'].iloc[0],
            'description': 'Learning progression (struggling → improving)'
        },
        {
            'history': [(1, 1), (2, 1), (3, 1), (4, 0)],
            'skill': df_sample['skill_name'].iloc[1],
            'description': 'Performance decline (good → struggling)'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            predictions = final_dkt.predict_proba(test_case['history'], test_case['skill'])
            print(f"\n   Test {i}: {test_case['description']}")
            print(f"   Skill: {test_case['skill']}")
            print(f"   History: {test_case['history']}")
            print(f"   Predictions: {[f'{p:.3f}' for p in predictions]}")
            
            if len(predictions) > 1:
                trend = "improving" if predictions[-1] > predictions[0] else "declining"
                print(f"   Learning trend: {trend}")
        except Exception as e:
            print(f"   Prediction failed: {e}")
    
    # Summary
    print(f"\n" + "="*70)
    print("8. SUMMARY")
    print("="*70)
    print(f"✅ Hyperparameter tuning completed successfully")
    print(f"✅ Best validation accuracy: {best_score:.4f}")
    print(f"✅ Optimal parameters identified and applied")
    print(f"✅ Final model trained and ready for use")
    print(f"✅ Model can make predictions for student learning progression")
    
    print(f"\n🎯 Key insights:")
    print(f"   • Hyperparameter tuning improved model performance")
    print(f"   • Optimal configuration found through systematic search")
    print(f"   • Model successfully captures learning patterns")
    print(f"   • Ready for deployment in educational applications")
    
    return final_dkt, tuning_results

if __name__ == "__main__":
    print("DKT Hyperparameter Tuning Example")
    print("This script demonstrates the complete workflow for:")
    print("1. Loading educational data")
    print("2. Performing hyperparameter optimization") 
    print("3. Training the final model with best parameters")
    print("4. Testing predictions and analyzing results")
    print()
    
    try:
        model, results = run_dkt_hyperparameter_example()
        print(f"\n🎉 Example completed successfully!")
        print(f"The optimized DKT model is ready for use.")
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        import traceback
        traceback.print_exc()
