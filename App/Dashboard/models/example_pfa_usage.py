import os
from train_pfa import PerformanceFactorsAnalysis

MODEL_PATH = os.path.join("output", "pfa_model.joblib")

# Example 1: Predict using problem_id and correct pairs
user_history_1 = [(12345, 1), (12346, 0), (12347, 1)]  # (problem_id, correct)
skill_1 = 'Addition and Subtraction Integers'
user_id_1 = 'user_123'

# Example 2: Predict using just correct values (requires problem_id for next prediction)
user_history_2 = [1, 0, 1, 1]  # correct values only
skill_2 = 'Equation Solving Two or Fewer Steps'
user_id_2 = 'user_456'
next_problem_id = 54321

if os.path.exists(MODEL_PATH):
    print("Loading trained PFA model...")
    pfa = PerformanceFactorsAnalysis.load(MODEL_PATH)
    print("✅ Model loaded successfully!")
    
    print("\n=== EXAMPLE 1: Using (problem_id, correct) pairs ===")
    print(f"User: {user_id_1}")
    print(f"Skill: {skill_1}")
    print(f"History: {user_history_1}")
    
    try:
        probas_1 = pfa.predict_proba(user_history_1, skill_1, user_id_1)
        print(f"Predicted probabilities: {probas_1}")
        
        # Show skill parameters
        skill_params = pfa.get_skill_parameters(skill_1)
        print(f"Skill learning rate: {skill_params['learning_rate']:.3f}")
        print(f"Total opportunities: {skill_params['total_opportunities']}")
        
    except Exception as e:
        print(f"Error in prediction: {e}")
    
    print("\n=== EXAMPLE 2: Using correct values only ===")
    print(f"User: {user_id_2}")
    print(f"Skill: {skill_2}")
    print(f"History: {user_history_2}")
    print(f"Next problem ID: {next_problem_id}")
    
    try:
        probas_2 = pfa.predict_proba(user_history_2, skill_2, user_id_2, next_problem_id)
        print(f"Predicted probabilities: {probas_2}")
        
        # Show skill parameters
        skill_params = pfa.get_skill_parameters(skill_2)
        print(f"Skill learning rate: {skill_params['learning_rate']:.3f}")
        print(f"Total opportunities: {skill_params['total_opportunities']}")
        
    except Exception as e:
        print(f"Error in prediction: {e}")
    
    print("\n=== MODEL STATISTICS ===")
    print(f"Number of skills: {len(pfa.skill_learning_rates)}")
    print(f"Number of students: {len(pfa.student_abilities)}")
    print(f"Number of problems: {len(pfa.problem_difficulties)}")
    
    # Show some example parameters
    print(f"\nExample skill learning rates:")
    for i, (skill, lr) in enumerate(list(pfa.skill_learning_rates.items())[:5]):
        print(f"  {skill}: {lr:.3f}")
    
else:
    print(f"Trained model not found at {MODEL_PATH}")
    print("Please train the PFA model first using the notebook or training script.") 