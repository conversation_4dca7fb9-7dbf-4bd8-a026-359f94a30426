import os
import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, optimizers, losses, metrics
import keras_tuner as kt
from sklearn.base import BaseEstimator
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, roc_auc_score, log_loss
from joblib import dump, load
import warnings
import time
from collections import defaultdict
import itertools
warnings.filterwarnings('ignore')

# Set TensorFlow to use GPU if available
physical_devices = tf.config.list_physical_devices('GPU')
if len(physical_devices) > 0:
    tf.config.experimental.set_memory_growth(physical_devices[0], True)


def prepare_sequences_for_training(sequences, max_seq_len=200):
    """
    Prepare sequences for TensorFlow training
    """
    inputs = []
    targets = []
    lengths = []

    for sequence in sequences:
        # Pad or truncate sequence
        if len(sequence) > max_seq_len:
            sequence = sequence[-max_seq_len:]

        # Create input and target sequences
        input_seq = sequence[:-1]  # All but last
        target_seq = sequence[1:]  # All but first

        # Pad sequences if needed
        seq_len = len(input_seq)
        if seq_len < max_seq_len - 1:
            padding_len = max_seq_len - 1 - seq_len
            input_seq.extend([0] * padding_len)
            target_seq.extend([0] * padding_len)

        inputs.append(input_seq)
        targets.append(target_seq)
        lengths.append(seq_len)

    return np.array(inputs), np.array(targets), np.array(lengths)


def create_dkt_model(num_skills, hidden_dim=128, num_layers=2, dropout=0.2, max_seq_len=200,
                     learning_rate=0.001, embedding_dim=None):
    """
    Create Deep Knowledge Tracing Model using TensorFlow/Keras
    """
    try:
        print(f"Creating DKT model with: skills={num_skills}, hidden_dim={hidden_dim}, "
              f"num_layers={num_layers}, dropout={dropout}, max_seq_len={max_seq_len}")

        # Input dimension: num_skills * 2 (skill + correctness)
        input_dim = num_skills * 2

        # Use hidden_dim as embedding_dim if not specified
        if embedding_dim is None:
            embedding_dim = hidden_dim

        # Input layer
        inputs = keras.Input(shape=(max_seq_len - 1,), name='input_sequences')

        # Embedding layer for skill-correctness pairs
        embedded = layers.Embedding(
            input_dim=input_dim + 1,
            output_dim=embedding_dim,
            mask_zero=True,  # Handle padding
            name='embedding'
        )(inputs)

        # LSTM layers
        lstm_out = embedded
        for i in range(num_layers):
            return_sequences = True  # Always return sequences for output
            lstm_out = layers.LSTM(
                hidden_dim,
                return_sequences=return_sequences,
                dropout=dropout,
                recurrent_dropout=dropout,
                name=f'lstm_{i+1}'
            )(lstm_out)

        # Apply dropout
        lstm_out = layers.Dropout(dropout, name='dropout')(lstm_out)

        # Output layer - predict for each skill
        output = layers.Dense(num_skills, activation='sigmoid', name='output')(lstm_out)

        # Create model
        model = keras.Model(inputs=inputs, outputs=output, name='DKT_Model')

        print(f"Model created successfully with {model.count_params():,} parameters")
        return model

    except Exception as e:
        print(f"Error creating model: {e}")
        raise


def build_model_for_tuning(hp, num_skills, max_seq_len):
    """
    Build model with hyperparameters for Keras Tuner
    """
    # Hyperparameters to tune
    hidden_dim = hp.Int('hidden_dim', min_value=64, max_value=256, step=32)
    num_layers = hp.Int('num_layers', min_value=1, max_value=3)
    dropout = hp.Float('dropout', min_value=0.1, max_value=0.5, step=0.1)
    learning_rate = hp.Float('learning_rate', min_value=1e-4, max_value=1e-2, sampling='LOG')
    embedding_dim = hp.Int('embedding_dim', min_value=32, max_value=128, step=16)

    # Create model
    model = create_dkt_model(
        num_skills=num_skills,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        dropout=dropout,
        max_seq_len=max_seq_len,
        embedding_dim=embedding_dim
    )

    # Compile model
    model.compile(
        optimizer=optimizers.Adam(learning_rate=learning_rate),
        loss='binary_crossentropy',
        metrics=['accuracy']
    )

    return model


class GridSearchHyperparameterTuner:
    """
    Custom grid search hyperparameter tuner for DKT
    """

    def __init__(self, param_grid, max_trials=10, patience=5):
        self.param_grid = param_grid
        self.max_trials = max_trials
        self.patience = patience
        self.results = []
        self.best_params = None
        self.best_score = -np.inf

    def search(self, train_inputs, train_targets, val_inputs, val_targets,
               num_skills, max_seq_len, epochs=20):
        """
        Perform grid search over hyperparameters
        """
        print(f"Starting grid search with up to {self.max_trials} trials...")

        # Generate parameter combinations
        param_names = list(self.param_grid.keys())
        param_values = list(self.param_grid.values())
        param_combinations = list(itertools.product(*param_values))

        print(f"Total possible combinations: {len(param_combinations)}")

        # Limit to max_trials
        if len(param_combinations) > self.max_trials:
            param_combinations = param_combinations[:self.max_trials]
            print(f"Limited to {self.max_trials} trials")

        successful_trials = 0

        for trial_idx, param_combo in enumerate(param_combinations):
            print(f"\nTrial {trial_idx + 1}/{len(param_combinations)}")

            # Create parameter dictionary
            params = dict(zip(param_names, param_combo))
            print(f"Testing parameters: {params}")

            try:
                # Create model with current parameters
                model = create_dkt_model(
                    num_skills=num_skills,
                    max_seq_len=max_seq_len,
                    hidden_dim=params.get('hidden_dim', 128),
                    num_layers=params.get('num_layers', 2),
                    dropout=params.get('dropout', 0.2),
                    learning_rate=params.get('learning_rate', 0.001)
                )

                # Compile model
                model.compile(
                    optimizer=optimizers.Adam(learning_rate=params.get('learning_rate', 0.001)),
                    loss='binary_crossentropy',
                    metrics=['accuracy']
                )

                print(f"Model created with {model.count_params():,} parameters")

                # Early stopping callback
                early_stopping = keras.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=self.patience,
                    restore_best_weights=True,
                    verbose=0
                )

                # Train model
                print(f"Training for up to {epochs} epochs...")
                history = model.fit(
                    train_inputs, train_targets,
                    validation_data=(val_inputs, val_targets),
                    epochs=epochs,
                    batch_size=params.get('batch_size', 32),
                    callbacks=[early_stopping],
                    verbose=0
                )

                # Check if training was successful
                if 'val_accuracy' not in history.history or len(history.history['val_accuracy']) == 0:
                    print("Training failed - no validation accuracy recorded")
                    continue

                # Get best validation accuracy
                best_val_acc = max(history.history['val_accuracy'])
                best_val_loss = min(history.history['val_loss'])

                # Store results
                result = {
                    'trial': trial_idx + 1,
                    'params': params.copy(),
                    'val_accuracy': best_val_acc,
                    'val_loss': best_val_loss,
                    'epochs_trained': len(history.history['loss'])
                }
                self.results.append(result)
                successful_trials += 1

                print(f"✅ Success! Validation accuracy: {best_val_acc:.4f}, Loss: {best_val_loss:.4f}")

                # Update best parameters
                if best_val_acc > self.best_score:
                    self.best_score = best_val_acc
                    self.best_params = params.copy()
                    print(f"🎯 New best score: {best_val_acc:.4f}")

                # Clean up model to free memory
                del model
                tf.keras.backend.clear_session()

            except Exception as e:
                print(f"❌ Trial failed with error: {e}")
                # Clean up on error
                tf.keras.backend.clear_session()
                continue

        print(f"\n" + "="*50)
        print(f"Grid search completed!")
        print(f"Successful trials: {successful_trials}/{len(param_combinations)}")

        if self.best_params is not None:
            print(f"Best parameters: {self.best_params}")
            print(f"Best validation accuracy: {self.best_score:.4f}")
        else:
            print("No successful trials found!")

        return self.best_params, self.best_score

    def get_results_df(self):
        """
        Get results as pandas DataFrame
        """
        if not self.results:
            return pd.DataFrame()

        # Flatten parameter dictionaries
        flattened_results = []
        for result in self.results:
            flat_result = result['params'].copy()
            flat_result.update({
                'trial': result['trial'],
                'val_accuracy': result['val_accuracy'],
                'val_loss': result['val_loss'],
                'epochs_trained': result['epochs_trained']
            })
            flattened_results.append(flat_result)

        return pd.DataFrame(flattened_results)


class DeepKnowledgeTracing(BaseEstimator):
    """
    Deep Knowledge Tracing model for educational data.
    
    This implementation uses LSTM networks to model student learning over time,
    predicting the probability of correct responses based on past interactions.
    """
    
    def __init__(self, hidden_dim=128, num_layers=2, dropout=0.2, learning_rate=0.001,
                 batch_size=32, max_epochs=50, patience=10, max_seq_len=200):
        # Model architecture parameters
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.dropout = dropout

        # Training parameters
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.max_epochs = max_epochs
        self.patience = patience
        self.max_seq_len = max_seq_len

        # Check GPU availability
        self.gpu_available = len(tf.config.list_physical_devices('GPU')) > 0

        # Model components
        self.model = None
        self.skill_encoder = None
        self.num_skills = None

        # Training history
        self.training_losses = []
        self.validation_losses = []
        self.training_accuracies = []
        self.validation_accuracies = []
        self.best_epoch = 0

        print(f"DKT Model initialized with GPU: {self.gpu_available}")
    
    def _prepare_data(self, df, skill_col='skill_name', user_col='user_id', 
                     problem_col='problem_id', correct_col='correct'):
        """Prepare data for DKT training"""
        print("Preparing data for DKT...")
        
        # Clean data
        essential_cols = [user_col, problem_col, skill_col, correct_col]
        df_clean = df.dropna(subset=essential_cols).copy()
        df_clean[correct_col] = df_clean[correct_col].astype(int)
        df_clean[user_col] = df_clean[user_col].astype(str)
        
        print(f"Cleaned dataset: {len(df_clean):,} interactions")
        print(f"Unique students: {df_clean[user_col].nunique():,}")
        print(f"Unique skills: {df_clean[skill_col].nunique():,}")
        
        return df_clean
    
    def _encode_skills(self, df, skill_col='skill_name'):
        """Encode skills to integers"""
        self.skill_encoder = LabelEncoder()
        df['skill_id'] = self.skill_encoder.fit_transform(df[skill_col])
        self.num_skills = len(self.skill_encoder.classes_)
        print(f"Encoded {self.num_skills} unique skills")
        return df
    
    def _create_sequences(self, df, user_col='user_id', skill_col='skill_id', 
                         correct_col='correct'):
        """Create sequences for each student"""
        print("Creating student sequences...")
        
        sequences = []
        user_groups = df.groupby(user_col)
        
        for user_id, user_data in user_groups:
            # Sort by some order (assuming chronological if available)
            if 'order_id' in user_data.columns:
                user_data = user_data.sort_values('order_id')
            
            # Create sequence of (skill_id, correct) pairs
            user_sequence = []
            for _, row in user_data.iterrows():
                skill_id = row[skill_col]
                correct = row[correct_col]
                
                # Encode as: skill_id + (correct * num_skills)
                # This creates unique IDs for (skill, correctness) pairs
                encoded_interaction = skill_id + 1 + (correct * self.num_skills)
                user_sequence.append(encoded_interaction)
            
            # Only include sequences with at least 2 interactions
            if len(user_sequence) >= 2:
                sequences.append(user_sequence)
        
        print(f"Created {len(sequences)} student sequences")
        print(f"Average sequence length: {np.mean([len(seq) for seq in sequences]):.1f}")
        
        return sequences
    
    def _split_sequences(self, sequences, validation_split=0.2):
        """Split sequences into train and validation sets"""
        np.random.shuffle(sequences)
        split_idx = int(len(sequences) * (1 - validation_split))
        
        train_sequences = sequences[:split_idx]
        val_sequences = sequences[split_idx:]
        
        print(f"Training sequences: {len(train_sequences)}")
        print(f"Validation sequences: {len(val_sequences)}")
        
        return train_sequences, val_sequences

    def fit(self, df, skill_col='skill_name', user_col='user_id',
            problem_col='problem_id', correct_col='correct', validation_split=0.2):
        """Train the DKT model"""
        print("Training Deep Knowledge Tracing model...")
        print(f"Dataset size: {len(df):,} interactions")

        # Prepare data
        df_clean = self._prepare_data(df, skill_col, user_col, problem_col, correct_col)
        df_encoded = self._encode_skills(df_clean, skill_col)

        # Create sequences
        sequences = self._create_sequences(df_encoded, user_col, 'skill_id', correct_col)
        train_sequences, val_sequences = self._split_sequences(sequences, validation_split)

        # Prepare data for TensorFlow
        train_inputs, train_targets, train_lengths = prepare_sequences_for_training(
            train_sequences, self.max_seq_len
        )
        val_inputs, val_targets, val_lengths = prepare_sequences_for_training(
            val_sequences, self.max_seq_len
        )

        print(f"Training data shape: {train_inputs.shape}")
        print(f"Validation data shape: {val_inputs.shape}")

        # Initialize model
        self.model = create_dkt_model(
            num_skills=self.num_skills,
            hidden_dim=self.hidden_dim,
            num_layers=self.num_layers,
            dropout=self.dropout,
            max_seq_len=self.max_seq_len
        )

        # Compile model
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=self.learning_rate),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        # Prepare targets for training
        train_targets_processed = self._prepare_targets_for_training(
            train_inputs, train_targets, train_lengths
        )
        val_targets_processed = self._prepare_targets_for_training(
            val_inputs, val_targets, val_lengths
        )

        print(f"\nStarting training with GPU: {self.gpu_available}...")
        print(f"Model parameters: {self.model.count_params():,}")

        # Setup callbacks
        early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=self.patience,
            restore_best_weights=True,
            verbose=1
        )

        # Custom callback to track training history
        class TrainingHistoryCallback(keras.callbacks.Callback):
            def __init__(self, dkt_instance):
                super().__init__()
                self.dkt_instance = dkt_instance

            def on_epoch_end(self, epoch, logs=None):
                self.dkt_instance.training_losses.append(logs.get('loss', 0))
                self.dkt_instance.validation_losses.append(logs.get('val_loss', 0))
                self.dkt_instance.training_accuracies.append(logs.get('accuracy', 0))
                self.dkt_instance.validation_accuracies.append(logs.get('val_accuracy', 0))

        history_callback = TrainingHistoryCallback(self)

        # Train the model
        history = self.model.fit(
            train_inputs,
            train_targets_processed,
            batch_size=self.batch_size,
            epochs=self.max_epochs,
            validation_data=(val_inputs, val_targets_processed),
            callbacks=[early_stopping, history_callback],
            verbose=1
        )

        # Find best epoch
        if 'val_loss' in history.history:
            self.best_epoch = np.argmin(history.history['val_loss'])
            best_val_loss = min(history.history['val_loss'])
        else:
            best_val_loss = 0.0

        print(f"\nTraining completed!")
        print(f"Best epoch: {self.best_epoch + 1}")
        print(f"Best validation loss: {best_val_loss:.4f}")

        return self

    def tune_hyperparameters(self, df, method='grid_search', max_trials=10,
                           skill_col='skill_name', user_col='user_id',
                           problem_col='problem_id', correct_col='correct',
                           validation_split=0.2, tuning_epochs=15):
        """
        Tune hyperparameters using grid search or Keras Tuner

        Args:
            df: Training dataframe
            method: 'grid_search' or 'keras_tuner'
            max_trials: Maximum number of trials
            tuning_epochs: Number of epochs for each trial

        Returns:
            Best parameters and results
        """
        print(f"Starting hyperparameter tuning using {method}...")

        # Prepare data
        df_clean = self._prepare_data(df, skill_col, user_col, problem_col, correct_col)
        df_encoded = self._encode_skills(df_clean, skill_col)
        sequences = self._create_sequences(df_encoded, user_col, 'skill_id', correct_col)
        train_sequences, val_sequences = self._split_sequences(sequences, validation_split)

        # Prepare data for training
        train_inputs, train_targets, train_lengths = prepare_sequences_for_training(
            train_sequences, self.max_seq_len
        )
        val_inputs, val_targets, val_lengths = prepare_sequences_for_training(
            val_sequences, self.max_seq_len
        )

        # Prepare targets
        train_targets_processed = self._prepare_targets_for_training(
            train_inputs, train_targets, train_lengths
        )
        val_targets_processed = self._prepare_targets_for_training(
            val_inputs, val_targets, val_lengths
        )

        if method == 'grid_search':
            return self._grid_search_tuning(
                train_inputs, train_targets_processed,
                val_inputs, val_targets_processed,
                max_trials, tuning_epochs
            )
        elif method == 'keras_tuner':
            return self._keras_tuner_tuning(
                train_inputs, train_targets_processed,
                val_inputs, val_targets_processed,
                max_trials, tuning_epochs
            )
        else:
            raise ValueError("Method must be 'grid_search' or 'keras_tuner'")

    def _grid_search_tuning(self, train_inputs, train_targets, val_inputs, val_targets,
                           max_trials, epochs):
        """
        Perform grid search hyperparameter tuning
        """
        # Define smaller parameter grid for faster testing
        param_grid = {
            'hidden_dim': [64, 128],
            'num_layers': [1, 2],
            'dropout': [0.2, 0.3],
            'learning_rate': [0.001, 0.01],
            'batch_size': [32, 64]
        }

        print(f"Parameter grid: {param_grid}")

        # Create tuner
        tuner = GridSearchHyperparameterTuner(
            param_grid=param_grid,
            max_trials=max_trials,
            patience=3  # Reduced patience for faster tuning
        )

        # Perform search
        try:
            best_params, best_score = tuner.search(
                train_inputs, train_targets,
                val_inputs, val_targets,
                self.num_skills, self.max_seq_len,
                epochs=epochs
            )

            # Get results DataFrame
            results_df = tuner.get_results_df()

            # If no successful trials, use default parameters
            if best_params is None or best_score == -np.inf:
                print("Warning: No successful trials found. Using default parameters.")
                best_params = {
                    'hidden_dim': 128,
                    'num_layers': 2,
                    'dropout': 0.2,
                    'learning_rate': 0.001,
                    'batch_size': 32
                }
                best_score = 0.5  # Default score

            return {
                'best_params': best_params,
                'best_score': best_score,
                'results': results_df,
                'tuner': tuner
            }

        except Exception as e:
            print(f"Grid search failed: {e}")
            # Return default parameters
            return {
                'best_params': {
                    'hidden_dim': 128,
                    'num_layers': 2,
                    'dropout': 0.2,
                    'learning_rate': 0.001,
                    'batch_size': 32
                },
                'best_score': 0.5,
                'results': pd.DataFrame(),
                'tuner': None
            }

    def _keras_tuner_tuning(self, train_inputs, train_targets, val_inputs, val_targets,
                           max_trials, epochs):
        """
        Perform hyperparameter tuning using Keras Tuner
        """
        try:
            # Create tuner
            tuner = kt.RandomSearch(
                lambda hp: build_model_for_tuning(hp, self.num_skills, self.max_seq_len),
                objective='val_accuracy',
                max_trials=max_trials,
                directory='dkt_tuning',
                project_name='dkt_hyperparameter_tuning'
            )

            # Search for best hyperparameters
            tuner.search(
                train_inputs, train_targets,
                validation_data=(val_inputs, val_targets),
                epochs=epochs,
                callbacks=[keras.callbacks.EarlyStopping(patience=3)],
                verbose=1
            )

            # Get best hyperparameters
            best_hps = tuner.get_best_hyperparameters(num_trials=1)[0]
            best_params = {
                'hidden_dim': best_hps.get('hidden_dim'),
                'num_layers': best_hps.get('num_layers'),
                'dropout': best_hps.get('dropout'),
                'learning_rate': best_hps.get('learning_rate'),
                'embedding_dim': best_hps.get('embedding_dim')
            }

            # Get best model
            best_model = tuner.get_best_models(num_models=1)[0]

            # Evaluate best model
            val_loss, val_accuracy = best_model.evaluate(val_inputs, val_targets, verbose=0)

            return {
                'best_params': best_params,
                'best_score': val_accuracy,
                'best_model': best_model,
                'tuner': tuner
            }

        except ImportError:
            print("Keras Tuner not available. Please install with: pip install keras-tuner")
            print("Falling back to grid search...")
            return self._grid_search_tuning(
                train_inputs, train_targets, val_inputs, val_targets,
                max_trials, epochs
            )

    def fit_with_best_params(self, df, best_params, skill_col='skill_name',
                           user_col='user_id', problem_col='problem_id',
                           correct_col='correct', validation_split=0.2):
        """
        Train model with best hyperparameters found during tuning
        """
        print("Training model with best hyperparameters...")
        print(f"Best parameters: {best_params}")

        # Update model parameters
        self.hidden_dim = best_params.get('hidden_dim', self.hidden_dim)
        self.num_layers = best_params.get('num_layers', self.num_layers)
        self.dropout = best_params.get('dropout', self.dropout)
        self.learning_rate = best_params.get('learning_rate', self.learning_rate)
        self.batch_size = best_params.get('batch_size', self.batch_size)

        # Train model with best parameters
        return self.fit(df, skill_col, user_col, problem_col, correct_col, validation_split)

    def _prepare_targets_for_training(self, inputs, targets, lengths):
        """
        Prepare targets for TensorFlow training by creating skill-specific targets
        """
        batch_size, seq_len = inputs.shape
        processed_targets = np.zeros((batch_size, seq_len, self.num_skills))

        for i in range(batch_size):
            seq_length = lengths[i]
            for j in range(seq_length):
                target_interaction = targets[i, j]
                if target_interaction == 0:  # Padding
                    continue

                # Decode target: skill_id = (target - 1) % num_skills
                target_skill = (target_interaction - 1) % self.num_skills
                target_correct = (target_interaction - 1) // self.num_skills

                processed_targets[i, j, target_skill] = target_correct

        return processed_targets



    def predict_proba(self, user_history, skill, user_id=None):
        """
        Predict probability of correct response for next interaction

        Args:
            user_history: List of (problem_id, correct) tuples or list of correct values
            skill: Skill name to predict for
            user_id: User ID (optional, for compatibility)

        Returns:
            List of predicted probabilities
        """
        if self.model is None:
            raise ValueError("Model not trained. Call fit() first.")

        # Encode skill
        if skill not in self.skill_encoder.classes_:
            print(f"Warning: Skill '{skill}' not seen during training")
            return [0.5] * len(user_history)

        skill_id = self.skill_encoder.transform([skill])[0]

        # Process user history
        if len(user_history) == 0:
            return []

        # Convert history to sequence
        sequence = []
        for item in user_history:
            if isinstance(item, tuple):
                # (problem_id, correct) format
                correct = item[1]
            else:
                # Just correct value
                correct = item

            # Encode interaction
            encoded_interaction = skill_id + 1 + (correct * self.num_skills)
            sequence.append(encoded_interaction)

        # Prepare input for TensorFlow
        # Pad sequence to max_seq_len - 1
        padded_sequence = sequence[:]
        if len(padded_sequence) < self.max_seq_len - 1:
            padded_sequence.extend([0] * (self.max_seq_len - 1 - len(padded_sequence)))
        elif len(padded_sequence) > self.max_seq_len - 1:
            padded_sequence = padded_sequence[-(self.max_seq_len - 1):]

        input_seq = np.array([padded_sequence])  # Shape: (1, seq_len)

        # Make prediction
        outputs = self.model.predict(input_seq, verbose=0)  # Shape: (1, seq_len, num_skills)

        # Get predictions for the target skill
        predictions = outputs[0, :len(sequence), skill_id]

        return predictions.tolist()

    def get_model_statistics(self):
        """Get model statistics"""
        stats = {
            'model_type': 'Deep Knowledge Tracing (DKT)',
            'num_skills': self.num_skills,
            'hidden_dim': self.hidden_dim,
            'num_layers': self.num_layers,
            'dropout': self.dropout,
            'max_seq_len': self.max_seq_len,
            'gpu_available': self.gpu_available,
            'total_parameters': self.model.count_params() if self.model else 0,
            'trainable_parameters': self.model.count_params() if self.model else 0,
            'best_epoch': self.best_epoch + 1 if hasattr(self, 'best_epoch') else 0,
            'training_epochs': len(self.training_losses),
            'final_train_loss': self.training_losses[-1] if self.training_losses else 0,
            'final_val_loss': self.validation_losses[-1] if self.validation_losses else 0,
            'final_train_acc': self.training_accuracies[-1] if self.training_accuracies else 0,
            'final_val_acc': self.validation_accuracies[-1] if self.validation_accuracies else 0,
        }
        return stats

    def save(self, path):
        """Save the trained model"""
        if self.model is None:
            raise ValueError("No model to save. Train the model first.")

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # Save TensorFlow model
        model_dir = path.replace('.joblib', '_tf_model')
        self.model.save(model_dir)

        # Prepare metadata
        model_data = {
            # Model architecture
            'hidden_dim': self.hidden_dim,
            'num_layers': self.num_layers,
            'dropout': self.dropout,
            'num_skills': self.num_skills,
            'max_seq_len': self.max_seq_len,

            # Training parameters
            'learning_rate': self.learning_rate,
            'batch_size': self.batch_size,
            'max_epochs': self.max_epochs,
            'patience': self.patience,

            # Model components
            'skill_encoder': self.skill_encoder,
            'model_dir': model_dir,

            # Training history
            'training_losses': self.training_losses,
            'validation_losses': self.validation_losses,
            'training_accuracies': self.training_accuracies,
            'validation_accuracies': self.validation_accuracies,
            'best_epoch': self.best_epoch,
        }

        # Save metadata using joblib
        dump(model_data, path)
        print(f"DKT model saved to {path}")
        print(f"TensorFlow model saved to {model_dir}")

    @staticmethod
    def load(path):
        """Load a trained model"""
        model_data = load(path)

        # Create model instance
        model = DeepKnowledgeTracing(
            hidden_dim=model_data['hidden_dim'],
            num_layers=model_data['num_layers'],
            dropout=model_data['dropout'],
            learning_rate=model_data['learning_rate'],
            batch_size=model_data['batch_size'],
            max_epochs=model_data['max_epochs'],
            patience=model_data['patience'],
            max_seq_len=model_data['max_seq_len']
        )

        # Load model components
        model.num_skills = model_data['num_skills']
        model.skill_encoder = model_data['skill_encoder']

        # Load TensorFlow model
        model_dir = model_data['model_dir']
        model.model = keras.models.load_model(model_dir)

        # Load training history
        model.training_losses = model_data.get('training_losses', [])
        model.validation_losses = model_data.get('validation_losses', [])
        model.training_accuracies = model_data.get('training_accuracies', [])
        model.validation_accuracies = model_data.get('validation_accuracies', [])
        model.best_epoch = model_data.get('best_epoch', 0)

        print(f"DKT model loaded from {path}")
        print(f"TensorFlow model loaded from {model_dir}")
        return model


# Utility functions
def load_skill_builder_data(csv_path):
    """Load the 2009 skill builder dataset"""
    df = pd.read_csv(csv_path, encoding='latin1')
    return df


def train_and_save_dkt_model(data_path, output_path, **kwargs):
    """Train and save DKT model"""
    print("Loading dataset...")
    df = load_skill_builder_data(data_path)

    # Default configuration
    default_config = {
        'hidden_dim': 128,
        'num_layers': 2,
        'dropout': 0.2,
        'learning_rate': 0.001,
        'batch_size': 32,
        'max_epochs': 50,
        'patience': 10,
        'max_seq_len': 200
    }

    # Update with provided kwargs
    config = {**default_config, **kwargs}

    print("Initializing DKT model...")
    print(f"Configuration: {config}")
    dkt = DeepKnowledgeTracing(**config)

    print("Training DKT model...")
    dkt.fit(df, validation_split=0.2)

    print("Saving model...")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    dkt.save(output_path)
    print(f"DKT model saved to {output_path}")

    # Print model statistics
    stats = dkt.get_model_statistics()
    print("\n=== MODEL STATISTICS ===")
    for key, value in stats.items():
        print(f"{key}: {value}")

    return dkt


def tune_and_train_dkt_model(data_path, output_path, tuning_method='grid_search',
                            max_trials=10, **kwargs):
    """
    Tune hyperparameters and train DKT model with best parameters

    Args:
        data_path: Path to training data
        output_path: Path to save the trained model
        tuning_method: 'grid_search' or 'keras_tuner'
        max_trials: Maximum number of hyperparameter trials
        **kwargs: Additional configuration parameters

    Returns:
        Trained DKT model and tuning results
    """
    print("Loading dataset for hyperparameter tuning...")
    df = load_skill_builder_data(data_path)

    # Default configuration for tuning
    default_config = {
        'hidden_dim': 128,  # Will be overridden by tuning
        'num_layers': 2,    # Will be overridden by tuning
        'dropout': 0.2,     # Will be overridden by tuning
        'learning_rate': 0.001,  # Will be overridden by tuning
        'batch_size': 32,   # Will be overridden by tuning
        'max_epochs': 50,
        'patience': 10,
        'max_seq_len': 200
    }

    # Update with provided kwargs
    config = {**default_config, **kwargs}

    print("Initializing DKT model for hyperparameter tuning...")
    dkt = DeepKnowledgeTracing(**config)

    print(f"Starting hyperparameter tuning with {tuning_method}...")
    print(f"Maximum trials: {max_trials}")

    # Perform hyperparameter tuning
    tuning_results = dkt.tune_hyperparameters(
        df,
        method=tuning_method,
        max_trials=max_trials,
        tuning_epochs=15  # Fewer epochs for faster tuning
    )

    best_params = tuning_results['best_params']
    best_score = tuning_results['best_score']

    print(f"\nHyperparameter tuning completed!")
    print(f"Best validation accuracy: {best_score:.4f}")
    print(f"Best parameters: {best_params}")

    # Train final model with best parameters
    print("\nTraining final model with best hyperparameters...")
    final_dkt = DeepKnowledgeTracing(**config)
    final_dkt.fit_with_best_params(df, best_params, validation_split=0.2)

    # Save the final model
    print("Saving tuned model...")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    final_dkt.save(output_path)
    print(f"Tuned DKT model saved to {output_path}")

    # Save tuning results
    tuning_results_path = output_path.replace('.joblib', '_tuning_results.csv')
    if 'results' in tuning_results and not tuning_results['results'].empty:
        tuning_results['results'].to_csv(tuning_results_path, index=False)
        print(f"Tuning results saved to {tuning_results_path}")

    # Print final model statistics
    stats = final_dkt.get_model_statistics()
    print("\n=== FINAL MODEL STATISTICS ===")
    for key, value in stats.items():
        print(f"{key}: {value}")

    return final_dkt, tuning_results


def compare_hyperparameter_results(tuning_results, top_n=5):
    """
    Compare and visualize hyperparameter tuning results

    Args:
        tuning_results: Results from hyperparameter tuning
        top_n: Number of top results to display

    Returns:
        DataFrame with top results
    """
    if 'results' not in tuning_results or tuning_results['results'].empty:
        print("No tuning results available for comparison")
        return pd.DataFrame()

    results_df = tuning_results['results']

    # Sort by validation accuracy
    top_results = results_df.nlargest(top_n, 'val_accuracy')

    print(f"\n=== TOP {top_n} HYPERPARAMETER COMBINATIONS ===")
    print(top_results.to_string(index=False))

    # Print parameter importance analysis
    print(f"\n=== PARAMETER ANALYSIS ===")

    # Analyze correlation between parameters and performance
    numeric_cols = ['hidden_dim', 'num_layers', 'dropout', 'learning_rate', 'batch_size', 'val_accuracy']
    numeric_cols = [col for col in numeric_cols if col in results_df.columns]

    if len(numeric_cols) > 1:
        correlations = results_df[numeric_cols].corr()['val_accuracy'].sort_values(ascending=False)
        print("Parameter correlations with validation accuracy:")
        for param, corr in correlations.items():
            if param != 'val_accuracy':
                print(f"  {param}: {corr:.3f}")

    return top_results


if __name__ == "__main__":
    # Example usage
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    OUTPUT_PATH = "output/dkt_model.joblib"
    TUNED_OUTPUT_PATH = "output/dkt_model_tuned.joblib"

    print("Choose training mode:")
    print("1. Standard training")
    print("2. Hyperparameter tuning + training")

    mode = input("Enter choice (1 or 2): ").strip()

    if mode == "2":
        print("\nChoose tuning method:")
        print("1. Grid search (faster, limited combinations)")
        print("2. Keras Tuner (more thorough, requires keras-tuner)")

        tuning_method_choice = input("Enter choice (1 or 2): ").strip()
        tuning_method = 'grid_search' if tuning_method_choice == "1" else 'keras_tuner'

        # Hyperparameter tuning and training
        print(f"\nStarting hyperparameter tuning with {tuning_method}...")
        dkt_model, tuning_results = tune_and_train_dkt_model(
            DATA_PATH,
            TUNED_OUTPUT_PATH,
            tuning_method=tuning_method,
            max_trials=12  # Reasonable number for demonstration
        )

        # Display tuning results
        print("\n=== HYPERPARAMETER TUNING RESULTS ===")
        top_results = compare_hyperparameter_results(tuning_results, top_n=5)

    else:
        # Standard training
        print("\nStarting standard training...")
        dkt_model = train_and_save_dkt_model(DATA_PATH, OUTPUT_PATH)

    # Example prediction
    print("\n=== EXAMPLE PREDICTION ===")
    example_history = [(12345, 1), (12346, 0), (12347, 1)]  # (problem_id, correct)
    example_skill = 'Addition and Subtraction Integers'

    try:
        example_predictions = dkt_model.predict_proba(example_history, example_skill)
        print(f"User history: {example_history}")
        print(f"Skill: {example_skill}")
        print(f"Predicted probabilities: {example_predictions}")
    except Exception as e:
        print(f"Prediction error: {e}")

    print("\n=== TRAINING COMPLETED ===")
    if mode == "2":
        print(f"✅ Hyperparameter tuning completed with best validation accuracy: {tuning_results['best_score']:.4f}")
        print(f"✅ Final model saved to: {TUNED_OUTPUT_PATH}")
    else:
        print(f"✅ Standard model saved to: {OUTPUT_PATH}")
    print("✅ Model ready for use!")
