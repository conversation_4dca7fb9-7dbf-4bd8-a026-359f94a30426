import os
import pandas as pd
import numpy as np
from sklearn.base import BaseEstimator
from joblib import dump, load
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

# Define an improved BKT model class with PFA-inspired training approach
class Bay<PERSON>an<PERSON><PERSON>wled<PERSON><PERSON>race<PERSON>(BaseEstimator):
    """
    Improved Bayesian Knowledge Tracing model with PFA-inspired training approach.

    Key improvements:
    1. Temporal ordering of data (like PFA)
    2. Iterative parameter optimization using gradient descent
    3. Skill-specific parameter estimation with better convergence
    4. Student-specific initial knowledge states
    5. Problem-specific difficulty adjustments
    """

    def __init__(self, p_init=0.1, p_learn=0.1, p_guess=0.2, p_slip=0.1,
                 learning_rate=0.01, max_iterations=100, convergence_threshold=1e-6):
        # Default BKT parameters (used as initialization)
        self.p_init = p_init
        self.p_learn = p_learn
        self.p_guess = p_guess
        self.p_slip = p_slip

        # Training parameters (inspired by PFA)
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.convergence_threshold = convergence_threshold

        # Model parameters
        self.skill_params = {}
        self.student_initial_knowledge = {}  # Student-specific initial knowledge
        self.problem_difficulties = {}       # Problem-specific difficulty adjustments
        self.training_history = {}           # Track training progress

    def fit(self, df, skill_col='skill_name', user_col='user_id', correct_col='correct'):
        skills = df[skill_col].unique()
        for skill in skills:
            skill_df = df[df[skill_col] == skill]
            # Estimate skill-specific parameters from data
            estimated_params = self._estimate_skill_parameters(skill_df, user_col, correct_col)
            self.skill_params[skill] = estimated_params
        return self

    def _estimate_skill_parameters(self, skill_df, user_col, correct_col):
        """
        Estimate BKT parameters for a specific skill using simple heuristics
        """
        # Calculate basic statistics
        overall_accuracy = skill_df[correct_col].mean()

        # Group by user to analyze learning patterns
        user_sequences = []
        for user_id, user_data in skill_df.groupby(user_col):
            sequence = user_data[correct_col].tolist()
            if len(sequence) >= 2:  # Need at least 2 attempts to see learning
                user_sequences.append(sequence)

        if not user_sequences:
            # Fallback to default parameters if no valid sequences
            return {
                'p_init': self.p_init,
                'p_learn': self.p_learn,
                'p_guess': self.p_guess,
                'p_slip': self.p_slip
            }

        # Estimate P(Init) - probability of knowing skill initially
        # Based on first attempt accuracy
        first_attempts = [seq[0] for seq in user_sequences]
        p_init_est = np.mean(first_attempts)

        # Estimate P(Learn) - learning rate
        # Based on improvement from first to last attempt
        improvements = []
        for seq in user_sequences:
            if len(seq) >= 3:
                first_half = np.mean(seq[:len(seq)//2])
                second_half = np.mean(seq[len(seq)//2:])
                improvement = max(0, second_half - first_half)
                improvements.append(improvement)

        p_learn_est = np.mean(improvements) if improvements else self.p_learn
        p_learn_est = max(0.05, min(0.5, p_learn_est))  # Bound between 0.05 and 0.5

        # Estimate P(Guess) - guessing probability
        # Based on performance of users who seem to not know the skill
        low_performers = [seq for seq in user_sequences if np.mean(seq) < 0.3]
        if low_performers:
            p_guess_est = np.mean([np.mean(seq) for seq in low_performers])
            p_guess_est = max(0.1, min(0.4, p_guess_est))
        else:
            p_guess_est = self.p_guess

        # Estimate P(Slip) - slip probability
        # Based on performance of users who seem to know the skill well
        high_performers = [seq for seq in user_sequences if np.mean(seq) > 0.8]
        if high_performers:
            # Slip rate is 1 - accuracy for high performers
            p_slip_est = 1 - np.mean([np.mean(seq) for seq in high_performers])
            p_slip_est = max(0.05, min(0.3, p_slip_est))
        else:
            p_slip_est = self.p_slip

        # Ensure p_init is reasonable given overall performance
        p_init_est = max(0.01, min(0.8, p_init_est))

        return {
            'p_init': round(p_init_est, 3),
            'p_learn': round(p_learn_est, 3),
            'p_guess': round(p_guess_est, 3),
            'p_slip': round(p_slip_est, 3)
        }

    def predict_proba(self, user_history, skill, n_steps=None):
        params = self.skill_params.get(skill, {
            'p_init': self.p_init,
            'p_learn': self.p_learn,
            'p_guess': self.p_guess,
            'p_slip': self.p_slip
        })
        p_know = params['p_init']
        p_learn = params['p_learn']
        p_guess = params['p_guess']
        p_slip = params['p_slip']
        probas = []
        for obs in user_history:
            # obs: 1 if correct, 0 if incorrect
            p_correct = p_know * (1 - p_slip) + (1 - p_know) * p_guess
            probas.append(p_correct)
            if obs == 1:
                num = p_know * (1 - p_slip)
                denom = p_know * (1 - p_slip) + (1 - p_know) * p_guess
            else:
                num = p_know * p_slip
                denom = p_know * p_slip + (1 - p_know) * (1 - p_guess)
            p_know = num / denom if denom > 0 else p_know
            p_know = p_know + (1 - p_know) * p_learn
        return probas

    def save(self, path):
        dump(self, path)

    @staticmethod
    def load(path):
        return load(path)


def load_skill_builder_data(csv_path):
    # The 2009 skill builder data has columns: user_id, problem_id, skill_name, correct, etc.
    df = pd.read_csv(csv_path, encoding='latin1')
    return df


def train_and_save_bkt_model(data_path, output_path):
    df = load_skill_builder_data(data_path)
    bkt = BayesianKnowledgeTracer()
    bkt.fit(df)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    bkt.save(output_path)
    print(f"Model saved to {output_path}")


def example_predict(model_path, user_history, skill):
    bkt = BayesianKnowledgeTracer.load(model_path)
    probas = bkt.predict_proba(user_history, skill)
    print(f"Predicted probabilities: {probas}")
    return probas

if __name__ == "__main__":
    # Example usage
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    OUTPUT_PATH = "models/output/bkt_model.joblib"
    train_and_save_bkt_model(DATA_PATH, OUTPUT_PATH)
    # Example prediction
    # user_history = [1, 0, 1, 1]  # 1=correct, 0=incorrect
    # skill = 'Addition'
    # example_predict(OUTPUT_PATH, user_history, skill) 