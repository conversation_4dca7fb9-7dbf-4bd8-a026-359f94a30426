import os
import pandas as pd
import numpy as np
from sklearn.base import BaseEstimator
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import log_loss, accuracy_score, roc_auc_score
from joblib import dump, load
import warnings
warnings.filterwarnings('ignore')

class PerformanceFactorsAnalysis(BaseEstimator):
    """
    Enhanced Performance Factors Analysis (PFA) model for knowledge tracing.

    Improvements over basic PFA:
    1. Adaptive learning rates per skill
    2. Regularization to prevent overfitting
    3. Multiple training epochs with convergence checking
    4. Skill-specific success and failure learning rates
    5. Time decay factors for forgetting
    6. Cross-validation and early stopping
    7. Better parameter initialization
    """

    def __init__(self, learning_rate=0.01, regularization=0.001, max_epochs=10,
                 convergence_threshold=1e-6, use_adaptive_lr=True, use_time_decay=True,
                 use_separate_sf_rates=True, early_stopping_patience=3):
        # Basic parameters
        self.learning_rate = learning_rate
        self.regularization = regularization
        self.max_epochs = max_epochs
        self.convergence_threshold = convergence_threshold

        # Advanced features
        self.use_adaptive_lr = use_adaptive_lr
        self.use_time_decay = use_time_decay
        self.use_separate_sf_rates = use_separate_sf_rates
        self.early_stopping_patience = early_stopping_patience

        # Model parameters
        self.student_abilities = {}
        self.problem_difficulties = {}
        self.skill_learning_rates = {}
        self.skill_opportunities = {}

        # Enhanced parameters
        self.skill_success_rates = {}    # Learning from successes
        self.skill_failure_rates = {}    # Learning from failures
        self.skill_adaptive_lr = {}      # Adaptive learning rates per skill
        self.time_decay_factors = {}     # Time-based forgetting

        # Training tracking
        self.training_losses = []
        self.validation_losses = []
        self.best_epoch = 0
    def fit(self, df, skill_col='skill_name', user_col='user_id',
            problem_col='problem_id', correct_col='correct', validation_split=0.2):
        """
        Enhanced fit method with multiple epochs, validation, and early stopping
        """
        print("Training Enhanced PFA model...")
        print(f"Dataset size: {len(df):,} interactions")
        print(f"Unique students: {df[user_col].nunique():,}")
        print(f"Unique problems: {df[problem_col].nunique():,}")
        print(f"Unique skills: {df[skill_col].nunique():,}")

        # Clean and prepare data
        df_clean = self._prepare_data(df, skill_col, user_col, problem_col, correct_col)

        # Split into train/validation
        train_df, val_df = self._train_val_split(df_clean, user_col, validation_split)
        print(f"Training set: {len(train_df):,} interactions")
        print(f"Validation set: {len(val_df):,} interactions")

        # Initialize parameters with better strategy
        self._initialize_parameters_enhanced(train_df, skill_col, user_col, problem_col, correct_col)

        # Train with multiple epochs and early stopping
        self._train_with_early_stopping(train_df, val_df, skill_col, user_col, problem_col, correct_col)

        print("Enhanced PFA model training completed!")
        print(f"Best epoch: {self.best_epoch}")
        print(f"Final training loss: {self.training_losses[-1]:.6f}")
        print(f"Final validation loss: {self.validation_losses[-1]:.6f}")
        return self
    
    def _prepare_data(self, df, skill_col, user_col, problem_col, correct_col):
        """Clean and prepare data for training"""
        # Remove rows with missing values in essential columns
        essential_cols = [skill_col, user_col, problem_col, correct_col]
        df_clean = df.dropna(subset=essential_cols).copy()

        # Ensure correct data types
        df_clean[correct_col] = df_clean[correct_col].astype(int)
        df_clean[user_col] = df_clean[user_col].astype(str)
        df_clean[problem_col] = df_clean[problem_col].astype(str)

        # Sort by user and order to maintain temporal sequence
        if 'order_id' in df_clean.columns:
            df_clean = df_clean.sort_values([user_col, 'order_id']).reset_index(drop=True)
        else:
            df_clean = df_clean.sort_values([user_col]).reset_index(drop=True)

        return df_clean

    def _train_val_split(self, df, user_col, validation_split):
        """Split data into training and validation sets by users"""
        unique_users = df[user_col].unique()
        np.random.seed(42)  # For reproducibility
        val_users = np.random.choice(unique_users,
                                   size=int(len(unique_users) * validation_split),
                                   replace=False)

        train_df = df[~df[user_col].isin(val_users)].copy()
        val_df = df[df[user_col].isin(val_users)].copy()

        return train_df, val_df

    def _initialize_parameters_enhanced(self, df, skill_col, user_col, problem_col, correct_col):
        """Enhanced parameter initialization with data-driven approach"""
        print("Initializing parameters with data-driven approach...")

        # Initialize student abilities based on overall performance
        user_stats = df.groupby(user_col)[correct_col].agg(['mean', 'count'])
        for user in user_stats.index:
            # Initialize based on user's overall accuracy, centered around 0
            accuracy = user_stats.loc[user, 'mean']
            self.student_abilities[user] = np.log(accuracy / (1 - accuracy + 1e-8))

        # Initialize problem difficulties based on problem accuracy
        problem_stats = df.groupby(problem_col)[correct_col].agg(['mean', 'count'])
        for problem in problem_stats.index:
            # Higher difficulty for problems with lower accuracy
            accuracy = problem_stats.loc[problem, 'mean']
            self.problem_difficulties[problem] = -np.log(accuracy / (1 - accuracy + 1e-8))

        # Initialize skill-specific parameters
        skill_stats = df.groupby(skill_col)[correct_col].agg(['mean', 'count', 'std'])
        for skill in skill_stats.index:
            accuracy = skill_stats.loc[skill, 'mean']
            count = skill_stats.loc[skill, 'count']
            std = skill_stats.loc[skill, 'std']

            # Base learning rate adjusted by skill difficulty and variance
            base_lr = self.learning_rate * (1 + std) * np.sqrt(count / 1000)
            self.skill_learning_rates[skill] = min(base_lr, 0.5)  # Cap at 0.5

            if self.use_separate_sf_rates:
                # Different rates for success and failure
                self.skill_success_rates[skill] = base_lr * 0.8
                self.skill_failure_rates[skill] = base_lr * 1.2

            if self.use_adaptive_lr:
                self.skill_adaptive_lr[skill] = base_lr

            if self.use_time_decay:
                # Time decay factor based on skill complexity
                self.time_decay_factors[skill] = 0.95 + 0.04 * accuracy  # 0.95-0.99

            self.skill_opportunities[skill] = {}

        print(f"Initialized parameters for {len(self.student_abilities)} students, "
              f"{len(self.problem_difficulties)} problems, {len(self.skill_learning_rates)} skills")
    
    def _train_with_early_stopping(self, train_df, val_df, skill_col, user_col, problem_col, correct_col):
        """Enhanced training with multiple epochs and early stopping"""
        best_val_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.max_epochs):
            print(f"Epoch {epoch + 1}/{self.max_epochs}")

            # Training phase
            train_loss = self._train_epoch(train_df, skill_col, user_col, problem_col, correct_col)

            # Validation phase
            val_loss = self._validate_epoch(val_df, skill_col, user_col, problem_col, correct_col)

            # Store losses
            self.training_losses.append(train_loss)
            self.validation_losses.append(val_loss)

            print(f"  Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

            # Early stopping check
            if val_loss < best_val_loss - self.convergence_threshold:
                best_val_loss = val_loss
                self.best_epoch = epoch
                patience_counter = 0
                # Save best parameters (could implement parameter saving here)
            else:
                patience_counter += 1

            if patience_counter >= self.early_stopping_patience:
                print(f"Early stopping at epoch {epoch + 1}")
                break

            # Adaptive learning rate decay
            if self.use_adaptive_lr and epoch > 0:
                if val_loss > self.validation_losses[-2]:
                    self._decay_learning_rates()

    def _train_epoch(self, df, skill_col, user_col, problem_col, correct_col):
        """Train for one epoch"""
        total_loss = 0.0
        num_samples = 0

        # Reset opportunity counters for this epoch
        epoch_opportunities = {}

        for idx, row in df.iterrows():
            user = row[user_col]
            problem = row[problem_col]
            skill = row[skill_col]
            correct = row[correct_col]

            # Get current opportunity count for this skill
            if skill not in epoch_opportunities:
                epoch_opportunities[skill] = {}
            if user not in epoch_opportunities[skill]:
                epoch_opportunities[skill][user] = self.skill_opportunities.get(skill, {}).get(user, 0)

            opportunity = epoch_opportunities[skill][user]

            # Calculate prediction
            prediction = self._predict_single_enhanced(user, problem, skill, opportunity)

            # Calculate loss (log loss)
            loss = -correct * np.log(prediction + 1e-8) - (1 - correct) * np.log(1 - prediction + 1e-8)
            total_loss += loss
            num_samples += 1

            # Calculate error
            error = correct - prediction

            # Update parameters with enhanced method
            self._update_parameters_enhanced(user, problem, skill, opportunity, error, correct)

            # Increment opportunity counter
            epoch_opportunities[skill][user] += 1

        # Update global opportunity counters
        self.skill_opportunities = epoch_opportunities

        return total_loss / num_samples if num_samples > 0 else 0.0
    
    def _predict_single_enhanced(self, user, problem, skill, opportunity):
        """Enhanced prediction with additional factors"""
        # Base prediction: student ability - problem difficulty
        base_pred = (self.student_abilities.get(user, 0.0) -
                    self.problem_difficulties.get(problem, 0.0))

        # Enhanced learning effect
        if self.use_separate_sf_rates:
            # Use average of success and failure rates for prediction
            success_rate = self.skill_success_rates.get(skill, self.learning_rate)
            failure_rate = self.skill_failure_rates.get(skill, self.learning_rate)
            avg_learning_rate = (success_rate + failure_rate) / 2
        else:
            avg_learning_rate = self.skill_learning_rates.get(skill, self.learning_rate)

        learning_effect = opportunity * avg_learning_rate

        # Time decay effect (forgetting)
        if self.use_time_decay and opportunity > 0:
            decay_factor = self.time_decay_factors.get(skill, 0.95)
            # Apply exponential decay based on opportunity gaps
            learning_effect *= (decay_factor ** (opportunity * 0.1))

        # Combine and apply sigmoid with regularization
        logit = base_pred + learning_effect

        # Apply L2 regularization to prevent extreme predictions
        if abs(logit) > 5:  # Clip extreme values
            logit = np.sign(logit) * 5

        prediction = 1 / (1 + np.exp(-logit))

        # Ensure prediction is within reasonable bounds
        prediction = np.clip(prediction, 0.01, 0.99)

        return prediction

    def _predict_single(self, user, problem, skill, opportunity):
        """Original prediction method for backward compatibility"""
        return self._predict_single_enhanced(user, problem, skill, opportunity)
    
    def _update_parameters_enhanced(self, user, problem, skill, opportunity, error, correct):
        """Enhanced parameter update with regularization and adaptive rates"""
        # Get adaptive learning rate for this skill
        if self.use_adaptive_lr:
            skill_lr = self.skill_adaptive_lr.get(skill, self.learning_rate)
        else:
            skill_lr = self.learning_rate

        # Update student ability with L2 regularization
        if user in self.student_abilities:
            regularization_term = self.regularization * self.student_abilities[user]
            self.student_abilities[user] += skill_lr * error - regularization_term

        # Update problem difficulty with L2 regularization
        if problem in self.problem_difficulties:
            regularization_term = self.regularization * self.problem_difficulties[problem]
            self.problem_difficulties[problem] -= skill_lr * error - regularization_term

        # Update skill learning rates with separate success/failure rates
        if self.use_separate_sf_rates:
            if correct == 1:  # Success
                if skill in self.skill_success_rates:
                    self.skill_success_rates[skill] += skill_lr * error * opportunity * 0.5
                    self.skill_success_rates[skill] = max(0.001, min(0.5, self.skill_success_rates[skill]))
            else:  # Failure
                if skill in self.skill_failure_rates:
                    self.skill_failure_rates[skill] += skill_lr * error * opportunity * 0.5
                    self.skill_failure_rates[skill] = max(0.001, min(0.5, self.skill_failure_rates[skill]))
        else:
            # Standard update
            if skill in self.skill_learning_rates:
                self.skill_learning_rates[skill] += skill_lr * error * opportunity * 0.5
                self.skill_learning_rates[skill] = max(0.001, min(0.5, self.skill_learning_rates[skill]))

    def _update_parameters(self, user, problem, skill, opportunity, error):
        """Original parameter update method for backward compatibility"""
        self._update_parameters_enhanced(user, problem, skill, opportunity, error, 1)

    def _validate_epoch(self, val_df, skill_col, user_col, problem_col, correct_col):
        """Validate model on validation set"""
        total_loss = 0.0
        num_samples = 0

        # Use current opportunity counts for validation
        val_opportunities = {}

        for idx, row in val_df.iterrows():
            user = row[user_col]
            problem = row[problem_col]
            skill = row[skill_col]
            correct = row[correct_col]

            # Get opportunity count (use existing or start from 0)
            if skill not in val_opportunities:
                val_opportunities[skill] = {}
            if user not in val_opportunities[skill]:
                val_opportunities[skill][user] = self.skill_opportunities.get(skill, {}).get(user, 0)

            opportunity = val_opportunities[skill][user]

            # Calculate prediction (no parameter updates)
            prediction = self._predict_single_enhanced(user, problem, skill, opportunity)

            # Calculate loss
            loss = -correct * np.log(prediction + 1e-8) - (1 - correct) * np.log(1 - prediction + 1e-8)
            total_loss += loss
            num_samples += 1

            # Increment opportunity counter for next prediction
            val_opportunities[skill][user] += 1

        return total_loss / num_samples if num_samples > 0 else 0.0

    def _decay_learning_rates(self):
        """Decay learning rates when validation loss increases"""
        decay_factor = 0.9

        if self.use_adaptive_lr:
            for skill in self.skill_adaptive_lr:
                self.skill_adaptive_lr[skill] *= decay_factor

        if self.use_separate_sf_rates:
            for skill in self.skill_success_rates:
                self.skill_success_rates[skill] *= decay_factor
            for skill in self.skill_failure_rates:
                self.skill_failure_rates[skill] *= decay_factor
        else:
            for skill in self.skill_learning_rates:
                self.skill_learning_rates[skill] *= decay_factor
    
    def predict_proba(self, user_history, skill, user_id=None, problem_id=None):
        """
        Enhanced prediction with better handling of user history

        Args:
            user_history: List of (problem_id, correct) tuples or just correct values
            skill: Skill name
            user_id: User identifier (optional)
            problem_id: Problem identifier for next prediction (optional)
        """
        if not user_history:
            return []

        # Handle different input formats
        if isinstance(user_history[0], (list, tuple)):
            # Format: [(problem_id, correct), ...]
            interactions = user_history
        else:
            # Format: [correct, correct, ...] - need problem_id for next prediction
            if problem_id is None:
                # Use a default problem_id if not provided
                problem_id = "default_problem"
            interactions = [(problem_id, correct) for correct in user_history]

        probas = []

        # Get initial opportunity count
        if skill in self.skill_opportunities and user_id in self.skill_opportunities[skill]:
            opportunity = self.skill_opportunities[skill][user_id]
        else:
            opportunity = 0

        for prob_id, correct in interactions:
            # Predict probability using enhanced method
            prob = self._predict_single_enhanced(user_id, prob_id, skill, opportunity)
            probas.append(prob)

            # Update opportunity counter
            opportunity += 1

        return probas

    def predict_next(self, user_history, skill, user_id=None, problem_id=None):
        """
        Predict probability for the next interaction
        """
        # Get current opportunity count
        if skill in self.skill_opportunities and user_id in self.skill_opportunities[skill]:
            opportunity = self.skill_opportunities[skill][user_id]
        else:
            opportunity = len(user_history) if user_history else 0

        # Use default problem_id if not provided
        if problem_id is None:
            problem_id = "next_problem"

        return self._predict_single_enhanced(user_id, problem_id, skill, opportunity)

    def get_model_statistics(self):
        """Get comprehensive model statistics"""
        stats = {
            'num_students': len(self.student_abilities),
            'num_problems': len(self.problem_difficulties),
            'num_skills': len(self.skill_learning_rates),
            'training_epochs': len(self.training_losses),
            'best_epoch': self.best_epoch,
            'final_train_loss': self.training_losses[-1] if self.training_losses else None,
            'final_val_loss': self.validation_losses[-1] if self.validation_losses else None,
        }

        if self.use_separate_sf_rates:
            stats['num_success_rates'] = len(self.skill_success_rates)
            stats['num_failure_rates'] = len(self.skill_failure_rates)

        if self.use_adaptive_lr:
            stats['adaptive_learning_rates'] = True

        if self.use_time_decay:
            stats['time_decay_enabled'] = True

        return stats
    
    def get_skill_parameters(self, skill):
        """Get learning parameters for a specific skill"""
        return {
            'learning_rate': self.skill_learning_rates.get(skill, self.learning_rate),
            'total_opportunities': sum(self.skill_opportunities.get(skill, {}).values())
        }
    
    def get_student_ability(self, user_id):
        """Get ability parameter for a specific student"""
        return self.student_abilities.get(user_id, 0.0)
    
    def get_problem_difficulty(self, problem_id):
        """Get difficulty parameter for a specific problem"""
        return self.problem_difficulties.get(problem_id, 0.0)
    
    def save(self, path):
        """Save the enhanced trained model"""
        model_data = {
            # Basic parameters
            'student_abilities': self.student_abilities,
            'problem_difficulties': self.problem_difficulties,
            'skill_learning_rates': self.skill_learning_rates,
            'skill_opportunities': self.skill_opportunities,
            'learning_rate': self.learning_rate,
            'regularization': self.regularization,

            # Enhanced parameters
            'skill_success_rates': getattr(self, 'skill_success_rates', {}),
            'skill_failure_rates': getattr(self, 'skill_failure_rates', {}),
            'skill_adaptive_lr': getattr(self, 'skill_adaptive_lr', {}),
            'time_decay_factors': getattr(self, 'time_decay_factors', {}),

            # Training configuration
            'max_epochs': self.max_epochs,
            'convergence_threshold': self.convergence_threshold,
            'use_adaptive_lr': self.use_adaptive_lr,
            'use_time_decay': self.use_time_decay,
            'use_separate_sf_rates': self.use_separate_sf_rates,
            'early_stopping_patience': self.early_stopping_patience,

            # Training history
            'training_losses': getattr(self, 'training_losses', []),
            'validation_losses': getattr(self, 'validation_losses', []),
            'best_epoch': getattr(self, 'best_epoch', 0)
        }

        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)
        dump(model_data, path)

    @staticmethod
    def load(path):
        """Load an enhanced trained model"""
        model_data = load(path)

        # Create model with saved configuration
        model = PerformanceFactorsAnalysis(
            learning_rate=model_data.get('learning_rate', 0.01),
            regularization=model_data.get('regularization', 0.001),
            max_epochs=model_data.get('max_epochs', 10),
            convergence_threshold=model_data.get('convergence_threshold', 1e-6),
            use_adaptive_lr=model_data.get('use_adaptive_lr', True),
            use_time_decay=model_data.get('use_time_decay', True),
            use_separate_sf_rates=model_data.get('use_separate_sf_rates', True),
            early_stopping_patience=model_data.get('early_stopping_patience', 3)
        )

        # Load basic parameters
        model.student_abilities = model_data['student_abilities']
        model.problem_difficulties = model_data['problem_difficulties']
        model.skill_learning_rates = model_data['skill_learning_rates']
        model.skill_opportunities = model_data['skill_opportunities']

        # Load enhanced parameters
        model.skill_success_rates = model_data.get('skill_success_rates', {})
        model.skill_failure_rates = model_data.get('skill_failure_rates', {})
        model.skill_adaptive_lr = model_data.get('skill_adaptive_lr', {})
        model.time_decay_factors = model_data.get('time_decay_factors', {})

        # Load training history
        model.training_losses = model_data.get('training_losses', [])
        model.validation_losses = model_data.get('validation_losses', [])
        model.best_epoch = model_data.get('best_epoch', 0)

        return model


def load_skill_builder_data(csv_path):
    """Load the 2009 skill builder dataset"""
    df = pd.read_csv(csv_path, encoding='latin1')
    return df


def train_and_save_enhanced_pfa_model(data_path, output_path, **kwargs):
    """Train enhanced PFA model with better configuration"""
    print("Loading dataset...")
    df = load_skill_builder_data(data_path)

    # Enhanced model configuration
    default_config = {
        'learning_rate': 0.01,
        'regularization': 0.001,
        'max_epochs': 15,
        'convergence_threshold': 1e-6,
        'use_adaptive_lr': True,
        'use_time_decay': True,
        'use_separate_sf_rates': True,
        'early_stopping_patience': 5
    }

    # Update with any provided kwargs
    config = {**default_config, **kwargs}

    print("Initializing Enhanced PFA model...")
    print(f"Configuration: {config}")
    pfa = PerformanceFactorsAnalysis(**config)

    print("Training Enhanced PFA model...")
    pfa.fit(df, validation_split=0.2)

    print("Saving enhanced model...")
    pfa.save(output_path)
    print(f"Enhanced PFA model saved to {output_path}")

    # Print model statistics
    stats = pfa.get_model_statistics()
    print("\n=== MODEL STATISTICS ===")
    for key, value in stats.items():
        print(f"{key}: {value}")

    return pfa

def train_and_save_pfa_model(data_path, output_path):
    """Backward compatibility function"""
    return train_and_save_enhanced_pfa_model(data_path, output_path)

def example_predict(model_path, user_history, skill, user_id=None, problem_id=None):
    """Example function to make predictions with trained PFA model"""
    pfa = PerformanceFactorsAnalysis.load(model_path)
    probas = pfa.predict_proba(user_history, skill, user_id, problem_id)
    print(f"Predicted probabilities: {probas}")

    # Also show next prediction
    next_prob = pfa.predict_next(user_history, skill, user_id, problem_id)
    print(f"Next interaction probability: {next_prob:.4f}")

    return probas

def evaluate_model_performance(model_path, test_data_path):
    """Evaluate model performance on test data"""
    print("Loading model and test data...")
    pfa = PerformanceFactorsAnalysis.load(model_path)
    test_df = load_skill_builder_data(test_data_path)

    # Clean test data
    test_df_clean = pfa._prepare_data(test_df, 'skill_name', 'user_id', 'problem_id', 'correct')

    print("Evaluating model performance...")
    predictions = []
    actuals = []

    # Reset opportunities for fair evaluation
    eval_opportunities = {}

    for idx, row in test_df_clean.iterrows():
        user = row['user_id']
        problem = row['problem_id']
        skill = row['skill_name']
        correct = row['correct']

        # Get opportunity count
        if skill not in eval_opportunities:
            eval_opportunities[skill] = {}
        if user not in eval_opportunities[skill]:
            eval_opportunities[skill][user] = 0

        opportunity = eval_opportunities[skill][user]

        # Make prediction
        pred = pfa._predict_single_enhanced(user, problem, skill, opportunity)
        predictions.append(pred)
        actuals.append(correct)

        # Update opportunity
        eval_opportunities[skill][user] += 1

    # Calculate metrics
    predictions = np.array(predictions)
    actuals = np.array(actuals)

    accuracy = accuracy_score(actuals, (predictions > 0.5).astype(int))
    try:
        auc = roc_auc_score(actuals, predictions)
    except:
        auc = None

    logloss = log_loss(actuals, predictions)

    print(f"\n=== EVALUATION RESULTS ===")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"AUC: {auc:.4f}" if auc else "AUC: Could not calculate")
    print(f"Log Loss: {logloss:.4f}")

    return {'accuracy': accuracy, 'auc': auc, 'log_loss': logloss}

if __name__ == "__main__":
    # Example usage with enhanced model
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    OUTPUT_PATH = "models/output/enhanced_pfa_model.joblib"

    # Train and save the enhanced model
    pfa_model = train_and_save_enhanced_pfa_model(DATA_PATH, OUTPUT_PATH)

    # Example prediction
    print("\n=== EXAMPLE PREDICTIONS ===")
    user_history = [(12345, 1), (12346, 0), (12347, 1)]  # (problem_id, correct)
    skill = 'Addition and Subtraction Integers'
    user_id = 'user_123'
    example_predict(OUTPUT_PATH, user_history, skill, user_id)