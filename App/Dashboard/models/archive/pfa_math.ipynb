{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Performance Factors Analysis (PFA) Model Training\n", "\n", "## ⚠️ **IMPORTANT: To see skill-specific parameters, please:**\n", "1. **<PERSON><PERSON>** (Kernel → Restart)\n", "2. **Run All Cells** (Cell → Run All)\n", "\n", "This ensures you get the latest PFA model with unique parameters for each skill!\n", "\n", "---\n", "\n", "This notebook demonstrates the complete workflow for training a PFA model with **skill-specific parameter estimation**:\n", "1. Load and explore the dataset\n", "2. Clean and preprocess the data\n", "3. Explore data statistics and skills\n", "4. Split data into train/test sets\n", "5. **Train the PFA model with skill-specific parameters**\n", "6. **Analyze parameter distributions and skill characteristics**\n", "7. Save the trained model\n", "8. Show training statistics\n", "9. Run prediction examples"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, roc_auc_score, classification_report\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PFA model imported successfully!\n", "✅ Using updated PFA model with skill-specific parameter estimation\n"]}], "source": ["# Import PFA model from the existing implementation\n", "import sys\n", "import os\n", "sys.path.append('App/Dashboard/models')\n", "\n", "# Force reload the module to get latest changes\n", "import importlib\n", "if 'train_pfa' in sys.modules:\n", "    importlib.reload(sys.modules['train_pfa'])\n", "\n", "from train_pfa import PerformanceFactorsAnalysis, load_skill_builder_data\n", "from joblib import dump, load\n", "\n", "print(\"PFA model imported successfully!\")\n", "print(\"✅ Using updated PFA model with skill-specific parameter estimation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Dataset\n", "\n", "We'll load the 2009 Skill Builder dataset which contains student responses to educational problems."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset...\n", "Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the dataset\n", "DATA_PATH = \"../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading dataset...\")\n", "df = load_skill_builder_data(DATA_PATH)\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 5 rows of the dataset:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>assignment_id</th>\n", "      <th>user_id</th>\n", "      <th>assistment_id</th>\n", "      <th>problem_id</th>\n", "      <th>original</th>\n", "      <th>correct</th>\n", "      <th>attempt_count</th>\n", "      <th>ms_first_response</th>\n", "      <th>tutor_mode</th>\n", "      <th>...</th>\n", "      <th>hint_count</th>\n", "      <th>hint_total</th>\n", "      <th>overlap_time</th>\n", "      <th>template_id</th>\n", "      <th>answer_id</th>\n", "      <th>answer_text</th>\n", "      <th>first_action</th>\n", "      <th>bottom_hint</th>\n", "      <th>opportunity</th>\n", "      <th>opportunity_original</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33022537</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33139</td>\n", "      <td>51424</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>32454</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>32454</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33022709</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33150</td>\n", "      <td>51435</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4922</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4922</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35450204</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33159</td>\n", "      <td>51444</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>25390</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>42000</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>88</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>35450295</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33110</td>\n", "      <td>51395</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4859</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4859</td>\n", "      <td>30059</td>\n", "      <td>NaN</td>\n", "      <td>41</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>35450311</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33196</td>\n", "      <td>51481</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>14</td>\n", "      <td>19813</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>124564</td>\n", "      <td>30060</td>\n", "      <td>NaN</td>\n", "      <td>65</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 30 columns</p>\n", "</div>"], "text/plain": ["   order_id  assignment_id  user_id  assistment_id  problem_id  original  \\\n", "0  33022537         277618    64525          33139       51424         1   \n", "1  33022709         277618    64525          33150       51435         1   \n", "2  35450204         220674    70363          33159       51444         1   \n", "3  35450295         220674    70363          33110       51395         1   \n", "4  35450311         220674    70363          33196       51481         1   \n", "\n", "   correct  attempt_count  ms_first_response tutor_mode  ... hint_count  \\\n", "0        1              1              32454      tutor  ...          0   \n", "1        1              1               4922      tutor  ...          0   \n", "2        0              2              25390      tutor  ...          0   \n", "3        1              1               4859      tutor  ...          0   \n", "4        0             14              19813      tutor  ...          3   \n", "\n", "   hint_total  overlap_time  template_id answer_id  answer_text  first_action  \\\n", "0           3         32454        30799       NaN           26             0   \n", "1           3          4922        30799       NaN           55             0   \n", "2           3         42000        30799       NaN           88             0   \n", "3           3          4859        30059       NaN           41             0   \n", "4           4        124564        30060       NaN           65             0   \n", "\n", "  bottom_hint  opportunity  opportunity_original  \n", "0         NaN            1                   1.0  \n", "1         NaN            2                   2.0  \n", "2         NaN            1                   1.0  \n", "3         NaN            2                   2.0  \n", "4         0.0            3                   3.0  \n", "\n", "[5 rows x 30 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display first few rows\n", "print(\"First 5 rows of the dataset:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Info:\n", "Total records: 401,756\n", "Memory usage: 200.29 MB\n", "\n", "Data types:\n", "order_id                  int64\n", "assignment_id             int64\n", "user_id                   int64\n", "assistment_id             int64\n", "problem_id                int64\n", "original                  int64\n", "correct                   int64\n", "attempt_count             int64\n", "ms_first_response         int64\n", "tutor_mode               object\n", "answer_type              object\n", "sequence_id               int64\n", "student_class_id          int64\n", "position                  int64\n", "type                     object\n", "base_sequence_id          int64\n", "skill_id                float64\n", "skill_name               object\n", "teacher_id                int64\n", "school_id                 int64\n", "hint_count                int64\n", "hint_total                int64\n", "overlap_time              int64\n", "template_id               int64\n", "answer_id               float64\n", "answer_text              object\n", "first_action              int64\n", "bottom_hint             float64\n", "opportunity               int64\n", "opportunity_original    float64\n", "dtype: object\n", "\n", "Missing values:\n", "order_id                     0\n", "assignment_id                0\n", "user_id                      0\n", "assistment_id                0\n", "problem_id                   0\n", "original                     0\n", "correct                      0\n", "attempt_count                0\n", "ms_first_response            0\n", "tutor_mode                   0\n", "answer_type                  0\n", "sequence_id                  0\n", "student_class_id             0\n", "position                     0\n", "type                         0\n", "base_sequence_id             0\n", "skill_id                 63755\n", "skill_name               76119\n", "teacher_id                   0\n", "school_id                    0\n", "hint_count                   0\n", "hint_total                   0\n", "overlap_time                 0\n", "template_id                  0\n", "answer_id               356302\n", "answer_text              89208\n", "first_action                 0\n", "bottom_hint             334712\n", "opportunity                  0\n", "opportunity_original     73465\n", "dtype: int64\n"]}], "source": ["# Basic dataset information\n", "print(\"Dataset Info:\")\n", "print(f\"Total records: {len(df):,}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "print(\"\\nData types:\")\n", "print(df.dtypes)\n", "print(\"\\nMissing values:\")\n", "print(df.isnull().sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Clean Dataset\n", "\n", "Clean the data by handling missing values, duplicates, and ensuring proper data types."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df.columns:\n", "        missing = df[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df.columns]\n", "df_clean = df.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Explore Dataset\n", "\n", "Analyze the dataset to understand the distribution of skills, users, and performance."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Statistics:\n", "Total interactions: 325,637\n", "Unique users: 4,151\n", "Unique problems: 16,891\n", "Unique skills: 110\n", "Overall accuracy: 0.658\n"]}], "source": ["# Basic statistics\n", "print(\"Dataset Statistics:\")\n", "print(f\"Total interactions: {len(df_clean):,}\")\n", "print(f\"Unique users: {df_clean['user_id'].nunique():,}\")\n", "print(f\"Unique problems: {df_clean['problem_id'].nunique():,}\")\n", "print(f\"Unique skills: {df_clean['skill_name'].nunique():,}\")\n", "print(f\"Overall accuracy: {df_clean['correct'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SKILLS ANALYSIS ===\n", "\n", "Top 15 skills by number of attempts:\n", "                                          Total_Attempts  Accuracy  \\\n", "skill_name                                                           \n", "Equation Solving Two or Fewer Steps                24253     0.679   \n", "Conversion of Fraction Decimals Percents           18742     0.637   \n", "Addition and Subtraction Integers                  12741     0.599   \n", "Addition and Subtraction Fractions                 11334     0.677   \n", "Percent Of                                          9497     0.595   \n", "Proportion                                          9054     0.641   \n", "Ordering Fractions                                  8539     0.792   \n", "Equation Solving More Than Two Steps                8115     0.758   \n", "Probability of Two Distinct Events                  7963     0.490   \n", "Finding Percents                                    7694     0.538   \n", "Subtraction Whole Numbers                           7669     0.641   \n", "Probability of a Single Event                       7438     0.742   \n", "Pattern Finding                                     7343     0.600   \n", "Absolute Value                                      7340     0.757   \n", "Ordering Positive Decimals                          7317     0.750   \n", "\n", "                                          Unique_Users  Unique_Problems  \n", "skill_name                                                               \n", "Equation Solving Two or Fewer Steps                961             1040  \n", "Conversion of Fraction Decimals Percents          1225              488  \n", "Addition and Subtraction Integers                 1226              413  \n", "Addition and Subtraction Fractions                1353              433  \n", "Percent Of                                        1115              465  \n", "Proportion                                         756              485  \n", "Ordering Fractions                                 882              464  \n", "Equation Solving More Than Two Steps               412              419  \n", "Probability of Two Distinct Events                 452              339  \n", "Finding Percents                                   771              371  \n", "Subtraction Whole Numbers                          903              242  \n", "Probability of a Single Event                      939              350  \n", "Pattern Finding                                    447              554  \n", "Absolute Value                                    1002              241  \n", "Ordering Positive Decimals                         942              543  \n"]}], "source": ["# Skills list and statistics\n", "print(\"\\n=== SKILLS ANALYSIS ===\")\n", "skill_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skill_stats.columns = ['Total_Attempts', 'Accuracy', 'Unique_Users', 'Unique_Problems']\n", "skill_stats = skill_stats.sort_values('Total_Attempts', ascending=False)\n", "\n", "print(f\"\\nTop 15 skills by number of attempts:\")\n", "print(skill_stats.head(15))"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize skill distribution\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Top 20 skills by attempts\n", "top_skills = skill_stats.head(20)\n", "axes[0,0].barh(range(len(top_skills)), top_skills['Total_Attempts'])\n", "axes[0,0].set_yticks(range(len(top_skills)))\n", "axes[0,0].set_yticklabels(top_skills.index, fontsize=8)\n", "axes[0,0].set_xlabel('Number of Attempts')\n", "axes[0,0].set_title('Top 20 Skills by Attempts')\n", "axes[0,0].invert_yaxis()\n", "\n", "# Accuracy distribution\n", "axes[0,1].hist(skill_stats['Accuracy'], bins=20, alpha=0.7)\n", "axes[0,1].set_xlabel('Accuracy')\n", "axes[0,1].set_ylabel('Number of Skills')\n", "axes[0,1].set_title('Distribution of Skill Accuracy')\n", "\n", "# User performance distribution\n", "user_accuracy = df_clean.groupby('user_id')['correct'].mean()\n", "axes[1,0].hist(user_accuracy, bins=30, alpha=0.7)\n", "axes[1,0].set_xlabel('User Accuracy')\n", "axes[1,0].set_ylabel('Number of Users')\n", "axes[1,0].set_title('Distribution of User Accuracy')\n", "\n", "# Attempts per user\n", "user_attempts = df_clean.groupby('user_id').size()\n", "axes[1,1].hist(user_attempts, bins=50, alpha=0.7)\n", "axes[1,1].set_xlabel('Number of Attempts')\n", "axes[1,1].set_ylabel('Number of Users')\n", "axes[1,1].set_title('Distribution of Attempts per User')\n", "axes[1,1].set_xlim(0, user_attempts.quantile(0.95))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Split Data into Train/Test Sets\n", "\n", "Split the data for training and evaluation while maintaining temporal order."]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Splitting data into train/test sets...\n", "Training set: 255,487 interactions (3,320 users)\n", "Test set: 70,150 interactions (831 users)\n", "Training accuracy: 0.655\n", "Test accuracy: 0.670\n"]}], "source": ["# Split data by users to avoid data leakage\n", "print(\"Splitting data into train/test sets...\")\n", "\n", "# Get unique users\n", "unique_users = df_clean['user_id'].unique()\n", "train_users, test_users = train_test_split(unique_users, test_size=0.2, random_state=42)\n", "\n", "# Split data based on users\n", "train_data = df_clean[df_clean['user_id'].isin(train_users)].copy()\n", "test_data = df_clean[df_clean['user_id'].isin(test_users)].copy()\n", "\n", "print(f\"Training set: {len(train_data):,} interactions ({len(train_users):,} users)\")\n", "print(f\"Test set: {len(test_data):,} interactions ({len(test_users):,} users)\")\n", "print(f\"Training accuracy: {train_data['correct'].mean():.3f}\")\n", "print(f\"Test accuracy: {test_data['correct'].mean():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Train PFA Model\n", "\n", "Train the Performance Factors Analysis model on the training data."]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing PFA model...\n", "Training PFA model on training data...\n", "Training Enhanced PFA model...\n", "Dataset size: 255,487 interactions\n", "Unique students: 3,320\n", "Unique problems: 16,527\n", "Unique skills: 110\n", "Training set: 203,640 interactions\n", "Validation set: 51,847 interactions\n", "Initializing parameters with data-driven approach...\n", "Initialized parameters for 2656 students, 16284 problems, 108 skills\n", "Epoch 1/10\n", "  Train Loss: nan, Val Loss: nan\n", "Epoch 2/10\n", "  Train Loss: nan, Val Loss: nan\n", "Epoch 3/10\n", "  Train Loss: nan, Val Loss: nan\n", "Early stopping at epoch 3\n", "Enhanced PFA model training completed!\n", "Best epoch: 0\n", "Final training loss: nan\n", "Final validation loss: nan\n", "✅ PFA model training completed!\n"]}], "source": ["# Initialize and train PFA model\n", "print(\"Initializing PFA model...\")\n", "pfa_model = PerformanceFactorsAnalysis(learning_rate=0.1, regularization=0.01)\n", "\n", "print(\"Training PFA model on training data...\")\n", "pfa_model.fit(train_data)\n", "\n", "print(\"✅ PFA model training completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Analyze Model Parameters\n", "\n", "Analyze the learned parameters to understand student abilities, problem difficulties, and skill learning rates."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== SKILL LEARNING RATES ANALYSIS ===\n", "\n", "Top 15 skills by learning rate:\n", "                                       Skill  Learning_Rate\n", "86       Equation Solving Two or Fewer Steps       0.057634\n", "29  Conversion of Fraction Decimals Percents       0.050734\n", "67         Addition and Subtraction Integers       0.041746\n", "70        Addition and Subtraction Fractions       0.039922\n", "43                                Percent Of       0.036270\n", "47                                Proportion       0.035609\n", "13        Probability of Two Distinct Events       0.033572\n", "32                        Ordering Fractions       0.032773\n", "44                 Subtraction Whole Numbers       0.032496\n", "46                          Finding Percents       0.032373\n", "55                          Pattern Finding        0.032279\n", "87      Equation Solving More Than Two Steps       0.032212\n", "14             Probability of a Single Event       0.031155\n", "31                Ordering Positive Decimals       0.031076\n", "53                            Absolute Value       0.030550\n", "\n", "Learning rate statistics:\n", "count    110.000000\n", "mean       0.016159\n", "std        0.011809\n", "min        0.000447\n", "25%        0.006307\n", "50%        0.014223\n", "75%        0.025184\n", "max        0.057634\n", "Name: Learning_Rate, dtype: float64\n"]}], "source": ["# Analyze skill learning rates\n", "print(\"=== SKILL LEARNING RATES ANALYSIS ===\")\n", "skill_learning_rates = {}\n", "for skill in train_data['skill_name'].unique():\n", "    params = pfa_model.get_skill_parameters(skill)\n", "    skill_learning_rates[skill] = params['learning_rate']\n", "\n", "# Convert to DataFrame for analysis\n", "learning_df = pd.DataFrame(list(skill_learning_rates.items()), \n", "                          columns=['Skill', 'Learning_Rate'])\n", "learning_df = learning_df.sort_values('Learning_Rate', ascending=False)\n", "\n", "print(f\"\\nTop 15 skills by learning rate:\")\n", "print(learning_df.head(15))\n", "\n", "print(f\"\\nLearning rate statistics:\")\n", "print(learning_df['Learning_Rate'].describe())"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== STUDENT ABILITIES ANALYSIS ===\n", "\n", "Top 10 students by ability:\n", "       User    Ability\n", "3308  90939  16.827753\n", "2     70677   0.000000\n", "2284  84356   0.000000\n", "2250  91953   0.000000\n", "2257  83239   0.000000\n", "2265  83291   0.000000\n", "2266  83293   0.000000\n", "2269  83303   0.000000\n", "2276  83648   0.000000\n", "2280  83664   0.000000\n", "\n", "Ability statistics:\n", "count    6.660000e+02\n", "mean     2.526690e-02\n", "std      6.520622e-01\n", "min     -5.063583e-07\n", "25%      0.000000e+00\n", "50%      0.000000e+00\n", "75%      0.000000e+00\n", "max      1.682775e+01\n", "Name: Ability, dtype: float64\n"]}], "source": ["# Analyze student abilities\n", "print(\"=== STUDENT ABILITIES ANALYSIS ===\")\n", "student_abilities = {}\n", "for user in train_data['user_id'].unique():\n", "    ability = pfa_model.get_student_ability(user)\n", "    student_abilities[user] = ability\n", "\n", "abilities_df = pd.DataFrame(list(student_abilities.items()), \n", "                           columns=['User', 'Ability'])\n", "abilities_df = abilities_df.sort_values('Ability', ascending=False)\n", "\n", "print(f\"\\nTop 10 students by ability:\")\n", "print(abilities_df.head(10))\n", "\n", "print(f\"\\nAbility statistics:\")\n", "print(abilities_df['Ability'].describe())"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PROBLEM DIFFICULTIES ANALYSIS ===\n", "\n", "Top 10 most difficult problems:\n", "      Problem  Difficulty\n", "16060  170440         inf\n", "412    147973         0.0\n", "10210   94189         0.0\n", "10197   95271         0.0\n", "10198   95272         0.0\n", "10199   95273         0.0\n", "10200   95274         0.0\n", "10201   95308         0.0\n", "10202   95309         0.0\n", "10203   95310         0.0\n", "\n", "Top 10 easiest problems:\n", "      Problem  Difficulty\n", "16517  128399         NaN\n", "16518  128400         NaN\n", "16519  128405         NaN\n", "16520  128406         NaN\n", "16521  128427         NaN\n", "16522  128428         NaN\n", "16523  128433         NaN\n", "16524  128434         NaN\n", "16525  128415         NaN\n", "16526  128416         NaN\n", "\n", "Difficulty statistics:\n", "count    248.000000\n", "mean            inf\n", "std             NaN\n", "min      -18.978896\n", "25%        0.000000\n", "50%        0.000000\n", "75%        0.000000\n", "max             inf\n", "Name: Di<PERSON>iculty, dtype: float64\n"]}], "source": ["# Analyze problem difficulties\n", "print(\"=== PROBLEM DIFFICULTIES ANALYSIS ===\")\n", "problem_difficulties = {}\n", "for problem in train_data['problem_id'].unique():\n", "    difficulty = pfa_model.get_problem_difficulty(problem)\n", "    problem_difficulties[problem] = difficulty\n", "\n", "difficulties_df = pd.DataFrame(list(problem_difficulties.items()), \n", "                              columns=['Problem', 'Difficulty'])\n", "difficulties_df = difficulties_df.sort_values('Difficulty', ascending=False)\n", "\n", "print(f\"\\nTop 10 most difficult problems:\")\n", "print(difficulties_df.head(10))\n", "\n", "print(f\"\\nTop 10 easiest problems:\")\n", "print(difficulties_df.tail(10))\n", "\n", "print(f\"\\nDifficulty statistics:\")\n", "print(difficulties_df['Difficulty'].describe())"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "supplied range of [-18.978896026249874, inf] is not finite", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[27], line 17\u001b[0m\n\u001b[1;32m     14\u001b[0m axes[\u001b[38;5;241m0\u001b[39m,\u001b[38;5;241m1\u001b[39m]\u001b[38;5;241m.\u001b[39mset_title(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDistribution of Student Abilities\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     16\u001b[0m \u001b[38;5;66;03m# Problem difficulties distribution\u001b[39;00m\n\u001b[0;32m---> 17\u001b[0m \u001b[43maxes\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhist\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdifficulties_df\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDifficulty\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbins\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m30\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43malpha\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.7\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     18\u001b[0m axes[\u001b[38;5;241m1\u001b[39m,\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mset_xlabel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mProblem Difficulty\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     19\u001b[0m axes[\u001b[38;5;241m1\u001b[39m,\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mset_ylabel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mNumber of Problems\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/matplotlib/__init__.py:1473\u001b[0m, in \u001b[0;36m_preprocess_data.<locals>.inner\u001b[0;34m(ax, data, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1470\u001b[0m \u001b[38;5;129m@functools\u001b[39m\u001b[38;5;241m.\u001b[39mwraps(func)\n\u001b[1;32m   1471\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21minner\u001b[39m(ax, \u001b[38;5;241m*\u001b[39margs, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m   1472\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m data \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 1473\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1474\u001b[0m \u001b[43m            \u001b[49m\u001b[43max\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1475\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msanitize_sequence\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1476\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43m{\u001b[49m\u001b[43mk\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43msanitize_sequence\u001b[49m\u001b[43m(\u001b[49m\u001b[43mv\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mitems\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1478\u001b[0m     bound \u001b[38;5;241m=\u001b[39m new_sig\u001b[38;5;241m.\u001b[39mbind(ax, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m   1479\u001b[0m     auto_label \u001b[38;5;241m=\u001b[39m (bound\u001b[38;5;241m.\u001b[39marguments\u001b[38;5;241m.\u001b[39mget(label_namer)\n\u001b[1;32m   1480\u001b[0m                   \u001b[38;5;129;01mor\u001b[39;00m bound\u001b[38;5;241m.\u001b[39mkwargs\u001b[38;5;241m.\u001b[39mget(label_namer))\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/matplotlib/axes/_axes.py:7001\u001b[0m, in \u001b[0;36mAxes.hist\u001b[0;34m(self, x, bins, range, density, weights, cumulative, bottom, histtype, align, orientation, rwidth, log, color, label, stacked, **kwargs)\u001b[0m\n\u001b[1;32m   6997\u001b[0m \u001b[38;5;66;03m# Loop through datasets\u001b[39;00m\n\u001b[1;32m   6998\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(nx):\n\u001b[1;32m   6999\u001b[0m     \u001b[38;5;66;03m# this will automatically overwrite bins,\u001b[39;00m\n\u001b[1;32m   7000\u001b[0m     \u001b[38;5;66;03m# so that each histogram uses the same bins\u001b[39;00m\n\u001b[0;32m-> 7001\u001b[0m     m, bins \u001b[38;5;241m=\u001b[39m \u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhistogram\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbins\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mweights\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mw\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mhist_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   7002\u001b[0m     tops\u001b[38;5;241m.\u001b[39mappend(m)\n\u001b[1;32m   7003\u001b[0m tops \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(tops, \u001b[38;5;28mfloat\u001b[39m)  \u001b[38;5;66;03m# causes problems later if it's an int\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/numpy/lib/histograms.py:780\u001b[0m, in \u001b[0;36mhistogram\u001b[0;34m(a, bins, range, density, weights)\u001b[0m\n\u001b[1;32m    680\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    681\u001b[0m \u001b[38;5;124;03mCompute the histogram of a dataset.\u001b[39;00m\n\u001b[1;32m    682\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    776\u001b[0m \n\u001b[1;32m    777\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    778\u001b[0m a, weights \u001b[38;5;241m=\u001b[39m _ravel_and_check_weights(a, weights)\n\u001b[0;32m--> 780\u001b[0m bin_edges, uniform_bins \u001b[38;5;241m=\u001b[39m \u001b[43m_get_bin_edges\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbins\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mrange\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mweights\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    782\u001b[0m \u001b[38;5;66;03m# Histogram is an integer or a float array depending on the weights.\u001b[39;00m\n\u001b[1;32m    783\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m weights \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/numpy/lib/histograms.py:426\u001b[0m, in \u001b[0;36m_get_bin_edges\u001b[0;34m(a, bins, range, weights)\u001b[0m\n\u001b[1;32m    423\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m n_equal_bins \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m    424\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m`bins` must be positive, when an integer\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m--> 426\u001b[0m     first_edge, last_edge \u001b[38;5;241m=\u001b[39m \u001b[43m_get_outer_edges\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mrange\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    428\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m np\u001b[38;5;241m.\u001b[39mndim(bins) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m    429\u001b[0m     bin_edges \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39masarray(bins)\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/numpy/lib/histograms.py:315\u001b[0m, in \u001b[0;36m_get_outer_edges\u001b[0;34m(a, range)\u001b[0m\n\u001b[1;32m    312\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    313\u001b[0m             \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmax must be larger than min in range parameter.\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m    314\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (np\u001b[38;5;241m.\u001b[39misfinite(first_edge) \u001b[38;5;129;01mand\u001b[39;00m np\u001b[38;5;241m.\u001b[39misfinite(last_edge)):\n\u001b[0;32m--> 315\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON>ueErro<PERSON>\u001b[39;00m(\n\u001b[1;32m    316\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msupplied range of [\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m] is not finite\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(first_edge, last_edge))\n\u001b[1;32m    317\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m a\u001b[38;5;241m.\u001b[39msize \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    318\u001b[0m     \u001b[38;5;66;03m# handle empty arrays. Can't determine range, so use 0-1.\u001b[39;00m\n\u001b[1;32m    319\u001b[0m     first_edge, last_edge \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mValueError\u001b[0m: supplied range of [-18.978896026249874, inf] is not finite"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize parameter distributions\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Learning rates distribution\n", "axes[0,0].hist(learning_df['Learning_Rate'], bins=30, alpha=0.7)\n", "axes[0,0].set_xlabel('Learning Rate')\n", "axes[0,0].set_ylabel('Number of Skills')\n", "axes[0,0].set_title('Distribution of Skill Learning Rates')\n", "\n", "# Student abilities distribution\n", "axes[0,1].hist(abilities_df['Ability'], bins=30, alpha=0.7)\n", "axes[0,1].set_xlabel('Student Ability')\n", "axes[0,1].set_ylabel('Number of Students')\n", "axes[0,1].set_title('Distribution of Student Abilities')\n", "\n", "# Problem difficulties distribution\n", "axes[1,0].hist(difficulties_df['Difficulty'], bins=30, alpha=0.7)\n", "axes[1,0].set_xlabel('Problem Difficulty')\n", "axes[1,0].set_ylabel('Number of Problems')\n", "axes[1,0].set_title('Distribution of Problem Difficulties')\n", "\n", "# Learning rate vs skill accuracy\n", "skill_accuracy = train_data.groupby('skill_name')['correct'].mean()\n", "learning_accuracy_df = learning_df.merge(\n", "    skill_accuracy.reset_index(), left_on='Skill', right_on='skill_name'\n", ")\n", "axes[1,1].scatter(learning_accuracy_df['Learning_Rate'], learning_accuracy_df['correct'], alpha=0.6)\n", "axes[1,1].set_xlabel('Learning Rate')\n", "axes[1,1].set_ylabel('Skill Accuracy')\n", "axes[1,1].set_title('Learning Rate vs Skill Accuracy')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Save Trained Model\n", "\n", "Save the trained PFA model for future use."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving PFA model to output/pfa_model.joblib...\n", "✅ Model saved successfully!\n"]}], "source": ["# Save the trained model\n", "MODEL_PATH = \"output/pfa_model.joblib\"\n", "os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "\n", "print(f\"Saving PFA model to {MODEL_PATH}...\")\n", "pfa_model.save(MODEL_PATH)\n", "print(\"✅ Model saved successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Training Statistics\n", "\n", "Display comprehensive training statistics and model summary."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PFA MODEL TRAINING STATISTICS ===\n", "\n", "📊 Dataset Statistics:\n", "  • Total training interactions: 255,487\n", "  • Unique students: 3,320\n", "  • Unique problems: 16,527\n", "  • Unique skills: 110\n", "  • Training accuracy: 0.655\n", "\n", "🎯 Model Parameters:\n", "  • Learning rate: 0.1\n", "  • Regularization: 0.01\n", "  • Student ability parameters: 3,320\n", "  • Problem difficulty parameters: 16,527\n", "  • Skill learning rate parameters: 110\n", "\n", "📈 Parameter Statistics:\n", "  • Student abilities - Mean: -0.047, Std: 0.449\n", "  • Problem difficulties - Mean: 0.009, Std: 0.354\n", "  • Skill learning rates - Mean: 0.339, Std: 0.844\n", "\n", "🏆 Top 5 Skills by Learning Rate:\n", "  1. <PERSON><PERSON> - <PERSON>bt<PERSON>, <PERSON><PERSON>, and Right: 2.519\n", "  2. <PERSON><PERSON> Finding : 2.487\n", "  3. Subtraction Whole Numbers: 2.014\n", "  4. Area Trapezoid: 1.669\n", "  5. Pythagorean Theorem: 1.442\n", "\n", "🎓 Top 5 Students by Ability:\n", "  1. <PERSON><PERSON> 92250: 3.058\n", "  2. <PERSON><PERSON> 96210: 1.814\n", "  3. <PERSON><PERSON> 96211: 1.741\n", "  4. <PERSON><PERSON> 92248: 1.672\n", "  5. <PERSON><PERSON> 84981: 1.606\n", "\n", "📚 Top 5 Most Difficult Problems:\n", "  1. Problem 107745: 4.215\n", "  2. Problem 107809: 4.000\n", "  3. Problem 107837: 3.967\n", "  4. Problem 61116: 3.805\n", "  5. Problem 107830: 3.803\n"]}], "source": ["# Training statistics\n", "print(\"=== PFA MODEL TRAINING STATISTICS ===\")\n", "print(f\"\\n📊 Dataset Statistics:\")\n", "print(f\"  • Total training interactions: {len(train_data):,}\")\n", "print(f\"  • Unique students: {len(train_users):,}\")\n", "print(f\"  • Unique problems: {train_data['problem_id'].nunique():,}\")\n", "print(f\"  • Unique skills: {train_data['skill_name'].nunique():,}\")\n", "print(f\"  • Training accuracy: {train_data['correct'].mean():.3f}\")\n", "\n", "print(f\"\\n🎯 Model Parameters:\")\n", "print(f\"  • Learning rate: {pfa_model.learning_rate}\")\n", "print(f\"  • Regularization: {pfa_model.regularization}\")\n", "print(f\"  • Student ability parameters: {len(pfa_model.student_abilities):,}\")\n", "print(f\"  • Problem difficulty parameters: {len(pfa_model.problem_difficulties):,}\")\n", "print(f\"  • Skill learning rate parameters: {len(pfa_model.skill_learning_rates):,}\")\n", "\n", "print(f\"\\n📈 Parameter Statistics:\")\n", "print(f\"  • Student abilities - Mean: {abilities_df['Ability'].mean():.3f}, Std: {abilities_df['Ability'].std():.3f}\")\n", "print(f\"  • Problem difficulties - Mean: {difficulties_df['Difficulty'].mean():.3f}, Std: {difficulties_df['Difficulty'].std():.3f}\")\n", "print(f\"  • Skill learning rates - Mean: {learning_df['Learning_Rate'].mean():.3f}, Std: {learning_df['Learning_Rate'].std():.3f}\")\n", "\n", "print(f\"\\n🏆 Top 5 Skills by Learning Rate:\")\n", "for i, (skill, lr) in enumerate(learning_df.head(5).values):\n", "    print(f\"  {i+1}. {skill}: {lr:.3f}\")\n", "\n", "print(f\"\\n🎓 Top 5 Students by Ability:\")\n", "for i, (user, ability) in enumerate(abilities_df.head(5).values):\n", "    print(f\"  {i+1}. User {user}: {ability:.3f}\")\n", "\n", "print(f\"\\n📚 Top 5 Most Difficult Problems:\")\n", "for i, (problem, difficulty) in enumerate(difficulties_df.head(5).values):\n", "    print(f\"  {i+1}. Problem {problem}: {difficulty:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Prediction Examples\n", "\n", "Demonstrate how to use the trained PFA model for making predictions."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PREDICTION EXAMPLE 1 ===\n", "Predicting performance for a user on a specific skill...\n", "No history found for user 87580 on skill Box and Whisker\n"]}], "source": ["# Example 1: Predict for a specific user and skill\n", "print(\"=== PREDICTION EXAMPLE 1 ===\")\n", "print(\"Predicting performance for a user on a specific skill...\")\n", "\n", "# Get a sample user and skill\n", "sample_user = train_users[0]\n", "sample_skill = train_data['skill_name'].iloc[0]\n", "sample_problem = train_data['problem_id'].iloc[0]\n", "\n", "# Get user's history on this skill\n", "user_skill_history = train_data[(train_data['user_id'] == sample_user) & \n", "                                (train_data['skill_name'] == sample_skill)]\n", "\n", "if len(user_skill_history) > 0:\n", "    # Create history as (problem_id, correct) tuples\n", "    history = list(zip(user_skill_history['problem_id'], user_skill_history['correct']))\n", "    \n", "    print(f\"User: {sample_user}\")\n", "    print(f\"Skill: {sample_skill}\")\n", "    print(f\"History: {history[:5]}...\")\n", "    \n", "    # Make predictions\n", "    predictions = pfa_model.predict_proba(history, sample_skill, sample_user)\n", "    \n", "    print(f\"\\nPredictions:\")\n", "    for i, (prob_id, correct) in enumerate(history[:10]):\n", "        print(f\"  Problem {prob_id}: Actual={correct}, Predicted={predictions[i]:.3f}\")\n", "else:\n", "    print(f\"No history found for user {sample_user} on skill {sample_skill}\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PREDICTION EXAMPLE 2 ===\n", "Comparing learning patterns across different skills...\n", "User 87580 has only 1 skill(s)\n"]}], "source": ["# Example 2: Compare different skills for the same user\n", "print(\"\\n=== PREDICTION EXAMPLE 2 ===\")\n", "print(\"Comparing learning patterns across different skills...\")\n", "\n", "# Get a user with multiple skills\n", "user_skills = train_data[train_data['user_id'] == sample_user]['skill_name'].unique()\n", "if len(user_skills) >= 2:\n", "    skill1, skill2 = user_skills[:2]\n", "    \n", "    # Get histories for both skills\n", "    history1 = train_data[(train_data['user_id'] == sample_user) & \n", "                          (train_data['skill_name'] == skill1)]\n", "    history2 = train_data[(train_data['user_id'] == sample_user) & \n", "                          (train_data['skill_name'] == skill2)]\n", "    \n", "    if len(history1) > 0 and len(history2) > 0:\n", "        # Create histories\n", "        h1 = list(zip(history1['problem_id'], history1['correct']))\n", "        h2 = list(zip(history2['problem_id'], history2['correct']))\n", "        \n", "        # Get predictions\n", "        pred1 = pfa_model.predict_proba(h1, skill1, sample_user)\n", "        pred2 = pfa_model.predict_proba(h2, skill2, sample_user)\n", "        \n", "        print(f\"User: {sample_user}\")\n", "        print(f\"\\nSkill 1: {skill1}\")\n", "        print(f\"  Learning rate: {pfa_model.get_skill_parameters(skill1)['learning_rate']:.3f}\")\n", "        print(f\"  Final prediction: {pred1[-1]:.3f}\")\n", "        \n", "        print(f\"\\nSkill 2: {skill2}\")\n", "        print(f\"  Learning rate: {pfa_model.get_skill_parameters(skill2)['learning_rate']:.3f}\")\n", "        print(f\"  Final prediction: {pred2[-1]:.3f}\")\n", "        \n", "        # Plot learning curves\n", "        plt.figure(figsize=(10, 6))\n", "        plt.plot(range(len(pred1)), pred1, label=skill1[:30] + '...', marker='o')\n", "        plt.plot(range(len(pred2)), pred2, label=skill2[:30] + '...', marker='s')\n", "        plt.xlabel('Attempt Number')\n", "        plt.ylabel('Predicted Probability of Success')\n", "        plt.title(f'Learning Curves for User {sample_user}')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        plt.show()\n", "    else:\n", "        print(\"Insufficient data for comparison\")\n", "else:\n", "    print(f\"User {sample_user} has only {len(user_skills)} skill(s)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. <PERSON> Summary\n", "\n", "Summary of the PFA model training and key insights."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎉 PFA MODEL TRAINING COMPLETED SUCCESSFULLY!\n", "============================================================\n", "\n", "📋 SUMMARY:\n", "  ✅ Trained PFA model on 255,487 interactions\n", "  ✅ Learned parameters for 110 skills\n", "  ✅ Estimated abilities for 3320 students\n", "  ✅ Calculated difficulties for 16527 problems\n", "  ✅ Model saved to: output/pfa_model.joblib\n", "\n", "🔍 KEY INSIGHTS:\n", "  • Average skill learning rate: 0.339\n", "  • Most learnable skill: <PERSON>les - Obtuse, Acute, and Right (2.519)\n", "  • Student ability range: -4.271 to 3.058\n", "  • Problem difficulty range: -3.910 to 4.215\n", "\n", "🚀 NEXT STEPS:\n", "  • Use the saved model for real-time predictions\n", "  • Integrate with your educational platform\n", "  • Fine-tune parameters based on domain knowledge\n", "  • Compare with other knowledge tracing models (BKT, DKT, etc.)\n", "\n", "📚 REFERENCES:\n", "  • <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, K. <PERSON> (2009). Performance Factors Analysis\n", "  • Performance Factors Analysis: A New Alternative to Knowledge Tracing\n", "  • Online Learning: From Theory to Practice\n", "\n", "============================================================\n"]}], "source": ["print(\"🎉 PFA MODEL TRAINING COMPLETED SUCCESSFULLY!\")\n", "print(\"=\" * 60)\n", "print(\"\\n📋 SUMMARY:\")\n", "print(f\"  ✅ Trained PFA model on {len(train_data):,} interactions\")\n", "print(f\"  ✅ Learned parameters for {len(pfa_model.skill_learning_rates)} skills\")\n", "print(f\"  ✅ Estimated abilities for {len(pfa_model.student_abilities)} students\")\n", "print(f\"  ✅ Calculated difficulties for {len(pfa_model.problem_difficulties)} problems\")\n", "print(f\"  ✅ Model saved to: {MODEL_PATH}\")\n", "\n", "print(\"\\n🔍 KEY INSIGHTS:\")\n", "print(f\"  • Average skill learning rate: {learning_df['Learning_Rate'].mean():.3f}\")\n", "print(f\"  • Most learnable skill: {learning_df.iloc[0]['Skill']} ({learning_df.iloc[0]['Learning_Rate']:.3f})\")\n", "print(f\"  • Student ability range: {abilities_df['Ability'].min():.3f} to {abilities_df['Ability'].max():.3f}\")\n", "print(f\"  • Problem difficulty range: {difficulties_df['Difficulty'].min():.3f} to {difficulties_df['Difficulty'].max():.3f}\")\n", "\n", "print(\"\\n🚀 NEXT STEPS:\")\n", "print(\"  • Use the saved model for real-time predictions\")\n", "print(\"  • Integrate with your educational platform\")\n", "print(\"  • Fine-tune parameters based on domain knowledge\")\n", "print(\"  • Compare with other knowledge tracing models (BKT, DKT, etc.)\")\n", "\n", "print(\"\\n📚 REFERENCES:\")\n", "print(\"  • <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, K. <PERSON> (2009). Performance Factors Analysis\")\n", "print(\"  • Performance Factors Analysis: A New Alternative to Knowledge Tracing\")\n", "print(\"  • Online Learning: From Theory to Practice\")\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}