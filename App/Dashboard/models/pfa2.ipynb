{"cells": [{"cell_type": "code", "execution_count": 10, "id": "905a9ca7-e824-4887-aaea-0a06ed96be56", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score\n", "from sklearn.preprocessing import StandardScaler\n", "import warnings\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": 24, "id": "4646c807-dfb7-409f-bf0e-a5e9bc25505f", "metadata": {}, "outputs": [], "source": ["class PFAKnowledgeTracing:\n", "    \"\"\"\n", "    Performance Factor Analysis Knowledge Tracing Model\n", "    \n", "    PFA models the probability of correct response based on:\n", "    - Prior success count for each skill\n", "    - Prior failure count for each skill\n", "    - Skill difficulty parameters\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        self.model = LogisticRegression(random_state=42, max_iter=1000)\n", "        self.scaler = StandardScaler()\n", "        self.skill_params = {}\n", "        self.user_skill_history = {}\n", "        self.is_fitted = False\n", "        \n", "    def create_pfa_features(self, df):\n", "        \"\"\"\n", "        Create PFA features: success/failure counts for each user-skill combination\n", "        \"\"\"\n", "        print(\"Creating PFA features...\")\n", "        \n", "        # Sort by user, skill, and order to ensure chronological order\n", "        df_sorted = df.sort_values(['user_id', 'skill_id', 'order_id']).copy()\n", "        \n", "        # Initialize feature columns\n", "        df_sorted['success_count'] = 0\n", "        df_sorted['failure_count'] = 0\n", "        df_sorted['total_attempts'] = 0\n", "        \n", "        # Track success/failure counts for each user-skill combination\n", "        user_skill_counts = {}\n", "        \n", "        for idx, row in df_sorted.iterrows():\n", "            user_id = row['user_id']\n", "            skill_id = row['skill_id']\n", "            is_correct = row['correct']\n", "            \n", "            # Create key for user-skill combination\n", "            key = (user_id, skill_id)\n", "            \n", "            # Initialize if first encounter\n", "            if key not in user_skill_counts:\n", "                user_skill_counts[key] = {'success': 0, 'failure': 0}\n", "            \n", "            # Set current counts (before this attempt)\n", "            df_sorted.loc[idx, 'success_count'] = user_skill_counts[key]['success']\n", "            df_sorted.loc[idx, 'failure_count'] = user_skill_counts[key]['failure']\n", "            df_sorted.loc[idx, 'total_attempts'] = user_skill_counts[key]['success'] + user_skill_counts[key]['failure']\n", "            \n", "            # Update counts after this attempt\n", "            if is_correct == 1:\n", "                user_skill_counts[key]['success'] += 1\n", "            else:\n", "                user_skill_counts[key]['failure'] += 1\n", "        \n", "        return df_sorted\n", "    \n", "    def prepare_features(self, df):\n", "        \"\"\"\n", "        Prepare feature matrix for training\n", "        \"\"\"\n", "        # Create PFA features\n", "        df_features = self.create_pfa_features(df)\n", "        \n", "        # Select features for model\n", "        feature_cols = [\n", "            'success_count', 'failure_count', 'total_attempts',\n", "            'attempt_count', 'hint_count', 'ms_first_response',\n", "            'opportunity'\n", "        ]\n", "        \n", "        # Handle missing values\n", "        df_features = df_features.dropna(subset=feature_cols + ['correct'])\n", "        df_features = df_features.reset_index(drop=True)\n", "        \n", "        # Create feature matrix\n", "        X = df_features[feature_cols].copy().reset_index(drop=True)\n", "        y = df_features['correct'].copy().reset_index(drop=True)\n", "        \n", "        # Add skill-specific features (one-hot encoding for top skills)\n", "        top_skills = df_features['skill_id'].value_counts().head(20).index\n", "        for skill in top_skills:\n", "            X[f'skill_{skill}'] = (df_features['skill_id'] == skill).astype(int)\n", "        \n", "        return X, y, df_features\n", "    \n", "    def fit(self, X, y, df):\n", "        \"\"\"\n", "        Train the PFA model\n", "        \"\"\"\n", "        print(\"Training PFA model...\")\n", "        \n", "        # Scale features\n", "        X_scaled = self.scaler.fit_transform(X)\n", "        \n", "        # Train logistic regression model\n", "        self.model.fit(X_scaled, y)\n", "        \n", "        # Store skill parameters\n", "        self.skill_params = self._extract_skill_params(X, df)\n", "        \n", "        # Store user skill history for analysis\n", "        self.user_skill_history = self._create_user_skill_history(df)\n", "        \n", "        self.is_fitted = True\n", "        print(\"Model training completed!\")\n", "        \n", "    def _extract_skill_params(self, X, df):\n", "        \"\"\"\n", "        Extract skill-specific parameters from the trained model\n", "        \"\"\"\n", "        skill_params = {}\n", "        \n", "        # Get skill columns\n", "        skill_cols = [col for col in X.columns if col.startswith('skill_')]\n", "        \n", "        for col in skill_cols:\n", "            skill_id = col.replace('skill_', '')\n", "            coef_idx = list(X.columns).index(col)\n", "            skill_params[skill_id] = {\n", "                'coefficient': self.model.coef_[0][coef_idx],\n", "                'difficulty': -self.model.coef_[0][coef_idx],  # Higher coef = easier skill\n", "                'frequency': df[df['skill_id'] == int(skill_id)].shape[0] if skill_id.isdigit() else 0\n", "            }\n", "        \n", "        return skill_params\n", "    \n", "    def _create_user_skill_history(self, df):\n", "        \"\"\"\n", "        Create user skill mastery history\n", "        \"\"\"\n", "        user_skill_history = {}\n", "        \n", "        for user_id in df['user_id'].unique():\n", "            user_data = df[df['user_id'] == user_id]\n", "            user_skill_history[user_id] = {}\n", "            \n", "            for skill_id in user_data['skill_id'].unique():\n", "                skill_data = user_data[user_data['skill_id'] == skill_id]\n", "                user_skill_history[user_id][skill_id] = {\n", "                    'total_attempts': len(skill_data),\n", "                    'correct_attempts': skill_data['correct'].sum(),\n", "                    'accuracy': skill_data['correct'].mean(),\n", "                    'avg_attempts': skill_data['attempt_count'].mean(),\n", "                    'mastery_level': self._calculate_mastery_level(skill_data)\n", "                }\n", "        \n", "        return user_skill_history\n", "    \n", "    def _calculate_mastery_level(self, skill_data):\n", "        \"\"\"\n", "        Calculate mastery level for a user-skill combination\n", "        \"\"\"\n", "        if len(skill_data) == 0:\n", "            return 0.0\n", "        \n", "        # Simple mastery calculation based on recent performance\n", "        recent_data = skill_data.tail(5)  # Last 5 attempts\n", "        recent_accuracy = recent_data['correct'].mean()\n", "        \n", "        # Weight by number of attempts\n", "        attempt_weight = min(len(skill_data) / 10, 1.0)\n", "        \n", "        mastery = recent_accuracy * attempt_weight\n", "        return round(mastery, 3)\n", "    \n", "    def predict(self, X):\n", "        \"\"\"\n", "        Make predictions using the trained model\n", "        \"\"\"\n", "        if not self.is_fitted:\n", "            raise ValueError(\"Model must be fitted before making predictions\")\n", "        \n", "        X_scaled = self.scaler.transform(X)\n", "        return self.model.predict_proba(X_scaled)[:, 1]\n", "    \n", "    def evaluate(self, X, y):\n", "        \"\"\"\n", "        Evaluate model performance\n", "        \"\"\"\n", "        y_pred_proba = self.predict(X)\n", "        y_pred = (y_pred_proba > 0.5).astype(int)\n", "        \n", "        metrics = {\n", "            'accuracy': accuracy_score(y, y_pred),\n", "            'precision': precision_score(y, y_pred),\n", "            'recall': recall_score(y, y_pred),\n", "            'f1': f1_score(y, y_pred),\n", "            'auc': roc_auc_score(y, y_pred_proba)\n", "        }\n", "        \n", "        return metrics\n", "\n"]}, {"cell_type": "code", "execution_count": 25, "id": "2c65f1cf-829a-455e-bc60-29dc34ab789e", "metadata": {}, "outputs": [], "source": ["def load_and_explore_data(file_path):\n", "    \"\"\"\n", "    Load and explore the student dataset\n", "    \"\"\"\n", "    print(\"Loading and exploring data...\")\n", "    \n", "    # Load data\n", "    df = pd.read_csv(file_path, encoding='latin1')\n", "    \n", "    print(f\"Dataset shape: {df.shape}\")\n", "    print(f\"Columns: {df.columns.tolist()}\")\n", "    \n", "    # Basic statistics\n", "    print(\"\\nBasic Statistics:\")\n", "    print(df.describe())\n", "    \n", "    # Check for missing values\n", "    print(f\"\\nMissing values:\\n{df.isnull().sum()}\")\n", "    \n", "    # Target variable distribution\n", "    print(f\"\\nTarget variable distribution:\")\n", "    print(df['correct'].value_counts())\n", "    \n", "    # Unique counts\n", "    print(f\"\\nUnique counts:\")\n", "    print(f\"Users: {df['user_id'].nunique()}\")\n", "    print(f\"Skills: {df['skill_id'].nunique()}\")\n", "    print(f\"Problems: {df['problem_id'].nunique()}\")\n", "    \n", "    return df\n", "\n", "\n", "def clean_data(df):\n", "    \"\"\"\n", "    Clean and preprocess the data\n", "    \"\"\"\n", "    print(\"Cleaning data...\")\n", "    \n", "    # Remove rows with missing critical columns\n", "    critical_cols = ['user_id', 'skill_id', 'correct', 'order_id']\n", "    df_clean = df.dropna(subset=critical_cols).copy()\n", "    \n", "    # Fill missing values for numeric columns\n", "    numeric_cols = ['attempt_count', 'hint_count', 'ms_first_response', 'opportunity']\n", "    for col in numeric_cols:\n", "        if col in df_clean.columns:\n", "            df_clean[col] = df_clean[col].fillna(df_clean[col].median())\n", "    \n", "    # Convert data types\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "    df_clean['skill_id'] = df_clean['skill_id'].astype(str)\n", "    \n", "    # Remove outliers (optional)\n", "    if 'ms_first_response' in df_clean.columns:\n", "        q99 = df_clean['ms_first_response'].quantile(0.99)\n", "        df_clean = df_clean[df_clean['ms_first_response'] <= q99]\n", "    \n", "    print(f\"Data cleaned. Shape: {df_clean.shape}\")\n", "    return df_clean\n", "\n"]}, {"cell_type": "code", "execution_count": 26, "id": "8bf6b231-f2f6-4a42-84b2-536c3ef2c986", "metadata": {}, "outputs": [], "source": ["def create_visualizations(df):\n", "    \"\"\"\n", "    Create exploratory data visualizations\n", "    \"\"\"\n", "    print(\"Creating visualizations...\")\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # 1. Correctness distribution\n", "    df['correct'].value_counts().plot(kind='bar', ax=axes[0, 0])\n", "    axes[0, 0].set_title('Correctness Distribution')\n", "    axes[0, 0].set_xlabel('Correct (0=Wrong, 1=Right)')\n", "    \n", "    # 2. Skill frequency\n", "    top_skills = df['skill_id'].value_counts().head(10)\n", "    top_skills.plot(kind='bar', ax=axes[0, 1])\n", "    axes[0, 1].set_title('Top 10 Skills by Frequency')\n", "    axes[0, 1].set_xlabel('Skill ID')\n", "    \n", "    # 3. Attempt count distribution\n", "    if 'attempt_count' in df.columns:\n", "        df['attempt_count'].hist(bins=20, ax=axes[1, 0])\n", "        axes[1, 0].set_title('Attempt Count Distribution')\n", "        axes[1, 0].set_xlabel('Attempt Count')\n", "    \n", "    # 4. Correctness by skill (top 10)\n", "    skill_accuracy = df.groupby('skill_id')['correct'].mean().sort_values(ascending=False).head(10)\n", "    skill_accuracy.plot(kind='bar', ax=axes[1, 1])\n", "    axes[1, 1].set_title('Accuracy by <PERSON><PERSON> (Top 10)')\n", "    axes[1, 1].set_ylabel('Accuracy')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": 27, "id": "72b57c73-395e-4589-87f1-6837a1c0d789", "metadata": {}, "outputs": [], "source": ["def analyze_model_parameters(pfa_model):\n", "    \"\"\"\n", "    Analyze trained model parameters\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"MODEL PARAMETER ANALYSIS\")\n", "    print(\"=\"*50)\n", "    \n", "    # Feature importance\n", "    print(\"\\nFeature Importance (Coefficients):\")\n", "    feature_names = ['success_count', 'failure_count', 'total_attempts', \n", "                    'attempt_count', 'hint_count', 'ms_first_response', 'opportunity']\n", "    \n", "    for i, feature in enumerate(feature_names):\n", "        if i < len(pfa_model.model.coef_[0]):\n", "            coef = pfa_model.model.coef_[0][i]\n", "            print(f\"{feature}: {coef:.4f}\")\n", "    \n", "    # Skill difficulty analysis\n", "    print(f\"\\nSkill Difficulty Analysis:\")\n", "    print(f\"Total skills analyzed: {len(pfa_model.skill_params)}\")\n", "    \n", "    if pfa_model.skill_params:\n", "        sorted_skills = sorted(pfa_model.skill_params.items(), \n", "                              key=lambda x: x[1]['difficulty'], reverse=True)\n", "        \n", "        print(\"\\nTop 5 Most Difficult Skills:\")\n", "        for skill_id, params in sorted_skills[:5]:\n", "            print(f\"Skill {skill_id}: Difficulty = {params['difficulty']:.4f}, \"\n", "                  f\"Frequency = {params['frequency']}\")\n", "        \n", "        print(\"\\nTop 5 Easiest Skills:\")\n", "        for skill_id, params in sorted_skills[-5:]:\n", "            print(f\"Skill {skill_id}: Difficulty = {params['difficulty']:.4f}, \"\n", "                  f\"Frequency = {params['frequency']}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 28, "id": "43f12d70-baf4-4196-aa11-9a274576a616", "metadata": {}, "outputs": [], "source": ["def analyze_user_mastery(pfa_model, top_n=5):\n", "    \"\"\"\n", "    Analyze user mastery levels\n", "    \"\"\"\n", "    print(f\"\\n\" + \"=\"*50)\n", "    print(\"USER MASTERY ANALYSIS\")\n", "    print(\"=\"*50)\n", "    \n", "    # Calculate overall user performance\n", "    user_performance = {}\n", "    for user_id, skills in pfa_model.user_skill_history.items():\n", "        total_attempts = sum(skill['total_attempts'] for skill in skills.values())\n", "        total_correct = sum(skill['correct_attempts'] for skill in skills.values())\n", "        avg_mastery = np.mean([skill['mastery_level'] for skill in skills.values()])\n", "        \n", "        user_performance[user_id] = {\n", "            'total_attempts': total_attempts,\n", "            'accuracy': total_correct / total_attempts if total_attempts > 0 else 0,\n", "            'skills_practiced': len(skills),\n", "            'avg_mastery': avg_mastery\n", "        }\n", "    \n", "    # Sort by average mastery\n", "    sorted_users = sorted(user_performance.items(), \n", "                         key=lambda x: x[1]['avg_mastery'], reverse=True)\n", "    \n", "    print(f\"\\nTop {top_n} Users by Mastery Level:\")\n", "    for user_id, performance in sorted_users[:top_n]:\n", "        print(f\"User {user_id}: Mastery = {performance['avg_mastery']:.3f}, \"\n", "              f\"Accuracy = {performance['accuracy']:.3f}, \"\n", "              f\"Skills = {performance['skills_practiced']}\")\n"]}, {"cell_type": "code", "execution_count": 29, "id": "74f7a0b1-7e07-426b-8b88-4b0156d504a3", "metadata": {}, "outputs": [], "source": ["def generate_training_stats(df, pfa_model):\n", "    \"\"\"\n", "    Generate comprehensive training statistics\n", "    \"\"\"\n", "    print(f\"\\n\" + \"=\"*50)\n", "    print(\"TRAINING STATISTICS\")\n", "    print(\"=\"*50)\n", "    \n", "    # Overall statistics\n", "    print(f\"Total interactions: {len(df)}\")\n", "    print(f\"Total users: {df['user_id'].nunique()}\")\n", "    print(f\"Total skills: {df['skill_id'].nunique()}\")\n", "    print(f\"Total problems: {df['problem_id'].nunique()}\")\n", "    print(f\"Overall accuracy: {df['correct'].mean():.3f}\")\n", "    \n", "    # Skill statistics\n", "    print(f\"\\nSkill Statistics:\")\n", "    skill_stats = df.groupby('skill_id').agg({\n", "        'correct': ['count', 'mean', 'std'],\n", "        'attempt_count': 'mean',\n", "        'user_id': 'nunique'\n", "    }).round(3)\n", "    \n", "    skill_stats.columns = ['Total_Interactions', 'Accuracy', 'Accuracy_Std', \n", "                          'Avg_Attempts', 'Unique_Users']\n", "    \n", "    print(skill_stats.head(10))\n", "    \n", "    # Problem statistics\n", "    print(f\"\\nProblem Statistics (Top 10):\")\n", "    problem_stats = df.groupby('problem_id').agg({\n", "        'correct': ['count', 'mean'],\n", "        'attempt_count': 'mean',\n", "        'user_id': 'nunique'\n", "    }).round(3)\n", "    \n", "    problem_stats.columns = ['Total_Interactions', 'Accuracy', 'Avg_Attempts', 'Unique_Users']\n", "    problem_stats = problem_stats.sort_values('Total_Interactions', ascending=False)\n", "    \n", "    print(problem_stats.head(10))\n", "    \n", "    # Student statistics\n", "    print(f\"\\nStudent Statistics (Top 10 by activity):\")\n", "    student_stats = df.groupby('user_id').agg({\n", "        'correct': ['count', 'mean'],\n", "        'skill_id': 'nunique',\n", "        'problem_id': 'nunique'\n", "    }).round(3)\n", "    \n", "    student_stats.columns = ['Total_Interactions', 'Accuracy', 'Skills_Practiced', 'Problems_Solved']\n", "    student_stats = student_stats.sort_values('Total_Interactions', ascending=False)\n", "    \n", "    print(student_stats.head(10))\n"]}, {"cell_type": "code", "execution_count": 30, "id": "89b0a1d6-cfce-4bf1-b7fa-00ebe640df6c", "metadata": {}, "outputs": [], "source": ["\n", "def prediction_examples(pfa_model, X_test, df_test, n_examples=5):\n", "    \"\"\"\n", "    Show prediction examples for trained skills\n", "    \"\"\"\n", "    print(f\"\\n\" + \"=\"*50)\n", "    print(\"PREDICTION EXAMPLES\")\n", "    print(\"=\"*50)\n", "    \n", "    # Make predictions\n", "    predictions = pfa_model.predict(X_test)\n", "    \n", "    # Select random examples\n", "    indices = np.random.choice(len(X_test), n_examples, replace=False)\n", "    \n", "    print(f\"Showing {n_examples} prediction examples:\")\n", "    print()\n", "    \n", "    for i, idx in enumerate(indices):\n", "        actual = df_test.iloc[idx]['correct']\n", "        predicted_prob = predictions[idx]\n", "        predicted_class = 1 if predicted_prob > 0.5 else 0\n", "        \n", "        user_id = df_test.iloc[idx]['user_id']\n", "        skill_id = df_test.iloc[idx]['skill_id']\n", "        success_count = X_test.iloc[idx]['success_count']\n", "        failure_count = X_test.iloc[idx]['failure_count']\n", "        \n", "        print(f\"Example {i+1}:\")\n", "        print(f\"  User: {user_id}, Skill: {skill_id}\")\n", "        print(f\"  Prior Success: {success_count}, Prior Failures: {failure_count}\")\n", "        print(f\"  Predicted Probability: {predicted_prob:.3f}\")\n", "        print(f\"  Predicted Class: {predicted_class}\")\n", "        print(f\"  Actual: {actual}\")\n", "        print(f\"  Correct: {'✓' if predicted_class == actual else '✗'}\")\n", "        print()\n"]}, {"cell_type": "code", "execution_count": 31, "id": "b65e30ae-ab0d-4f58-9468-9a61c849dde7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Performance Factor Analysis Knowledge Tracing\n", "==================================================\n", "Loading and exploring data...\n", "Dataset shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n", "\n", "Basic Statistics:\n", "           order_id  assignment_id        user_id  assistment_id  \\\n", "count  4.017560e+05  401756.000000  401756.000000  401756.000000   \n", "mean   3.066256e+07  273701.845882   83414.154542   46443.517526   \n", "std    5.264886e+06   11338.460017    7417.814021   11832.443427   \n", "min    2.022408e+07  217900.000000      14.000000      86.000000   \n", "25%    2.660218e+07  266784.000000   78970.000000   37046.000000   \n", "50%    3.110513e+07  271629.000000   80111.000000   44498.000000   \n", "75%    3.494364e+07  279158.000000   88142.000000   53142.000000   \n", "max    3.831020e+07  291503.000000   96299.000000  106210.000000   \n", "\n", "          problem_id       original        correct  attempt_count  \\\n", "count  401756.000000  401756.000000  401756.000000  401756.000000   \n", "mean    81117.030011       0.817140       0.642923       1.596417   \n", "std     25426.799662       0.386552       0.479139      12.050437   \n", "min        83.000000       0.000000       0.000000       0.000000   \n", "25%     58467.000000       1.000000       0.000000       1.000000   \n", "50%     80734.000000       1.000000       1.000000       1.000000   \n", "75%     93102.000000       1.000000       1.000000       1.000000   \n", "max    207348.000000       1.000000       1.000000    3824.000000   \n", "\n", "       ms_first_response    sequence_id  ...      school_id     hint_count  \\\n", "count       4.017560e+05  401756.000000  ...  401756.000000  401756.000000   \n", "mean        4.748464e+04    7284.411088  ...    3031.291025       0.487470   \n", "std         3.614590e+05    1497.941072  ...    1830.451486       1.187255   \n", "min        -7.759575e+06    5870.000000  ...       1.000000       0.000000   \n", "25%         8.518000e+03    5979.000000  ...    2770.000000       0.000000   \n", "50%         1.945300e+04    6910.000000  ...    2770.000000       0.000000   \n", "75%         4.457825e+04    8032.000000  ...    5056.000000       0.000000   \n", "max         8.407692e+07   13362.000000  ...    9948.000000      10.000000   \n", "\n", "          hint_total  overlap_time    template_id      answer_id  \\\n", "count  401756.000000  4.017560e+05  401756.000000   45454.000000   \n", "mean        2.235817  5.964848e+04   39571.335029  145094.431667   \n", "std         1.804244  3.822188e+05   12679.439926   47127.478285   \n", "min         0.000000 -7.759575e+06      86.000000       1.000000   \n", "25%         0.000000  1.066900e+04   30244.000000  104412.000000   \n", "50%         3.000000  2.426450e+04   30987.000000  136247.000000   \n", "75%         4.000000  5.698925e+04   46399.000000  184077.000000   \n", "max        10.000000  8.407692e+07  106180.000000  323181.000000   \n", "\n", "        first_action   bottom_hint    opportunity  opportunity_original  \n", "count  401756.000000  67044.000000  401756.000000         328291.000000  \n", "mean        0.130012      0.724092      20.553535             14.403307  \n", "std         0.394099      0.446974      62.523994             62.393684  \n", "min         0.000000      0.000000       1.000000              1.000000  \n", "25%         0.000000      0.000000       3.000000              3.000000  \n", "50%         0.000000      1.000000       8.000000              6.000000  \n", "75%         0.000000      1.000000      19.000000             13.000000  \n", "max         2.000000      1.000000    3371.000000           3371.000000  \n", "\n", "[8 rows x 25 columns]\n", "\n", "Missing values:\n", "order_id                     0\n", "assignment_id                0\n", "user_id                      0\n", "assistment_id                0\n", "problem_id                   0\n", "original                     0\n", "correct                      0\n", "attempt_count                0\n", "ms_first_response            0\n", "tutor_mode                   0\n", "answer_type                  0\n", "sequence_id                  0\n", "student_class_id             0\n", "position                     0\n", "type                         0\n", "base_sequence_id             0\n", "skill_id                 63755\n", "skill_name               76119\n", "teacher_id                   0\n", "school_id                    0\n", "hint_count                   0\n", "hint_total                   0\n", "overlap_time                 0\n", "template_id                  0\n", "answer_id               356302\n", "answer_text              89208\n", "first_action                 0\n", "bottom_hint             334712\n", "opportunity                  0\n", "opportunity_original     73465\n", "dtype: int64\n", "\n", "Target variable distribution:\n", "correct\n", "1    258298\n", "0    143458\n", "Name: count, dtype: int64\n", "\n", "Unique counts:\n", "Users: 4217\n", "Skills: 123\n", "Problems: 26688\n"]}], "source": ["\n", "print(\"Performance Factor Analysis Knowledge Tracing\")\n", "print(\"=\" * 50)\n", "\n", "# 1. Load and explore data\n", "file_path = \"../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"  # Updated to correct file path\n", "df = load_and_explore_data(file_path)\n"]}, {"cell_type": "code", "execution_count": 32, "id": "270353c5-9287-43f7-9652-67ae798ebf21", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>assignment_id</th>\n", "      <th>user_id</th>\n", "      <th>assistment_id</th>\n", "      <th>problem_id</th>\n", "      <th>original</th>\n", "      <th>correct</th>\n", "      <th>attempt_count</th>\n", "      <th>ms_first_response</th>\n", "      <th>tutor_mode</th>\n", "      <th>...</th>\n", "      <th>hint_count</th>\n", "      <th>hint_total</th>\n", "      <th>overlap_time</th>\n", "      <th>template_id</th>\n", "      <th>answer_id</th>\n", "      <th>answer_text</th>\n", "      <th>first_action</th>\n", "      <th>bottom_hint</th>\n", "      <th>opportunity</th>\n", "      <th>opportunity_original</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33022537</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33139</td>\n", "      <td>51424</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>32454</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>32454</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33022709</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33150</td>\n", "      <td>51435</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4922</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4922</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35450204</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33159</td>\n", "      <td>51444</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>25390</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>42000</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>88</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>35450295</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33110</td>\n", "      <td>51395</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4859</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4859</td>\n", "      <td>30059</td>\n", "      <td>NaN</td>\n", "      <td>41</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>35450311</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33196</td>\n", "      <td>51481</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>14</td>\n", "      <td>19813</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>124564</td>\n", "      <td>30060</td>\n", "      <td>NaN</td>\n", "      <td>65</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401751</th>\n", "      <td>38214014</td>\n", "      <td>291495</td>\n", "      <td>96299</td>\n", "      <td>57830</td>\n", "      <td>108976</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>38234</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>38234</td>\n", "      <td>55692</td>\n", "      <td>200260.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>54</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401752</th>\n", "      <td>38214016</td>\n", "      <td>291495</td>\n", "      <td>96299</td>\n", "      <td>57843</td>\n", "      <td>109015</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>6500</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6500</td>\n", "      <td>55693</td>\n", "      <td>200299.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>55</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401753</th>\n", "      <td>38214195</td>\n", "      <td>291503</td>\n", "      <td>96299</td>\n", "      <td>34577</td>\n", "      <td>54060</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>18344</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>18344</td>\n", "      <td>30677</td>\n", "      <td>NaN</td>\n", "      <td>0.8</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>56</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401754</th>\n", "      <td>38214196</td>\n", "      <td>291503</td>\n", "      <td>96299</td>\n", "      <td>34577</td>\n", "      <td>54061</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>12922</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>12922</td>\n", "      <td>30677</td>\n", "      <td>NaN</td>\n", "      <td>-6</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>57</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401755</th>\n", "      <td>38214198</td>\n", "      <td>291503</td>\n", "      <td>96299</td>\n", "      <td>34577</td>\n", "      <td>54062</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>36047</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>163422</td>\n", "      <td>30677</td>\n", "      <td>NaN</td>\n", "      <td>-6.8</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>58</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>401756 rows × 30 columns</p>\n", "</div>"], "text/plain": ["        order_id  assignment_id  user_id  assistment_id  problem_id  original  \\\n", "0       33022537         277618    64525          33139       51424         1   \n", "1       33022709         277618    64525          33150       51435         1   \n", "2       35450204         220674    70363          33159       51444         1   \n", "3       35450295         220674    70363          33110       51395         1   \n", "4       35450311         220674    70363          33196       51481         1   \n", "...          ...            ...      ...            ...         ...       ...   \n", "401751  38214014         291495    96299          57830      108976         0   \n", "401752  38214016         291495    96299          57843      109015         0   \n", "401753  38214195         291503    96299          34577       54060         0   \n", "401754  38214196         291503    96299          34577       54061         0   \n", "401755  38214198         291503    96299          34577       54062         0   \n", "\n", "        correct  attempt_count  ms_first_response tutor_mode  ... hint_count  \\\n", "0             1              1              32454      tutor  ...          0   \n", "1             1              1               4922      tutor  ...          0   \n", "2             0              2              25390      tutor  ...          0   \n", "3             1              1               4859      tutor  ...          0   \n", "4             0             14              19813      tutor  ...          3   \n", "...         ...            ...                ...        ...  ...        ...   \n", "401751        1              1              38234      tutor  ...          0   \n", "401752        1              1               6500      tutor  ...          0   \n", "401753        1              1              18344      tutor  ...          0   \n", "401754        1              1              12922      tutor  ...          0   \n", "401755        0              9              36047      tutor  ...          1   \n", "\n", "        hint_total  overlap_time  template_id answer_id  answer_text  \\\n", "0                3         32454        30799       NaN           26   \n", "1                3          4922        30799       NaN           55   \n", "2                3         42000        30799       NaN           88   \n", "3                3          4859        30059       NaN           41   \n", "4                4        124564        30060       NaN           65   \n", "...            ...           ...          ...       ...          ...   \n", "401751           2         38234        55692  200260.0          NaN   \n", "401752           0          6500        55693  200299.0          NaN   \n", "401753           3         18344        30677       NaN          0.8   \n", "401754           2         12922        30677       NaN           -6   \n", "401755           3        163422        30677       NaN         -6.8   \n", "\n", "        first_action bottom_hint  opportunity  opportunity_original  \n", "0                  0         NaN            1                   1.0  \n", "1                  0         NaN            2                   2.0  \n", "2                  0         NaN            1                   1.0  \n", "3                  0         NaN            2                   2.0  \n", "4                  0         0.0            3                   3.0  \n", "...              ...         ...          ...                   ...  \n", "401751             0         NaN           54                   NaN  \n", "401752             0         NaN           55                   NaN  \n", "401753             0         NaN           56                   NaN  \n", "401754             0         NaN           57                   NaN  \n", "401755             0         0.0           58                   NaN  \n", "\n", "[401756 rows x 30 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 33, "id": "1bc390c3-8809-4182-87f4-b6fe3d4bd77e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning data...\n", "Data cleaned. Shape: (334621, 30)\n", "Creating visualizations...\n"]}, {"data": {"image/png": "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***************************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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 2. Clean data\n", "df_clean = clean_data(df)\n", "\n", "# 3. Create visualizations\n", "create_visualizations(df_clean)\n"]}, {"cell_type": "code", "execution_count": 34, "id": "01bb0187-e181-4cdb-a960-ad026e8183ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Splitting data for train/test...\n"]}], "source": ["# 4. Split data for training and testing\n", "print(\"\\nSplitting data for train/test...\")\n", "\n", "# Initialize PFA model\n", "pfa_model = PFAKnowledgeTracing()"]}, {"cell_type": "code", "execution_count": 36, "id": "b377228b-dead-4815-876a-e9679f879b34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating PFA features...\n", "Training set size: 267696\n", "Test set size: 66925\n"]}], "source": ["# Prepare features\n", "X, y, df_features = pfa_model.prepare_features(df_clean)\n", "\n", "# Split data (80% train, 20% test)\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "# Split the dataframe for analysis\n", "df_train = df_features.iloc[X_train.index]\n", "df_test = df_features.iloc[X_test.index]\n", "\n", "print(f\"Training set size: {len(X_train)}\")\n", "print(f\"Test set size: {len(X_test)}\")\n"]}, {"cell_type": "code", "execution_count": 37, "id": "c991caf0-7627-4caa-993b-43225d6989e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training PFA model...\n", "Model training completed!\n"]}], "source": ["# 5. Train PFA model\n", "pfa_model.fit(X_train, y_train, df_train)"]}, {"cell_type": "code", "execution_count": 38, "id": "e634b11a-8490-4923-aa21-71ca614719d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Evaluating model performance...\n", "\n", "Training Metrics:\n", "  accuracy: 0.8744\n", "  precision: 0.8452\n", "  recall: 0.9893\n", "  f1: 0.9116\n", "  auc: 0.8621\n", "\n", "Testing Metrics:\n", "  accuracy: 0.8746\n", "  precision: 0.8457\n", "  recall: 0.9888\n", "  f1: 0.9116\n", "  auc: 0.8610\n"]}], "source": ["# 6. Evaluate model\n", "print(\"\\nEvaluating model performance...\")\n", "train_metrics = pfa_model.evaluate(X_train, y_train)\n", "test_metrics = pfa_model.evaluate(X_test, y_test)\n", "\n", "print(f\"\\nTraining Metrics:\")\n", "for metric, value in train_metrics.items():\n", "    print(f\"  {metric}: {value:.4f}\")\n", "\n", "print(f\"\\nTesting Metrics:\")\n", "for metric, value in test_metrics.items():\n", "    print(f\"  {metric}: {value:.4f}\")"]}, {"cell_type": "code", "execution_count": 40, "id": "5a545325-9098-4fb0-acef-93da9c2ce670", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "MODEL PARAMETER ANALYSIS\n", "==================================================\n", "\n", "Feature Importance (Coefficients):\n", "success_count: 0.5223\n", "failure_count: -0.6181\n", "total_attempts: -0.0235\n", "attempt_count: -22.0907\n", "hint_count: -7.4769\n", "ms_first_response: -0.2011\n", "opportunity: 0.0581\n", "\n", "Skill Difficulty Analysis:\n", "Total skills analyzed: 20\n", "\n", "Top 5 Most Difficult Skills:\n", "Skill 17.0: Difficulty = 0.1514, Frequency = 0\n", "Skill 77.0: Difficulty = 0.1231, Frequency = 0\n", "Skill 74.0: Difficulty = 0.0954, Frequency = 0\n", "Skill 85.0: Difficulty = 0.0769, Frequency = 0\n", "Skill 312.0: Difficulty = 0.0766, Frequency = 0\n", "\n", "Top 5 Easiest Skills:\n", "Skill 70.0: Difficulty = -0.0097, Frequency = 0\n", "Skill 18.0: Difficulty = -0.0293, Frequency = 0\n", "Skill 277.0: Difficulty = -0.0904, Frequency = 0\n", "Skill 280.0: Difficulty = -0.1732, Frequency = 0\n", "Skill 47.0: Difficulty = -0.2009, Frequency = 0\n"]}], "source": ["# 7. Analyze model parameters\n", "analyze_model_parameters(pfa_model)"]}, {"cell_type": "code", "execution_count": 42, "id": "13bd67b5-4545-46f5-9579-d4bac5acf231", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "USER MASTERY ANALYSIS\n", "==================================================\n", "\n", "Top 5 Users by Mastery Level:\n", "User 85111: Mastery = 1.000, Accuracy = 0.857, Skills = 1\n", "User 88201: Mastery = 1.000, Accuracy = 0.846, Skills = 1\n", "User 87060: Mastery = 1.000, Accuracy = 0.833, Skills = 1\n", "User 81623: Mastery = 1.000, Accuracy = 0.846, Skills = 1\n", "User 88229: Mastery = 1.000, Accuracy = 1.000, Skills = 1\n"]}], "source": ["# 8. Analyze user mastery\n", "analyze_user_mastery(pfa_model)"]}, {"cell_type": "code", "execution_count": 43, "id": "dcf46329-2ff0-43b1-855c-21e6c0480951", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "TRAINING STATISTICS\n", "==================================================\n", "Total interactions: 334621\n", "Total users: 4160\n", "Total skills: 123\n", "Total problems: 17733\n", "Overall accuracy: 0.654\n", "\n", "Skill Statistics:\n", "          Total_Interactions  Accuracy  Accuracy_Std  Avg_Attempts  \\\n", "skill_id                                                             \n", "1.0                     3949     0.753         0.431         1.494   \n", "10.0                    6697     0.740         0.438         1.027   \n", "101.0                   1305     0.651         0.477         0.936   \n", "102.0                      1     0.000           NaN         1.000   \n", "104.0                    106     0.472         0.502         1.094   \n", "105.0                    544     0.636         0.482         1.438   \n", "11.0                    5775     0.709         0.454         1.225   \n", "110.0                    455     0.846         0.361         1.196   \n", "12.0                    2856     0.532         0.499         1.540   \n", "13.0                    5667     0.657         0.475         1.385   \n", "\n", "          Unique_Users  \n", "skill_id                \n", "1.0                505  \n", "10.0               712  \n", "101.0              315  \n", "102.0                1  \n", "104.0               33  \n", "105.0              262  \n", "11.0               552  \n", "110.0              202  \n", "12.0               676  \n", "13.0               786  \n", "\n", "Problem Statistics (Top 10):\n", "            Total_Interactions  Accuracy  Avg_Attempts  Unique_Users\n", "problem_id                                                          \n", "49319                      266     0.722         0.887           112\n", "49313                      264     0.742         0.879           112\n", "49325                      262     0.771         0.939           111\n", "49373                      258     0.729         0.891           102\n", "49352                      256     0.766         0.859           101\n", "49331                      256     0.711         0.938           103\n", "49292                      246     0.813         0.911           101\n", "49301                      242     0.669         0.917            97\n", "49376                      236     0.729         0.898            89\n", "49364                      236     0.712         0.881            99\n", "\n", "Student Statistics (Top 10 by activity):\n", "         Total_Interactions  Accuracy  Skills_Practiced  Problems_Solved\n", "user_id                                                                 \n", "75169                  1285     0.802                92              979\n", "79021                  1269     0.554                80             1011\n", "78970                  1223     0.591                75              990\n", "79032                  1205     0.471                76              966\n", "96274                  1162     0.478                75              944\n", "78979                  1156     0.545                72              966\n", "96244                  1129     0.550                71              952\n", "79029                  1126     0.520                78              811\n", "78978                  1105     0.590                76              873\n", "71881                  1081     0.716                87              857\n"]}], "source": ["# 9. Generate training statistics\n", "generate_training_stats(df_clean, pfa_model)\n"]}, {"cell_type": "code", "execution_count": 44, "id": "3e70d835-ea46-4576-a185-0c6d58650896", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "PREDICTION EXAMPLES\n", "==================================================\n", "Showing 5 prediction examples:\n", "\n", "Example 1:\n", "  User: 78278, Skill: 86.0\n", "  Prior Success: 7, Prior Failures: 3\n", "  Predicted Probability: 0.865\n", "  Predicted Class: 1\n", "  Actual: 1\n", "  Correct: ✓\n", "\n", "Example 2:\n", "  User: 85192, Skill: 280.0\n", "  Prior Success: 1, Prior Failures: 2\n", "  Predicted Probability: 0.929\n", "  Predicted Class: 1\n", "  Actual: 1\n", "  Correct: ✓\n", "\n", "Example 3:\n", "  User: 79064, Skill: 27.0\n", "  Prior Success: 18, Prior Failures: 5\n", "  Predicted Probability: 0.864\n", "  Predicted Class: 1\n", "  Actual: 1\n", "  Correct: ✓\n", "\n", "Example 4:\n", "  User: 78947, Skill: 37.0\n", "  Prior Success: 1, Prior Failures: 3\n", "  Predicted Probability: 0.000\n", "  Predicted Class: 0\n", "  Actual: 0\n", "  Correct: ✓\n", "\n", "Example 5:\n", "  User: 88153, Skill: 280.0\n", "  Prior Success: 0, Prior Failures: 3\n", "  Predicted Probability: 0.000\n", "  Predicted Class: 0\n", "  Actual: 0\n", "  Correct: ✓\n", "\n"]}], "source": ["# 10. Show prediction examples\n", "prediction_examples(pfa_model, X_test, df_test)"]}, {"cell_type": "code", "execution_count": null, "id": "3dafd7c8-ab0d-4514-af1c-ff4a7c89984e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}