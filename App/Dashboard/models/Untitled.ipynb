{"cells": [{"cell_type": "code", "execution_count": 2, "id": "062c9408-90ae-4393-8c4a-72ef7960efd5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-10 22:21:54.551963: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.preprocessing import LabelEncoder\n", "from train_dkt import DeepKnowledgeTracing, load_skill_builder_data\n", "\n", "def preprocess_data(df):\n", "    # Sort by user and time\n", "    df = df.sort_values(['user_id', 'order_id'])\n", "    \n", "    # Encode skills and users\n", "    skill_encoder = LabelEncoder()\n", "    user_encoder = LabelEncoder()\n", "    \n", "    df['skill_id'] = skill_encoder.fit_transform(df['skill_id'])\n", "    df['user_id'] = user_encoder.fit_transform(df['user_id'])\n", "    \n", "    # Group into sequences\n", "    sequences = []\n", "    for _, group in df.groupby('user_id'):\n", "        seq = group[['skill_id', 'correct']].values\n", "        sequences.append(seq)\n", "    \n", "    return sequences, skill_encoder.classes_\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c2d92c44-fff9-4fb9-bac5-8674268cda80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset...\n", "Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the dataset\n", "DATA_PATH = \"../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading dataset...\")\n", "df = load_skill_builder_data(DATA_PATH)\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "89a1e840-9d44-4e69-9420-9eedb39a9c0a", "metadata": {}, "outputs": [], "source": ["\n", "# Load data\n", "DATA_PATH = \"../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"\n", "\n", "df = pd.read_csv(DATA_PATH, encoding='ISO-8859-1')  # Replace with your data\n", "sequences, skill_classes = preprocess_data(df)\n", "num_skills = len(skill_classes)"]}, {"cell_type": "code", "execution_count": 9, "id": "25db4577-2bc7-44ae-a17b-9005bb4345ed", "metadata": {}, "outputs": [], "source": ["from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Pad sequences to max length\n", "\n", "# Pad sequences to max length\n", "max_len = max(len(seq) for seq in sequences)\n", "X = pad_sequences(\n", "    sequences, \n", "    maxlen=max_len, \n", "    dtype='int32', \n", "    padding='post',  # Pad at the end\n", "    truncating='post',  # Truncate from end if exceeding max_len\n", "    value=0  # Padding value (0 typically means \"no skill\")\n", ")\n", "\n", "# Labels (shift correctness by 1)\n", "y = pad_sequences(\n", "    [seq[:, 1][1:] for seq in sequences],  # Skip first interaction\n", "    maxlen=max_len-1,  # Since we shifted\n", "    padding='post',\n", "    truncating='post',\n", "    value=-1  # Use -1 for padded labels (ignore in loss later)\n", ")\n", "# Split train/val\n", "X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2)"]}, {"cell_type": "code", "execution_count": 16, "id": "e5296cb3-57f4-4c82-845f-23a0b49fc435", "metadata": {}, "outputs": [], "source": ["from tensorflow.keras.models import Model\n", "from tensorflow.keras.layers import Input, Embedding, LSTM, Dense, Dropout, Concatenate\n", "from tensorflow.keras.optimizers import Adam\n", "import tensorflow as tf\n", "\n", "def build_dkt_model(hp, num_skills, max_len):\n", "    \"\"\"Builds a DKT model with hyperparameter tuning using Keras Tuner.\n", "    \n", "    Args:\n", "        hp: HyperParameters object from Keras Tuner\n", "        num_skills: Number of unique skills in the dataset\n", "        max_len: Maximum sequence length after padding\n", "        \n", "    Returns:\n", "        A compiled Keras model ready for training\n", "    \"\"\"\n", "    # Hyperparameters to tune\n", "    hidden_size = hp.Int('hidden_size', min_value=64, max_value=256, step=64)\n", "    num_layers = hp.Int('num_layers', min_value=1, max_value=3)\n", "    dropout_rate = hp.Float('dropout', min_value=0.1, max_value=0.5, step=0.1)\n", "    learning_rate = hp.Float('lr', min_value=1e-4, max_value=1e-2, sampling='log')\n", "    embed_size = hp.Int('embed_size', min_value=32, max_value=128, step=32)\n", "    \n", "    # Input layer - shape (batch_size, max_len, 2)\n", "    # Where each timestep has (skill_id, correct)\n", "    inputs = Input(shape=(max_len, 2), dtype='float32', name='sequence_input')\n", "    \n", "    # Separate skill and correctness inputs\n", "    skill_input = inputs[:, :, 0]  # shape (batch_size, max_len)\n", "    correct_input = inputs[:, :, 1]  # shape (batch_size, max_len)\n", "    \n", "    # Embedding layers\n", "    skill_embed = Embedding(\n", "        input_dim=num_skills,\n", "        output_dim=embed_size,\n", "        mask_zero=True,\n", "        name='skill_embedding'\n", "    )(skill_input)\n", "    \n", "    correct_embed = Embedding(\n", "        input_dim=2,  # 0=incorrect, 1=correct\n", "        output_dim=embed_size,\n", "        mask_zero=True,\n", "        name='correctness_embedding'\n", "    )(correct_input)\n", "    \n", "    # Combine embeddings\n", "    combined = Concatenate()([skill_embed, correct_embed])  # shape (batch_size, max_len, embed_size*2)\n", "    \n", "    # LSTM layers\n", "    x = combined\n", "    for i in range(num_layers):\n", "        return_sequences = i < (num_layers - 1)  # Only return sequences for intermediate layers\n", "        x = LSTM(\n", "            hidden_size,\n", "            return_sequences=return_sequences,\n", "            name=f'lstm_layer_{i}'\n", "        )(x)\n", "        x = Dropout(dropout_rate)(x)\n", "    \n", "    # Output layer - predict next skill correctness\n", "    outputs = Dense(num_skills, activation='sigmoid', dtype='float32')(x)\n", "\n", "    \n", "    # Create model\n", "    model = Model(inputs=inputs, outputs=outputs)\n", "    \n", "    # Compile with tuned learning rate\n", "    model.compile(\n", "        optimizer=Adam(learning_rate=learning_rate),\n", "        loss='binary_crossentropy',\n", "        metrics=['accuracy']\n", "    )\n", "    \n", "    return model"]}, {"cell_type": "code", "execution_count": 17, "id": "a8d3c353-6bd4-4c0d-af1a-32bd0f162fd6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reloading Tuner from dkt_tuning/dkt_opt/tuner0.json\n", "\n", "Search: Running Trial #3\n", "\n", "Value             |Best Value So Far |Hyperparameter\n", "128               |192               |hidden_size\n", "1                 |3                 |num_layers\n", "0.3               |0.3               |dropout\n", "0.00016102        |0.00011629        |lr\n", "64                |128               |embed_size\n", "\n", "Epoch 1/10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py\", line 274, in _try_run_and_update_trial\n", "    self._run_and_update_trial(trial, *fit_args, **fit_kwargs)\n", "  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py\", line 239, in _run_and_update_trial\n", "    results = self.run_trial(trial, *fit_args, **fit_kwargs)\n", "  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/tuner.py\", line 314, in run_trial\n", "    obj_value = self._build_and_fit_model(trial, *args, **copied_kwargs)\n", "  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/tuner.py\", line 233, in _build_and_fit_model\n", "    results = self.hypermodel.fit(hp, model, *args, **kwargs)\n", "  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/hypermodel.py\", line 149, in fit\n", "    return model.fit(*args, **kwargs)\n", "  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py\", line 122, in error_handler\n", "    raise e.with_traceback(filtered_tb) from None\n", "  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py\", line 122, in error_handler\n", "    raise e.with_traceback(filtered_tb) from None\n", "TypeError: Exception encountered when calling BroadcastTo.call().\n", "\n", "\u001b[1mFailed to convert elements of (None, 1606, 64) to Tensor. Consider casting elements to a supported type. See https://www.tensorflow.org/api_docs/python/tf/dtypes for supported TF dtypes.\u001b[0m\n", "\n", "Arguments received by BroadcastTo.call():\n", "  • x=tf.Tensor(shape=(None, 1606, 1), dtype=bool)\n"]}, {"ename": "RuntimeError", "evalue": "Number of consecutive failures exceeded the limit of 3.\nTraceback (most recent call last):\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py\", line 274, in _try_run_and_update_trial\n    self._run_and_update_trial(trial, *fit_args, **fit_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py\", line 239, in _run_and_update_trial\n    results = self.run_trial(trial, *fit_args, **fit_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/tuner.py\", line 314, in run_trial\n    obj_value = self._build_and_fit_model(trial, *args, **copied_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/tuner.py\", line 233, in _build_and_fit_model\n    results = self.hypermodel.fit(hp, model, *args, **kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/hypermodel.py\", line 149, in fit\n    return model.fit(*args, **kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py\", line 122, in error_handler\n    raise e.with_traceback(filtered_tb) from None\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py\", line 122, in error_handler\n    raise e.with_traceback(filtered_tb) from None\nTypeError: Exception encountered when calling BroadcastTo.call().\n\n\u001b[1mFailed to convert elements of (None, 1606, 64) to Tensor. Consider casting elements to a supported type. See https://www.tensorflow.org/api_docs/python/tf/dtypes for supported TF dtypes.\u001b[0m\n\nArguments received by BroadcastTo.call():\n  • x=tf.Tensor(shape=(None, 1606, 1), dtype=bool)\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[17], line 12\u001b[0m\n\u001b[1;32m      3\u001b[0m tuner \u001b[38;5;241m=\u001b[39m kt\u001b[38;5;241m.\u001b[39mRandomSearch(\n\u001b[1;32m      4\u001b[0m     partial(build_dkt_model, num_skills\u001b[38;5;241m=\u001b[39mnum_skills, max_len\u001b[38;5;241m=\u001b[39mmax_len),\n\u001b[1;32m      5\u001b[0m     objective\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mval_loss\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m      8\u001b[0m     project_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdkt_opt\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m      9\u001b[0m )\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# Run search\u001b[39;00m\n\u001b[0;32m---> 12\u001b[0m \u001b[43mtuner\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msearch\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     13\u001b[0m \u001b[43m    \u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mX_train\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     14\u001b[0m \u001b[43m    \u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43my_train\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     15\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvalidation_data\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mX_val\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_val\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     16\u001b[0m \u001b[43m    \u001b[49m\u001b[43mepochs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     17\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m64\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     18\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[43mtf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkeras\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mEarlyStopping\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpatience\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m3\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\n\u001b[1;32m     19\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     21\u001b[0m \u001b[38;5;66;03m# Best hyperparameters\u001b[39;00m\n\u001b[1;32m     22\u001b[0m best_hps \u001b[38;5;241m=\u001b[39m tuner\u001b[38;5;241m.\u001b[39mget_best_hyperparameters()[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py:235\u001b[0m, in \u001b[0;36mBaseTuner.search\u001b[0;34m(self, *fit_args, **fit_kwargs)\u001b[0m\n\u001b[1;32m    233\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mon_trial_begin(trial)\n\u001b[1;32m    234\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_try_run_and_update_trial(trial, \u001b[38;5;241m*\u001b[39mfit_args, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mfit_kwargs)\n\u001b[0;32m--> 235\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mon_trial_end\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtrial\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    236\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mon_search_end()\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py:339\u001b[0m, in \u001b[0;36mBaseTuner.on_trial_end\u001b[0;34m(self, trial)\u001b[0m\n\u001b[1;32m    333\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mon_trial_end\u001b[39m(\u001b[38;5;28mself\u001b[39m, trial):\n\u001b[1;32m    334\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Called at the end of a trial.\u001b[39;00m\n\u001b[1;32m    335\u001b[0m \n\u001b[1;32m    336\u001b[0m \u001b[38;5;124;03m    Args:\u001b[39;00m\n\u001b[1;32m    337\u001b[0m \u001b[38;5;124;03m        trial: A `Trial` instance.\u001b[39;00m\n\u001b[1;32m    338\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 339\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moracle\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mend_trial\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtrial\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    340\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msave()\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/oracle.py:108\u001b[0m, in \u001b[0;36msynchronized.<locals>.wrapped_func\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    106\u001b[0m     LOCKS[oracle]\u001b[38;5;241m.\u001b[39macquire()\n\u001b[1;32m    107\u001b[0m     THREADS[oracle] \u001b[38;5;241m=\u001b[39m thread_name\n\u001b[0;32m--> 108\u001b[0m ret_val \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    109\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m need_acquire:\n\u001b[1;32m    110\u001b[0m     THREADS[oracle] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/oracle.py:588\u001b[0m, in \u001b[0;36mOracle.end_trial\u001b[0;34m(self, trial)\u001b[0m\n\u001b[1;32m    586\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retry(trial):\n\u001b[1;32m    587\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mend_order\u001b[38;5;241m.\u001b[39mappend(trial\u001b[38;5;241m.\u001b[39mtrial_id)\n\u001b[0;32m--> 588\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_check_consecutive_failures\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    590\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_save_trial(trial)\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msave()\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/oracle.py:545\u001b[0m, in \u001b[0;36mOracle._check_consecutive_failures\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    543\u001b[0m     consecutive_failures \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m    544\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m consecutive_failures \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_consecutive_failed_trials:\n\u001b[0;32m--> 545\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m    546\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNumber of consecutive failures exceeded the limit \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    547\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mof \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_consecutive_failed_trials\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    548\u001b[0m         \u001b[38;5;241m+\u001b[39m (trial\u001b[38;5;241m.\u001b[39mmessage \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    549\u001b[0m     )\n", "\u001b[0;31mRuntimeError\u001b[0m: Number of consecutive failures exceeded the limit of 3.\nTraceback (most recent call last):\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py\", line 274, in _try_run_and_update_trial\n    self._run_and_update_trial(trial, *fit_args, **fit_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py\", line 239, in _run_and_update_trial\n    results = self.run_trial(trial, *fit_args, **fit_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/tuner.py\", line 314, in run_trial\n    obj_value = self._build_and_fit_model(trial, *args, **copied_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/tuner.py\", line 233, in _build_and_fit_model\n    results = self.hypermodel.fit(hp, model, *args, **kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/hypermodel.py\", line 149, in fit\n    return model.fit(*args, **kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py\", line 122, in error_handler\n    raise e.with_traceback(filtered_tb) from None\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py\", line 122, in error_handler\n    raise e.with_traceback(filtered_tb) from None\nTypeError: Exception encountered when calling BroadcastTo.call().\n\n\u001b[1mFailed to convert elements of (None, 1606, 64) to Tensor. Consider casting elements to a supported type. See https://www.tensorflow.org/api_docs/python/tf/dtypes for supported TF dtypes.\u001b[0m\n\nArguments received by BroadcastTo.call():\n  • x=tf.Tensor(shape=(None, 1606, 1), dtype=bool)\n"]}], "source": ["from functools import partial\n", "\n", "tuner = kt.RandomSearch(\n", "    partial(build_dkt_model, num_skills=num_skills, max_len=max_len),\n", "    objective='val_loss',\n", "    max_trials=20,\n", "    directory='dkt_tuning',\n", "    project_name='dkt_opt'\n", ")\n", "\n", "# Run search\n", "tuner.search(\n", "    x=X_train,\n", "    y=y_train,\n", "    validation_data=(X_val, y_val),\n", "    epochs=10,\n", "    batch_size=64,\n", "    callbacks=[tf.keras.callbacks.EarlyStopping(patience=3)]\n", ")\n", "\n", "# Best hyperparameters\n", "best_hps = tuner.get_best_hyperparameters()[0]\n", "print(f\"\"\"\n", "Best hyperparameters:\n", "- Hidden size: {best_hps.get('hidden_size')}\n", "- LSTM layers: {best_hps.get('num_layers')}\n", "- Dropout: {best_hps.get('dropout')}\n", "- Learning rate: {best_hps.get('lr')}\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 18, "id": "aa9ebae5-3c30-4bf4-95ae-ed6eff8a99da", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'best_hps' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[18], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Build best model\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m model \u001b[38;5;241m=\u001b[39m tuner\u001b[38;5;241m.\u001b[39mhypermodel\u001b[38;5;241m.\u001b[39mbuild(\u001b[43mbest_hps\u001b[49m)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# Train\u001b[39;00m\n\u001b[1;32m      5\u001b[0m history \u001b[38;5;241m=\u001b[39m model\u001b[38;5;241m.\u001b[39mfit(\n\u001b[1;32m      6\u001b[0m     X_train,\n\u001b[1;32m      7\u001b[0m     y_train,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     14\u001b[0m     ]\n\u001b[1;32m     15\u001b[0m )\n", "\u001b[0;31mNameError\u001b[0m: name 'best_hps' is not defined"]}], "source": ["# Build best model\n", "model = tuner.hypermodel.build(best_hps)\n", "\n", "# Train\n", "history = model.fit(\n", "    X_train,\n", "    y_train,\n", "    validation_data=(X_val, y_val),\n", "    epochs=50,\n", "    batch_size=64,\n", "    callbacks=[\n", "        tf.keras.callbacks.EarlyStopping(patience=5),\n", "        tf.keras.callbacks.ModelCheckpoint('best_dkt_model.h5', save_best_only=True)\n", "    ]\n", ")\n", "\n", "# Evaluate\n", "test_loss, test_acc = model.evaluate(X_val, y_val)\n", "print(f\"Test Accuracy: {test_acc:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "8092a162-24f1-46fe-9171-65dee4bade35", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}