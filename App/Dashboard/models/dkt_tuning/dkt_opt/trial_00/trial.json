{"trial_id": "00", "hyperparameters": {"space": [{"class_name": "Int", "config": {"name": "hidden_size", "default": null, "conditions": [], "min_value": 64, "max_value": 256, "step": 64, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "num_layers", "default": null, "conditions": [], "min_value": 1, "max_value": 3, "step": 1, "sampling": "linear"}}, {"class_name": "Float", "config": {"name": "dropout", "default": 0.1, "conditions": [], "min_value": 0.1, "max_value": 0.5, "step": 0.1, "sampling": "linear"}}, {"class_name": "Float", "config": {"name": "lr", "default": 0.0001, "conditions": [], "min_value": 0.0001, "max_value": 0.01, "step": null, "sampling": "log"}}, {"class_name": "Int", "config": {"name": "embed_size", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}], "values": {"hidden_size": 192, "num_layers": 3, "dropout": 0.30000000000000004, "lr": 0.0001162935622812577, "embed_size": 128}}, "metrics": {"metrics": {}}, "score": null, "best_step": 0, "status": "FAILED", "message": "Traceback (most recent call last):\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py\", line 274, in _try_run_and_update_trial\n    self._run_and_update_trial(trial, *fit_args, **fit_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/base_tuner.py\", line 239, in _run_and_update_trial\n    results = self.run_trial(trial, *fit_args, **fit_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/tuner.py\", line 314, in run_trial\n    obj_value = self._build_and_fit_model(trial, *args, **copied_kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/tuner.py\", line 233, in _build_and_fit_model\n    results = self.hypermodel.fit(hp, model, *args, **kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras_tuner/src/engine/hypermodel.py\", line 149, in fit\n    return model.fit(*args, **kwargs)\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py\", line 122, in error_handler\n    raise e.with_traceback(filtered_tb) from None\n  File \"/home/<USER>/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/keras/src/utils/traceback_utils.py\", line 122, in error_handler\n    raise e.with_traceback(filtered_tb) from None\nTypeError: Exception encountered when calling BroadcastTo.call().\n\n\u001b[1mFailed to convert elements of (None, 1606, 128) to Tensor. Consider casting elements to a supported type. See https://www.tensorflow.org/api_docs/python/tf/dtypes for supported TF dtypes.\u001b[0m\n\nArguments received by BroadcastTo.call():\n  • x=tf.Tensor(shape=(None, 1606, 1), dtype=bool)\n"}