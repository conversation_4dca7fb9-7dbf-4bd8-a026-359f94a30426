{"trial_id": "02", "hyperparameters": {"space": [{"class_name": "Int", "config": {"name": "hidden_size", "default": null, "conditions": [], "min_value": 64, "max_value": 256, "step": 64, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "num_layers", "default": null, "conditions": [], "min_value": 1, "max_value": 3, "step": 1, "sampling": "linear"}}, {"class_name": "Float", "config": {"name": "dropout", "default": 0.1, "conditions": [], "min_value": 0.1, "max_value": 0.5, "step": 0.1, "sampling": "linear"}}, {"class_name": "Float", "config": {"name": "lr", "default": 0.0001, "conditions": [], "min_value": 0.0001, "max_value": 0.01, "step": null, "sampling": "log"}}, {"class_name": "Int", "config": {"name": "embed_size", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}], "values": {"hidden_size": 128, "num_layers": 1, "dropout": 0.30000000000000004, "lr": 0.00016101585915849793, "embed_size": 64}}, "metrics": {"metrics": {}}, "score": null, "best_step": 0, "status": "RUNNING", "message": null}