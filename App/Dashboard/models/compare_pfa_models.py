#!/usr/bin/env python3
"""
Comparison script between original PFA and enhanced PFA models.
This script demonstrates the improvements achieved with the enhanced training approach.
"""

import pandas as pd
import numpy as np
import time
from sklearn.metrics import accuracy_score, roc_auc_score, log_loss
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns

# Import both models
from train_pfa import PerformanceFactorsAnalysis, load_skill_builder_data

def create_original_pfa():
    """Create original PFA model with basic configuration"""
    return PerformanceFactorsAnalysis(
        learning_rate=0.1,
        regularization=0.01,
        max_epochs=1,  # Single epoch like original
        use_adaptive_lr=False,
        use_time_decay=False,
        use_separate_sf_rates=False,
        early_stopping_patience=1
    )

def create_enhanced_pfa():
    """Create enhanced PFA model with advanced configuration"""
    return PerformanceFactorsAnalysis(
        learning_rate=0.01,
        regularization=0.001,
        max_epochs=15,
        convergence_threshold=1e-6,
        use_adaptive_lr=True,
        use_time_decay=True,
        use_separate_sf_rates=True,
        early_stopping_patience=5
    )

def evaluate_model(model, test_df):
    """Evaluate model performance on test data"""
    predictions = []
    actuals = []
    
    # Reset opportunities for fair evaluation
    eval_opportunities = {}
    
    for idx, row in test_df.iterrows():
        user = row['user_id']
        problem = row['problem_id']
        skill = row['skill_name']
        correct = row['correct']
        
        # Get opportunity count
        if skill not in eval_opportunities:
            eval_opportunities[skill] = {}
        if user not in eval_opportunities[skill]:
            eval_opportunities[skill][user] = 0
        
        opportunity = eval_opportunities[skill][user]
        
        # Make prediction
        pred = model._predict_single_enhanced(user, problem, skill, opportunity)
        predictions.append(pred)
        actuals.append(correct)
        
        # Update opportunity
        eval_opportunities[skill][user] += 1
    
    # Calculate metrics
    predictions = np.array(predictions)
    actuals = np.array(actuals)
    
    accuracy = accuracy_score(actuals, (predictions > 0.5).astype(int))
    try:
        auc = roc_auc_score(actuals, predictions)
    except:
        auc = None
    
    logloss = log_loss(actuals, np.clip(predictions, 1e-8, 1-1e-8))
    
    return {
        'accuracy': accuracy,
        'auc': auc,
        'log_loss': logloss,
        'predictions': predictions,
        'actuals': actuals
    }

def main():
    """Main comparison function"""
    print("=" * 60)
    print("PFA MODEL COMPARISON: Original vs Enhanced")
    print("=" * 60)
    
    # Load and prepare data
    print("\n1. Loading and preparing data...")
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    df = load_skill_builder_data(DATA_PATH)
    
    # Clean data
    essential_cols = ['user_id', 'problem_id', 'skill_name', 'correct']
    df_clean = df.dropna(subset=essential_cols).copy()
    df_clean['correct'] = df_clean['correct'].astype(int)
    df_clean['user_id'] = df_clean['user_id'].astype(str)
    df_clean['problem_id'] = df_clean['problem_id'].astype(str)
    
    # Use a subset for faster comparison (optional)
    if len(df_clean) > 50000:
        df_clean = df_clean.sample(n=50000, random_state=42)
        print(f"Using subset of {len(df_clean):,} interactions for comparison")
    
    # Split data
    unique_users = df_clean['user_id'].unique()
    train_users, test_users = train_test_split(unique_users, test_size=0.3, random_state=42)
    
    train_df = df_clean[df_clean['user_id'].isin(train_users)].copy()
    test_df = df_clean[df_clean['user_id'].isin(test_users)].copy()
    
    print(f"Training set: {len(train_df):,} interactions")
    print(f"Test set: {len(test_df):,} interactions")
    
    # Train original PFA model
    print("\n2. Training Original PFA Model...")
    original_pfa = create_original_pfa()
    
    start_time = time.time()
    original_pfa.fit(train_df, validation_split=0.2)
    original_training_time = time.time() - start_time
    
    print(f"Original PFA training time: {original_training_time:.2f} seconds")
    
    # Train enhanced PFA model
    print("\n3. Training Enhanced PFA Model...")
    enhanced_pfa = create_enhanced_pfa()
    
    start_time = time.time()
    enhanced_pfa.fit(train_df, validation_split=0.2)
    enhanced_training_time = time.time() - start_time
    
    print(f"Enhanced PFA training time: {enhanced_training_time:.2f} seconds")
    
    # Evaluate both models
    print("\n4. Evaluating Models...")
    
    print("Evaluating Original PFA...")
    original_results = evaluate_model(original_pfa, test_df)
    
    print("Evaluating Enhanced PFA...")
    enhanced_results = evaluate_model(enhanced_pfa, test_df)
    
    # Compare results
    print("\n" + "=" * 60)
    print("COMPARISON RESULTS")
    print("=" * 60)
    
    print(f"\n{'Metric':<20} {'Original PFA':<15} {'Enhanced PFA':<15} {'Improvement':<15}")
    print("-" * 70)
    
    # Accuracy
    acc_improvement = (enhanced_results['accuracy'] - original_results['accuracy']) / original_results['accuracy'] * 100
    print(f"{'Accuracy':<20} {original_results['accuracy']:<15.4f} {enhanced_results['accuracy']:<15.4f} {acc_improvement:+.2f}%")
    
    # AUC
    if original_results['auc'] and enhanced_results['auc']:
        auc_improvement = (enhanced_results['auc'] - original_results['auc']) / original_results['auc'] * 100
        print(f"{'AUC':<20} {original_results['auc']:<15.4f} {enhanced_results['auc']:<15.4f} {auc_improvement:+.2f}%")
    
    # Log Loss (lower is better)
    logloss_improvement = (original_results['log_loss'] - enhanced_results['log_loss']) / original_results['log_loss'] * 100
    print(f"{'Log Loss':<20} {original_results['log_loss']:<15.4f} {enhanced_results['log_loss']:<15.4f} {logloss_improvement:+.2f}%")
    
    # Training time
    time_ratio = enhanced_training_time / original_training_time
    print(f"{'Training Time (s)':<20} {original_training_time:<15.2f} {enhanced_training_time:<15.2f} {time_ratio:.2f}x")
    
    # Model complexity
    original_stats = original_pfa.get_model_statistics()
    enhanced_stats = enhanced_pfa.get_model_statistics()
    
    print(f"\n{'Model Statistics':<20} {'Original':<15} {'Enhanced':<15}")
    print("-" * 50)
    print(f"{'Training Epochs':<20} {original_stats['training_epochs']:<15} {enhanced_stats['training_epochs']:<15}")
    print(f"{'Best Epoch':<20} {original_stats['best_epoch']:<15} {enhanced_stats['best_epoch']:<15}")
    
    if enhanced_stats.get('final_train_loss') and enhanced_stats.get('final_val_loss'):
        print(f"{'Final Train Loss':<20} {'-':<15} {enhanced_stats['final_train_loss']:<15.6f}")
        print(f"{'Final Val Loss':<20} {'-':<15} {enhanced_stats['final_val_loss']:<15.6f}")
    
    # Feature comparison
    print(f"\n{'Enhanced Features':<30} {'Status'}")
    print("-" * 40)
    print(f"{'Adaptive Learning Rates':<30} {'✅ Enabled' if enhanced_stats.get('adaptive_learning_rates') else '❌ Disabled'}")
    print(f"{'Time Decay (Forgetting)':<30} {'✅ Enabled' if enhanced_stats.get('time_decay_enabled') else '❌ Disabled'}")
    print(f"{'Separate Success/Failure Rates':<30} {'✅ Enabled' if enhanced_stats.get('num_success_rates') else '❌ Disabled'}")
    print(f"{'Early Stopping':<30} {'✅ Enabled'}")
    print(f"{'L2 Regularization':<30} {'✅ Enabled'}")
    
    # Summary
    print(f"\n{'=' * 60}")
    print("SUMMARY")
    print("=" * 60)
    print(f"✅ Enhanced PFA shows {acc_improvement:+.2f}% accuracy improvement")
    print(f"✅ Enhanced PFA shows {logloss_improvement:+.2f}% log loss improvement")
    if original_results['auc'] and enhanced_results['auc']:
        print(f"✅ Enhanced PFA shows {auc_improvement:+.2f}% AUC improvement")
    print(f"✅ Enhanced PFA uses advanced training techniques")
    print(f"✅ Enhanced PFA includes regularization and early stopping")
    print(f"⚠️  Enhanced PFA takes {time_ratio:.1f}x longer to train (but more epochs)")
    
    return {
        'original_results': original_results,
        'enhanced_results': enhanced_results,
        'original_stats': original_stats,
        'enhanced_stats': enhanced_stats
    }

if __name__ == "__main__":
    results = main()
