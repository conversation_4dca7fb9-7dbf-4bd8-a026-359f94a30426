# Bayesian Knowledge Tracing (BKT) Model

This module provides a simple implementation of the Bayesian Knowledge Tracing (BKT) algorithm for educational data, specifically designed to work with the 2009 Skill Builder dataset.

## Overview

Bayesian Knowledge Tracing is a probabilistic model used to estimate a student's mastery of skills over time based on their performance on related tasks. This implementation allows you to train a BKT model on student response data and use it to predict the probability of correct responses in the future.

## File Structure

- `train_bkt.py`: Main script for training the BKT model and saving it to disk.
- `output/`: Directory where the trained model is saved (`bkt_model.joblib`).
- `example_bkt_usage.py`: Example script for loading the trained model and making predictions.

## Dataset

The model is designed to work with the [2009 Skill Builder dataset](../../EduData/datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv). The dataset should have at least the following columns:
- `user_id`: Unique identifier for each student
- `problem_id`: Unique identifier for each problem
- `skill_name`: Name of the skill being assessed
- `correct`: 1 if the student answered correctly, 0 otherwise

## Training the Model

To train the BKT model on the dataset and save it:

```bash
python train_bkt.py
```

This will:
- Load the dataset from `../../EduData/datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv`
- Train a BKT model for each skill
- Save the trained model to `output/bkt_model.joblib`

## Making Predictions

To use the trained model to predict the probability of a correct response for a new user history:

```bash
python example_bkt_usage.py
```

You can modify `user_history` and `skill` in `example_bkt_usage.py` to test different scenarios.

## Model Details

- The BKT model tracks the probability that a student knows a skill after each response.
- Parameters (`p_init`, `p_learn`, `p_guess`, `p_slip`) are set to default values but can be tuned.
- The model is saved using `joblib` for easy loading and inference.

## Customization

- To change model parameters, edit the `BayesianKnowledgeTracer` initialization in `train_bkt.py`.
- To use a different dataset, update the `DATA_PATH` in `train_bkt.py`.

## Requirements

- Python 3.7+
- pandas
- numpy
- scikit-learn
- joblib

Install dependencies with:

```bash
pip install -r ../../requirements.txt
```

## References
- [Corbett, A. T., & Anderson, J. R. (1995). Knowledge tracing: Modeling the acquisition of procedural knowledge. User Modeling and User-Adapted Interaction, 4(4), 253-278.](https://link.springer.com/article/10.1007/BF01099821)

---
For questions or improvements, please contact the project maintainers. 