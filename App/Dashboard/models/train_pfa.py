import os
import pandas as pd
import numpy as np
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import OneHotEncoder
from joblib import dump, load


def compute_pfa_features(df, user_col='user_id', skill_col='skill_name', correct_col='correct'):
    """
    For each row, compute the number of prior correct and incorrect attempts for the student-skill pair.
    Returns a DataFrame with n_correct, n_incorrect as features.
    """
    df = df.copy()
    df['n_correct'] = 0
    df['n_incorrect'] = 0
    # Sort by user, skill, and order (if available)
    if 'order_id' in df.columns:
        df = df.sort_values([user_col, skill_col, 'order_id'])
    else:
        df = df.sort_values([user_col, skill_col])
    # Compute prior correct/incorrect counts
    group = df.groupby([user_col, skill_col])
    for name, idx in group.groups.items():
        corrects = df.loc[idx, correct_col].shift().fillna(0).cumsum()
        incorrects = (1 - df.loc[idx, correct_col]).shift().fillna(0).cumsum()
        df.loc[idx, 'n_correct'] = corrects
        df.loc[idx, 'n_incorrect'] = incorrects
    return df


def train_pfa_classic(df, output_path):
    """
    Train classic PFA (n_correct, n_incorrect as features) using LogisticRegression.
    """
    print("Training classic PFA (n_correct, n_incorrect features)...")
    features = ['n_correct', 'n_incorrect']
    X = df[features].values
    y = df['correct'].values
    model = LogisticRegression(solver='lbfgs', max_iter=1000)
    model.fit(X, y)
    dump(model, output_path)
    print(f"Classic PFA model saved to {output_path}")
    return model


def train_pfa_plus(df, output_path):
    """
    Train PFA+ (n_correct, n_incorrect, student, skill as categorical features) using LogisticRegression.
    """
    print("Training PFA+ (n_correct, n_incorrect, student, skill categorical features)...")
    features = ['n_correct', 'n_incorrect', 'user_id', 'skill_name']
    X_num = df[['n_correct', 'n_incorrect']].values
    X_cat = df[['user_id', 'skill_name']].astype(str)
    enc = OneHotEncoder(sparse=False, handle_unknown='ignore')
    X_cat_enc = enc.fit_transform(X_cat)
    X = np.hstack([X_num, X_cat_enc])
    y = df['correct'].values
    model = LogisticRegression(solver='lbfgs', max_iter=1000)
    model.fit(X, y)
    # Save both model and encoder
    dump({'model': model, 'encoder': enc}, output_path)
    print(f"PFA+ model saved to {output_path}")
    return model, enc


def predict_pfa_classic(model_path, n_correct, n_incorrect):
    model = load(model_path)
    X = np.array([[n_correct, n_incorrect]])
    proba = model.predict_proba(X)[0, 1]
    return proba


def predict_pfa_plus(model_path, n_correct, n_incorrect, user_id, skill_name):
    data = load(model_path)
    model = data['model']
    enc = data['encoder']
    X_num = np.array([[n_correct, n_incorrect]])
    X_cat = np.array([[str(user_id), str(skill_name)]])
    X_cat_enc = enc.transform(X_cat)
    X = np.hstack([X_num, X_cat_enc])
    proba = model.predict_proba(X)[0, 1]
    return proba


def load_skill_builder_data(csv_path):
    df = pd.read_csv(csv_path, encoding='latin1')
    return df


def main():
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    OUTPUT_CLASSIC = "models/output/pfa_classic_model.joblib"
    OUTPUT_PLUS = "models/output/pfa_plus_model.joblib"
    print("Loading data...")
    df = load_skill_builder_data(DATA_PATH)
    print("Computing PFA features...")
    df = compute_pfa_features(df)
    # Remove rows with missing values in essential columns
    df = df.dropna(subset=['user_id', 'skill_name', 'correct'])
    # Ensure correct types
    df['correct'] = df['correct'].astype(int)
    df['user_id'] = df['user_id'].astype(str)
    df['skill_name'] = df['skill_name'].astype(str)
    # Train classic PFA
    train_pfa_classic(df, OUTPUT_CLASSIC)
    # Train PFA+
    train_pfa_plus(df, OUTPUT_PLUS)

if __name__ == "__main__":
    main()