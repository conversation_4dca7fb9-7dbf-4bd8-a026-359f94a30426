# Performance Factors Analysis (PFA) Model

This module provides an implementation of the Performance Factors Analysis (PFA) algorithm for educational data, specifically designed to work with the 2009 Skill Builder dataset.

## Overview

Performance Factors Analysis (PFA) is a logistic regression-based approach to knowledge tracing that models student performance as a function of:
- **Student ability** (bias term for each student)
- **Problem difficulty** (bias term for each problem)
- **Learning from previous attempts** on the same skill (opportunity count × learning rate)

PFA offers several advantages over traditional Bayesian Knowledge Tracing (BKT):
- **Interpretable parameters**: Each parameter has a clear meaning
- **Flexible learning rates**: Different skills can have different learning rates
- **Problem-specific modeling**: Accounts for individual problem difficulties
- **Student-specific modeling**: Accounts for individual student abilities

## File Structure

- `train_pfa.py`: Main script for training the PFA model and saving it to disk
- `pfa_math.ipynb`: Jupyter notebook with complete training workflow and analysis
- `output/`: Directory where the trained model is saved (`pfa_model.joblib`)
- `example_pfa_usage.py`: Example script for loading the trained model and making predictions
- `readme_pfa.md`: This documentation file

## Dataset

The model is designed to work with the [2009 Skill Builder dataset](../../EduData/datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv). The dataset should have at least the following columns:
- `user_id`: Unique identifier for each student
- `problem_id`: Unique identifier for each problem
- `skill_name`: Name of the skill being assessed
- `correct`: 1 if the student answered correctly, 0 otherwise
- `order_id`: Temporal ordering of interactions (for maintaining sequence)

## Model Architecture

### PFA Formula
The probability of a correct response is modeled as:

```
P(correct = 1) = σ(θ_s - β_p + γ_k × opportunity_count)
```

Where:
- `θ_s`: Student ability parameter for student s
- `β_p`: Problem difficulty parameter for problem p
- `γ_k`: Learning rate parameter for skill k
- `opportunity_count`: Number of previous attempts on skill k by student s
- `σ()`: Sigmoid function

### Parameters
- **Student Abilities** (`θ_s`): Baseline performance level for each student
- **Problem Difficulties** (`β_p`): Difficulty level for each problem
- **Skill Learning Rates** (`γ_k`): How quickly students learn each skill
- **Opportunity Counters**: Track how many times each student has attempted each skill

## Training the Model

### Using the Training Script
```bash
python train_pfa.py
```

### Using the Jupyter Notebook
```bash
jupyter notebook pfa_math.ipynb
```

The training process:
1. Loads and cleans the dataset
2. Initializes parameters for all students, problems, and skills
3. Trains using stochastic gradient descent
4. Updates parameters based on prediction errors
5. Saves the trained model

## Making Predictions

### Example 1: Using (problem_id, correct) pairs
```python
from train_pfa import PerformanceFactorsAnalysis

# Load model
pfa = PerformanceFactorsAnalysis.load("output/pfa_model.joblib")

# User history as (problem_id, correct) tuples
user_history = [(12345, 1), (12346, 0), (12347, 1)]
skill = 'Addition and Subtraction Integers'
user_id = 'user_123'

# Make predictions
predictions = pfa.predict_proba(user_history, skill, user_id)
print(f"Predicted probabilities: {predictions}")
```

### Example 2: Using correct values only
```python
# User history as correct values only
user_history = [1, 0, 1, 1]  # correct values
skill = 'Equation Solving Two or Fewer Steps'
user_id = 'user_456'
next_problem_id = 54321

# Make predictions (requires problem_id for next prediction)
predictions = pfa.predict_proba(user_history, skill, user_id, next_problem_id)
print(f"Predicted probabilities: {predictions}")
```

## Model Analysis

### Skill Parameters
```python
# Get learning parameters for a specific skill
skill_params = pfa.get_skill_parameters('Addition and Subtraction Integers')
print(f"Learning rate: {skill_params['learning_rate']}")
print(f"Total opportunities: {skill_params['total_opportunities']}")
```

### Student Abilities
```python
# Get ability parameter for a specific student
ability = pfa.get_student_ability('user_123')
print(f"Student ability: {ability}")
```

### Problem Difficulties
```python
# Get difficulty parameter for a specific problem
difficulty = pfa.get_problem_difficulty('problem_456')
print(f"Problem difficulty: {difficulty}")
```

## Comparison with BKT

| Feature | PFA | BKT |
|---------|-----|-----|
| **Model Type** | Logistic Regression | Hidden Markov Model |
| **Parameters** | Student ability, problem difficulty, skill learning rate | P(init), P(learn), P(guess), P(slip) |
| **Interpretability** | High - each parameter has clear meaning | Medium - parameters are probabilities |
| **Flexibility** | High - different learning rates per skill | Low - same parameters for all skills |
| **Problem Modeling** | Individual problem difficulties | No problem-specific modeling |
| **Student Modeling** | Individual student abilities | No student-specific modeling |
| **Learning Rate** | Varies by skill | Fixed across all skills |
| **Computational Cost** | Lower | Higher |

## Advantages of PFA

1. **Interpretable Parameters**: Each parameter has a clear educational meaning
2. **Skill-Specific Learning**: Different skills can have different learning rates
3. **Problem-Specific Modeling**: Accounts for individual problem difficulties
4. **Student-Specific Modeling**: Accounts for individual student abilities
5. **Computational Efficiency**: Faster training and prediction than BKT
6. **Flexibility**: Easy to extend with additional features

## Limitations

1. **Linear Assumption**: Assumes linear relationship between features and log-odds
2. **No Skill Interactions**: Doesn't model relationships between different skills
3. **Fixed Learning Rate**: Learning rate is constant within each skill
4. **No Forgetting**: Doesn't model forgetting or skill decay over time

## Requirements

- Python 3.7+
- pandas
- numpy
- scikit-learn
- joblib
- matplotlib (for notebook)
- seaborn (for notebook)

Install dependencies with:
```bash
pip install -r ../../requirements.txt
```

## Usage Examples

### Training and Saving
```python
from train_pfa import train_and_save_pfa_model

# Train and save model
pfa_model = train_and_save_pfa_model(
    data_path="path/to/dataset.csv",
    output_path="output/pfa_model.joblib"
)
```

### Loading and Predicting
```python
from train_pfa import PerformanceFactorsAnalysis

# Load model
pfa = PerformanceFactorsAnalysis.load("output/pfa_model.joblib")

# Make predictions
history = [(12345, 1), (12346, 0)]
predictions = pfa.predict_proba(history, "skill_name", "user_id")
```

### Analyzing Parameters
```python
# Get all skill learning rates
for skill, lr in pfa.skill_learning_rates.items():
    print(f"{skill}: {lr:.3f}")

# Get student abilities
for user, ability in pfa.student_abilities.items():
    print(f"User {user}: {ability:.3f}")
```

## References

- **Pavlik, P. I., Cen, H., & Koedinger, K. R. (2009).** Performance Factors Analysis: A New Alternative to Knowledge Tracing. *Proceedings of the 14th International Conference on Artificial Intelligence in Education*, 531-538.

- **Cen, H., Koedinger, K. R., & Junker, B. (2006).** Learning Factors Analysis: A General Method for Cognitive Model Evaluation and Improvement. *Proceedings of the 8th International Conference on Intelligent Tutoring Systems*, 164-175.

- **Pavlik, P. I., & Anderson, J. R. (2005).** Practice and Forgetting Effects on Vocabulary Memory: An Activation-Based Model of the Spacing Effect. *Cognitive Science*, 29(4), 559-586.

## Contributing

To improve the PFA implementation:
1. Add regularization options
2. Implement cross-validation
3. Add feature engineering capabilities
4. Extend to multi-skill problems
5. Add forgetting mechanisms

---

For questions or improvements, please contact the project maintainers. 