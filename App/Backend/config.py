from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Adaptive Learning Platform API"
    
    # Security
    SECRET_KEY: str = "your-secret-key-here"  # Should be loaded from env in production
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Database
    DB_HOST: str = "db"
    DB_PORT: int = 3306
    DB_NAME: str = "adaptive_learning"
    DB_USERNAME: str = "app_user"
    DB_PASSWORD: str = "app_password"  # Should be loaded from env in production

    class Config:
        env_file = ".env"

settings = Settings()