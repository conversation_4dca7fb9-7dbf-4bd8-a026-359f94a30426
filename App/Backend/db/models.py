from sqlalchemy import Column, Integer, String, Text, Float, Boolean, Foreign<PERSON>ey, Table, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from db.database import Base

# Association tables for many-to-many relationships
course_skill_association = Table(
    'course_skill_association', 
    Base.metadata,
    Column('course_id', Integer, ForeignKey('courses.id')),
    Column('skill_id', Integer, ForeignKey('skills.id'))
)

module_skill_association = Table(
    'module_skill_association', 
    Base.metadata,
    Column('module_id', Integer, ForeignKey('modules.id')),
    Column('skill_id', Integer, ForeignKey('skills.id'))
)

user_course_association = Table(
    'user_course_association', 
    Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id')),
    <PERSON>umn('course_id', Integer, ForeignKey('courses.id')),
    <PERSON>umn('enrollment_date', DateTime, default=func.now())
)

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True)
    first_name = Column(String(100))
    last_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(50))  # student, teacher, admin, etc.
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    courses = relationship("Course", secondary=user_course_association, back_populates="users")
    created_courses = relationship("Course", back_populates="creator")
    progress = relationship("UserProgress", back_populates="user")
    knowledge_states = relationship("KnowledgeState", back_populates="user")


class Course(Base):
    __tablename__ = "courses"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), index=True)
    description = Column(Text)
    domain = Column(String(100), index=True)
    creator_id = Column(Integer, ForeignKey("users.id"))
    is_published = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    creator = relationship("User", back_populates="created_courses")
    modules = relationship("Module", back_populates="course")
    skills = relationship("Skill", secondary=course_skill_association, back_populates="courses")
    users = relationship("User", secondary=user_course_association, back_populates="courses")


class Module(Base):
    __tablename__ = "modules"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), index=True)
    description = Column(Text)
    content = Column(Text)
    order = Column(Integer)  # Order within the course
    course_id = Column(Integer, ForeignKey("courses.id"))
    difficulty = Column(Float, default=1.0)  # 1.0 to 5.0
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    course = relationship("Course", back_populates="modules")
    skills = relationship("Skill", secondary=module_skill_association, back_populates="modules")
    assessments = relationship("Assessment", back_populates="module")


class Skill(Base):
    __tablename__ = "skills"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), index=True, unique=True)
    description = Column(Text)
    domain = Column(String(100), index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    courses = relationship("Course", secondary=course_skill_association, back_populates="skills")
    modules = relationship("Module", secondary=module_skill_association, back_populates="skills")
    knowledge_states = relationship("KnowledgeState", back_populates="skill")


class Assessment(Base):
    __tablename__ = "assessments"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255))
    description = Column(Text)
    module_id = Column(Integer, ForeignKey("modules.id"))
    assessment_type = Column(String(50))  # quiz, assignment, project, etc.
    content = Column(Text)  # JSON content with questions and answers
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    module = relationship("Module", back_populates="assessments")
    user_responses = relationship("UserResponse", back_populates="assessment")


class UserResponse(Base):
    __tablename__ = "user_responses"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    assessment_id = Column(Integer, ForeignKey("assessments.id"))
    response_data = Column(Text)  # JSON with user's responses
    score = Column(Float)
    time_taken = Column(Integer)  # in seconds
    created_at = Column(DateTime, default=func.now())

    # Relationships
    assessment = relationship("Assessment", back_populates="user_responses")


class UserProgress(Base):
    __tablename__ = "user_progress"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    module_id = Column(Integer, ForeignKey("modules.id"))
    status = Column(String(50))  # not_started, in_progress, completed
    progress_percentage = Column(Float, default=0.0)
    last_accessed = Column(DateTime)
    completion_date = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="progress")


class KnowledgeState(Base):
    __tablename__ = "knowledge_states"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    skill_id = Column(Integer, ForeignKey("skills.id"))
    mastery_level = Column(Float, default=0.0)  # 0.0 to 1.0
    confidence = Column(Float, default=0.0)  # 0.0 to 1.0
    last_updated = Column(DateTime, default=func.now())

    # Relationships
    user = relationship("User", back_populates="knowledge_states")
    skill = relationship("Skill", back_populates="knowledge_states")