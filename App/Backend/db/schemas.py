from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional
from datetime import datetime

# Base schemas
class UserBase(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    role: str

class CourseBase(BaseModel):
    title: str
    description: str
    domain: str

class ModuleBase(BaseModel):
    title: str
    description: str
    content: str
    order: int
    difficulty: Optional[float] = 1.0

class SkillBase(BaseModel):
    name: str
    description: Optional[str] = None
    domain: Optional[str] = None

class AssessmentBase(BaseModel):
    title: str
    description: Optional[str] = None
    assessment_type: str
    content: str  # JSON string

class UserResponseBase(BaseModel):
    response_data: str  # JSON string
    score: Optional[float] = None
    time_taken: int

class UserProgressBase(BaseModel):
    status: str
    progress_percentage: float = 0.0

class KnowledgeStateBase(BaseModel):
    mastery_level: float = 0.0
    confidence: float = 0.0

# Create schemas
class UserCreate(UserBase):
    password: str

class CourseCreate(CourseBase):
    is_published: bool = False
    skill_ids: List[int] = []

class ModuleCreate(ModuleBase):
    course_id: int
    skill_ids: List[int] = []

class SkillCreate(SkillBase):
    pass

class AssessmentCreate(AssessmentBase):
    module_id: int

class UserResponseCreate(UserResponseBase):
    assessment_id: int

class UserProgressCreate(UserProgressBase):
    module_id: int

class KnowledgeStateCreate(KnowledgeStateBase):
    skill_id: int

# Response schemas
class Skill(SkillBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class Module(ModuleBase):
    id: int
    course_id: int
    skills: List[Skill] = []
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class Course(CourseBase):
    id: int
    creator_id: int
    is_published: bool
    modules: List[Module] = []
    skills: List[Skill] = []
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class Assessment(AssessmentBase):
    id: int
    module_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class UserResponse(UserResponseBase):
    id: int
    user_id: int
    assessment_id: int
    created_at: datetime

    class Config:
        orm_mode = True

class UserProgress(UserProgressBase):
    id: int
    user_id: int
    module_id: int
    last_accessed: Optional[datetime] = None
    completion_date: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class KnowledgeState(KnowledgeStateBase):
    id: int
    user_id: int
    skill_id: int
    last_updated: datetime

    class Config:
        orm_mode = True

class User(UserBase):
    id: int
    is_active: bool
    created_courses: List[Course] = []
    courses: List[Course] = []
    progress: List[UserProgress] = []
    knowledge_states: List[KnowledgeState] = []
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    email: Optional[str] = None