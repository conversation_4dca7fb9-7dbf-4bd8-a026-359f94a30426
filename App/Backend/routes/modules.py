from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from db import database, models, schemas
from auth.auth import get_current_active_user

router = APIRouter()

@router.post("/", response_model=schemas.Module)
async def create_module(
    module: schemas.ModuleCreate,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if course exists
    course = db.query(models.Course).filter(models.Course.id == module.course_id).first()
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    # Check if user has permission to add modules to this course
    if course.creator_id != current_user.id and current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Not authorized to add modules to this course")
    
    # Create new module
    db_module = models.Module(
        title=module.title,
        description=module.description,
        content=module.content,
        order=module.order,
        difficulty=module.difficulty,
        course_id=module.course_id
    )
    db.add(db_module)
    db.commit()
    db.refresh(db_module)
    
    # Add skills to module
    if module.skill_ids:
        skills = db.query(models.Skill).filter(models.Skill.id.in_(module.skill_ids)).all()
        for skill in skills:
            db_module.skills.append(skill)
        db.commit()
        db.refresh(db_module)
    
    return db_module

@router.get("/course/{course_id}", response_model=List[schemas.Module])
async def read_course_modules(
    course_id: int,
    db: Session = Depends(database.get_db)
):
    # Check if course exists
    course = db.query(models.Course).filter(models.Course.id == course_id).first()
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    # Get modules for course
    modules = db.query(models.Module).filter(models.Module.course_id == course_id).order_by(models.Module.order).all()
    return modules

@router.get("/{module_id}", response_model=schemas.Module)
async def read_module(
    module_id: int,
    db: Session = Depends(database.get_db)
):
    module = db.query(models.Module).filter(models.Module.id == module_id).first()
    if module is None:
        raise HTTPException(status_code=404, detail="Module not found")
    return module

@router.put("/{module_id}", response_model=schemas.Module)
async def update_module(
    module_id: int,
    module: schemas.ModuleCreate,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if module exists
    db_module = db.query(models.Module).filter(models.Module.id == module_id).first()
    if db_module is None:
        raise HTTPException(status_code=404, detail="Module not found")
    
    # Check if user has permission to update
    course = db.query(models.Course).filter(models.Course.id == db_module.course_id).first()
    if course.creator_id != current_user.id and current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Not authorized to update this module")
    
    # Update module fields
    for field, value in module.dict(exclude={"skill_ids"}).items():
        setattr(db_module, field, value)
    
    # Update skills
    if module.skill_ids is not None:
        # Clear existing skills
        db_module.skills = []
        
        # Add new skills
        skills = db.query(models.Skill).filter(models.Skill.id.in_(module.skill_ids)).all()
        for skill in skills:
            db_module.skills.append(skill)
    
    db.commit()
    db.refresh(db_module)
    return db_module

@router.delete("/{module_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_module(
    module_id: int,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if module exists
    db_module = db.query(models.Module).filter(models.Module.id == module_id).first()
    if db_module is None:
        raise HTTPException(status_code=404, detail="Module not found")
    
    # Check if user has permission to delete
    course = db.query(models.Course).filter(models.Course.id == db_module.course_id).first()
    if course.creator_id != current_user.id and current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Not authorized to delete this module")
    
    # Delete module
    db.delete(db_module)
    db.commit()
    
    return {"detail": "Module deleted successfully"}