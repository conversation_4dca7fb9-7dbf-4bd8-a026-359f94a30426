from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from db import database, models, schemas
from auth.auth import get_current_active_user

router = APIRouter()

@router.post("/", response_model=schemas.Skill)
async def create_skill(
    skill: schemas.SkillCreate,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if user has permission to create skills
    if current_user.role not in ["teacher", "admin", "creator"]:
        raise HTTPException(status_code=403, detail="Not authorized to create skills")
    
    # Check if skill with same name already exists
    existing_skill = db.query(models.Skill).filter(models.Skill.name == skill.name).first()
    if existing_skill:
        raise HTTPException(status_code=400, detail="Skill with this name already exists")
    
    # Create new skill
    db_skill = models.Skill(
        name=skill.name,
        description=skill.description,
        domain=skill.domain
    )
    db.add(db_skill)
    db.commit()
    db.refresh(db_skill)
    
    return db_skill

@router.get("/", response_model=List[schemas.Skill])
async def read_skills(
    skip: int = 0,
    limit: int = 100,
    domain: str = None,
    db: Session = Depends(database.get_db)
):
    query = db.query(models.Skill)
    
    # Filter by domain if provided
    if domain:
        query = query.filter(models.Skill.domain == domain)
    
    skills = query.offset(skip).limit(limit).all()
    return skills

@router.get("/{skill_id}", response_model=schemas.Skill)
async def read_skill(
    skill_id: int,
    db: Session = Depends(database.get_db)
):
    skill = db.query(models.Skill).filter(models.Skill.id == skill_id).first()
    if skill is None:
        raise HTTPException(status_code=404, detail="Skill not found")
    return skill

@router.put("/{skill_id}", response_model=schemas.Skill)
async def update_skill(
    skill_id: int,
    skill: schemas.SkillCreate,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if user has permission to update skills
    if current_user.role not in ["teacher", "admin", "creator"]:
        raise HTTPException(status_code=403, detail="Not authorized to update skills")
    
    # Check if skill exists
    db_skill = db.query(models.Skill).filter(models.Skill.id == skill_id).first()
    if db_skill is None:
        raise HTTPException(status_code=404, detail="Skill not found")
    
    # Check if new name conflicts with existing skill
    if skill.name != db_skill.name:
        existing_skill = db.query(models.Skill).filter(models.Skill.name == skill.name).first()
        if existing_skill:
            raise HTTPException(status_code=400, detail="Skill with this name already exists")
    
    # Update skill fields
    for field, value in skill.dict().items():
        setattr(db_skill, field, value)
    
    db.commit()
    db.refresh(db_skill)
    return db_skill

@router.delete("/{skill_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_skill(
    skill_id: int,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if user has permission to delete skills
    if current_user.role not in ["admin"]:
        raise HTTPException(status_code=403, detail="Not authorized to delete skills")
    
    # Check if skill exists
    db_skill = db.query(models.Skill).filter(models.Skill.id == skill_id).first()
    if db_skill is None:
        raise HTTPException(status_code=404, detail="Skill not found")
    
    # Check if skill is in use
    if db_skill.courses or db_skill.modules:
        raise HTTPException(
            status_code=400, 
            detail="Cannot delete skill that is in use by courses or modules"
        )
    
    # Delete skill
    db.delete(db_skill)
    db.commit()
    
    return {"detail": "Skill deleted successfully"}