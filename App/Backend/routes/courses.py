from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from db import database, models, schemas
from auth.auth import get_current_active_user

router = APIRouter()

@router.post("/", response_model=schemas.Course)
async def create_course(
    course: schemas.CourseCreate,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if user has permission to create courses
    if current_user.role not in ["teacher", "admin", "creator"]:
        raise HTTPException(status_code=403, detail="Not authorized to create courses")
    
    # Create new course
    db_course = models.Course(
        title=course.title,
        description=course.description,
        domain=course.domain,
        creator_id=current_user.id,
        is_published=course.is_published
    )
    db.add(db_course)
    db.commit()
    db.refresh(db_course)
    
    # Add skills to course
    if course.skill_ids:
        skills = db.query(models.Skill).filter(models.Skill.id.in_(course.skill_ids)).all()
        for skill in skills:
            db_course.skills.append(skill)
        db.commit()
        db.refresh(db_course)
    
    return db_course

@router.get("/", response_model=List[schemas.Course])
async def read_courses(
    skip: int = 0,
    limit: int = 100,
    domain: str = None,
    db: Session = Depends(database.get_db)
):
    query = db.query(models.Course)
    
    # Filter by domain if provided
    if domain:
        query = query.filter(models.Course.domain == domain)
    
    # Only show published courses
    query = query.filter(models.Course.is_published == True)
    
    courses = query.offset(skip).limit(limit).all()
    return courses

@router.get("/my-courses", response_model=List[schemas.Course])
async def read_my_courses(
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Get courses created by current user
    courses = db.query(models.Course).filter(models.Course.creator_id == current_user.id).all()
    return courses

@router.get("/enrolled", response_model=List[schemas.Course])
async def read_enrolled_courses(
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Get courses the user is enrolled in
    user = db.query(models.User).filter(models.User.id == current_user.id).first()
    return user.courses

@router.get("/{course_id}", response_model=schemas.Course)
async def read_course(
    course_id: int,
    db: Session = Depends(database.get_db)
):
    course = db.query(models.Course).filter(models.Course.id == course_id).first()
    if course is None:
        raise HTTPException(status_code=404, detail="Course not found")
    
    # Only show published courses unless user is the creator
    if not course.is_published:
        current_user = get_current_active_user()
        if current_user.id != course.creator_id and current_user.role != "admin":
            raise HTTPException(status_code=404, detail="Course not found")
    
    return course

@router.put("/{course_id}", response_model=schemas.Course)
async def update_course(
    course_id: int,
    course: schemas.CourseCreate,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if course exists
    db_course = db.query(models.Course).filter(models.Course.id == course_id).first()
    if db_course is None:
        raise HTTPException(status_code=404, detail="Course not found")
    
    # Check if user has permission to update
    if db_course.creator_id != current_user.id and current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Not authorized to update this course")
    
    # Update course fields
    for field, value in course.dict(exclude={"skill_ids"}).items():
        setattr(db_course, field, value)
    
    # Update skills
    if course.skill_ids is not None:
        # Clear existing skills
        db_course.skills = []
        
        # Add new skills
        skills = db.query(models.Skill).filter(models.Skill.id.in_(course.skill_ids)).all()
        for skill in skills:
            db_course.skills.append(skill)
    
    db.commit()
    db.refresh(db_course)
    return db_course

@router.delete("/{course_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_course(
    course_id: int,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Check if course exists
    db_course = db.query(models.Course).filter(models.Course.id == course_id).first()
    if db_course is None:
        raise HTTPException(status_code=404, detail="Course not found")
    
    # Check if user has permission to delete
    if db_course.creator_id != current_user.id and current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Not authorized to delete this course")
    
    # Delete course
    db.delete(db_course)
    db.commit()
    
    return {"detail": "Course deleted successfully"}