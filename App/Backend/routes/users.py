from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from db import database, models, schemas
from auth.auth import get_current_active_user

router = APIRouter()

@router.get("/me", response_model=schemas.User)
async def read_users_me(current_user: schemas.User = Depends(get_current_active_user)):
    return current_user

@router.get("/{user_id}", response_model=schemas.User)
async def read_user(
    user_id: int,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Only allow users to view their own profile or admins to view any profile
    if current_user.id != user_id and current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Not authorized to view this user")
        
    db_user = db.query(models.User).filter(models.User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

@router.get("/", response_model=List[schemas.User])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Only admins can list all users
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Not authorized to list users")
        
    users = db.query(models.User).offset(skip).limit(limit).all()
    return users

@router.put("/me", response_model=schemas.User)
async def update_user(
    user_data: schemas.UserBase,
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Update user fields
    db_user = db.query(models.User).filter(models.User.id == current_user.id).first()
    
    for field, value in user_data.dict().items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

@router.delete("/me", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    db: Session = Depends(database.get_db),
    current_user: schemas.User = Depends(get_current_active_user)
):
    # Deactivate user instead of deleting
    db_user = db.query(models.User).filter(models.User.id == current_user.id).first()
    db_user.is_active = False
    db.commit()
    return {"detail": "User deactivated successfully"}