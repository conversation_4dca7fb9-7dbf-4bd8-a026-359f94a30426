from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import uvicorn

from db import models, database, schemas
from routes import users, courses, modules, skills
from auth import auth

# Create the database tables
models.Base.metadata.create_all(bind=database.engine)

app = FastAPI(
    title="Adaptive Learning Platform API",
    description="API for an AI-driven adaptive learning platform",
    version="0.1.0"
)

# CORS middleware setup
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(users.router, prefix="/api/users", tags=["Users"])
app.include_router(courses.router, prefix="/api/courses", tags=["Courses"])
app.include_router(modules.router, prefix="/api/modules", tags=["Modules"])
app.include_router(skills.router, prefix="/api/skills", tags=["Skills"])

@app.get("/", tags=["Root"])
async def read_root():
    return {"message": "Welcome to the Adaptive Learning Platform API"}

@app.get("/api/health", tags=["Health"])
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)