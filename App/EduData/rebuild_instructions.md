# Rebuilding EduData from Local Source

This guide explains how to rebuild and install the EduData package from your local source code, allowing you to make and test changes to files like `EduData/main.py`.

## Method 1: Using Development Mode (Recommended)

Development mode (or "editable" mode) installs the package in a way that changes to the source code are immediately reflected without reinstallation.

### Step 1: Activate your conda environment

```bash
conda activate edudata_env
```

### Step 2: Navigate to the EduData directory

```bash
cd App/EduData
```

### Step 3: Install in development mode

```bash
pip install -e .
```

This command installs the package in "editable" mode, creating a special link to your source code instead of copying the files. Now any changes you make to the source code will be immediately available when you run `edudata` commands.

## Method 2: Full Rebuild

If you need to completely rebuild the package:

### Step 1: Activate your conda environment

```bash
conda activate edudata_env
```

### Step 2: Navigate to the EduData directory

```bash
cd App/EduData
```

### Step 3: Uninstall existing package

```bash
pip uninstall -y EduData
```

### Step 4: Clean build artifacts

```bash
make clean
# Or manually:
rm -rf build/ dist/ *.egg-info/
```

### Step 5: Rebuild and install

```bash
pip install .
# Or for development mode:
pip install -e .
```

## Fixing Import Errors in main.py

If you're modifying `EduData/main.py` and encounter import errors, make sure your import statements use the correct format:

```python
# Incorrect relative imports:
from './DataSet/junyi' import extract_relations

# Correct absolute imports:
from EduData.DataSet.junyi import extract_relations
```

## Verifying Your Installation

After rebuilding, verify that your changes are working:

```bash
edudata --help
```

This should show the CLI interface with any changes you've made to the command structure.