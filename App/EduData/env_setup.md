# EduData Environment Setup Guide

This guide provides instructions for setting up a proper environment to run the EduData application, which allows you to download and process educational datasets.

## Prerequisites

- **Anaconda or Miniconda**: Make sure you have Anaconda or Miniconda installed on your system. If not, download and install from:
  - Anaconda: https://www.anaconda.com/products/distribution
  - Miniconda: https://docs.conda.io/en/latest/miniconda.html

## Automatic Setup (Recommended)

We provide a script that automatically sets up a conda environment with all required dependencies.

### Step 1: Run the setup script

```bash
# Navigate to the EduData directory
cd App/EduData

# Make the script executable (Linux/Mac)
chmod +x env_setup.py

# Run the setup script with default settings
python env_setup.py
```

This will create a conda environment named `edudata_env` with Python 3.9 and install all required packages.

### Advanced Options

The setup script supports several options:

```bash
# Create environment with a custom name
python env_setup.py --env-name my_custom_env

# Use a different Python version
python env_setup.py --python-version 3.8

# Install in development mode (if you're working on EduData code)
python env_setup.py --install-mode dev

# Include test dependencies
python env_setup.py --with-test

# Force reinstall if environment already exists
python env_setup.py --force-reinstall
```

## Manual Setup

If you prefer to set up the environment manually:

### Step 1: Create a conda environment

```bash
conda create -n edudata_env python=3.9
conda activate edudata_env
```

### Step 2: Install EduData

```bash
# Install from PyPI
pip install EduData

# OR install in development mode (if you have the source code)
pip install -e .
```

## Using EduData

After setting up the environment, you can use EduData to download and process datasets.

### Step 1: Activate the environment

```bash
conda activate edudata_env
```

### Step 2: List available datasets

```bash
edudata ls
```

### Step 3: Download a dataset

```bash
# Download to current directory
edudata download assistment-2009-2010-skill

# Download to specific directory
edudata download assistment-2009-2010-skill /path/to/data/directory
```

### Step 4: Process datasets

EduData provides various tools for processing datasets. For example:

```bash
# Convert formats
edudata tl2json source_file target_file

# Analyze dataset statistics
edudata kt_stat filename

# Split dataset
edudata train_valid_test source_file target_prefix --train_size 0.8 --valid_size 0.1 --test_size 0.1
```

## Troubleshooting

- **Missing dependencies**: If you encounter errors about missing packages, try reinstalling with the `--force-reinstall` option.
- **Permission issues**: On Linux/Mac, make sure the script is executable with `chmod +x env_setup.py`.
- **Path issues**: Ensure you're running the commands from the EduData directory.
- **ModuleNotFoundError: No module named 'longling.ML'**: This error occurs when the longling package is installed without ML support. To fix it:
  ```bash
  # Activate your environment
  conda activate edudata_env
  
  # Reinstall longling with ML support
  pip install 'longling[ml]>=1.3.15'
  
  # Or run the setup script with force-reinstall
  python env_setup.py --force-reinstall
  ```

For more information, refer to the [EduData documentation](https://edudata.readthedocs.io/en/latest/).
