#!/usr/bin/env python
# coding: utf-8
# EduData Environment Setup Script

import os
import sys
import subprocess
import argparse

def run_command(command):
    """Run a shell command and return the output"""
    try:
        result = subprocess.run(command, shell=True, check=True, 
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                               universal_newlines=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {command}")
        print(f"Error message: {e.stderr}")
        sys.exit(1)

def check_conda_installed():
    """Check if conda is installed"""
    try:
        run_command("conda --version")
        return True
    except:
        print("Conda is not installed or not in PATH. Please install Anaconda or Miniconda first.")
        sys.exit(1)

def create_conda_env(env_name, python_version):
    """Create a new conda environment"""
    print(f"Creating new conda environment: {env_name} with Python {python_version}")
    run_command(f"conda create -n {env_name} python={python_version} -y")

def check_env_exists(env_name):
    """Check if conda environment exists"""
    envs = run_command("conda env list")
    return env_name in envs

def install_edudata(env_name, install_mode="pip"):
    """Install EduData package"""
    print(f"Installing EduData in {env_name} environment...")
    
    if install_mode == "pip":
        run_command(f"conda run -n {env_name} pip install EduData")
    elif install_mode == "dev":
        # For development mode, install from current directory
        run_command(f"conda run -n {env_name} pip install -e .")
    else:
        print(f"Unknown installation mode: {install_mode}")
        sys.exit(1)

def install_dependencies(env_name):
    """Install all dependencies for EduData"""
    print(f"Installing dependencies in {env_name} environment...")
    
    # First install longling with ML support explicitly
    run_command(f"conda run -n {env_name} pip install 'longling[ml]>=1.3.15'")
    
    # Core dependencies from setup.py
    dependencies = [
        "tqdm",
        "networkx",
        "longling[ml]==1.3.15",
        "requests",
        "bs4",
        "rarfile",
        "pandas",
        "fire",
        "lxml",
        "numpy>=1.16.5",
        "scipy>=1.2.0",
        "scikit-learn>=0.23.2",
    ]
    
    # Install each dependency
    for dep in dependencies:
        run_command(f"conda run -n {env_name} pip install '{dep}'")
    
    # Install test dependencies if requested
    if args.with_test:
        test_deps = [
            "pytest>=4",
            "pytest-cov>=2.6.0",
            "pytest-flake8",
            "flake8<5.0.0"
        ]
        for dep in test_deps:
            run_command(f"conda run -n {env_name} pip install '{dep}'")

def main():
    """Main function to set up the environment"""
    check_conda_installed()
    
    env_name = args.env_name
    python_version = args.python_version
    
    # Create environment if it doesn't exist
    if not check_env_exists(env_name):
        create_conda_env(env_name, python_version)
        install_dependencies(env_name)
        install_edudata(env_name, args.install_mode)
    else:
        print(f"Using existing conda environment: {env_name}")
        if args.force_reinstall:
            print("Reinstalling dependencies and EduData...")
            install_dependencies(env_name)
            install_edudata(env_name, args.install_mode)
    
    print("\nEnvironment setup complete!")
    print(f"To activate the environment, run: conda activate {env_name}")
    print("To download a dataset, run: edudata download <dataset-name>")
    print("For example: edudata download assistment-2009-2010-skill")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Set up conda environment for EduData")
    parser.add_argument("--env-name", default="edudata_env", help="Name of conda environment")
    parser.add_argument("--python-version", default="3.9", help="Python version to use")
    parser.add_argument("--install-mode", default="pip", choices=["pip", "dev"], 
                        help="Installation mode: 'pip' for PyPI, 'dev' for development mode")
    parser.add_argument("--with-test", action="store_true", help="Install test dependencies")
    parser.add_argument("--force-reinstall", action="store_true", 
                        help="Force reinstall dependencies if environment exists")
    
    args = parser.parse_args()
    main()
