{"cells": [{"cell_type": "code", "execution_count": 1, "source": ["import pandas as pd\n", "import numpy as np\n", "import plotly.express as px"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 2, "source": ["file_path = \"/home/<USER>/pisa2015_science/pisa2015_science/cog_science.csv\"\n", "df = pd.read_csv(file_path, low_memory=False)"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["# Data Description"], "metadata": {}}, {"cell_type": "markdown", "source": ["Field | annotation\n", "----- | -----\n", "CNTRYID \t| The country which the student comes from\n", "CNTSTUID\t| Student ID\n", "Region\t\t| The region which the student comes from\n", "STRATUM\t\t| Types of students classified by school and place of birth, etc.\n", "SUBNATIO\t| Further division of country\n", "ADMINMODE\t| The means by which the student answer questions(paper or computer)\n", "LANGTEST_COG| The language which the student uses\n", "BOOKID\t\t| Form ID\n", "CBASCI\t\t| Science Cluster Combination Random Number(S)\n", "CS601Q01S, CS601Q02S ... DS626Q04C | Question ID"], "metadata": {}}, {"cell_type": "markdown", "source": ["# Record Examples"], "metadata": {}}, {"cell_type": "code", "execution_count": 3, "source": ["df.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CNTRYID</th>\n", "      <th>CNTSTUID</th>\n", "      <th>Region</th>\n", "      <th>STRATUM</th>\n", "      <th>SUBNATIO</th>\n", "      <th>ADMINMODE</th>\n", "      <th>LANGTEST_COG</th>\n", "      <th>BOOKID</th>\n", "      <th>CBASCI</th>\n", "      <th>DS269Q01C</th>\n", "      <th>...</th>\n", "      <th>CS601Q01S</th>\n", "      <th>CS601Q02S</th>\n", "      <th>CS601Q04S</th>\n", "      <th>DS610Q01C</th>\n", "      <th>CS610Q02S</th>\n", "      <th>CS610Q04S</th>\n", "      <th>CS626Q01S</th>\n", "      <th>CS626Q02S</th>\n", "      <th>CS626Q03S</th>\n", "      <th>DS626Q04C</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Albania</td>\n", "      <td>803627</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>Albania</td>\n", "      <td>Paper</td>\n", "      <td>Albanian</td>\n", "      <td>Form 27 (PBA)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Albania</td>\n", "      <td>800454</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>Albania</td>\n", "      <td>Paper</td>\n", "      <td>Albanian</td>\n", "      <td>Form 9 (PBA)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Albania</td>\n", "      <td>800893</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>Albania</td>\n", "      <td>Paper</td>\n", "      <td>Albanian</td>\n", "      <td>Form 18 (PBA)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Albania</td>\n", "      <td>804180</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>Albania</td>\n", "      <td>Paper</td>\n", "      <td>Albanian</td>\n", "      <td>Form 10 (PBA)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Albania</td>\n", "      <td>800491</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>Albania</td>\n", "      <td>Paper</td>\n", "      <td>Albanian</td>\n", "      <td>Form 19 (PBA)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 192 columns</p>\n", "</div>"], "text/plain": ["   CNTRYID  CNTSTUID   Region                                   STRATUM  \\\n", "0  Albania    803627  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "1  Albania    800454  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "2  Albania    800893  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "3  Albania    804180  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "4  Albania    800491  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "\n", "  SUBNATIO ADMINMODE LANGTEST_COG         BOOKID CBASCI DS269Q01C  ...  \\\n", "0  Albania     Paper     Albanian  Form 27 (PBA)    NaN       NaN  ...   \n", "1  Albania     Paper     Albanian   Form 9 (PBA)    NaN       NaN  ...   \n", "2  Albania     Paper     Albanian  Form 18 (PBA)    NaN       NaN  ...   \n", "3  Albania     Paper     Albanian  Form 10 (PBA)    NaN       NaN  ...   \n", "4  Albania     Paper     Albanian  Form 19 (PBA)    NaN       NaN  ...   \n", "\n", "  CS601Q01S CS601Q02S CS601Q04S DS610Q01C CS610Q02S CS610Q04S CS626Q01S  \\\n", "0       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "1       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "2       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "3       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "4       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "\n", "  CS626Q02S CS626Q03S DS626Q04C  \n", "0       NaN       NaN       NaN  \n", "1       NaN       NaN       NaN  \n", "2       NaN       NaN       NaN  \n", "3       NaN       NaN       NaN  \n", "4       NaN       NaN       NaN  \n", "\n", "[5 rows x 192 columns]"]}, "metadata": {}, "execution_count": 3}], "metadata": {"scrolled": true}}, {"cell_type": "code", "execution_count": 4, "source": ["print(\"{} Students from {} regions around the world participate in this assessment.\".format(len(df['CNTSTUID']), len(df['Region'].unique())))"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["519334 Students from 118 regions around the world participate in this assessment.\n"]}], "metadata": {}}, {"cell_type": "markdown", "source": ["The columns begin with \"DS\" or \"CS\" are the questions."], "metadata": {}}, {"cell_type": "code", "execution_count": 5, "source": ["question_ids = [col for col in df.columns if 'DS' in col or 'CS' in col]\n", "print(\"There are {} questions involved.\".format(len(question_ids)))"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["There are 183 questions involved.\n"]}], "metadata": {}}, {"cell_type": "markdown", "source": ["# Data cleaning\n"], "metadata": {}}, {"cell_type": "markdown", "source": ["## Drop useless columns"], "metadata": {}}, {"cell_type": "code", "execution_count": 6, "source": ["useless_columns = [\"CBASCI\", \"BOOKID\", \"ADMINMODE\", \"SUBNATIO\", \"LANGTEST_COG\"]\n", "df.drop(columns=useless_columns, inplace=True)\n", "df.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CNTRYID</th>\n", "      <th>CNTSTUID</th>\n", "      <th>Region</th>\n", "      <th>STRATUM</th>\n", "      <th>DS269Q01C</th>\n", "      <th>DS269Q03C</th>\n", "      <th>CS269Q04S</th>\n", "      <th>CS408Q01S</th>\n", "      <th>DS408Q03C</th>\n", "      <th>CS408Q04S</th>\n", "      <th>...</th>\n", "      <th>CS601Q01S</th>\n", "      <th>CS601Q02S</th>\n", "      <th>CS601Q04S</th>\n", "      <th>DS610Q01C</th>\n", "      <th>CS610Q02S</th>\n", "      <th>CS610Q04S</th>\n", "      <th>CS626Q01S</th>\n", "      <th>CS626Q02S</th>\n", "      <th>CS626Q03S</th>\n", "      <th>DS626Q04C</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Albania</td>\n", "      <td>803627</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Albania</td>\n", "      <td>800454</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Albania</td>\n", "      <td>800893</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Albania</td>\n", "      <td>804180</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Albania</td>\n", "      <td>800491</td>\n", "      <td>Albania</td>\n", "      <td>ALB - stratum 05: Urban \\ South \\ Public</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 187 columns</p>\n", "</div>"], "text/plain": ["   CNTRYID  CNTSTUID   Region                                   STRATUM  \\\n", "0  Albania    803627  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "1  Albania    800454  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "2  Albania    800893  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "3  Albania    804180  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "4  Albania    800491  Albania  ALB - stratum 05: Urban \\ South \\ Public   \n", "\n", "  DS269Q01C DS269Q03C CS269Q04S CS408Q01S DS408Q03C CS408Q04S  ... CS601Q01S  \\\n", "0       NaN       NaN       NaN       NaN       NaN       NaN  ...       NaN   \n", "1       NaN       NaN       NaN       NaN       NaN       NaN  ...       NaN   \n", "2       NaN       NaN       NaN       NaN       NaN       NaN  ...       NaN   \n", "3       NaN       NaN       NaN       NaN       NaN       NaN  ...       NaN   \n", "4       NaN       NaN       NaN       NaN       NaN       NaN  ...       NaN   \n", "\n", "  CS601Q02S CS601Q04S DS610Q01C CS610Q02S CS610Q04S CS626Q01S CS626Q02S  \\\n", "0       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "1       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "2       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "3       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "4       NaN       NaN       NaN       NaN       NaN       NaN       NaN   \n", "\n", "  CS626Q03S DS626Q04C  \n", "0       NaN       NaN  \n", "1       NaN       NaN  \n", "2       NaN       NaN  \n", "3       NaN       NaN  \n", "4       NaN       NaN  \n", "\n", "[5 rows x 187 columns]"]}, "metadata": {}, "execution_count": 6}], "metadata": {}}, {"cell_type": "markdown", "source": ["## Transform the result to score"], "metadata": {}}, {"cell_type": "code", "execution_count": 7, "source": ["def transform_to_number(ans):\n", "    if isinstance(ans, float):\n", "        return np.Na<PERSON>\n", "    if 'No credit' in ans:\n", "        return 0\n", "    if 'Partial credit' in ans:\n", "        return 1\n", "    if 'Full credit' in ans:\n", "        return 2\n", "    if 'No Response' in ans:\n", "        return np.Na<PERSON>\n", "    if 'Not Reached' in ans:\n", "        return np.Na<PERSON>\n", "    if 'Not Applicable' in ans:\n", "        return np.Na<PERSON>\n", "    \n", "    return np.Na<PERSON>\n", "\n", "for q in question_ids:\n", "\n", "    df[q] = df[q].map(transform_to_number)\n", "    \n", "df_question = df[question_ids]\n"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 8, "source": ["df_difficulty = df_question.transpose().mean(1) / 2\n", "# define difficulty equals average score divided by full score(2)\n", "df_question = df_question / df_difficulty\n", "# The more difficult questions will bring more score"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 9, "source": ["print(\"5 least answered questions:\")\n", "df_question.count().nsmallest()"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["5 least answered questions:\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["DS327Q02C        0\n", "DS438Q03C    38225\n", "DS425Q04C    39228\n", "DS524Q07C    39777\n", "DS268Q02C    39958\n", "dtype: int64"]}, "metadata": {}, "execution_count": 9}], "metadata": {}}, {"cell_type": "markdown", "source": ["The question \"DS327Q02C\" is bad, because no one has given the correct answer."], "metadata": {}}, {"cell_type": "code", "execution_count": 10, "source": ["df_question.drop(columns=['DS327Q02C'], inplace=True)\n", "print(\"The number of students who answer each question:\")\n", "df_question.count()"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The number of students who answer each question:\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["DS269Q01C    44197\n", "DS269Q03C    43324\n", "CS269Q04S    48415\n", "CS408Q01S    48440\n", "DS408Q03C    42613\n", "             ...  \n", "CS610Q04S    91081\n", "CS626Q01S    92980\n", "CS626Q02S    91846\n", "CS626Q03S    91033\n", "DS626Q04C    89762\n", "Length: 182, dtype: int64"]}, "metadata": {}, "execution_count": 10}], "metadata": {"scrolled": true}}, {"cell_type": "markdown", "source": ["## Calculate average score for each student"], "metadata": {}}, {"cell_type": "code", "execution_count": 11, "source": ["df_student = df.copy()\n", "df_student['Avg score'] = df_question.mean(1)\n", "df_student['Question count'] = df_question.count(1)\n", "df_student.drop(columns=question_ids, inplace=True)\n", "df_student.drop(df_student[df_student['Question count'] == 0].index, inplace=True)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 12, "source": ["print(\"{} students give at least one valid answer.\".format(len(df_student)))"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["446051 students give at least one valid answer.\n"]}], "metadata": {}}, {"cell_type": "code", "execution_count": 13, "source": ["df_student.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CNTRYID</th>\n", "      <th>CNTSTUID</th>\n", "      <th>Region</th>\n", "      <th>STRATUM</th>\n", "      <th>Avg score</th>\n", "      <th>Question count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10734</th>\n", "      <td>Australia</td>\n", "      <td>3610676</td>\n", "      <td>Australia</td>\n", "      <td>AUS - stratum 21: QLD-Gov, Y10</td>\n", "      <td>2.945319</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10735</th>\n", "      <td>Australia</td>\n", "      <td>3611874</td>\n", "      <td>Australia</td>\n", "      <td>AUS - stratum 21: QLD-Gov, Y10</td>\n", "      <td>2.693977</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10736</th>\n", "      <td>Australia</td>\n", "      <td>3601769</td>\n", "      <td>Australia</td>\n", "      <td>AUS - stratum 21: QLD-Gov, Y10</td>\n", "      <td>2.982012</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10737</th>\n", "      <td>Australia</td>\n", "      <td>3605996</td>\n", "      <td>Australia</td>\n", "      <td>AUS - stratum 21: QLD-Gov, Y10</td>\n", "      <td>2.330750</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10738</th>\n", "      <td>Australia</td>\n", "      <td>3608147</td>\n", "      <td>Australia</td>\n", "      <td>AUS - stratum 21: QLD-Gov, Y10</td>\n", "      <td>1.752320</td>\n", "      <td>32</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         CNTRYID  CNTSTUID     Region                         STRATUM  \\\n", "10734  Australia   3610676  Australia  AUS - stratum 21: QLD-Gov, Y10   \n", "10735  Australia   3611874  Australia  AUS - stratum 21: QLD-Gov, Y10   \n", "10736  Australia   3601769  Australia  AUS - stratum 21: QLD-Gov, Y10   \n", "10737  Australia   3605996  Australia  AUS - stratum 21: QLD-Gov, Y10   \n", "10738  Australia   3608147  Australia  AUS - stratum 21: QLD-Gov, Y10   \n", "\n", "       Avg score  Question count  \n", "10734   2.945319              19  \n", "10735   2.693977              27  \n", "10736   2.982012              23  \n", "10737   2.330750              32  \n", "10738   1.752320              32  "]}, "metadata": {}, "execution_count": 13}], "metadata": {"scrolled": true}}, {"cell_type": "markdown", "source": ["# Sort questions by average score"], "metadata": {}}, {"cell_type": "code", "execution_count": 27, "source": ["q_top20 = df_difficulty.nlargest(20)\n", "fig = px.bar(\n", "    q_top20.iloc[::-1], \n", "    labels={\"value\": \"Difficulty\", \"index\": \"Question Name\"},\n", "    orientation='h',\n", "    title=\"Top 20 easy questions\"\n", "            )\n", "fig.update_layout(showlegend=False)\n", "fig.show('svg')"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-9bf750\"><g class=\"clips\"><clipPath id=\"clip9bf750xyplot\" class=\"plotclip\"><rect width=\"514\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9bf750x\"><rect x=\"106\" y=\"0\" width=\"514\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9bf750y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9bf750xy\"><rect x=\"106\" y=\"100\" width=\"514\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"106\" y=\"100\" width=\"514\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(214.09,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(322.16999999999996,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(430.26,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(538.3399999999999,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(106,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(106,100)\" clip-path=\"url(#clip9bf750xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H372.61V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,302.4V289.6H376.71V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,286.4V273.6H377.83V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,270.4V257.6H380.18V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,254.4V241.6H384.08V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,238.4V225.6H392.66V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,222.4V209.6H395.03V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,206.4V193.6H399.98V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,190.4V177.6H403.27V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,174.4V161.6H407.78V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,158.4V145.6H415.24V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,142.4V129.6H419.22V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,126.4V113.6H425.47V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,110.4V97.6H427.9V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,94.4V81.6H429.04V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,78.4V65.6H455.5V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,62.4V49.6H456.18V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,46.4V33.6H474.56V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,30.4V17.6H474.82V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,14.4V1.6H488.3V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(106,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(214.09,0)\">0.2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(322.16999999999996,0)\">0.4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(430.26,0)\">0.6</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(538.3399999999999,0)\">0.8</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,412)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS641Q04S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,396)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS657Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,380)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS635Q02S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,364)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS415Q07S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,348)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS603Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,332)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS438Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,316)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS415Q02S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,300)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS638Q02S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,284)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS627Q03S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,268)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS437Q06C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,252)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS646Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,236)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS602Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,220)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS615Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,204)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS620Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,188)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS514Q02C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,172)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS607Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,156)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS610Q02S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,140)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS521Q06S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,124)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS256Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,108)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS641Q03S</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-9bf750\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Top 20 easy questions</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"363\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Difficulty</text></g><g class=\"g-ytitle\" transform=\"translate(0.8466796875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,12.153125000000003,260)\" x=\"12.153125000000003\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Question Name</text></g></g></svg>"}, "metadata": {}}], "metadata": {}}, {"cell_type": "code", "execution_count": 14, "source": ["q_bottom20 = df_difficulty.nsmallest(20)\n", "fig = px.bar(\n", "    q_bottom20.iloc[::-1], \n", "    labels={\"value\": \"Difficulty\", \"index\": \"Question Name\"},\n", "    orientation='h',\n", "    title=\"Top 20 difficult questions\"\n", ")\n", "fig.update_layout(showlegend=False)\n", "fig.show('svg')"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-d15651\"><g class=\"clips\"><clipPath id=\"clipd15651xyplot\" class=\"plotclip\"><rect width=\"514\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd15651x\"><rect x=\"106\" y=\"0\" width=\"514\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd15651y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd15651xy\"><rect x=\"106\" y=\"100\" width=\"514\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"106\" y=\"100\" width=\"514\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(195.76,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(285.52,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(375.27,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(465.03,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(554.79,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(106,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(106,100)\" clip-path=\"url(#clipd15651xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H488.3V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,302.4V289.6H484.52V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,286.4V273.6H473.57V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,270.4V257.6H468.24V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,254.4V241.6H462.39V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,238.4V225.6H456.82V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,222.4V209.6H442.35V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,206.4V193.6H418.29V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,190.4V177.6H412.97V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,174.4V161.6H322.25V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,158.4V145.6H315.87V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,142.4V129.6H301.84V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,126.4V113.6H279.21V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,110.4V97.6H270.04V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,94.4V81.6H258.89V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,78.4V65.6H257.29V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,62.4V49.6H219.17V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,46.4V33.6H214.91V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,30.4V17.6H206.1V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,14.4V1.6H116.25V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(106,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(195.76,0)\">0.05</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(285.52,0)\">0.1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(375.27,0)\">0.15</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(465.03,0)\">0.2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(554.79,0)\">0.25</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,412)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS638Q04S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,396)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS519Q03C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,380)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS645Q05C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,364)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS615Q07S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,348)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS643Q04S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,332)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS604Q04C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,316)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS649Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,300)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS643Q05C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,284)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS326Q04S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,268)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS634Q03C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,252)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS615Q05S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,236)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS634Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,220)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS649Q02C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,204)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS458Q01C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,188)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS637Q02S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,172)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS635Q05C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,156)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS527Q01S</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,140)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS646Q05C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,124)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DS634Q05C</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"105\" y=\"4.199999999999999\" transform=\"translate(0,108)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">CS601Q01S</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-d15651\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Top 20 difficult questions</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"363\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Difficulty</text></g><g class=\"g-ytitle\" transform=\"translate(0.8466796875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,12.153125000000003,260)\" x=\"12.153125000000003\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Question Name</text></g></g></svg>"}, "metadata": {}}], "metadata": {}}, {"cell_type": "markdown", "source": ["# Sort regions by average score"], "metadata": {}}, {"cell_type": "code", "execution_count": 15, "source": ["df_region = \\\n", "    df_student[[\"Region\", \"Avg score\", \"Question count\"]] \\\n", "        .groupby(\"Region\").mean().rename(columns={\"Avg score\": \"Region avg score\"})"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 16, "source": ["df_region.describe()"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Region avg score</th>\n", "      <th>Question count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>103.000000</td>\n", "      <td>103.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.988160</td>\n", "      <td>29.004939</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.397446</td>\n", "      <td>1.280883</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.822475</td>\n", "      <td>22.413025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.782955</td>\n", "      <td>28.684978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2.119420</td>\n", "      <td>29.295016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.259783</td>\n", "      <td>29.780795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>2.635470</td>\n", "      <td>30.473813</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Region avg score  Question count\n", "count        103.000000      103.000000\n", "mean           1.988160       29.004939\n", "std            0.397446        1.280883\n", "min            0.822475       22.413025\n", "25%            1.782955       28.684978\n", "50%            2.119420       29.295016\n", "75%            2.259783       29.780795\n", "max            2.635470       30.473813"]}, "metadata": {}, "execution_count": 16}], "metadata": {}}, {"cell_type": "code", "execution_count": 17, "source": ["region_top20 = df_region[\"Region avg score\"].nlargest(20)\n", "fig = px.bar(\n", "    region_top20.iloc[::-1],\n", "    labels={\"value\": \"Region average Score\", \"index\": \"Region\"},\n", "    orientation='h',\n", "    title=\"Top 20 regions\"\n", ")\n", "fig.update_layout(showlegend=False)\n", "fig.show(\"svg\")"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-6cdaa6\"><g class=\"clips\"><clipPath id=\"clip6cdaa6xyplot\" class=\"plotclip\"><rect width=\"423\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6cdaa6x\"><rect x=\"197\" y=\"0\" width=\"423\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6cdaa6y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6cdaa6xy\"><rect x=\"197\" y=\"100\" width=\"423\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"197\" y=\"100\" width=\"423\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(273.24,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(349.48,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(425.72,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(501.96,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(578.19,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(197,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(197,100)\" clip-path=\"url(#clip6cdaa6xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H350.43V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,302.4V289.6H350.44V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,286.4V273.6H350.62V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,270.4V257.6H352.76V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,254.4V241.6H354.61V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,238.4V225.6H361.23V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,222.4V209.6H362.16V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,206.4V193.6H365.18V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,190.4V177.6H365.92V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,174.4V161.6H369.46V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,158.4V145.6H370.19V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,142.4V129.6H372.78V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,126.4V113.6H375.37V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,110.4V97.6H377.74V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,94.4V81.6H380.16V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,78.4V65.6H384.68V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,62.4V49.6H387.26V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,46.4V33.6H391.16V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,30.4V17.6H393.72V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,14.4V1.6H401.85V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(197,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(273.24,0)\">0.5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(349.48,0)\">1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(425.72,0)\">1.5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(501.96,0)\">2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(578.19,0)\">2.5</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,412)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Spain: Navarre</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,396)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Germany</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,380)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Italy: Lombardia</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,364)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">New Zealand</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,348)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Italy: Bolzano</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,332)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Spain: Castile and Leon</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,316)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Spain: Madrid</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,300)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Macao</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,284)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">United States: Massachusetts</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,268)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">United Kingdom: England</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,252)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Canada: Quebec</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,236)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">B-S-J-G (China)</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,220)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Finland</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,204)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Hong Kong</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,188)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Chinese Taipei</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,172)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Estonia</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,156)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Canada: Alberta</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,140)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Canada: British Columbia</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,124)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Japan</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"196\" y=\"4.199999999999999\" transform=\"translate(0,108)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Singapore</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-6cdaa6\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Top 20 regions</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"408.5\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Region average Score</text></g><g class=\"g-ytitle\" transform=\"translate(0.5498046875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,12.449999999999989,260)\" x=\"12.449999999999989\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Region</text></g></g></svg>"}, "metadata": {}}], "metadata": {}}, {"cell_type": "code", "execution_count": 18, "source": ["region_bottom20 = df_region[\"Region avg score\"].nsmallest(20)\n", "fig = px.bar(\n", "    region_bottom20.iloc[::-1],\n", "    labels={\"index\": \"Region\", \"value\": \"Region\"},\n", "    orientation=\"h\",\n", "    title=\"Bottom 20 regions\"\n", ")\n", "fig.update_layout(showlegend=False)\n", "fig.show(\"svg\")"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-b681b1\"><g class=\"clips\"><clipPath id=\"clipb681b1xyplot\" class=\"plotclip\"><rect width=\"375\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb681b1x\"><rect x=\"245\" y=\"0\" width=\"375\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb681b1y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb681b1xy\"><rect x=\"245\" y=\"100\" width=\"375\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"245\" y=\"100\" width=\"375\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(363.15,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(481.31,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(599.46,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(245,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(245,100)\" clip-path=\"url(#clipb681b1xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H356.25V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,302.4V289.6H355.42V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,286.4V273.6H352.41V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,270.4V257.6H340.23V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,254.4V241.6H338.67V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,238.4V225.6H337.31V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,222.4V209.6H332.74V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,206.4V193.6H331.47V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,190.4V177.6H327.93V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,174.4V161.6H318.45V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,158.4V145.6H311.21V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,142.4V129.6H303.47V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,126.4V113.6H302.48V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,110.4V97.6H295.21V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,94.4V81.6H291.64V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,78.4V65.6H283.52V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,62.4V49.6H276.19V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,46.4V33.6H271.5V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,30.4V17.6H263.41V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,14.4V1.6H194.36V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(245,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(363.15,0)\">0.5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(481.31,0)\">1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(599.46,0)\">1.5</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,412)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Thailand</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,396)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">United Arab Emirates: Sharjah</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,380)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Colombia: Medellín</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,364)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">United Arab Emirates: Abu Dhabi</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,348)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Qatar</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,332)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Turkey</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,316)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Colombia: Manizales</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,300)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Colombia: Cali</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,284)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Montenegro</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,268)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Costa Rica</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,252)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Mexico</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,236)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">United Arab Emirates: Fujairah</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,220)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Brazil</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,204)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Colombia</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,188)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">United Arab Emirates: Ras Al Khaimah</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,172)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">United Arab Emirates: Ajman</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,156)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Peru</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,140)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">United Arab Emirates: Umm Al Quwain</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,124)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Tunisia</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"244\" y=\"4.199999999999999\" transform=\"translate(0,108)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Dominican Republic</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-b681b1\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Bottom 20 regions</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"432.5\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Region</text></g><g class=\"g-ytitle\" transform=\"translate(1.2060546875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,11.793749999999989,260)\" x=\"11.793749999999989\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Region</text></g></g></svg>"}, "metadata": {}}], "metadata": {}}, {"cell_type": "markdown", "source": ["# The distrbution of scores"], "metadata": {}}, {"cell_type": "code", "execution_count": 19, "source": ["df_student.describe()"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CNTSTUID</th>\n", "      <th>Avg score</th>\n", "      <th>Question count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>4.460510e+05</td>\n", "      <td>446051.000000</td>\n", "      <td>446051.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>4.689134e+07</td>\n", "      <td>1.955394</td>\n", "      <td>28.581337</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.016672e+07</td>\n", "      <td>1.059917</td>\n", "      <td>5.101046</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>3.600001e+06</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.880574e+07</td>\n", "      <td>1.101785</td>\n", "      <td>26.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>4.400022e+07</td>\n", "      <td>1.816121</td>\n", "      <td>29.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>7.520527e+07</td>\n", "      <td>2.694062</td>\n", "      <td>33.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.731129e+07</td>\n", "      <td>8.116385</td>\n", "      <td>55.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           CNTSTUID      Avg score  Question count\n", "count  4.460510e+05  446051.000000   446051.000000\n", "mean   4.689134e+07       1.955394       28.581337\n", "std    3.016672e+07       1.059917        5.101046\n", "min    3.600001e+06       0.000000        1.000000\n", "25%    1.880574e+07       1.101785       26.000000\n", "50%    4.400022e+07       1.816121       29.000000\n", "75%    7.520527e+07       2.694062       33.000000\n", "max    9.731129e+07       8.116385       55.000000"]}, "metadata": {}, "execution_count": 19}], "metadata": {}}, {"cell_type": "code", "execution_count": 21, "source": ["def level(score):\n", "    return int(score * 10)\n", "\n", "df_student_level = df_student[\"Avg score\"].apply(level)\n", "df_student_level_dist = df_student_level.groupby(df_student_level).size()\n", "fig = px.bar(\n", "    df_student_level_dist,\n", "    labels={\"value\": \"Students counts\", \"index\": \"Score range\"},\n", "    title=\"Score distribution\",\n", "    hover_data={\n", "        \"variable\":<PERSON><PERSON><PERSON>,\n", "    }\n", ")\n", "fig.update_layout(showlegend=False)\n", "fig.show('svg')\n", "\n", "df_student_level_dist\n"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-742521\"><g class=\"clips\"><clipPath id=\"clip742521xyplot\" class=\"plotclip\"><rect width=\"540\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip742521x\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip742521y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip742521xy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,383.41)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,346.82)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,310.23)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,273.64)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,237.05)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,200.47)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,163.88)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,127.28999999999999)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,420)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url(#clip742521xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0.66,320V282.4H5.93V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M7.24,320V280.5H12.51V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M13.83,320V245.94H19.1V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M20.41,320V207.6H25.68V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M27,320V158.81H32.27V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M33.59,320V114.37H38.85V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M40.17,320V80.05H45.44V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M46.76,320V44.63H52.02V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M53.34,320V33.34H58.61V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M59.93,320V21.58H65.2V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M66.51,320V16H71.78V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M73.1,320V19.46H78.37V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M79.68,320V23.5H84.95V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M86.27,320V24.8H91.54V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M92.85,320V29.41H98.12V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M99.44,320V40.53H104.71V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M106.02,320V48.99H111.29V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M112.61,320V52.39H117.88V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M119.2,320V60.22H124.46V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M125.78,320V66.11H131.05V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M132.37,320V72.48H137.63V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M138.95,320V79.24H144.22V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M145.54,320V86.07H150.8V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M152.12,320V95.82H157.39V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M158.71,320V98.27H163.98V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M165.29,320V107.09H170.56V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M171.88,320V118.43H177.15V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M178.46,320V126.08H183.73V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M185.05,320V133.51H190.32V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M191.63,320V144.83H196.9V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M198.22,320V154.73H203.49V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M204.8,320V163.34H210.07V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M211.39,320V174.3H216.66V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M217.98,320V185.11H223.24V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M224.56,320V198.84H229.83V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M231.15,320V211.48H236.41V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M237.73,320V220.83H243V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M244.32,320V235.08H249.59V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M250.9,320V244.52H256.17V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M257.49,320V253.17H262.76V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M264.07,320V263.89H269.34V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M270.66,320V270.37H275.93V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M277.24,320V278.75H282.51V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M283.83,320V285.84H289.1V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M290.41,320V292.36H295.68V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M297,320V297.52H302.27V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M303.59,320V303.02H308.85V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M310.17,320V306.52H315.44V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M316.76,320V308.05H322.02V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M323.34,320V311.26H328.61V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M329.93,320V311.97H335.2V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M336.51,320V315.39H341.78V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M343.1,320V315.23H348.37V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M349.68,320V315.92H354.95V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M356.27,320V317.4H361.54V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M362.85,320V317.93H368.12V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M369.44,320V318.61H374.71V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M376.02,320V318.68H381.29V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M382.61,320V319.25H387.88V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M389.2,320V319.45H394.46V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M395.78,320V319.41H401.05V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M402.37,320V319.78H407.63V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M408.95,320V319.8H414.22V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M415.54,320V319.91H420.8V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M422.12,320V319.87H427.39V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M428.71,320V319.89H433.98V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M435.29,320V319.95H440.56V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M441.88,320V319.98H447.15V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M461.63,320V319.98H466.9V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M474.8,320V319.98H480.07V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M520.9,320V319.98H526.17V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M534.07,320V319.95H539.34V320Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(83.29,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(215,0)\">20</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(346.71,0)\">40</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(478.41,0)\">60</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(610.12,0)\">80</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,420)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,383.41)\">2k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,346.82)\">4k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,310.23)\">6k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,273.64)\">8k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,237.05)\">10k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,200.47)\">12k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,163.88)\">14k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,127.28999999999999)\">16k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-742521\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Score distribution</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Score range</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,34.840625,260)\" x=\"34.840625\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Students counts</text></g></g></svg>"}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": ["Avg score\n", "0     2055\n", "1     2159\n", "2     4048\n", "3     6144\n", "4     8811\n", "      ... \n", "67       1\n", "70       1\n", "72       1\n", "79       1\n", "81       3\n", "Name: Avg score, Length: 72, dtype: int64"]}, "metadata": {}, "execution_count": 21}], "metadata": {}}, {"cell_type": "markdown", "source": ["# Sort students by average score"], "metadata": {}}, {"cell_type": "code", "execution_count": 22, "source": ["df_student[\"CNTSTUID\"] = df_student[\"CNTSTUID\"].apply(str)\n", "s_top500 = \\\n", "    df_student[df_student[\"Question count\"] >= 10] \\\n", "        .nlargest(500, [\"Avg score\"]).set_index(\"CNTSTUID\")\n", "# We only select students who have answered more than 10 questions\n", "s_top500.index = s_top500.index.map(str)\n", "fig = px.bar(\n", "    s_top500[:20].iloc[::-1],\n", "    x = \"Avg score\",\n", "    orientation=\"h\",\n", "    title=\"The score of top 20 students\"\n", ")\n", "fig.update_layout(yaxis_type='category')\n", "fig.update_layout(showlegend=False)\n", "fig.show('svg')\n", "\n"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-0fdf3d\"><g class=\"clips\"><clipPath id=\"clip0fdf3dxyplot\" class=\"plotclip\"><rect width=\"529\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0fdf3dx\"><rect x=\"91\" y=\"0\" width=\"529\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0fdf3dy\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0fdf3dxy\"><rect x=\"91\" y=\"100\" width=\"529\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"91\" y=\"100\" width=\"529\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(167.09,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(243.17,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(319.26,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(395.35,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(471.44,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(547.52,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(91,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(91,100)\" clip-path=\"url(#clip0fdf3dxyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H479.27V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,302.4V289.6H482.62V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,286.4V273.6H483.54V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,270.4V257.6H483.64V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,254.4V241.6H484.47V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,238.4V225.6H486.56V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,222.4V209.6H487.64V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,206.4V193.6H487.96V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,190.4V177.6H488.67V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,174.4V161.6H490.31V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,158.4V145.6H490.84V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,142.4V129.6H491.91V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,126.4V113.6H492.21V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,110.4V97.6H494.74V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,94.4V81.6H497.58V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,78.4V65.6H497.58V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,62.4V49.6H497.58V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,46.4V33.6H497.58V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,30.4V17.6H497.58V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,14.4V1.6H502.55V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(91,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(167.09,0)\">1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(243.17,0)\">2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(319.26,0)\">3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(395.35,0)\">4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(471.44,0)\">5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(547.52,0)\">6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,412)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">70205561</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,396)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">55403679</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,380)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">15801011</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,364)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">70203542</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,348)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">25003876</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,332)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">34803452</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,316)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">23304152</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,300)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">38000847</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,284)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">70506245</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,268)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">70206212</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,252)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">55404823</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,236)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">12427245</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,220)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">3608788</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,204)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">25007210</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,188)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">70501428</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,172)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">70505354</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,156)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">23301534</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,140)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">23300751</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,124)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">12402079</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"90\" y=\"4.199999999999999\" transform=\"translate(0,108)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">12426188</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-0fdf3d\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">The score of top 20 students</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"355.5\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Avg score</text></g><g class=\"g-ytitle\" transform=\"translate(1.1904296875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,11.809375000000003,260)\" x=\"11.809375000000003\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">CNTSTUID</text></g></g></svg>"}, "metadata": {}}], "metadata": {}}, {"cell_type": "code", "execution_count": 24, "source": ["s_top500_dist = s_top500.groupby(\"CNTRYID\").size()\n", "s_top500_dist = s_top500_dist[s_top500_dist > 10]\n", "fig = px.pie(\n", "    values=s_top500_dist,\n", "    names=s_top500_dist.index,\n", "    title=\"Top 500 students come from\"\n", ")\n", "fig.show('svg')"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-5cd9a4\"><g class=\"clips\"/><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M270,260l0,-160a160,160 0 0 1 97.8132807448698,33.380245972734244Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(308.89644753865804,149.54198679153382)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">10.5%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-76.98349395543087,-140.26240286838856a160,160 0 0 1 76.98349395543087,-19.737597131611437Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(238.00465190794162,139.84691461529712)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.99%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-133.46629755006984,-88.24254879748327a160,160 0 0 1 56.482803594638966,-52.019854070905296Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(182.42046944530105,169.0848795819955)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.71%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-159.2082033925126,-15.898049330919433a160,160 0 0 1 25.741905842442748,-72.34449946656383Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(148.26642533964917,220.7426555105298)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.71%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-150.27172902604394,54.94003508847822a160,160 0 0 1 -8.936474366468644,-70.83808441939765Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(140.0519774698938,280.372704619187)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.16%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-111.4106662716916,114.83755239858502a160,160 0 0 1 -38.861062754352346,-59.8975173101068Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(160.09642299741967,335.1981790102901)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.16%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-65.83598710506178,145.8273732942555a160,160 0 0 1 -45.57467916662982,-30.989820895670476Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(193.08658933784278,376.87018826323583)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">5.51%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-12.449918524790311,159.51488810993814a160,160 0 0 1 -53.38606858027146,-13.687514815682647Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(235.93745697634276,396.2158646308436)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">5.51%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l39.736730747872514,154.98707116876258a160,160 0 0 1 -52.18664927266283,4.527816941175558Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(281.7204514302777,400.9783646982985)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">5.23%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l85.33440181429567,135.34415342746183a160,160 0 0 1 -45.59767106642316,19.64291774130075Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(324.6357475304182,391.2083783671141)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">4.96%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l120.91993189668133,104.77771743124558a160,160 0 0 1 -35.58553008238566,30.566435996216256Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(360.7602222848948,369.85430607064563)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">4.68%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l144.96044074517056,67.7234864649325a160,160 0 0 1 -24.040508848489225,37.05423096631307Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(387.7809402136281,340.52183859786066)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">4.41%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l157.9535931236569,25.508085371627736a160,160 0 0 1 -12.993152378486343,42.21540109330477Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(404.2091377165515,305.3576898663035)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">4.41%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l159.45952073926867,-13.14006261791556a160,160 0 0 1 -1.5059276156117676,38.6481479895433Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(399.2799958592789,269.0404388998976)rotate(2.2314049586777287)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.86%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l152.5077152673246,-48.387981813056044a160,160 0 0 1 6.951805471944056,35.24791919514048Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(399.2503575509187,238.58552533644817)rotate(-11.157024793388416)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.58%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l139.25122873206283,-78.79781276539796a160,160 0 0 1 13.256486535261786,30.40983095234192Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(122, 230, 248); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(393.0092315316964,210.7404184013777)rotate(-23.553719008264466)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.31%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l120.00861334707052,-105.82028502377666a160,160 0 0 1 19.242615384992305,27.0224722583787Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 204, 219); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(380.2070452654598,186.43244876206137)rotate(-35.45454545454544)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.31%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l97.8132807448699,-126.61975402726569a160,160 0 0 1 22.195332602200622,20.799469003489023Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(232, 248, 214); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(364.5621696179785,164.94146410681316)rotate(-46.85950413223139)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.03%</text></g></g></g></g><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-5cd9a4\"><g class=\"clips\"/><clipPath id=\"legend5cd9a4\"><rect width=\"220\" height=\"320\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(467.6,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"220\" height=\"320\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend5cd9a4)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Singapore</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">B-S-J-G (China)</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,52.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Canada</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,71.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Chinese Taipei</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,90.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Australia</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,109.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">United Kingdom</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,128.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Germany</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,147.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Slovenia</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,166.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Estonia</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,185.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Japan</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,204.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Spain (Regions)_duplicated_971</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,223.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Austria</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,242.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Czech Republic</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,261.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Netherlands</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,280.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Finland</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,299.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Korea</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(122, 230, 248); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,318.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Norway</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 204, 219); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,337.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Belgium</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(232, 248, 214); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Top 500 students come from</text></g></g></svg>"}, "metadata": {}}], "metadata": {}}, {"cell_type": "code", "execution_count": 25, "source": ["s_bottom500 = df_student[(df_student[\"Question count\"] > 10) & (df_student[\"Avg score\"] > 0.1)]\\\n", "    .nsmallest(500, \"Avg score\")\n", "# A too low score indicates that the student may answer questions blindly\n", "s_bottom500.set_index(\"CNTSTUID\")\n", "s_bottom500.index = s_bottom500.index.map(str)\n", "fig = px.bar(\n", "    s_bottom500.iloc[:20].iloc[::-1],\n", "    x = \"Avg score\",\n", "    orientation=\"h\",\n", "    title=\"The score of bottom 20 students\"\n", ")\n", "fig.update_layout(yaxis_type='category')\n", "fig.show('svg')"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-4b1954\"><g class=\"clips\"><clipPath id=\"clip4b1954xyplot\" class=\"plotclip\"><rect width=\"540\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4b1954x\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4b1954y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4b1954xy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(180.97,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(281.94,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(382.91,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(483.87,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(584.8399999999999,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(80,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url(#clip4b1954xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H513V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,302.4V289.6H512.99V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,286.4V273.6H512.93V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,270.4V257.6H512.93V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,254.4V241.6H512.74V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,238.4V225.6H511.59V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,222.4V209.6H510.08V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,206.4V193.6H510.08V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,190.4V177.6H510.08V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,174.4V161.6H510.08V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,158.4V145.6H510.08V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,142.4V129.6H510.08V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,126.4V113.6H510.08V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,110.4V97.6H510.08V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,94.4V81.6H505.42V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,78.4V65.6H505.42V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,62.4V49.6H505.27V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,46.4V33.6H505.27V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,30.4V17.6H505.27V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,14.4V1.6H504.98V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(80,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(180.97,0)\">0.02</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(281.94,0)\">0.04</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(382.91,0)\">0.06</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(483.87,0)\">0.08</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(584.8399999999999,0)\">0.1</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,412)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">47407</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,396)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">48267</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,380)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">215776</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,364)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">103244</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,348)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">423731</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,332)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">196745</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,316)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">470656</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,300)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">469362</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,284)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">462848</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,268)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">368648</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,252)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271041</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,236)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">258559</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,220)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">145639</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,204)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">55625</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,188)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">182926</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,172)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">47186</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,156)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">423299</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,140)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">126597</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,124)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">48022</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,108)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">42598</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-4b1954\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">The score of bottom 20 students</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Avg score</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,14.153125000000003,260)\" x=\"14.153125000000003\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">index</text></g></g></svg>"}, "metadata": {}}], "metadata": {}}, {"cell_type": "code", "execution_count": 26, "source": ["s_bottom500_dist = s_bottom500.groupby(\"CNTRYID\").size()\n", "s_bottom500_dist = s_bottom500_dist[s_bottom500_dist > 10]\n", "fig = px.pie(\n", "    names=s_bottom500_dist.index,\n", "    values=s_bottom500_dist,\n", "    title=\"Bottom 500 students come from\",\n", ")\n", "fig.show('svg')"], "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-2b1218\"><g class=\"clips\"/><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M270,260l0,-160a160,160 0 0 1 156.21164049418155,125.38897034012746Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(331.4332390401223,187.26106915547462)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">21.5%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-91.02552949082238,-131.58401491410507a160,160 0 0 1 91.02552949082238,-28.41598508589493Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(233.10314624083566,146.33306837914313)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">9.63%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-148.6907354863461,-59.08523656997113a160,160 0 0 1 57.665205995523735,-72.49877834413394Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(172.72766942418065,186.76051527544456)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">9.35%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-157.5966375009519,27.627881721072292a160,160 0 0 1 8.9059020146058,-86.71311829104343Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(144.74771527185285,251.15276912766518)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">8.78%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-130.3571770674041,92.77395317122935a160,160 0 0 1 -27.239460433547805,-65.14607145015705Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(148.90512699729123,314.5646764963517)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.08%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-80.20544159255519,138.44524960771756a160,160 0 0 1 -50.15173547484892,-45.671296436488205Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(180.9482742525729,361.6077081665867)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">6.8%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l-21.29588596443261,158.57643343507848a160,160 0 0 1 -58.909555628122575,-20.13118382736093Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(226.5230255033258,390.7458695209057)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">6.23%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l35.30580274579438,156.05608060077333a160,160 0 0 1 -56.601688710226995,2.5203528343051573Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(275.8827271681045,399.797854106655)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">5.67%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l77.72862552630976,139.8508518879692a160,160 0 0 1 -42.422822780515375,16.205228712804114Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(319.83537413276025,394.8909165670249)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">4.53%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l109.81784102467189,116.36168524338194a160,160 0 0 1 -32.08921549836214,23.48916664458727Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(342.96281236569104,366.4486314391388)rotate(53.79603399433432)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.97%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l133.5765290687141,88.07559753958515a160,160 0 0 1 -23.758688044042216,28.286087703796795Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(367.29393158945663,346.9451079777151)rotate(40.028328611898075)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.68%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l149.21068072738333,57.75961181371349a160,160 0 0 1 -15.634151658669225,30.315985725871656Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(388.70360618946444,325.71680774913403)rotate(27.28045325779044)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.4%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l157.59663750095189,27.62788172107247a160,160 0 0 1 -8.385956773568552,30.13173009264102Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(397.5606242288819,299.6533998624107)rotate(15.55240793201142)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.12%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l159.9603993400836,-3.559584661302874a160,160 0 0 1 -2.363761839131712,31.187466382375344Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(402.83776623640324,274.0795183025885)rotate(4.33427762039662)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.12%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M270,260l156.21164049418155,-34.61102965987257a160,160 0 0 1 3.7487588459020458,31.051444998569696Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(403.0388169286772,247.96762016356968)rotate(-6.883852691218124)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">3.12%</text></g></g></g></g><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-2b1218\"><g class=\"clips\"/><clipPath id=\"legend2b1218\"><rect width=\"220\" height=\"295\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(467.6,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"220\" height=\"295\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend2b1218)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Brazil</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Dominican Republic</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,52.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">United Arab Emirates</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,71.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Colombia</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,90.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Qatar</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,109.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Peru</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,128.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Spain (Regions)_duplicated_971</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,147.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Montenegro</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,166.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Thailand</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,185.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Italy</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,204.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Australia</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,223.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Mexico</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,242.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Israel</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,261.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Tunisia</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,280.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Uruguay</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"214.453125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Bottom 500 students come from</text></g></g></svg>"}, "metadata": {}}], "metadata": {}}, {"cell_type": "code", "execution_count": null, "source": [], "outputs": [], "metadata": {}}], "metadata": {"interpreter": {"hash": "e7370f93d1d0cde622a1f8e1c04877d8463912d04d973331ad4851f04de6915a"}, "kernelspec": {"name": "python3", "display_name": "Python 3.9.7 64-bit"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}