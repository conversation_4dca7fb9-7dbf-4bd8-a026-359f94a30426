{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#  OLI data in fall, 2011（transaction）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import pandas as pd\n", "import numpy as np\n", "# global configuration: show every rows and cols\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('max_colwidth',None)\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. Data Description\n", "## 1.1 Column Description"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Field</th>\n", "      <th>Annotation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>A row counter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Sample Name</td>\n", "      <td>The sample that contains the transaction. If a transaction appears in multiple samples,                    the transaction will be repeated, but with a different sample name.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Transaction Id</td>\n", "      <td>A unique ID that identifies the transaction. Currently used for annotating transactions with                    custom fields via                    web services.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON> Student Id</td>\n", "      <td>DataShop-generated anonymous student ID. To obtain original student identifiers or to                    learn more about data anonymization, see About data anonymization                    below.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Session Id</td>\n", "      <td>A dataset-unique string that identifies the user's session with the tutor.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Time</td>\n", "      <td>Time the transaction occurred. For instance, if a student types \"25\" and presses return, the                     transaction time is at the point in which they press return.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Time Zone</td>\n", "      <td>The local time zone (e.g., EST, PST, US/Eastern).</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Duration (sec)</td>\n", "      <td>Duration of the transaction in seconds. This is the time of the current transaction minus                    that of the preceding transaction or problem start event—whichever is closer in time to the                    current transaction. If this difference is greater than 10 minutes, or if the prior transaction occurred                    during a different user session, DataShop reports the duration as null                    (a dot). If the current transaction is preceded by neither another transaction or a problem start event,                    duration is shown as null. The duration is formatted without decimal places if the two times used                    in the calculation were without millisecond precision.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Student Response Type</td>\n", "      <td>The type of attempt made by the student (e.g., \"ATTEMPT\" or \"HINT_REQUEST\").                    This is logged in the                     semantic_event element.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Student Response Subtype</td>\n", "      <td>A more detailed classification of the student attempt. For example,                    the CTAT software describes actions taken by the tutor on behalf of the student                    as having subtype \"tutor-performed\".</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Tutor Response Type</td>\n", "      <td>The type of response made by the tutor (e.g., \"RESULT\" or \"HINT_MSG\").</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Tutor Response Subtype</td>\n", "      <td>A more detailed classification of the tutor response.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Level (level_type)</td>\n", "      <td>The problem hierarchy name (e.g., \"Understanding Fractions\") of the type                    specified in the column header (e.g., \"Unit\"). There may be multiple \"Level\" columns if                    the problem hierarchy is more than one level deep. Level is logged in                    the                     level element.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Problem Name</td>\n", "      <td>The name of the problem. Two problems with the same \"Problem Name\" are                    considered different \"problems\" by DataShop if the following logged values are not                    identical: problem name, context, tutor_flag (whether or not the problem                    or activity is tutored) and \"other\" field. These fields are logged in the                                        problem element.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Problem View</td>\n", "      <td>The number of times the student encountered the problem so far. This counter increases                    with each instance of the same problem. See \"Problem View\" in the \"By Student-Step\" table below.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Problem Start Time</td>\n", "      <td>If the problem start time is not given in the original log data, then it is set to the time                    of the last transaction of the prior problem. If there is no prior problem for the session,                    the time of the earliest transaction is used. Earliest transaction time is equivalent                    to the minimum transaction time for the earliest step of the problem. For more detail                    on how problem start time is determined, see Determining                    Problem Start Time.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Step Name</td>\n", "      <td>Formed by concatenating the \"selection\" and \"action\". Also see the                    glossary entry for \"step\".</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Attempt at Step</td>\n", "      <td>As of this transaction, the current number of attempts toward the identified step.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Outcome</td>\n", "      <td>The tutor's evaluation of the student's attempt. For example, \"CORRECT\", \"INCORRECT\", or \"HINT\".                    This is logged in the                     action_evaluation element.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Selection</td>\n", "      <td>A description of the interface element(s) that the student selected or                    interacted with (for example, \"LowestCommonDenominatorCell\"). This is logged in the                    event_descriptor                    element.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Action</td>\n", "      <td>A description of the manipulation applied to the selection.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Input</td>\n", "      <td>The input the student submitted (e.g., the text entered, the text of a menu item or a combobox entry).</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Feedback Text</td>\n", "      <td>The body of a hint, success, or incorrect action message shown to the student.                    It is generally a text value, logged in the                     tutor_advice element.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Feedback Classification</td>\n", "      <td>The type of error (e.g., \"sign error\") or type of hint.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Help Level</td>\n", "      <td>In the case of hierarchical hints, this is the depth of the hint. \"1\", for example, is an initial hint,                    while \"3\" is the third hint.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Total Num Hints</td>\n", "      <td>The total number of hints available. This is logged in the                    action_evaluation                    element.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Condition Name</td>\n", "      <td>The name of the condition (e.g., \"Unworked\").</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Condition Type</td>\n", "      <td>A condition classification (e.g., \"Experimental\", \"Control\"); optional at the time of logging.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td><PERSON> (model_name)</td>\n", "      <td>The knowledge component for this transaction. It is a member of the knowledge component model                    named in the column header. One \"KC (model_name)\" column should appear in the export for each KC model                    in the dataset.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>KC Category (model_name)</td>\n", "      <td>The knowledge component \"category\" logged by some tutors.                    It is a member of the knowledge component model named in the column header. One \"KC Category (model_name)\"                    column should appear in the export for each KC model in the dataset.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>School</td>\n", "      <td>The name of the school where the student used the tutor to create this transaction.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>Class</td>\n", "      <td>The name of the class the student was in when he or she                    used the tutor to create this transaction.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>CF (custom_field_name)</td>\n", "      <td>The value of a custom field. This is usually information that did not fit into any of the                    other logging fields (i.e., any of the other columns), and so was logged in this                    special container.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>Event Type</td>\n", "      <td>Allowed values are \"assess\", \"instruct\" and \"assess_instruct\". Blank is also allowed.                    Only \"instruct\" and \"assess_instruct\" values are treated as learning opportunities.</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       Field  \\\n", "0                        Row   \n", "1                Sample Name   \n", "2             Transaction Id   \n", "3            Anon Student Id   \n", "4                 Session Id   \n", "5                       Time   \n", "6                  Time Zone   \n", "7             Duration (sec)   \n", "8      Student Response Type   \n", "9   Student Response Subtype   \n", "10       Tutor Response Type   \n", "11    Tutor Response Subtype   \n", "12        Level (level_type)   \n", "13              Problem Name   \n", "14              Problem View   \n", "15        Problem Start Time   \n", "16                 Step Name   \n", "17           Attempt at Step   \n", "18                   Outcome   \n", "19                 Selection   \n", "20                    Action   \n", "21                     Input   \n", "22             Feedback Text   \n", "23   Feedback Classification   \n", "24                Help Level   \n", "25           Total Num Hints   \n", "26            Condition Name   \n", "27            Condition Type   \n", "28           KC (model_name)   \n", "29  KC Category (model_name)   \n", "30                    School   \n", "31                     Class   \n", "32    CF (custom_field_name)   \n", "33                Event Type   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Annotation  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               A row counter  \n", "1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              The sample that contains the transaction. If a transaction appears in multiple samples,                    the transaction will be repeated, but with a different sample name.  \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          A unique ID that identifies the transaction. Currently used for annotating transactions with                    custom fields via                    web services.  \n", "3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        DataShop-generated anonymous student ID. To obtain original student identifiers or to                    learn more about data anonymization, see About data anonymization                    below.  \n", "4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  A dataset-unique string that identifies the user's session with the tutor.  \n", "5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Time the transaction occurred. For instance, if a student types \"25\" and presses return, the                     transaction time is at the point in which they press return.  \n", "6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           The local time zone (e.g., EST, PST, US/Eastern).  \n", "7   Duration of the transaction in seconds. This is the time of the current transaction minus                    that of the preceding transaction or problem start event—whichever is closer in time to the                    current transaction. If this difference is greater than 10 minutes, or if the prior transaction occurred                    during a different user session, DataShop reports the duration as null                    (a dot). If the current transaction is preceded by neither another transaction or a problem start event,                    duration is shown as null. The duration is formatted without decimal places if the two times used                    in the calculation were without millisecond precision.  \n", "8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           The type of attempt made by the student (e.g., \"ATTEMPT\" or \"HINT_REQUEST\").                    This is logged in the                     semantic_event element.  \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              A more detailed classification of the student attempt. For example,                    the CTAT software describes actions taken by the tutor on behalf of the student                    as having subtype \"tutor-performed\".  \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     The type of response made by the tutor (e.g., \"RESULT\" or \"HINT_MSG\").  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      A more detailed classification of the tutor response.  \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                     The problem hierarchy name (e.g., \"Understanding Fractions\") of the type                    specified in the column header (e.g., \"Unit\"). There may be multiple \"Level\" columns if                    the problem hierarchy is more than one level deep. Level is logged in                    the                     level element.  \n", "13                                                                                                                                                                                                                                                                                                                              The name of the problem. Two problems with the same \"Problem Name\" are                    considered different \"problems\" by DataShop if the following logged values are not                    identical: problem name, context, tutor_flag (whether or not the problem                    or activity is tutored) and \"other\" field. These fields are logged in the                                        problem element.  \n", "14                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 The number of times the student encountered the problem so far. This counter increases                    with each instance of the same problem. See \"Problem View\" in the \"By Student-Step\" table below.  \n", "15                                                                                                                                                                                                            If the problem start time is not given in the original log data, then it is set to the time                    of the last transaction of the prior problem. If there is no prior problem for the session,                    the time of the earliest transaction is used. Earliest transaction time is equivalent                    to the minimum transaction time for the earliest step of the problem. For more detail                    on how problem start time is determined, see Determining                    Problem Start Time.  \n", "16                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Formed by concatenating the \"selection\" and \"action\". Also see the                    glossary entry for \"step\".  \n", "17                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         As of this transaction, the current number of attempts toward the identified step.  \n", "18                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   The tutor's evaluation of the student's attempt. For example, \"CORRECT\", \"INCORRECT\", or \"HINT\".                    This is logged in the                     action_evaluation element.  \n", "19                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              A description of the interface element(s) that the student selected or                    interacted with (for example, \"LowestCommonDenominatorCell\"). This is logged in the                    event_descriptor                    element.  \n", "20                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                A description of the manipulation applied to the selection.  \n", "21                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     The input the student submitted (e.g., the text entered, the text of a menu item or a combobox entry).  \n", "22                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    The body of a hint, success, or incorrect action message shown to the student.                    It is generally a text value, logged in the                     tutor_advice element.  \n", "23                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    The type of error (e.g., \"sign error\") or type of hint.  \n", "24                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    In the case of hierarchical hints, this is the depth of the hint. \"1\", for example, is an initial hint,                    while \"3\" is the third hint.  \n", "25                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                The total number of hints available. This is logged in the                    action_evaluation                    element.  \n", "26                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              The name of the condition (e.g., \"Unworked\").  \n", "27                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             A condition classification (e.g., \"Experimental\", \"Control\"); optional at the time of logging.  \n", "28                                                                                                                                                                                                                                                                                                                                                                                                                                                                             The knowledge component for this transaction. It is a member of the knowledge component model                    named in the column header. One \"KC (model_name)\" column should appear in the export for each KC model                    in the dataset.                      \n", "29                                                                                                                                                                                                                                                                                                                                                                                                                                                                            The knowledge component \"category\" logged by some tutors.                    It is a member of the knowledge component model named in the column header. One \"KC Category (model_name)\"                    column should appear in the export for each KC model in the dataset.  \n", "30                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        The name of the school where the student used the tutor to create this transaction.  \n", "31                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      The name of the class the student was in when he or she                    used the tutor to create this transaction.  \n", "32                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    The value of a custom field. This is usually information that did not fit into any of the                    other logging fields (i.e., any of the other columns), and so was logged in this                    special container.                      \n", "33                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Allowed values are \"assess\", \"instruct\" and \"assess_instruct\". Blank is also allowed.                    Only \"instruct\" and \"assess_instruct\" values are treated as learning opportunities.                      "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# help_table1: the description for data by transactions\n", "df1 = pd.read_csv('OLI_data/help_table1.csv',sep=',',encoding=\"gbk\")\n", "df1 = df1.loc[:, ['Field', 'Annotation']]\n", "df1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.2 Summarization of Data\n", "\n", "**This table organizes the data as student-problem-step-transaction**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th>Sample Name</th>\n", "      <th>Transaction Id</th>\n", "      <th><PERSON><PERSON> Student Id</th>\n", "      <th>Session Id</th>\n", "      <th>Time</th>\n", "      <th>Time Zone</th>\n", "      <th>Duration (sec)</th>\n", "      <th>Student Response Type</th>\n", "      <th>Student Response Subtype</th>\n", "      <th>Tutor Response Type</th>\n", "      <th>Tutor Response Subtype</th>\n", "      <th>Level (Sequence)</th>\n", "      <th>Level (Unit)</th>\n", "      <th>Level (Module)</th>\n", "      <th>Level (Section1)</th>\n", "      <th>Problem Name</th>\n", "      <th>Problem View</th>\n", "      <th>Problem Start Time</th>\n", "      <th>Step Name</th>\n", "      <th>Attempt At Step</th>\n", "      <th>Is Last Attempt</th>\n", "      <th>Outcome</th>\n", "      <th>Selection</th>\n", "      <th>Action</th>\n", "      <th>Input</th>\n", "      <th>Input.1</th>\n", "      <th>Feedback Text</th>\n", "      <th>Feedback Classification</th>\n", "      <th>Help Level</th>\n", "      <th>Total Num Hints</th>\n", "      <th><PERSON> (Single-KC)</th>\n", "      <th>KC Category (Single-KC)</th>\n", "      <th><PERSON> (Unique-step)</th>\n", "      <th>KC Category (Unique-step)</th>\n", "      <th>KC (F2011)</th>\n", "      <th>KC Category (F2011)</th>\n", "      <th>KC (F2011).1</th>\n", "      <th>KC Category (F2011).1</th>\n", "      <th>KC (F2011).2</th>\n", "      <th>KC Category (F2011).2</th>\n", "      <th>School</th>\n", "      <th>Class</th>\n", "      <th>CF (oli:activityGuid)</th>\n", "      <th>CF (oli:highStakes)</th>\n", "      <th>CF (oli:purpose)</th>\n", "      <th>CF (oli:resourceType)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>All Data</td>\n", "      <td>2adbe4abefd649d48862d3f62b1abf5e</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:26:36</td>\n", "      <td>US/Eastern</td>\n", "      <td>1</td>\n", "      <td>VIEW_PAGE</td>\n", "      <td>UI Event</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Statics</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Navigation</td>\n", "      <td>SelectPageNumber</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>All Data</td>\n", "      <td>4393251e32a6f00502f3f1ef894af8fe</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:35:28</td>\n", "      <td>US/Eastern</td>\n", "      <td>23.13</td>\n", "      <td>ATTEMPT</td>\n", "      <td>NaN</td>\n", "      <td>RESULT</td>\n", "      <td>NaN</td>\n", "      <td>Statics</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>q1_point1i1 UpdateComboBox</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>CORRECT</td>\n", "      <td>q1_point1i1</td>\n", "      <td>UpdateComboBox</td>\n", "      <td>&lt;material&gt;cord c&lt;/material&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Single-KC</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>identify_interaction</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>All Data</td>\n", "      <td>e2fb2cb788d10ebaa6f288e0757d1b09</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:35:28</td>\n", "      <td>US/Eastern</td>\n", "      <td>23.13</td>\n", "      <td>ATTEMPT</td>\n", "      <td>NaN</td>\n", "      <td>RESULT</td>\n", "      <td>NaN</td>\n", "      <td>Statics</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>q1_point3i3 UpdateComboBox</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>CORRECT</td>\n", "      <td>q1_point3i3</td>\n", "      <td>UpdateComboBox</td>\n", "      <td>&lt;material&gt;120 lb&lt;/material&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Single-KC</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>gravitational_forces</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>All Data</td>\n", "      <td>e7e150d423862e346dc7e36a95e394e4</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:35:28</td>\n", "      <td>US/Eastern</td>\n", "      <td>23.13</td>\n", "      <td>ATTEMPT</td>\n", "      <td>NaN</td>\n", "      <td>RESULT</td>\n", "      <td>NaN</td>\n", "      <td>Statics</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>q1_point6i2 UpdateComboBox</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>INCORRECT</td>\n", "      <td>q1_point6i2</td>\n", "      <td>UpdateComboBox</td>\n", "      <td>&lt;material&gt;no interaction&lt;/material&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Single-KC</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>represent_interaction_spring</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>All Data</td>\n", "      <td>684b1f770a225f21745c6c4c977ddc32</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:35:28</td>\n", "      <td>US/Eastern</td>\n", "      <td>23.13</td>\n", "      <td>ATTEMPT</td>\n", "      <td>NaN</td>\n", "      <td>RESULT</td>\n", "      <td>NaN</td>\n", "      <td>Statics</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>q1_point1i2 UpdateComboBox</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>CORRECT</td>\n", "      <td>q1_point1i2</td>\n", "      <td>UpdateComboBox</td>\n", "      <td>&lt;material&gt;up&lt;/material&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Single-KC</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>represent_interaction_cord</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Row Sample Name                    Transaction Id  \\\n", "0    1    All Data  2adbe4abefd649d48862d3f62b1abf5e   \n", "1    2    All Data  4393251e32a6f00502f3f1ef894af8fe   \n", "2    3    All Data  e2fb2cb788d10ebaa6f288e0757d1b09   \n", "3    4    All Data  e7e150d423862e346dc7e36a95e394e4   \n", "4    5    All Data  684b1f770a225f21745c6c4c977ddc32   \n", "\n", "                        Anon Student Id                        Session Id  \\\n", "0  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "1  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "2  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "3  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "4  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "\n", "                  Time   Time Zone Duration (sec) Student Response Type  \\\n", "0  2011-09-21 17:26:36  US/Eastern              1             VIEW_PAGE   \n", "1  2011-09-21 17:35:28  US/Eastern          23.13               ATTEMPT   \n", "2  2011-09-21 17:35:28  US/Eastern          23.13               ATTEMPT   \n", "3  2011-09-21 17:35:28  US/Eastern          23.13               ATTEMPT   \n", "4  2011-09-21 17:35:28  US/Eastern          23.13               ATTEMPT   \n", "\n", "  Student Response Subtype Tutor Response Type  Tutor Response Subtype  \\\n", "0                 UI Event                 NaN                     NaN   \n", "1                      NaN              RESULT                     NaN   \n", "2                      NaN              RESULT                     NaN   \n", "3                      NaN              RESULT                     NaN   \n", "4                      NaN              RESULT                     NaN   \n", "\n", "  Level (Sequence)                           Level (Unit)  \\\n", "0          Statics  Concentrated Forces and Their Effects   \n", "1          Statics  Concentrated Forces and Their Effects   \n", "2          Statics  Concentrated Forces and Their Effects   \n", "3          Statics  Concentrated Forces and Their Effects   \n", "4          Statics  Concentrated Forces and Their Effects   \n", "\n", "                       Level (Module) Level (Section1) Problem Name  \\\n", "0  Introduction to Free Body Diagrams              NaN   _m2_assess   \n", "1  Introduction to Free Body Diagrams              NaN   _m2_assess   \n", "2  Introduction to Free Body Diagrams              NaN   _m2_assess   \n", "3  Introduction to Free Body Diagrams              NaN   _m2_assess   \n", "4  Introduction to Free Body Diagrams              NaN   _m2_assess   \n", "\n", "   Problem View   Problem Start Time                   Step Name  \\\n", "0             1  2011-09-21 17:26:35                         NaN   \n", "1             1  2011-09-21 17:26:35  q1_point1i1 UpdateComboBox   \n", "2             1  2011-09-21 17:26:35  q1_point3i3 UpdateComboBox   \n", "3             1  2011-09-21 17:26:35  q1_point6i2 UpdateComboBox   \n", "4             1  2011-09-21 17:26:35  q1_point1i2 UpdateComboBox   \n", "\n", "   Attempt At Step  Is Last Attempt    Outcome    Selection            Action  \\\n", "0              NaN              NaN        NaN   Navigation  SelectPageNumber   \n", "1              1.0              1.0    CORRECT  q1_point1i1    UpdateComboBox   \n", "2              1.0              1.0    CORRECT  q1_point3i3    UpdateComboBox   \n", "3              1.0              1.0  INCORRECT  q1_point6i2    UpdateComboBox   \n", "4              1.0              1.0    CORRECT  q1_point1i2    UpdateComboBox   \n", "\n", "                                 Input Input.1 Feedback Text  \\\n", "0                                    1     NaN           NaN   \n", "1          <material>cord c</material>     NaN           NaN   \n", "2          <material>120 lb</material>     NaN           NaN   \n", "3  <material>no interaction</material>     NaN           NaN   \n", "4              <material>up</material>     NaN           NaN   \n", "\n", "   Feedback Classification  Help Level  Total Num Hints KC (Single-KC)  \\\n", "0                      NaN         NaN              NaN            NaN   \n", "1                      NaN         NaN              NaN      Single-KC   \n", "2                      NaN         NaN              NaN      Single-KC   \n", "3                      NaN         NaN              NaN      Single-KC   \n", "4                      NaN         NaN              NaN      Single-KC   \n", "\n", "   KC Category (Single-KC) KC (Unique-step)  KC Category (Unique-step)  \\\n", "0                      NaN              NaN                        NaN   \n", "1                      NaN              NaN                        NaN   \n", "2                      NaN              NaN                        NaN   \n", "3                      NaN              NaN                        NaN   \n", "4                      NaN              NaN                        NaN   \n", "\n", "                     KC (F2011)  KC Category (F2011) KC (F2011).1  \\\n", "0                           NaN                  NaN          NaN   \n", "1          identify_interaction                  NaN          NaN   \n", "2          gravitational_forces                  NaN          NaN   \n", "3  represent_interaction_spring                  NaN          NaN   \n", "4    represent_interaction_cord                  NaN          NaN   \n", "\n", "   KC Category (F2011).1 KC (F2011).2  KC Category (F2011).2  \\\n", "0                    NaN          NaN                    NaN   \n", "1                    NaN          NaN                    NaN   \n", "2                    NaN          NaN                    NaN   \n", "3                    NaN          NaN                    NaN   \n", "4                    NaN          NaN                    NaN   \n", "\n", "                     School        Class CF (oli:activityGuid)  \\\n", "0  Marion Technical College  MET2010B-01                   NaN   \n", "1  Marion Technical College  MET2010B-01                   NaN   \n", "2  Marion Technical College  MET2010B-01                   NaN   \n", "3  Marion Technical College  MET2010B-01                   NaN   \n", "4  Marion Technical College  MET2010B-01                   NaN   \n", "\n", "  CF (oli:highStakes) CF (oli:purpose) CF (oli:resourceType)  \n", "0                 NaN              NaN                   NaN  \n", "1                 NaN              NaN                   NaN  \n", "2                 NaN              NaN                   NaN  \n", "3                 NaN              NaN                   NaN  \n", "4                 NaN              NaN                   NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df_transaction =  pd.read_csv('OLI_data/AllData_transaction_2011F.csv',low_memory=False) # sep=\"\\t\"\n", "df_transaction.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Data Analysis"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th>Tutor Response Subtype</th>\n", "      <th>Problem View</th>\n", "      <th>Attempt At Step</th>\n", "      <th>Is Last Attempt</th>\n", "      <th>Feedback Classification</th>\n", "      <th>Help Level</th>\n", "      <th>Total Num Hints</th>\n", "      <th>KC Category (Single-KC)</th>\n", "      <th>KC Category (Unique-step)</th>\n", "      <th>KC Category (F2011)</th>\n", "      <th>KC Category (F2011).1</th>\n", "      <th>KC Category (F2011).2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>361092.000000</td>\n", "      <td>0.0</td>\n", "      <td>361092.000000</td>\n", "      <td>289858.000000</td>\n", "      <td>289858.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>180546.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.180192</td>\n", "      <td>2.382867</td>\n", "      <td>0.658678</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>104238.426039</td>\n", "      <td>NaN</td>\n", "      <td>0.907172</td>\n", "      <td>9.948941</td>\n", "      <td>0.474154</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>90273.750000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>180546.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>270819.250000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>2.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>361092.000000</td>\n", "      <td>NaN</td>\n", "      <td>32.000000</td>\n", "      <td>427.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 Row  Tutor Response Subtype   Problem View  Attempt At Step  \\\n", "count  361092.000000                     0.0  361092.000000    289858.000000   \n", "mean   180546.500000                     NaN       1.180192         2.382867   \n", "std    104238.426039                     NaN       0.907172         9.948941   \n", "min         1.000000                     NaN       1.000000         1.000000   \n", "25%     90273.750000                     NaN       1.000000         1.000000   \n", "50%    180546.500000                     NaN       1.000000         1.000000   \n", "75%    270819.250000                     NaN       1.000000         2.000000   \n", "max    361092.000000                     NaN      32.000000       427.000000   \n", "\n", "       Is Last Attempt  Feedback Classification  Help Level  Total Num Hints  \\\n", "count    289858.000000                      0.0         0.0              0.0   \n", "mean          0.658678                      NaN         NaN              NaN   \n", "std           0.474154                      NaN         NaN              NaN   \n", "min           0.000000                      NaN         NaN              NaN   \n", "25%           0.000000                      NaN         NaN              NaN   \n", "50%           1.000000                      NaN         NaN              NaN   \n", "75%           1.000000                      NaN         NaN              NaN   \n", "max           1.000000                      NaN         NaN              NaN   \n", "\n", "       KC Category (Single-KC)  KC Category (Unique-step)  \\\n", "count                      0.0                        0.0   \n", "mean                       NaN                        NaN   \n", "std                        NaN                        NaN   \n", "min                        <PERSON><PERSON>   \n", "25%                        NaN                        NaN   \n", "50%                        NaN                        NaN   \n", "75%                        NaN                        NaN   \n", "max                        NaN                        NaN   \n", "\n", "       KC Category (F2011)  KC Category (F2011).1  KC Category (F2011).2  \n", "count                  0.0                    0.0                    0.0  \n", "mean                   NaN                    NaN                    NaN  \n", "std                    NaN                    NaN                    NaN  \n", "min                    NaN                    NaN                    NaN  \n", "25%                    NaN                    NaN                    NaN  \n", "50%                    NaN                    NaN                    NaN  \n", "75%                    NaN                    NaN                    NaN  \n", "max                    NaN                    NaN                    NaN  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_transaction.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （1）Analysis for Null and Unique value of column attributes"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------------------num_unique_toal and num_nonull_toal----------------------\n", "<class 'pandas.core.series.Series'>\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_name</th>\n", "      <th>num_nonull</th>\n", "      <th>num_null</th>\n", "      <th>num_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Sample Name</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Transaction Id</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON> Student Id</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Session Id</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>6656</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Time</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>263172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Time Zone</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Duration (sec)</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>2565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Student Response Type</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Student Response Subtype</td>\n", "      <td>71234</td>\n", "      <td>289858</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Tutor Response Type</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Tutor Response Subtype</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Level (Sequence)</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Level (Unit)</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Level (Module)</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Level (Section1)</td>\n", "      <td>59480</td>\n", "      <td>301612</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Problem Name</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Problem View</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Problem Start Time</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>46473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Step Name</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>383</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Attempt At Step</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>428</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Is Last Attempt</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Outcome</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Selection</td>\n", "      <td>361082</td>\n", "      <td>10</td>\n", "      <td>287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Action</td>\n", "      <td>361082</td>\n", "      <td>10</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Input</td>\n", "      <td>302086</td>\n", "      <td>59006</td>\n", "      <td>6827</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Input.1</td>\n", "      <td>1</td>\n", "      <td>361091</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Feedback Text</td>\n", "      <td>231063</td>\n", "      <td>130029</td>\n", "      <td>1579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>Feedback Classification</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>Help Level</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Total Num Hints</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td><PERSON> (Single-KC)</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>KC Category (Single-KC)</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td><PERSON> (Unique-step)</td>\n", "      <td>283336</td>\n", "      <td>77756</td>\n", "      <td>1179</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>KC Category (Unique-step)</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>KC (F2011)</td>\n", "      <td>152592</td>\n", "      <td>208500</td>\n", "      <td>81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>KC Category (F2011)</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>KC (F2011).1</td>\n", "      <td>16904</td>\n", "      <td>344188</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>KC Category (F2011).1</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>KC (F2011).2</td>\n", "      <td>6690</td>\n", "      <td>354402</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>KC Category (F2011).2</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>School</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>Class</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>CF (oli:activityGuid)</td>\n", "      <td>45002</td>\n", "      <td>316090</td>\n", "      <td>1244</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>CF (oli:highStakes)</td>\n", "      <td>45002</td>\n", "      <td>316090</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>CF (oli:purpose)</td>\n", "      <td>44516</td>\n", "      <td>316576</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>CF (oli:resourceType)</td>\n", "      <td>45002</td>\n", "      <td>316090</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     col_name  num_nonull  num_null  num_unique\n", "0                         Row      361092         0      361092\n", "1                 Sample Name      361092         0           1\n", "2              Transaction Id      361092         0      361092\n", "3             Anon Student Id      361092         0         335\n", "4                  Session Id      361092         0        6656\n", "5                        Time      361092         0      263172\n", "6                   Time Zone      361092         0           1\n", "7              Duration (sec)      361092         0        2565\n", "8       Student Response Type      361092         0           5\n", "9    Student Response Subtype       71234    289858           2\n", "10        Tutor Response Type      289858     71234           3\n", "11     Tutor Response Subtype           0    361092           1\n", "12           Level (Sequence)      361092         0           1\n", "13               Level (Unit)      361092         0           7\n", "14             Level (Module)      361092         0          19\n", "15           Level (Section1)       59480    301612          10\n", "16               Problem Name      361092         0         300\n", "17               Problem View      361092         0          32\n", "18         Problem Start Time      361092         0       46473\n", "19                  Step Name      289858     71234         383\n", "20            Attempt At Step      289858     71234         428\n", "21            Is Last Attempt      289858     71234           3\n", "22                    Outcome      289858     71234           4\n", "23                  Selection      361082        10         287\n", "24                     Action      361082        10          10\n", "25                      Input      302086     59006        6827\n", "26                    Input.1           1    361091           2\n", "27              Feedback Text      231063    130029        1579\n", "28    Feedback Classification           0    361092           1\n", "29                 Help Level           0    361092           1\n", "30            Total Num Hints           0    361092           1\n", "31             <PERSON> (Single-KC)      289858     71234           2\n", "32    KC Category (Single-KC)           0    361092           1\n", "33           KC (Unique-step)      283336     77756        1179\n", "34  KC Category (Unique-step)           0    361092           1\n", "35                 KC (F2011)      152592    208500          81\n", "36        KC Category (F2011)           0    361092           1\n", "37               KC (F2011).1       16904    344188          19\n", "38      KC Category (F2011).1           0    361092           1\n", "39               KC (F2011).2        6690    354402           9\n", "40      KC Category (F2011).2           0    361092           1\n", "41                     School      361092         0           7\n", "42                      Class      361092         0           9\n", "43      CF (oli:activityGuid)       45002    316090        1244\n", "44        CF (oli:highStakes)       45002    316090           3\n", "45           CF (oli:purpose)       44516    316576           4\n", "46      CF (oli:resourceType)       45002    316090           3"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["def work_col_analysis(df_work):\n", "    num_nonull_toal = df_work.notnull().sum()  # Not Null\n", "    dict_col_1 = {'col_name':num_nonull_toal.index,'num_nonull':num_nonull_toal.values}\n", "    df_work_col_1 = pd.DataFrame(dict_col_1)\n", "\n", "    num_null_toal = df_work.isnull().sum()  # Null\n", "    dict_col_2 = {'col_name':num_null_toal.index,'num_null':num_null_toal.values}\n", "    df_work_col_2 = pd.DataFrame(dict_col_2)\n", "\n", "    num_unique_toal = df_work.apply(lambda col: len(col.unique()))   # axis=0\n", "    print(type(num_unique_toal))\n", "    dict_col_3 = {'col_name':num_unique_toal.index,'num_unique':num_unique_toal.values}\n", "    df_work_col_3 = pd.DataFrame(dict_col_3)\n", "\n", "    df_work_col = pd.merge(df_work_col_1, df_work_col_2, on=['col_name'])\n", "    df_work_col = pd.merge(df_work_col, df_work_col_3, on=['col_name'])\n", "    return df_work_col\n", "print(\"-------------------num_unique_toal and num_nonull_toal----------------------\")\n", "df_result = work_col_analysis(df_transaction)\n", "df_result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （2）Analysis for Discrete value of column attributes\n", "> Columns with a small number of discrete values may represent very informative, so identify these columns first and analyze them one by one"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Student Response Type  :  ['VIEW_PAGE', 'ATTEMPT', 'SAVE_ATTEMPT', 'SUBMIT_ATTEMPT', 'HINT_REQUEST']\n", "--------------------------------------------------------------------------------\n", "Student Response Subtype  :  ['UI Event', nan]\n", "--------------------------------------------------------------------------------\n", "Tutor Response Type  :  [nan, 'RESULT', 'HINT_MSG']\n", "--------------------------------------------------------------------------------\n", "Level (Unit)  :  ['Concentrated Forces and Their Effects', 'Engineering Systems - Single Body Equilibrium', 'Complex Interactions Between Bodies', 'Multiple Body Equilibrium - Frames', 'Multiple Body Equilibrium - Trusses', 'Friction', 'Moments of Inertia']\n", "--------------------------------------------------------------------------------\n", "Level (Module)  :  ['Introduction to Free Body Diagrams', 'Effects of Force', 'Representing Interactions Between Bodies', 'Effects of Multiple Forces', 'Equilibrium Under 2D Concentrated Forces', 'Equilibrium of a Single Subsystem', 'Couples', 'Statically Equivalent Loads', 'Applications of Static Equivalency to Distributed Forces', 'Representing Engineering Connections', 'Drawing FBDs of a Single Subsystem', 'Choosing a Solvable Subsystem', 'Drawing FBDs of Multiple Subsystems', 'Solving Multiple Subsystems', 'Method of Joints', 'Method of Sections', 'Friction', 'Second Moment of Area', 'Mass Moment of Inertia']\n", "--------------------------------------------------------------------------------\n", "Level (Section1)  :  [nan, 'Combining Concurrent Forces', 'Combining Moments', 'Applying Force Equilibrium', 'Applying Force and Moment Equilibrium', 'Simplifying 3D loadings to 2D or 1D loading', 'Fixed Connections', 'Pin Connections', 'Other Connections', 'Center of Gravity and Centroid']\n", "--------------------------------------------------------------------------------\n", "Is Last Attempt  :  [nan, 1.0, 0.0]\n", "--------------------------------------------------------------------------------\n", "Outcome  :  [nan, 'CORRECT', 'INCORRECT', 'HINT']\n", "--------------------------------------------------------------------------------\n", "Action  :  ['SelectPageNumber', 'UpdateComboBox', 'Click', 'UpdateRadioButton', 'UpdateCheckbox', 'UpdateNumberField', 'UpdateShortAnswer', 'UpdateHotspotSingle', 'UpdateHotspotMultiple', nan]\n", "--------------------------------------------------------------------------------\n", "Input.1  :  [nan, 'No, the forces of B on A and A on B shown on the diagram on the right are not correct because body B and body A are interacting on one another when ???F??? is applied to the body ???B??? but A opposite senses on each other. In this case B will push A in a']\n", "--------------------------------------------------------------------------------\n", "<PERSON> (Single-KC)  :  [nan, 'Single-KC']\n", "--------------------------------------------------------------------------------\n", "KC (F2011).1  :  [nan, 'rotation_sense_of_force', 'identify_interaction', 'motion_dependence_on_force', 'couple_represents_net_zero_force', 'recognize_equivalence_from_motion', 'relate_direction_normal_force_and_contact', 'moment_sign_sense_relation', 'possible_interaction_for_nonuniform_contact', 'represent_interaction_contacting_body', 'represent_forces_two-force_member', 'represent_interaction_cord', 'identify_enabling_unknown', 'identify_equation_isolates_specific_unknown', 'sense_if_assuming_tension', 'determine_joint_is_solvable', 'judge_force_sense_based_on_sign', 'identify_internal_load_points_on_section', 'identify_external_load_points_on_section']\n", "--------------------------------------------------------------------------------\n", "KC (F2011).2  :  [nan, 'rotation_sense_of_force', 'statics_problem_force_and_moment', 'represent_interaction_cord', 'represent_interaction_pin_connection', 'recognize_variable_solvable_from_subsystem', 'tension_vs_compression_given_force_senses', 'sense_if_assuming_tension', 'identify_internal_load_points_on_section']\n", "--------------------------------------------------------------------------------\n", "School  :  ['Marion Technical College', 'Sinclair Community College', 'Carnegie Mellon University', 'Kettering University', 'Miami University', 'University of Maryland Eastern Shore', 'University of Mississippi']\n", "--------------------------------------------------------------------------------\n", "Class  :  ['MET2010B-01', 'F11-E213-01', '24-261Fall11', 'FEA-Fall11', 'DesignFall11', 'F11-E213-50', 'F11-MME-211', 'ENGE260-F11', 'ENGR309H-F11']\n", "--------------------------------------------------------------------------------\n", "CF (oli:highStakes)  :  [nan, <PERSON>alse, True]\n", "--------------------------------------------------------------------------------\n", "CF (oli:purpose)  :  [nan, 'quiz', 'didigetthis', 'learnbydoing']\n", "--------------------------------------------------------------------------------\n", "CF (oli:resourceType)  :  [nan, 'x-oli-assessment2', 'x-oli-inline-assessment']\n", "--------------------------------------------------------------------------------\n"]}], "source": ["discrete_cols = []\n", "series = []\n", "cols = list(df_transaction.columns.values)\n", "\n", "for col in cols:\n", "    if len(df_transaction[col].unique().tolist()) <= 20 and len(df_transaction[col].unique().tolist()) >= 2:\n", "        discrete_cols.append(col)\n", "        series.append(df_transaction[col].unique().tolist())\n", "\n", "for a,b in zip(discrete_cols,series):\n", "    print(a,\" : \",b)\n", "    print(\"-\"*80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （3）Data Cleaning \n", "> **Data Cleaning Suggestions**\n", "> - Redundant columns: Columns that are all NULL or Single value.\n", "> - Others"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["df_transaction_clear = df_transaction.copy(deep=True) # deep copy"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the cols num before clear:  47\n", "the cols num after clear: 35\n", "drop:--- <PERSON>ple Name\n", "drop:--- Time Zone\n", "drop:--- Tutor Response Subtype\n", "drop:--- Level (Sequence)\n", "drop:--- Feedback Classification\n", "drop:--- Help Level\n", "drop:--- Total Num Hints\n", "drop:--- KC Category (Single-KC)\n", "drop:--- KC Category (Unique-step)\n", "drop:--- KC Category (F2011)\n", "drop:--- KC Category (F2011).1\n", "drop:--- KC Category (F2011).2\n"]}], "source": ["# 直接清除所有”冗余列“\n", "cols = list(df_transaction.columns.values)\n", "drop_cols = []\n", "for col in cols:\n", "    if len(df_transaction_clear[col].unique().tolist()) == 1:\n", "        df_transaction_clear.drop(col,axis =1,inplace=True)\n", "        drop_cols.append(col)\n", "\n", "print(\"the cols num before clear: \",len(df_transaction.columns.to_list()))\n", "print(\"the cols num after clear:\",len(df_transaction_clear.columns.to_list()))\n", "for col in drop_cols:\n", "    print(\"drop:---\",col)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th>Transaction Id</th>\n", "      <th><PERSON><PERSON> Student Id</th>\n", "      <th>Session Id</th>\n", "      <th>Time</th>\n", "      <th>Duration (sec)</th>\n", "      <th>Student Response Type</th>\n", "      <th>Student Response Subtype</th>\n", "      <th>Tutor Response Type</th>\n", "      <th>Level (Unit)</th>\n", "      <th>Level (Module)</th>\n", "      <th>Level (Section1)</th>\n", "      <th>Problem Name</th>\n", "      <th>Problem View</th>\n", "      <th>Problem Start Time</th>\n", "      <th>Step Name</th>\n", "      <th>Attempt At Step</th>\n", "      <th>Is Last Attempt</th>\n", "      <th>Outcome</th>\n", "      <th>Selection</th>\n", "      <th>Action</th>\n", "      <th>Input</th>\n", "      <th>Input.1</th>\n", "      <th>Feedback Text</th>\n", "      <th><PERSON> (Single-KC)</th>\n", "      <th><PERSON> (Unique-step)</th>\n", "      <th>KC (F2011)</th>\n", "      <th>KC (F2011).1</th>\n", "      <th>KC (F2011).2</th>\n", "      <th>School</th>\n", "      <th>Class</th>\n", "      <th>CF (oli:activityGuid)</th>\n", "      <th>CF (oli:highStakes)</th>\n", "      <th>CF (oli:purpose)</th>\n", "      <th>CF (oli:resourceType)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2adbe4abefd649d48862d3f62b1abf5e</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:26:36</td>\n", "      <td>1</td>\n", "      <td>VIEW_PAGE</td>\n", "      <td>UI Event</td>\n", "      <td>NaN</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Navigation</td>\n", "      <td>SelectPageNumber</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>4393251e32a6f00502f3f1ef894af8fe</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:35:28</td>\n", "      <td>23.13</td>\n", "      <td>ATTEMPT</td>\n", "      <td>NaN</td>\n", "      <td>RESULT</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>q1_point1i1 UpdateComboBox</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>CORRECT</td>\n", "      <td>q1_point1i1</td>\n", "      <td>UpdateComboBox</td>\n", "      <td>&lt;material&gt;cord c&lt;/material&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Single-KC</td>\n", "      <td>NaN</td>\n", "      <td>identify_interaction</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>e2fb2cb788d10ebaa6f288e0757d1b09</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:35:28</td>\n", "      <td>23.13</td>\n", "      <td>ATTEMPT</td>\n", "      <td>NaN</td>\n", "      <td>RESULT</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>q1_point3i3 UpdateComboBox</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>CORRECT</td>\n", "      <td>q1_point3i3</td>\n", "      <td>UpdateComboBox</td>\n", "      <td>&lt;material&gt;120 lb&lt;/material&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Single-KC</td>\n", "      <td>NaN</td>\n", "      <td>gravitational_forces</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>e7e150d423862e346dc7e36a95e394e4</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:35:28</td>\n", "      <td>23.13</td>\n", "      <td>ATTEMPT</td>\n", "      <td>NaN</td>\n", "      <td>RESULT</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>q1_point6i2 UpdateComboBox</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>INCORRECT</td>\n", "      <td>q1_point6i2</td>\n", "      <td>UpdateComboBox</td>\n", "      <td>&lt;material&gt;no interaction&lt;/material&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Single-KC</td>\n", "      <td>NaN</td>\n", "      <td>represent_interaction_spring</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>684b1f770a225f21745c6c4c977ddc32</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>8dd109e680020ca6016f8e64290b5610</td>\n", "      <td>2011-09-21 17:35:28</td>\n", "      <td>23.13</td>\n", "      <td>ATTEMPT</td>\n", "      <td>NaN</td>\n", "      <td>RESULT</td>\n", "      <td>Concentrated Forces and Their Effects</td>\n", "      <td>Introduction to Free Body Diagrams</td>\n", "      <td>NaN</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011-09-21 17:26:35</td>\n", "      <td>q1_point1i2 UpdateComboBox</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>CORRECT</td>\n", "      <td>q1_point1i2</td>\n", "      <td>UpdateComboBox</td>\n", "      <td>&lt;material&gt;up&lt;/material&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Single-KC</td>\n", "      <td>NaN</td>\n", "      <td>represent_interaction_cord</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Marion Technical College</td>\n", "      <td>MET2010B-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Row                    Transaction Id  \\\n", "0    1  2adbe4abefd649d48862d3f62b1abf5e   \n", "1    2  4393251e32a6f00502f3f1ef894af8fe   \n", "2    3  e2fb2cb788d10ebaa6f288e0757d1b09   \n", "3    4  e7e150d423862e346dc7e36a95e394e4   \n", "4    5  684b1f770a225f21745c6c4c977ddc32   \n", "\n", "                        Anon Student Id                        Session Id  \\\n", "0  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "1  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "2  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "3  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "4  Stu_00b2b35fd027e7891e8a1a527125dd65  8dd109e680020ca6016f8e64290b5610   \n", "\n", "                  Time Duration (sec) Student Response Type  \\\n", "0  2011-09-21 17:26:36              1             VIEW_PAGE   \n", "1  2011-09-21 17:35:28          23.13               ATTEMPT   \n", "2  2011-09-21 17:35:28          23.13               ATTEMPT   \n", "3  2011-09-21 17:35:28          23.13               ATTEMPT   \n", "4  2011-09-21 17:35:28          23.13               ATTEMPT   \n", "\n", "  Student Response Subtype Tutor Response Type  \\\n", "0                 UI Event                 NaN   \n", "1                      NaN              RESULT   \n", "2                      NaN              RESULT   \n", "3                      NaN              RESULT   \n", "4                      NaN              RESULT   \n", "\n", "                            Level (Unit)                      Level (Module)  \\\n", "0  Concentrated Forces and Their Effects  Introduction to Free Body Diagrams   \n", "1  Concentrated Forces and Their Effects  Introduction to Free Body Diagrams   \n", "2  Concentrated Forces and Their Effects  Introduction to Free Body Diagrams   \n", "3  Concentrated Forces and Their Effects  Introduction to Free Body Diagrams   \n", "4  Concentrated Forces and Their Effects  Introduction to Free Body Diagrams   \n", "\n", "  Level (Section1) Problem Name  Problem View   Problem Start Time  \\\n", "0              NaN   _m2_assess             1  2011-09-21 17:26:35   \n", "1              NaN   _m2_assess             1  2011-09-21 17:26:35   \n", "2              NaN   _m2_assess             1  2011-09-21 17:26:35   \n", "3              NaN   _m2_assess             1  2011-09-21 17:26:35   \n", "4              NaN   _m2_assess             1  2011-09-21 17:26:35   \n", "\n", "                    Step Name  Attempt At Step  Is Last Attempt    Outcome  \\\n", "0                         NaN              NaN              NaN        NaN   \n", "1  q1_point1i1 UpdateComboBox              1.0              1.0    CORRECT   \n", "2  q1_point3i3 UpdateComboBox              1.0              1.0    CORRECT   \n", "3  q1_point6i2 UpdateComboBox              1.0              1.0  INCORRECT   \n", "4  q1_point1i2 UpdateComboBox              1.0              1.0    CORRECT   \n", "\n", "     Selection            Action                                Input Input.1  \\\n", "0   Navigation  SelectPageNumber                                    1     NaN   \n", "1  q1_point1i1    UpdateComboBox          <material>cord c</material>     NaN   \n", "2  q1_point3i3    UpdateComboBox          <material>120 lb</material>     NaN   \n", "3  q1_point6i2    UpdateComboBox  <material>no interaction</material>     NaN   \n", "4  q1_point1i2    UpdateComboBox              <material>up</material>     NaN   \n", "\n", "  Feedback <PERSON> (Single-KC) KC (Unique-step)                    KC (F2011)  \\\n", "0           NaN            NaN              NaN                           NaN   \n", "1           NaN      Single-KC              NaN          identify_interaction   \n", "2           NaN      Single-KC              NaN          gravitational_forces   \n", "3           NaN      Single-KC              NaN  represent_interaction_spring   \n", "4           NaN      Single-KC              NaN    represent_interaction_cord   \n", "\n", "  KC (F2011).1 KC (F2011).2                    School        Class  \\\n", "0          NaN          NaN  Marion Technical College  MET2010B-01   \n", "1          NaN          NaN  Marion Technical College  MET2010B-01   \n", "2          NaN          NaN  Marion Technical College  MET2010B-01   \n", "3          NaN          NaN  Marion Technical College  MET2010B-01   \n", "4          NaN          NaN  Marion Technical College  MET2010B-01   \n", "\n", "  CF (oli:activityGuid) CF (oli:highStakes) CF (oli:purpose)  \\\n", "0                   NaN                 NaN              NaN   \n", "1                   NaN                 NaN              NaN   \n", "2                   NaN                 NaN              NaN   \n", "3                   NaN                 NaN              NaN   \n", "4                   NaN                 NaN              NaN   \n", "\n", "  CF (oli:resourceType)  \n", "0                   NaN  \n", "1                   NaN  \n", "2                   NaN  \n", "3                   NaN  \n", "4                   NaN  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_transaction_clear.head()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------------------num_unique_toal and num_nonull_toal----------------------\n", "<class 'pandas.core.series.Series'>\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_name</th>\n", "      <th>num_nonull</th>\n", "      <th>num_null</th>\n", "      <th>num_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Transaction Id</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>361092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON> Student Id</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Session Id</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>6656</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Time</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>263172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Duration (sec)</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>2565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Student Response Type</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Student Response Subtype</td>\n", "      <td>71234</td>\n", "      <td>289858</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Tutor Response Type</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Level (Unit)</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Level (Module)</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Level (Section1)</td>\n", "      <td>59480</td>\n", "      <td>301612</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Problem Name</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Problem View</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Problem Start Time</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>46473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Step Name</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>383</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Attempt At Step</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>428</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Is Last Attempt</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Outcome</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Selection</td>\n", "      <td>361082</td>\n", "      <td>10</td>\n", "      <td>287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Action</td>\n", "      <td>361082</td>\n", "      <td>10</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Input</td>\n", "      <td>302086</td>\n", "      <td>59006</td>\n", "      <td>6827</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Input.1</td>\n", "      <td>1</td>\n", "      <td>361091</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Feedback Text</td>\n", "      <td>231063</td>\n", "      <td>130029</td>\n", "      <td>1579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td><PERSON> (Single-KC)</td>\n", "      <td>289858</td>\n", "      <td>71234</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td><PERSON> (Unique-step)</td>\n", "      <td>283336</td>\n", "      <td>77756</td>\n", "      <td>1179</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>KC (F2011)</td>\n", "      <td>152592</td>\n", "      <td>208500</td>\n", "      <td>81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>KC (F2011).1</td>\n", "      <td>16904</td>\n", "      <td>344188</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>KC (F2011).2</td>\n", "      <td>6690</td>\n", "      <td>354402</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>School</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Class</td>\n", "      <td>361092</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>CF (oli:activityGuid)</td>\n", "      <td>45002</td>\n", "      <td>316090</td>\n", "      <td>1244</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>CF (oli:highStakes)</td>\n", "      <td>45002</td>\n", "      <td>316090</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>CF (oli:purpose)</td>\n", "      <td>44516</td>\n", "      <td>316576</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>CF (oli:resourceType)</td>\n", "      <td>45002</td>\n", "      <td>316090</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    col_name  num_nonull  num_null  num_unique\n", "0                        Row      361092         0      361092\n", "1             Transaction Id      361092         0      361092\n", "2            Anon Student Id      361092         0         335\n", "3                 Session Id      361092         0        6656\n", "4                       Time      361092         0      263172\n", "5             Duration (sec)      361092         0        2565\n", "6      Student Response Type      361092         0           5\n", "7   Student Response Subtype       71234    289858           2\n", "8        Tutor Response Type      289858     71234           3\n", "9               Level (Unit)      361092         0           7\n", "10            Level (Module)      361092         0          19\n", "11          Level (Section1)       59480    301612          10\n", "12              Problem Name      361092         0         300\n", "13              Problem View      361092         0          32\n", "14        Problem Start Time      361092         0       46473\n", "15                 Step Name      289858     71234         383\n", "16           Attempt At Step      289858     71234         428\n", "17           Is Last Attempt      289858     71234           3\n", "18                   Outcome      289858     71234           4\n", "19                 Selection      361082        10         287\n", "20                    Action      361082        10          10\n", "21                     Input      302086     59006        6827\n", "22                   Input.1           1    361091           2\n", "23             Feedback Text      231063    130029        1579\n", "24            KC (Single-KC)      289858     71234           2\n", "25          KC (Unique-step)      283336     77756        1179\n", "26                KC (F2011)      152592    208500          81\n", "27              KC (F2011).1       16904    344188          19\n", "28              KC (F2011).2        6690    354402           9\n", "29                    School      361092         0           7\n", "30                     Class      361092         0           9\n", "31     CF (oli:activityGuid)       45002    316090        1244\n", "32       CF (oli:highStakes)       45002    316090           3\n", "33          CF (oli:purpose)       44516    316576           4\n", "34     CF (oli:resourceType)       45002    316090           3"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# the remaining columns\n", "print(\"-------------------num_unique_toal and num_nonull_toal----------------------\")\n", "df_result = work_col_analysis(df_transaction_clear)\n", "df_result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlier Analysis\n", "> - <p>It is found that there is a non-numeric type in duration that is '.' , which should represent 0</p>"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Duration (sec)    float64\n", "dtype: object\n"]}], "source": ["# Change . to 0 in \"duration\"\n", "rectify_cols = ['Duration (sec)']\n", "for col in rectify_cols:\n", "    df_transaction_clear[col] = df_transaction_clear[col].apply(lambda x: 0 if x=='.' else x)\n", "    df_transaction_clear[col] = df_transaction_clear[col].astype(float)\n", "print(df_transaction_clear[rectify_cols].dtypes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. Data Visualization"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import plotly.graph_objs as go"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Histogram of discrete values\n", "def show_value_counts_bar(colname, sort = True):\n", "    ds = df_transaction[colname].value_counts().reset_index()\n", "    ds.columns = [\n", "        colname,\n", "        'Count'\n", "    ]\n", "    if sort:\n", "        ds = ds.sort_values(by='Count', ascending=False)\n", "    # histogram\n", "    fig = px.bar(\n", "        ds,\n", "        x = colname,\n", "        y = 'Count',\n", "        title = colname + ' distribution'\n", "    )\n", "    fig.show(\"svg\")\n", "\n", "\n", "# Pie of discrete values\n", "def show_value_counts_pie(colname, sort = True):\n", "    ds = df_transaction[colname].value_counts().reset_index()\n", "    ds.columns = [\n", "        colname,\n", "        'percent'\n", "    ]\n", "    ds['percent'] /= len(df_transaction)\n", "    if sort:\n", "        ds = ds.sort_values(by='percent', ascending=False)\n", "    fig = px.pie(\n", "        ds,\n", "        names = colname,\n", "        values = 'percent',\n", "        title = colname+ ' Percentage',\n", "    )\n", "    fig.show(\"svg\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-f213af\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M320.5,235l0,-135a135,135 0 1 1 -133.3945079880528,155.75825711916383Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(371.768124491597,283.7039292295211)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">72.5%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M320.5,235l-87.63109011476554,-102.69270687491806a135,135 0 0 1 87.63109011476554,-32.30729312508194Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(285.8035698122369,145.6854312255898)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">11.2%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M320.5,235l-125.43448076953092,-49.91183260588795a135,135 0 0 1 37.80339065476538,-52.78087426903011Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(232.0219765145567,176.42601704237978)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.73%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M320.5,235l-134.5865357614982,10.557669805357957a135,135 0 0 1 9.152054991967276,-60.46950241124591Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(211.6711511423334,223.32563628061826)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.27%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M320.5,235l-133.39450798805282,20.758257119163734a135,135 0 0 1 -1.1920277734453748,-10.200587313805777Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(162.71713047974052,255.56321830866764)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.21%</text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-f213af\"><g class=\"clips\"/><clipPath id=\"legendf213af\"><rect width=\"156\" height=\"105\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(532.14,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"156\" height=\"105\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legendf213af')\"><g class=\"groups\"><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,14.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">ATTEMPT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"150.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,33.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">VIEW_PAGE</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"150.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,52.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">HINT_REQUEST</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"150.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,71.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">SAVE_ATTEMPT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"150.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,90.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">SUBMIT_ATTEMPT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"150.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Student Response Type Percentage</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-7f1b4e\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M323.5,235l0,-135a135,135 0 1 1 -76.78513855657448,23.96377844663634Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(343.6094197153222,304.23181302831665)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">90.4%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M323.5,235l-76.78513855657452,-111.03622155336362a135,135 0 0 1 76.78513855657452,-23.963778446636383Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(292.5127880039627,140.5071335305514)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">9.63%</text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-7f1b4e\"><g class=\"clips\"/><clipPath id=\"legend7f1b4e\"><rect width=\"111\" height=\"48\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(576.74,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"111\" height=\"48\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend7f1b4e')\"><g class=\"groups\"><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,14.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">RESULT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"105.703125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,33.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">HINT_MSG</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"105.703125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Tutor Response Type Percentage</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-7c2278\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M320,235l0,-135a135,135 0 1 1 -108.41244850300293,215.4471317672901Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(380.29652020285266,270.13793054239966)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">64.8%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M320,235l-134.92483563224027,4.504301234702801a135,135 0 0 1 134.92483563224027,-139.5043012347028Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(263.5428785798275,185.19305446925853)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">25.5%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M320,235l-108.41244850300295,80.44713176729009a135,135 0 0 1 -26.512387129237325,-75.9428305325873Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(221.79943473719086,274.0796556693398)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">9.63%</text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-7c2278\"><g class=\"clips\"/><clipPath id=\"legend7c2278\"><rect width=\"118\" height=\"67\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(569.6,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"118\" height=\"67\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend7c2278')\"><g class=\"groups\"><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,14.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">CORRECT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"112.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,33.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">INCORRECT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"112.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,52.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">HINT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"112.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Outcome Percentage</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["col_pies = ['Student Response Type','Tutor Response Type','Outcome']\n", "for col in col_pies:\n", "    show_value_counts_pie(col)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Analysis by label description:**\n", "> - If the Student Response Type == ATTEMPT, then the Tutor Response Type == Result, then the Student Response Type => correct or incorrect\n", "\n", "> - If the Student Response Type == HINT_REQUEST, then the Tutor Response Type == HINT_MSG, then the outCome => hint\n", "\n", "> - If Student Response Type == other,then the Tutor Response Type == NaN, then the outCome => NaN"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-090e4e\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M320,235l0,-135a135,135 0 1 1 -132.1924702496281,162.38888112539817Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(372.347992979589,282.41069471859487)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">71.7%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M320,235l-132.1924702496281,27.388881125398118a135,135 0 0 1 132.1924702496281,-162.38888112539811Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(261.0338139459034,191.79552329617908)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">28.3%</text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-090e4e\"><g class=\"clips\"/><clipPath id=\"legend090e4e\"><rect width=\"118\" height=\"48\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(569.6,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"118\" height=\"48\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend090e4e')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">CORRECT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"112.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">INCORRECT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"112.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Outcome Percentage when Student Response Type ==ATTEMPT</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-838e84\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M340.5,100a135,135 0 1 1 1.6532731788489267e-14,270a135,135 0 1 1 -1.6532731788489267e-14,-270Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(340.5,239.796875)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">100%</text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-838e84\"><g class=\"clips\"/><clipPath id=\"legend838e84\"><rect width=\"77\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(611.42,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"77\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend838e84')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">HINT</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"71.046875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Outcome Percentage when Student Response Type ==HINT_REQUEST</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "def show_value_counts_pie2(col1,type1,col2, sort = True):\n", "    df_tmp = df_transaction[df_transaction[col1] == type1]\n", "    ds = df_tmp[col2].value_counts().reset_index()\n", "    ds.columns = [\n", "        col2,\n", "        'percent'\n", "    ]\n", "    ds['percent'] /= len(df_tmp)\n", "    if sort:\n", "        ds = ds.sort_values(by='percent', ascending=False)\n", "    fig = px.pie(\n", "        ds,\n", "        names = col2,\n", "        values = 'percent',\n", "        title = col2+ ' Percentage when ' + col1 + ' =='+ type1,\n", "    )\n", "    fig.show(\"svg\")\n", "\n", "# Take Student Response Type as an example\n", "col1 = 'Student Response Type'\n", "col2 = 'Outcome'\n", "# col1 = 'Tutor Response Type'\n", "# col2 = 'Outcome'\n", "\n", "show_value_counts_pie2(col1,\"ATTEMPT\",col2)\n", "show_value_counts_pie2(col1,\"HINT_REQUEST\",col2)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-ebc54c\"><g class=\"clips\"><clipPath id=\"clipebc54cxyplot\" class=\"plotclip\"><rect width=\"188.1\" height=\"60\"/></clipPath><clipPath id=\"clipebc54cx2y2plot\" class=\"plotclip\"><rect width=\"188.1\" height=\"60\"/></clipPath><clipPath id=\"clipebc54cx3y3plot\" class=\"plotclip\"><rect width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath id=\"clipebc54cx4y4plot\" class=\"plotclip\"><rect width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx\"><rect x=\"80\" y=\"0\" width=\"188.1\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cy\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cxy\"><rect x=\"80\" y=\"100\" width=\"188.1\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cy2\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cxy2\"><rect x=\"80\" y=\"100\" width=\"188.1\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cy3\"><rect x=\"0\" y=\"205\" width=\"700\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cxy3\"><rect x=\"80\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cy4\"><rect x=\"0\" y=\"205\" width=\"700\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cxy4\"><rect x=\"80\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx2\"><rect x=\"309.9\" y=\"0\" width=\"188.1\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx2y\"><rect x=\"309.9\" y=\"100\" width=\"188.1\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx2y2\"><rect x=\"309.9\" y=\"100\" width=\"188.1\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx2y3\"><rect x=\"309.9\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx2y4\"><rect x=\"309.9\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx3\"><rect x=\"80\" y=\"0\" width=\"188.1\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx3y\"><rect x=\"80\" y=\"100\" width=\"188.1\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx3y2\"><rect x=\"80\" y=\"100\" width=\"188.1\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx3y3\"><rect x=\"80\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx3y4\"><rect x=\"80\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx4\"><rect x=\"309.9\" y=\"0\" width=\"188.1\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx4y\"><rect x=\"309.9\" y=\"100\" width=\"188.1\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx4y2\"><rect x=\"309.9\" y=\"100\" width=\"188.1\" height=\"60\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx4y3\"><rect x=\"309.9\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\"/></clipPath><clipPath class=\"axesclip\" id=\"clipebc54cx4y4\"><rect x=\"309.9\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"188.1\" height=\"60\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"309.9\" y=\"100\" width=\"188.1\" height=\"60\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"80\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"309.9\" y=\"205\" width=\"188.1\" height=\"60.000000000000014\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,131.31)\" d=\"M80,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,102.62)\" d=\"M80,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,160)\" d=\"M80,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clipebc54cxyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M2.69,60V3H24.18V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(13.419320557491288,32.569686411149824)scale(0.2229965156794425)rotate(90 0.0703125 -4.796875)\">Complex Interactions Between Bodies</text></g><g class=\"point\"><path d=\"M29.56,60V9.19H51.06V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(40.29473773841962,35.53210286103542)scale(0.19535694822888283)rotate(90 0.078125 -4.796875)\">Concentrated Forces and Their Effects</text></g><g class=\"point\"><path d=\"M56.43,60V13.68H77.93V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(67.18,37.52139601453264)scale(0.1420499834856325)rotate(90 0 -4.796875)\">Engineering Systems - Single Body Equilibrium</text></g><g class=\"point\"><path d=\"M83.3,60V39.77H104.8V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(94.0448603640115,43.647164542038176)scale(0.07309704516984747)\">Multiple Body Equilibrium - Trusses</text></g><g class=\"point\"><path d=\"M110.17,60V44.54H131.67V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(120.91999999999999,51.722712579058324)scale(0.34855938158819366)\">Friction</text></g><g class=\"point\"><path d=\"M137.04,60V46.53H158.54V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(147.78484212083117,50.41027804481254)scale(0.07335650373437846)\">Multiple Body Equilibrium - Frames</text></g><g class=\"point\"><path d=\"M163.92,60V56.1H185.41V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(174.665,52.64985582154516)scale(0.18707290533188256)\">Moments of Inertia</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><path class=\"ylines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"173\" transform=\"translate(93.44,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"173\" transform=\"translate(147.18,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"173\" transform=\"translate(200.92000000000002,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"173\" transform=\"translate(254.66,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,160)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,131.31)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">50k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,102.62)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">100k</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x2y2\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x2\"/><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,146.36)\" d=\"M309.9,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,132.72)\" d=\"M309.9,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.08)\" d=\"M309.9,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,105.43)\" d=\"M309.9,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y2zl zl crisp\" transform=\"translate(0,160)\" d=\"M309.9,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(309.9,100)\" clip-path=\"url('#clipebc54cx2y2plot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0.99,60V3H8.91V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(4.95,19.448329718004324)scale(0.1332754880694142)rotate(90 0 -4.796875)\">Choosing a Solvable Subsystem</text></g><g class=\"point\"><path d=\"M10.89,60V10.68H18.81V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(14.85,28.094577006507567)scale(0.13327548806941408)rotate(90 0 -4.796875)\">Equilibrium of a Single Subsystem</text></g><g class=\"point\"><path d=\"M20.79,60V20.7H28.71V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(24.739587852494576,35.10754880694143)scale(0.13327548806941433)rotate(90 0.078125 -4.796875)\">Statically Equivalent Loads</text></g><g class=\"point\"><path d=\"M30.69,60V22.08H38.61V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(34.680705269579086,41.49983013467224)scale(0.09586035380789264)rotate(90 -0.3203125 -4.796875)\">Applications of Static Equivalency to Distributed Forces</text></g><g class=\"point\"><path d=\"M40.59,60V23.24H48.51V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(44.55,29.842603036876344)scale(0.13327548806941392)rotate(90 0 -4.796875)\">Friction</text></g><g class=\"point\"><path d=\"M50.49,60V23.29H58.41V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(54.44062906724512,33.52956616052058)scale(0.1332754880694139)rotate(90 0.0703125 -4.796875)\">Method of Joints</text></g><g class=\"point\"><path d=\"M60.39,60V25.74H68.31V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(64.34140406375471,43.39779048546052)scale(0.11002798393965205)rotate(90 0.078125 -4.796875)\">Equilibrium Under 2D Concentrated Forces</text></g><g class=\"point\"><path d=\"M70.29,60V29.32H78.21V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(74.24320526154787,45.12355215662282)scale(0.09663628020801468)rotate(90 0.0703125 -4.796875)\">Representing Interactions Between Bodies</text></g><g class=\"point\"><path d=\"M80.19,60V30.34H88.11V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(84.14284042767804,45.65844193396544)scale(0.10182502857911371)rotate(90 0.0703125 -4.796875)\">Representing Engineering Connections</text></g><g class=\"point\"><path d=\"M90.09,60V31.37H98.01V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(94.03958785249459,38.18084598698482)scale(0.1332754880694144)rotate(90 0.078125 -4.796875)\">Couples</text></g><g class=\"point\"><path d=\"M99.99,60V34.8H107.91V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(103.94101964452759,48.0126598066729)scale(0.12772061116308078)rotate(90 0.0703125 -4.796875)\">Effects of Multiple Forces</text></g><g class=\"point\"><path d=\"M109.89,60V35.76H117.81V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(113.84173763362928,48.38730929516217)scale(0.10575828954520748)rotate(90 0.078125 -4.796875)\">Solving Multiple Subsystems</text></g><g class=\"point\"><path d=\"M119.79,60V39.04H127.71V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(123.74687635574837,48.86516268980473)scale(0.1332754880694134)rotate(90 0.0234375 -4.796875)\">Effects of Force</text></g><g class=\"point\"><path d=\"M129.69,60V48.62H137.61V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(133.64671639766718,54.53401464804014)scale(0.04670012206700124)rotate(90 0.0703125 -4.796875)\">Method of Sections</text></g><g class=\"point\"><path d=\"M139.59,60V50.3H147.51V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(143.54866618601298,55.23189617880317)scale(0.0170728190338861)rotate(90 0.078125 -4.796875)\">Introduction to Free Body Diagrams</text></g><g class=\"point\"><path d=\"M149.49,60V52.23H157.41V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(153.44940858374974,55.330935040043805)scale(0.008411253336983997)\">Drawing FBDs of Multiple Subsystems</text></g><g class=\"point\"><path d=\"M159.39,60V54.41H167.31V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(163.35,51.2779714223858)scale(0.05486901926823999)\">Second Moment of Area</text></g><g class=\"point\"><path d=\"M169.29,60V56.2H177.21V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(173.25,53.1146838276441)scale(0.0354560716284276)\">Drawing FBDs of a Single Subsystem</text></g><g class=\"point\"><path d=\"M179.19,60V56.31H187.11V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(183.15,53.176438896189225)scale(0.055505913272010626)\">Mass Moment of Inertia</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><path class=\"ylines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"173\" transform=\"translate(314.84999999999997,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"173\" transform=\"translate(364.34999999999997,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"173\" transform=\"translate(413.84999999999997,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"173\" transform=\"translate(463.34999999999997,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">15</text></g></g><g class=\"yaxislayer-above\"><g class=\"y2tick\"><text text-anchor=\"end\" x=\"308.9\" y=\"4.199999999999999\" transform=\"translate(0,160)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"308.9\" y=\"4.199999999999999\" transform=\"translate(0,146.36)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10k</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"308.9\" y=\"4.199999999999999\" transform=\"translate(0,132.72)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20k</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"308.9\" y=\"4.199999999999999\" transform=\"translate(0,119.08)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">30k</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"308.9\" y=\"4.199999999999999\" transform=\"translate(0,105.43)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40k</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x3y3\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x3\"/><g class=\"y3\"><path class=\"y3grid crisp\" transform=\"translate(0,250.4)\" d=\"M80,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y3grid crisp\" transform=\"translate(0,235.8)\" d=\"M80,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y3grid crisp\" transform=\"translate(0,221.2)\" d=\"M80,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y3grid crisp\" transform=\"translate(0,206.6)\" d=\"M80,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y3zl zl crisp\" transform=\"translate(0,265)\" d=\"M80,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,205)\" clip-path=\"url('#clipebc54cx3y3plot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M2.09,60V3H18.81V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(10.45,32.42338995046001)scale(0.19249823071479122)rotate(90 0 -4.796875)\">Simplifying 3D loadings to 2D or 1D loading</text></g><g class=\"point\"><path d=\"M22.99,60V35.74H39.71V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(31.395114814455965,48.4131469818816)scale(0.11322933824241836)rotate(90 -0.3984375 -4.796875)\">Applying Force Equilibrium</text></g><g class=\"point\"><path d=\"M43.89,60V35.82H60.61V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(52.25,48.380595278246204)scale(0.09810455311973018)rotate(90 0 -4.796875)\">Center of Gravity and Centroid</text></g><g class=\"point\"><path d=\"M64.79,60V36.67H81.51V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(73.17870882868837,48.68063178067953)scale(0.07205353082569999)rotate(90 -0.3984375 -4.796875)\">Applying Force and Moment Equilibrium</text></g><g class=\"point\"><path d=\"M85.69,60V41.58H102.41V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(94.04291365538228,51.27344617725371)scale(0.10078356789653863)rotate(90 0.0703125 -4.796875)\">Combining Moments</text></g><g class=\"point\"><path d=\"M106.59,60V44.07H123.31V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(114.94576879221121,47.792126129286906)scale(0.06017717744057538)\">Combining Concurrent Forces</text></g><g class=\"point\"><path d=\"M127.49,60V53.2H144.21V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(135.8363799283154,49.7805017921147)scale(0.17433691756272415)\">Pin Connections</text></g><g class=\"point\"><path d=\"M148.39,60V57.06H165.11V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(156.7383174958077,53.700178870877586)scale(0.14953605366126352)\">Other Connections</text></g><g class=\"point\"><path d=\"M169.29,60V59.18H186.01V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(177.6380775812892,55.812789503707926)scale(0.1526069594980034)\">Fixed Connections</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><path class=\"ylines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x3tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(90.45,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"x3tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(132.25,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"x3tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(174.05,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4</text></g><g class=\"x3tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(215.85,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">6</text></g><g class=\"x3tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(257.65,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">8</text></g></g><g class=\"yaxislayer-above\"><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,265)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,250.4)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5k</text></g><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,235.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10k</text></g><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,221.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">15k</text></g><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,206.6)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20k</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x4y4\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x4\"/><g class=\"y4\"><path class=\"y4grid crisp\" transform=\"translate(0,238.27)\" d=\"M309.9,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y4grid crisp\" transform=\"translate(0,211.55)\" d=\"M309.9,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y4zl zl crisp\" transform=\"translate(0,265)\" d=\"M309.9,0h188.1\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(309.9,205)\" clip-path=\"url('#clipebc54cx4y4plot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0.24,60V3H2.12V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(1.1698047722342735,13.729457700650753)scale(0.13049891540130146)rotate(90 0.078125 -4.796875)\">resolve_into_components</text></g><g class=\"point\"><path d=\"M2.59,60V16.57H4.47V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(3.5269414316702816,30.825986984815607)scale(0.1304989154013014)rotate(90 0.0234375 -4.796875)\">couple_represents_net_zero_force</text></g><g class=\"point\"><path d=\"M4.94,60V22.17H6.82V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(5.8759219088937105,41.233036876355726)scale(0.13049891540130135)rotate(90 0.03125 -4.796875)\">replace_general_loads_with_force_and_couple</text></g><g class=\"point\"><path d=\"M7.29,60V22.93H9.17V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(8.23,31.15550976138828)scale(0.13049891540130146)rotate(90 0 -4.796875)\">identify_interaction</text></g><g class=\"point\"><path d=\"M9.64,60V25.92H11.52V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(10.58,31.268416485900214)scale(0.13049891540130143)rotate(90 0 -4.796875)\">simple_step</text></g><g class=\"point\"><path d=\"M11.99,60V27.56H13.87V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(12.919804772234274,38.136529284164844)scale(0.13049891540130137)rotate(90 0.078125 -4.796875)\">couple_related_to_forces</text></g><g class=\"point\"><path d=\"M14.34,60V29.56H16.22V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(15.28,37.489848156182205)scale(0.13049891540130137)rotate(90 0 -4.796875)\">find_moment_arm</text></g><g class=\"point\"><path d=\"M16.69,60V33.3H18.57V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(17.630000000000003,45.295704989154)scale(0.13049891540130135)rotate(90 0 -4.796875)\">represent_interaction_spring</text></g><g class=\"point\"><path d=\"M19.05,60V36.97H20.93V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(19.986746256004523,48.98444970330602)scale(0.10411980785532637)rotate(90 0.03125 -4.796875)\">statics_problem_force_and_moment</text></g><g class=\"point\"><path d=\"M21.4,60V37.31H23.28V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(22.34,49.27917831541219)scale(0.13012186379928314)rotate(90 0 -4.796875)\">find_linear_force_per_length</text></g><g class=\"point\"><path d=\"M23.75,60V37.55H25.63V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(24.689999999999998,48.82388286334054)scale(0.13049891540130135)rotate(90 0 -4.796875)\">represent_interaction_cord</text></g><g class=\"point\"><path d=\"M26.1,60V39H27.98V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(27.078732683006802,50.02848594147061)scale(0.11017296499713092)rotate(90 -0.3515625 -4.796875)\">judge_equilibrium_qualitatively</text></g><g class=\"point\"><path d=\"M28.45,60V39.26H30.33V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(29.386941431670284,48.46119305856832)scale(0.13049891540130137)rotate(90 0.0234375 -4.796875)\">find_symmetry_plane</text></g><g class=\"point\"><path d=\"M30.8,60V39.85H32.68V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(31.735340886569332,50.33367080663275)scale(0.08519521701790314)rotate(90 0.0546875 -4.796875)\">represent_interaction_contacting_body</text></g><g class=\"point\"><path d=\"M33.15,60V40.14H35.03V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(34.09,50.48771855302823)scale(0.08708139216223623)rotate(90 0 -4.796875)\">represent_interaction_pin_connection</text></g><g class=\"point\"><path d=\"M35.5,60V43.24H37.38V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(36.438890409145074,51.84709626164099)scale(0.04734254314339938)rotate(90 0.0234375 -4.796875)\">replace_forces_in_opposite_sense_with_force_and_couple</text></g><g class=\"point\"><path d=\"M37.86,60V43.34H39.74V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(38.8,52.11975553992262)scale(0.09376011255715791)rotate(90 0 -4.796875)\">moment_sign_sense_relation</text></g><g class=\"point\"><path d=\"M40.21,60V44.62H42.09V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(41.150000000000006,52.89320899209486)scale(0.1215810276679842)rotate(90 0 -4.796875)\">body_draw_force_on</text></g><g class=\"point\"><path d=\"M42.56,60V44.78H44.44V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(43.5,52.647496969028985)scale(0.053680149895293724)rotate(90 0 -4.796875)\">moving_force_perpendicular_to_line_of_action</text></g><g class=\"point\"><path d=\"M44.91,60V46.04H46.79V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(45.849999999999994,53.410747629467544)scale(0.08145878920495989)rotate(90 0 -4.796875)\">centroid_of_composite_area</text></g><g class=\"point\"><path d=\"M47.26,60V46.12H49.14V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(48.197773500160416,53.51569030050262)scale(0.09499732648914556)rotate(90 0.0234375 -4.796875)\">rotation_sense_of_force</text></g><g class=\"point\"><path d=\"M49.61,60V47.09H51.49V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(50.5439480592537,53.916589161822614)scale(0.07746484155259702)rotate(90 0.078125 -4.796875)\">anticipate_solved_variables</text></g><g class=\"point\"><path d=\"M51.96,60V48.05H53.84V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(52.898056278464544,54.32336125569291)scale(0.06219908913467796)rotate(90 0.03125 -4.796875)\">moving_force_to_general_point</text></g><g class=\"point\"><path d=\"M54.31,60V48.19H56.19V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(55.24848057294794,54.40597606998885)scale(0.0648288875546788)rotate(90 0.0234375 -4.796875)\">motion_dependence_on_force</text></g><g class=\"point\"><path d=\"M56.67,60V49.67H58.55V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(57.60728554744526,55.02018598540146)scale(0.03860554744525547)rotate(90 0.0703125 -4.796875)\">recognize_equivalence_of_translated_forces</text></g><g class=\"point\"><path d=\"M59.02,60V49.79H60.9V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(59.96,55.331069838619925)scale(0.09090706733444631)rotate(90 0 -4.796875)\">interpret_equation</text></g><g class=\"point\"><path d=\"M61.37,60V50.45H63.25V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(62.31,55.415132944228276)scale(0.03963683527885861)rotate(90 0 -4.796875)\">represent_interaction_roller_connection</text></g><g class=\"point\"><path d=\"M63.72,60V50.68H65.6V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(64.65491710296683,55.65208987783595)scale(0.06506108202443281)rotate(90 0.078125 -4.796875)\">equivalence_of_couples</text></g><g class=\"point\"><path d=\"M66.07,60V51.9H67.95V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(67.00912128444347,56.129843783901066)scale(0.037491863744847045)rotate(90 0.0234375 -4.796875)\">identify_forces_in_symmetry_plane</text></g><g class=\"point\"><path d=\"M68.42,60V52.11H70.3V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(69.35927548209366,56.20328466483012)scale(0.030912764003673095)rotate(90 0.0234375 -4.796875)\">recognize_conditions_for_full_equivalence</text></g><g class=\"point\"><path d=\"M70.77,60V52.17H72.65V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(71.70775593259201,56.222785738851314)scale(0.02872406282242347)rotate(90 0.078125 -4.796875)\">force_at_joint_implied_by_previous_analysis</text></g><g class=\"point\"><path d=\"M73.12,60V52.7H75V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(74.06,56.508001974055276)scale(0.03293852227862379)rotate(90 0 -4.796875)\">recognize_equivalence_from_motion</text></g><g class=\"point\"><path d=\"M75.48,60V52.83H77.36V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(76.40875493271274,56.63772488110897)scale(0.04643124557320653)rotate(90 0.2421875 -4.796875)\">statics_problem_collinear</text></g><g class=\"point\"><path d=\"M77.83,60V52.84H79.71V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(78.76891973445986,56.585820760410385)scale(0.03456849728424863)rotate(90 0.03125 -4.796875)\">determine_subsystem_is_solvable</text></g><g class=\"point\"><path d=\"M80.18,60V52.87H82.06V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(81.13338351122562,56.59612697828487)scale(0.033589988958410026)rotate(90 -0.3984375 -4.796875)\">judge_force_sense_based_on_sign</text></g><g class=\"point\"><path d=\"M82.53,60V52.98H84.41V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(83.46021193530396,56.690329057445624)scale(0.04176240936977135)rotate(90 0.234375 -4.796875)\">identify_two-force_member</text></g><g class=\"point\"><path d=\"M84.88,60V53.04H86.76V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(85.81900417787443,56.6422869570194)scale(0.025493046414468038)rotate(90 0.0390625 -4.796875)\">possible_interaction_for_nonuniform_contact</text></g><g class=\"point\"><path d=\"M87.23,60V53.51H89.11V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(88.17,56.89115074484078)scale(0.028383217165504997)rotate(90 0 -4.796875)\">find_net_force_for_linear_distribution</text></g><g class=\"point\"><path d=\"M89.58,60V53.61H91.46V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(90.52,56.93169400671661)scale(0.026411779901834152)rotate(90 0 -4.796875)\">find_pressure_under_linear_distribution</text></g><g class=\"point\"><path d=\"M91.93,60V53.8H93.81V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(92.87,57.007853581142335)scale(0.022484134179510437)rotate(90 0 -4.796875)\">represent_interaction_pin_in_slot_connection</text></g><g class=\"point\"><path d=\"M94.29,60V53.95H96.17V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(95.22643418467584,57.218267845448594)scale(0.05071381794368039)rotate(90 0.0703125 -4.796875)\">gravitational_forces</text></g><g class=\"point\"><path d=\"M96.64,60V54.15H98.52V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(97.58,57.17399404696285)scale(0.02063719545805314)rotate(90 0 -4.796875)\">find_net_force_position_for_linear_distribution</text></g><g class=\"point\"><path d=\"M98.99,60V54.44H100.87V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(99.9292672279389,54.417430620517614)scale(0.009379482382288792)\">recognize_knowns_vs_unknowns</text></g><g class=\"point\"><path d=\"M101.34,60V54.47H103.22V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(102.27974033149171,54.45000552486188)scale(0.00830939226519335)\">moment_about_point_due_to_couple</text></g><g class=\"point\"><path d=\"M103.69,60V54.66H105.57V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(104.63,54.63587333333333)scale(0.010026666666666642)\">find_uniform_force_per_length</text></g><g class=\"point\"><path d=\"M106.04,60V54.93H107.92V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(106.98,54.911054835754484)scale(0.00787331501112418)\">represent_interaction_fixed_connection</text></g><g class=\"point\"><path d=\"M108.39,60V55.02H110.27V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(109.33,54.980854515954576)scale(0.016268253109789035)\">Newtons_third_law</text></g><g class=\"point\"><path d=\"M110.74,60V55.06H112.62V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(111.68,55.043995577667225)scale(0.006651188501934805)\">represent_interaction_rigid_sliding_connection</text></g><g class=\"point\"><path d=\"M113.1,60V55.07H114.98V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(114.03982280867106,55.05180835689601)scale(0.007560163367891965)\">replace_general_loads_with_single_force</text></g><g class=\"point\"><path d=\"M115.45,60V55.47H117.33V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(116.38928213831142,55.44543317776835)scale(0.010209588459906636)\">find_angle_given_components</text></g><g class=\"point\"><path d=\"M117.8,60V55.84H119.68V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(118.7397963163597,55.824316359696645)scale(0.0065178764897075095)\">replace_forces_in_same_sense_with_one_force</text></g><g class=\"point\"><path d=\"M120.15,60V56.31H122.03V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(121.08974126066612,56.290077071290945)scale(0.008279658684282942)\">find_uniform_pressure_under_weight</text></g><g class=\"point\"><path d=\"M122.5,60V56.41H124.38V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(123.44,56.392021857923496)scale(0.007471435668156961)\">identify_external_load_points_on_section</text></g><g class=\"point\"><path d=\"M124.85,60V56.61H126.73V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(125.78919354838709,56.58516129032258)scale(0.010322580645161344)\">count_independent_equations</text></g><g class=\"point\"><path d=\"M127.2,60V56.69H129.08V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(128.14000000000001,56.66926074498567)scale(0.008618911174785145)\">can_connection_be_modeled_in_2D</text></g><g class=\"point\"><path d=\"M129.55,60V56.94H131.43V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(130.48957760402624,56.913980408016535)scale(0.010813336928192658)\">recognize_forces_concurrent</text></g><g class=\"point\"><path d=\"M131.91,60V57H133.79V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(132.84938691209507,56.97901876947605)scale(0.008719472425538062)\">find_magnitude_given_components</text></g><g class=\"point\"><path d=\"M134.26,60V57.04H136.14V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(135.2,57.02024024024024)scale(0.008211848211848193)\">impose_solve_concurrent_equilibrium</text></g><g class=\"point\"><path d=\"M136.61,60V57.19H138.49V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(137.55,57.16636957231472)scale(0.009820437479595144)\">distinguish_rotation_translation</text></g><g class=\"point\"><path d=\"M138.96,60V57.27H140.84V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(139.89724716907457,57.24173760249903)scale(0.011745411948457605)\">recognize_forces_collinear</text></g><g class=\"point\"><path d=\"M141.31,60V57.59H143.19V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(142.24804112253403,57.56988885801612)scale(0.008357877188107788)\">represent_forces_two-force_member</text></g><g class=\"point\"><path d=\"M143.66,60V57.6H145.54V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(144.59922887612797,57.576249384741594)scale(0.00987038556193599)\">is_net_moment_sense_obvious</text></g><g class=\"point\"><path d=\"M146.01,60V57.66H147.89V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(146.94938360655738,57.622030163934426)scale(0.015779672131147504)\">determine_moment</text></g><g class=\"point\"><path d=\"M148.36,60V57.81H150.24V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(149.3,57.791289905648185)scale(0.007775623626728688)\">find_net_force_for_uniform_distribution</text></g><g class=\"point\"><path d=\"M150.72,60V57.95H152.6V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(151.66,57.90536231884058)scale(0.018550724637681114)\">identify_centroid</text></g><g class=\"point\"><path d=\"M153.07,60V58.02H154.95V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(154.00972138241744,58.00283715691506)scale(0.007132610113225426)\">relate_direction_normal_force_and_contact</text></g><g class=\"point\"><path d=\"M155.42,60V58.08H157.3V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(156.36,58.06474765567379)scale(0.006338636603097752)\">find_net_force_position_for_uniform_distribution</text></g><g class=\"point\"><path d=\"M157.77,60V58.2H159.65V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(158.71,58.16029621503017)scale(0.016500274273176043)\">choose_subsystem</text></g><g class=\"point\"><path d=\"M160.12,60V58.21H162V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(161.05870503597123,58.16568345323741)scale(0.018417266187050314)\">count_unknowns</text></g><g class=\"point\"><path d=\"M162.47,60V58.51H164.35V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(163.41,58.48045111247193)scale(0.0122800571545213)\">choose_moment_method</text></g><g class=\"point\"><path d=\"M164.82,60V58.57H166.7V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(165.75928917120387,58.54810647307925)scale(0.0090986085904416)\">distinguish_fixed_pin_connections</text></g><g class=\"point\"><path d=\"M167.17,60V58.83H169.05V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(168.11,58.80270224401282)scale(0.011344521968697108)\">identify_enabling_unknown</text></g><g class=\"point\"><path d=\"M169.53,60V58.83H171.41V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(170.47,58.81336091954023)scale(0.0069149425287356155)\">identify_equation_isolates_specific_unknown</text></g><g class=\"point\"><path d=\"M171.88,60V59.36H173.76V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(172.8195176188847,59.33523776941498)scale(0.010290797126240139)\">conditions_equal_force_pulley</text></g><g class=\"point\"><path d=\"M174.23,60V59.5H176.11V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(175.1698312084755,59.4826707368169)scale(0.0072017717124559484)\">second_moment_of_area_tabulated_shape</text></g><g class=\"point\"><path d=\"M176.58,60V59.5H178.46V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(177.52,59.480683213237256)scale(0.008027755537763524)\">relative_magnitudes_moment_of_area</text></g><g class=\"point\"><path d=\"M178.93,60V59.5H180.81V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(179.87,59.48463758887828)scale(0.006384378647988948)\">second_moment_of_area_parallel_axis_theorem</text></g><g class=\"point\"><path d=\"M181.28,60V59.5H183.16V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(182.22,59.46804415011037)scale(0.01328035320088297)\">polar_moment_of_area</text></g><g class=\"point\"><path d=\"M183.63,60V59.56H185.51V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(184.57,59.544692820133236)scale(0.0063614253991751985)\">mass_moment_of_inertia_parallel_axis_theorem</text></g><g class=\"point\"><path d=\"M185.98,60V59.56H187.86V60Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(186.92000000000002,59.544121969946254)scale(0.006598661840517798)\">relative_magnitudes_mass_moment_of_inertia</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><path class=\"ylines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x4tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(311.08,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"x4tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(358.09999999999997,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20</text></g><g class=\"x4tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(405.13,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40</text></g><g class=\"x4tick\"><text text-anchor=\"middle\" x=\"0\" y=\"278\" transform=\"translate(452.15,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">60</text></g></g><g class=\"yaxislayer-above\"><g class=\"y4tick\"><text text-anchor=\"end\" x=\"308.9\" y=\"4.199999999999999\" transform=\"translate(0,265)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"y4tick\"><text text-anchor=\"end\" x=\"308.9\" y=\"4.199999999999999\" transform=\"translate(0,238.27)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5k</text></g><g class=\"y4tick\"><text text-anchor=\"end\" x=\"308.9\" y=\"4.199999999999999\" transform=\"translate(0,211.55)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-ebc54c\"><g class=\"clips\"/><clipPath id=\"legendebc54c\"><rect width=\"182\" height=\"86\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(506.36,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"182\" height=\"86\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legendebc54c')\"><g class=\"groups\"><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,14.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type: Level (Unit)</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"176.953125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,33.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type: Level (Module)</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"176.953125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,52.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type: Level (Section1)</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"176.953125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,71.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type: KC (F2011)</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"176.953125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Bar of distributions for every type</text></g><g class=\"g-xtitle\"/><g class=\"g-x2title\"/><g class=\"g-x3title\"/><g class=\"g-x4title\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/><g class=\"g-y3title\"/><g class=\"g-y4title\"/><g class=\"annotation\" data-index=\"0\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,174.05,88.5)\"><g class=\"cursor-pointer\" transform=\"translate(125,77)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"97\" height=\"22\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"49.1875\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Level (Unit)</text></g></g></g><g class=\"annotation\" data-index=\"1\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,403.95,88.5)\"><g class=\"cursor-pointer\" transform=\"translate(342,77)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"122\" height=\"22\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"61.53125\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Level (Module)</text></g></g></g><g class=\"annotation\" data-index=\"2\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,174.05,193.5)\"><g class=\"cursor-pointer\" transform=\"translate(107,182)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"134\" height=\"22\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"67.6875\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Level (Section1)</text></g></g></g><g class=\"annotation\" data-index=\"3\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,403.95,193.5)\"><g class=\"cursor-pointer\" transform=\"translate(356,182)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"95\" height=\"22\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"48.15625\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">KC (F2011)</text></g></g></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["col_bars = ['Level (Unit)','Level (Module)','Level (Section1)','KC (F2011)']\n", "\n", "fig = make_subplots(rows=3, cols=2,   # 2*2\n", "      start_cell=\"top-left\",  \n", "      subplot_titles=col_bars,   \n", "      column_widths=[0.5, 0.5]) \n", "traces = [\n", "    go.Bar(\n", "        x = df_transaction[colname].value_counts().reset_index().index.tolist(),\n", "        y = df_transaction[colname].value_counts().reset_index()[colname].tolist(),\n", "        name = 'Type: ' + str(colname),\n", "        text = df_transaction[colname].value_counts().reset_index()['index'].tolist(),\n", "        textposition = 'auto',\n", "    ) for colname in col_bars\n", "]\n", "for i in range(len(traces)):\n", "    fig.append_trace(\n", "        traces[i],\n", "        (i //2) + 1, # pos_row\n", "        (i % 2) + 1  # pos_col\n", "    )\n", "    \n", "fig.update_layout(\n", "    title_text = 'Bar of distributions for every type',\n", ")\n", "\n", "fig.show(\"svg\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> According to the chart below, there are 3 schools with a smaller sample of students."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-22c914\"><g class=\"clips\"><clipPath id=\"clip22c914xyplot\" class=\"plotclip\"><rect width=\"519\" height=\"211\"/></clipPath><clipPath class=\"axesclip\" id=\"clip22c914x\"><rect x=\"80\" y=\"0\" width=\"519\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clip22c914y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"211\"/></clipPath><clipPath class=\"axesclip\" id=\"clip22c914xy\"><rect x=\"80\" y=\"100\" width=\"519\" height=\"211\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"519\" height=\"211\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,243.28)\" d=\"M80,0h519\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,175.56)\" d=\"M80,0h519\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,107.84)\" d=\"M80,0h519\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,311)\" d=\"M80,0h519\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clip22c914xyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M7.41,211V198.81H66.73V211Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(37.05873203719358,206.13689771766695)scale(0.3605748098055791)\">Marion Technical College</text></g><g class=\"point\"><path d=\"M81.56,211V159.53H140.87V211Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(111.20747529876729,166.3826470311471)scale(0.3210539192622565)\">Sinclair Community College</text></g><g class=\"point\"><path d=\"M155.7,211V10.55H215.01V211Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(185.3003125,98.7921875)rotate(90 0.0546875 -4.796875)\">Carnegie Mellon University</text></g><g class=\"point\"><path d=\"M229.84,211V159.53H289.16V211Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(259.47912294440096,167.87452623335943)scale(0.4453771861132865)\">Kettering University</text></g><g class=\"point\"><path d=\"M303.99,211V102.65H363.3V211Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(333.598125,160.525)rotate(90 0.046875 -4.796875)\">Miami University</text></g><g class=\"point\"><path d=\"M378.13,211V189.33H437.44V211Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(407.7794418572322,195.17576909710155)scale(0.2371474247584625)\">University of Maryland Eastern Shore</text></g><g class=\"point\"><path d=\"M452.27,211V202.87H511.59V211Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(481.92999999999995,198.874871459695)scale(0.41355991285403043)\">University of Mississippi</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"324\" transform=\"translate(117.07,0) rotate(30,0,318)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Marion Technical College</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"324\" transform=\"translate(191.20999999999998,0) rotate(30,0,318)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Sinclair Community College</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"324\" transform=\"translate(265.36,0) rotate(30,0,318)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Carnegie Mellon University</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"324\" transform=\"translate(339.5,0) rotate(30,0,318)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Kettering University</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"324\" transform=\"translate(413.64,0) rotate(30,0,318)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Miami University</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"324\" transform=\"translate(487.79,0) rotate(30,0,318)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">University of Maryland Eastern Shore</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"324\" transform=\"translate(561.9300000000001,0) rotate(30,0,318)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">University of Mississippi</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,311)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,243.28)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">50</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,175.56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">100</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,107.84)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">150</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-22c914\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-635b83\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M293.5,235l0,-119.5a119.5,119.5 0 0 1 42.737771562930774,231.09629421192597Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(342.5141026360957,225.87996669247613)rotate(79.52238805970148)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">Carnegie Mellon University</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">44.2%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M293.5,235l-119.20454487598921,-8.398004578962514a119.5,119.5 0 0 1 119.20454487598921,-111.10199542103749Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(233.26709889902006,170.4431290196804)rotate(-42.98507462686564)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">Miami University</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">23.9%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M293.5,235l-95.67993941565281,71.59329014242192a119.5,119.5 0 0 1 -23.524605460336403,-79.99129472138443Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(88.30807624026471,266.90997074256427)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">Sinclair Community College</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">11.3%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M293.5,235l-25.57573581126272,116.73102303035168a119.5,119.5 0 0 1 -70.1042036043901,-45.13773288792976Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(161.401490600213,343.0786569304065)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">Kettering University</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">11.3%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M293.5,235l10.073948810711236,119.07462179389516a119.5,119.5 0 0 1 -35.64968462197396,-2.3435987635434827Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(165.73915415914735,373.0786569304065)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">University of Maryland Eastern Shore</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">4.78%</tspan></text></g><path class=\"textline\" stroke-width=\"1.5\" d=\"M285.66102915914735,354.2426120820752V376.0786569304065h-7.5\" fill=\"none\" style=\"stroke: rgb(42, 63, 95); stroke-opacity: 1;\"/></g><g class=\"slice\"><path class=\"surface\" d=\"M293.5,235l29.935479148537112,115.68974495670521a119.5,119.5 0 0 1 -19.861530337825876,3.3848768371899496Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(394.9824281524101,389.43185624920477)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">Marion Technical College</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.69%</tspan></text></g><path class=\"textline\" stroke-width=\"1.5\" d=\"M313.5761781524101,352.80151557086475V392.43185624920477h7.5\" fill=\"none\" style=\"stroke: rgb(42, 63, 95); stroke-opacity: 1;\"/></g><g class=\"slice\"><path class=\"surface\" d=\"M293.5,235l42.73777156293075,111.596294211926a119.5,119.5 0 0 1 -12.80229241439364,4.0934507447792186Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(409.11297243973553,359.43185624920477)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">University of Mississippi</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.79%</tspan></text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-635b83\"><g class=\"clips\"/><clipPath id=\"legend635b83\"><rect width=\"270\" height=\"143\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(417.78,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"270\" height=\"143\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend635b83')\"><g class=\"groups\"><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,14.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Carnegie Mellon University</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"264.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,33.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Miami University</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"264.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,52.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Sinclair Community College</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"264.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,71.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Kettering University</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"264.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,90.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">University of Maryland Eastern Shore</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"264.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,109.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Marion Technical College</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"264.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,128.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">University of Mississippi</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"264.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 按学校统计学生人数\n", "schools = [item for item in df_transaction_clear['School'].unique().tolist()]\n", "students = [len(df_transaction_clear[df_transaction_clear['School'] == sch]['Anon Student Id'].unique()) for sch in schools]\n", "fig = go.Figure(data=[go.Bar(\n", "        x = schools,\n", "        y = students,\n", "        name = 'The number of students is counted by school',\n", "        text = schools,\n", "        textposition = 'auto',\n", "    )])\n", "fig.show(\"svg\")\n", "fig = go.Figure(data=[go.Pie(\n", "        labels = schools,\n", "        values = students,\n", "        name = 'The number of students is counted by school',\n", "        text = schools, \n", "        textposition = 'auto',\n", "    )])\n", "fig.show(\"svg\")"]}], "metadata": {"celltoolbar": "原始单元格格式", "kernelspec": {"display_name": "Data", "language": "python", "name": "data"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 4}