{"cells": [{"cell_type": "markdown", "id": "electoral-seventh", "metadata": {}, "source": ["# math2015-Math2 Data Analysis"]}, {"cell_type": "code", "execution_count": 1, "id": "greenhouse-sphere", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import plotly.graph_objs as go"]}, {"cell_type": "code", "execution_count": 2, "id": "green-backup", "metadata": {}, "outputs": [], "source": ["path = \"C:/Users/<USER>/Desktop/Math2/rawdata.txt\"\n", "data = pd.read_table(path,header=None)"]}, {"cell_type": "markdown", "id": "unlikely-compression", "metadata": {}, "source": ["## RECORDS"]}, {"cell_type": "markdown", "id": "critical-kennedy", "metadata": {}, "source": ["The learning records are saved in a score matrix. Each row corresponds to the records of a student on different test items."]}, {"cell_type": "code", "execution_count": 3, "id": "impossible-simple", "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>10</th>\n", "      <th>11</th>\n", "      <th>12</th>\n", "      <th>13</th>\n", "      <th>14</th>\n", "      <th>15</th>\n", "      <th>16</th>\n", "      <th>17</th>\n", "      <th>18</th>\n", "      <th>19</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>12</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3906</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3907</th>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3908</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>12</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3909</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3910</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>6</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3911 rows × 20 columns</p>\n", "</div>"], "text/plain": ["      0   1   2   3   4   5   6   7   8   9   10  11  12  13  14  15  16  17  \\\n", "0      3   0   0   0   3   0   3   3   3   3   0   0   0   0   0   0   0   0   \n", "1      3   3   3   3   3   3   3   0   3   0   0   3   4   0   0   4  12  12   \n", "2      3   3   3   0   0   0   3   3   0   0   0   3   4   0   0   0   8   0   \n", "3      0   3   3   3   0   3   3   0   3   3   0   3   0   0   0   0  12  12   \n", "4      3   3   0   0   3   3   0   0   3   0   0   3   0   0   0   4   4  12   \n", "...   ..  ..  ..  ..  ..  ..  ..  ..  ..  ..  ..  ..  ..  ..  ..  ..  ..  ..   \n", "3906   3   3   0   3   3   0   0   0   3   0   0   3   0   0   0   0   6   0   \n", "3907   3   0   3   3   3   3   3   3   0   0   3   3   4   4   0   0   6   2   \n", "3908   3   3   0   3   3   3   3   0   3   3   0   0   0   0   0   0   2  12   \n", "3909   3   3   0   3   3   3   0   0   3   0   0   3   0   0   4   0   1  12   \n", "3910   3   3   3   3   0   3   0   3   3   0   0   0   0   0   0   0   6   8   \n", "\n", "      18  19  \n", "0      6   0  \n", "1      6   1  \n", "2      6   0  \n", "3      6   0  \n", "4      6   0  \n", "...   ..  ..  \n", "3906   6   1  \n", "3907   6   0  \n", "3908   6   4  \n", "3909   3   0  \n", "3910   6   2  \n", "\n", "[3911 rows x 20 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.set_option('display.max_rows',10)\n", "data"]}, {"cell_type": "markdown", "id": "premier-commission", "metadata": {}, "source": ["For example, the first row presents the learning records of student 0 where he gets 3 point on item 0 and 6 point on item 18. "]}, {"cell_type": "markdown", "id": "anticipated-engine", "metadata": {}, "source": ["## General features"]}, {"cell_type": "code", "execution_count": 4, "id": "front-pillow", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>10</th>\n", "      <th>11</th>\n", "      <th>12</th>\n", "      <th>13</th>\n", "      <th>14</th>\n", "      <th>15</th>\n", "      <th>16</th>\n", "      <th>17</th>\n", "      <th>18</th>\n", "      <th>19</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.00000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "      <td>3911.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2.633342</td>\n", "      <td>2.120174</td>\n", "      <td>1.608540</td>\n", "      <td>2.162363</td>\n", "      <td>1.300946</td>\n", "      <td>1.893122</td>\n", "      <td>1.765789</td>\n", "      <td>0.441064</td>\n", "      <td>2.145487</td>\n", "      <td>1.72360</td>\n", "      <td>1.418307</td>\n", "      <td>1.548709</td>\n", "      <td>1.557658</td>\n", "      <td>0.413194</td>\n", "      <td>0.805932</td>\n", "      <td>0.773204</td>\n", "      <td>4.384301</td>\n", "      <td>5.333419</td>\n", "      <td>6.609051</td>\n", "      <td>1.724367</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.982743</td>\n", "      <td>1.365965</td>\n", "      <td>1.496259</td>\n", "      <td>1.346009</td>\n", "      <td>1.486924</td>\n", "      <td>1.447754</td>\n", "      <td>1.476453</td>\n", "      <td>1.062517</td>\n", "      <td>1.354184</td>\n", "      <td>1.48343</td>\n", "      <td>1.497965</td>\n", "      <td>1.499401</td>\n", "      <td>1.950719</td>\n", "      <td>1.217549</td>\n", "      <td>1.604637</td>\n", "      <td>1.579750</td>\n", "      <td>3.812531</td>\n", "      <td>4.820757</td>\n", "      <td>3.085368</td>\n", "      <td>2.542170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>6.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.00000</td>\n", "      <td>0.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>4.000000</td>\n", "      <td>5.000000</td>\n", "      <td>6.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.00000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>4.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>6.000000</td>\n", "      <td>11.000000</td>\n", "      <td>7.000000</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.00000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>12.000000</td>\n", "      <td>12.000000</td>\n", "      <td>12.000000</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                0            1            2            3            4   \\\n", "count  3911.000000  3911.000000  3911.000000  3911.000000  3911.000000   \n", "mean      2.633342     2.120174     1.608540     2.162363     1.300946   \n", "std       0.982743     1.365965     1.496259     1.346009     1.486924   \n", "min       0.000000     0.000000     0.000000     0.000000     0.000000   \n", "25%       3.000000     0.000000     0.000000     0.000000     0.000000   \n", "50%       3.000000     3.000000     3.000000     3.000000     0.000000   \n", "75%       3.000000     3.000000     3.000000     3.000000     3.000000   \n", "max       3.000000     3.000000     3.000000     3.000000     3.000000   \n", "\n", "                5            6            7            8           9   \\\n", "count  3911.000000  3911.000000  3911.000000  3911.000000  3911.00000   \n", "mean      1.893122     1.765789     0.441064     2.145487     1.72360   \n", "std       1.447754     1.476453     1.062517     1.354184     1.48343   \n", "min       0.000000     0.000000     0.000000     0.000000     0.00000   \n", "25%       0.000000     0.000000     0.000000     0.000000     0.00000   \n", "50%       3.000000     3.000000     0.000000     3.000000     3.00000   \n", "75%       3.000000     3.000000     0.000000     3.000000     3.00000   \n", "max       3.000000     3.000000     3.000000     3.000000     3.00000   \n", "\n", "                10           11           12           13           14  \\\n", "count  3911.000000  3911.000000  3911.000000  3911.000000  3911.000000   \n", "mean      1.418307     1.548709     1.557658     0.413194     0.805932   \n", "std       1.497965     1.499401     1.950719     1.217549     1.604637   \n", "min       0.000000     0.000000     0.000000     0.000000     0.000000   \n", "25%       0.000000     0.000000     0.000000     0.000000     0.000000   \n", "50%       0.000000     3.000000     0.000000     0.000000     0.000000   \n", "75%       3.000000     3.000000     4.000000     0.000000     0.000000   \n", "max       3.000000     3.000000     4.000000     4.000000     4.000000   \n", "\n", "                15           16           17           18           19  \n", "count  3911.000000  3911.000000  3911.000000  3911.000000  3911.000000  \n", "mean      0.773204     4.384301     5.333419     6.609051     1.724367  \n", "std       1.579750     3.812531     4.820757     3.085368     2.542170  \n", "min       0.000000     0.000000     0.000000     0.000000     0.000000  \n", "25%       0.000000     0.000000     0.000000     6.000000     0.000000  \n", "50%       0.000000     4.000000     5.000000     6.000000     0.000000  \n", "75%       0.000000     6.000000    11.000000     7.000000     3.000000  \n", "max       4.000000    12.000000    12.000000    12.000000    10.000000  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "code", "execution_count": 5, "id": "powered-mistake", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The number of records:3911\n"]}], "source": ["print('The number of records:' + str(len(data)))"]}, {"cell_type": "code", "execution_count": 6, "id": "standing-speaker", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-6d3c27\"><g class=\"clips\"><clipPath id=\"clip6d3c27xyplot\" class=\"plotclip\"><rect width=\"540\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6d3c27x\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6d3c27y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6d3c27xy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"270\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"270\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(191.51999999999998,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(303.03999999999996,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(414.57,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(526.0899999999999,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(80,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clip6d3c27xyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,269.46V265.14H446.09V269.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,264.06V259.74H446.09V264.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,258.66V254.34H446.09V258.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,253.26V248.94H451.66V253.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,247.86V243.54H451.66V247.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,242.46V238.14H451.66V242.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,237.06V232.74H451.66V237.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,231.66V227.34H451.66V231.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,226.26V221.94H457.24V226.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,220.86V216.54H457.24V220.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,215.46V211.14H457.24V215.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,210.06V205.74H457.24V210.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,204.66V200.34H457.24V204.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,199.26V194.94H457.24V199.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,193.86V189.54H457.24V193.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,188.46V184.14H457.24V188.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,183.06V178.74H457.24V183.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,177.66V173.34H457.24V177.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,172.26V167.94H457.24V172.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,166.86V162.54H462.82V166.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,161.46V157.14H462.82V161.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,156.06V151.74H462.82V156.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,150.66V146.34H462.82V150.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,145.26V140.94H462.82V145.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,139.86V135.54H462.82V139.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,134.46V130.14H468.39V134.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,129.06V124.74H468.39V129.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,123.66V119.34H468.39V123.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,118.26V113.94H468.39V118.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,112.86V108.54H468.39V112.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,107.46V103.14H468.39V107.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,102.06V97.74H468.39V102.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,96.66V92.34H468.39V96.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,91.26V86.94H468.39V91.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,85.86V81.54H473.97V85.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,80.46V76.14H473.97V80.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,75.06V70.74H479.54V75.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,69.66V65.34H479.54V69.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,64.26V59.94H479.54V64.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,58.86V54.54H485.12V58.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,53.46V49.14H485.12V53.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,48.06V43.74H485.12V48.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,42.66V38.34H490.7V42.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,37.26V32.94H496.27V37.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,31.86V27.54H496.27V31.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,26.46V22.14H496.27V26.46Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,21.06V16.74H496.27V21.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,15.66V11.34H501.85V15.66Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,10.26V5.94H513V10.26Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,4.86V0.54H513V4.86Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(80,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(191.51999999999998,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(303.03999999999996,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(414.57,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">60</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(526.0899999999999,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">80</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,367.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2266-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,351.1)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1606-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,334.9)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">428-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,318.7)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1156-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,302.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">468-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,286.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1575-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,270.1)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">959-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,253.9)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2115-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,237.7)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2193-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,221.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1613-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,205.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3513-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,189.1)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">474-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,172.9)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2342-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,156.7)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3856-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,140.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2348-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,124.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2942-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,108.1)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3079-</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-6d3c27\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Top 50 students by score</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"410.20625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">count</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,18.231250000000003,235)\" x=\"18.231250000000003\" y=\"235\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">index1</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data['count']=data.apply(lambda x: x.sum(),axis=1)\n", "data['index1']=data.index\n", "ds=data.loc[:, ['count', 'index1']]\n", "ds['index1'] = ds['index1'].astype(str) + '-'\n", "ds = ds.sort_values(['count']).tail(50)\n", "fig = px.bar(\n", "    ds,\n", "    x = 'count',\n", "    y = 'index1',\n", "    orientation='h',\n", "    title='Top 50 students by score'\n", ")\n", "\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "certain-premium", "metadata": {}, "source": ["This figure shows the total score of Top 50 students."]}, {"cell_type": "code", "execution_count": 7, "id": "general-uruguay", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-c58dfa\"><g class=\"clips\"><clipPath id=\"clipc58dfaxyplot\" class=\"plotclip\"><rect width=\"540\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc58dfax\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc58dfay\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc58dfaxy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"270\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"270\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,324.64)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,279.28)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,233.93)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,188.57)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,143.21)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,370)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clipc58dfaxyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" shape-rendering=\"crispEdges\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,270V113.02H13.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M13.5,270V76.55H27V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M27,270V91.29H40.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M40.5,270V58.31H54V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M54,270V53.6H67.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M67.5,270V68.61H81V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M81,270V104.13H94.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M94.5,270V82.81H108V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M108,270V102.22H121.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M121.5,270V92.15H135V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M135,270V111.43H148.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M148.5,270V74.19H162V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M162,270V43.03H175.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M175.5,270V74.01H189V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M189,270V120.27H202.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M202.5,270V63.03H216V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M216,270V53.96H229.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M229.5,270V67.25H243V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M243,270V63.35H256.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M256.5,270V67.61H270V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M270,270V80.13H283.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M283.5,270V13.5H297V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M297,270V77.91H310.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M310.5,270V77.5H324V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M324,270V140.14H337.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M337.5,270V126.4H351V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M351,270V89.2H364.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M364.5,270V60.31H378V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M378,270V31.64H391.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M391.5,270V30.19H405V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M405,270V81.36H418.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M418.5,270V80.77H432V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M432,270V74.64H445.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M445.5,270V107.3H459V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M459,270V120.05H472.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M472.5,270V54.19H486V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M486,270V72.78H499.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M499.5,270V59.22H513V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M513,270V74.96H526.5V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M526.5,270V252.08H540V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(99, 110, 250); fill-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(80.07,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(215.07,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1000</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(350.07,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2000</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(485.07,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3000</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,324.64)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,279.28)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,233.93)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,188.57)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,143.21)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5000</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-c58dfa\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Students score distribution</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"410.20625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">index1</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,23.684375000000003,235)\" x=\"23.684375000000003\" y=\"235\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">sum of count</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ds=data.loc[:, ['count', 'index1']]\n", "ds = ds.sort_values(['index1'])\n", "fig = px.histogram(\n", "    ds,\n", "    x='index1',\n", "    y='count',\n", "    title='Students score distribution'\n", ")\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "gothic-bradford", "metadata": {}, "source": ["## Sort by correct rate"]}, {"cell_type": "code", "execution_count": 8, "id": "ordinary-apollo", "metadata": {}, "outputs": [], "source": ["path = \"C:/Users/<USER>/Desktop/Math2/data.txt\"\n", "data = pd.read_table(path,header=None)"]}, {"cell_type": "code", "execution_count": 9, "id": "peaceful-archives", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-b3335b\"><g class=\"clips\"><clipPath id=\"clipb3335bxyplot\" class=\"plotclip\"><rect width=\"540\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb3335bx\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb3335by\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb3335bxy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"270\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"270\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(196.89,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(313.77,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(430.66,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(547.54,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(80,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clipb3335bxyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,268.65V257.85H60.37V268.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,255.15V244.35H83.98V255.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,241.65V230.85H85.92V241.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,228.15V217.35H112.97V228.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,214.65V203.85H117.75V214.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,201.15V190.35H213.53V201.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,187.65V176.85H227.58V187.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,174.15V163.35H253.44V174.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,160.65V149.85H259.75V160.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,147.15V136.35H276.3V147.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,133.65V122.85H301.7V133.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,120.15V109.35H313.36V120.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,106.65V95.85H321.88V106.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,93.15V82.35H335.77V93.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,79.65V68.85H343.99V79.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,66.15V55.35H368.8V66.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,52.65V41.85H413.03V52.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,39.15V28.35H417.96V39.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,25.65V14.85H421.25V25.65Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,12.15V1.35H513V12.15Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(80,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(196.89,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(313.77,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(430.66,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.6</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(547.54,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.8</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,363.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">13-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,336.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">7-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,309.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">14-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,282.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,255.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">17-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,228.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">11-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,201.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">18-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,174.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">6-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,147.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,120.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3-</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-b3335b\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Average correct rate of questions</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"410.20625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">count</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,33.48125,235)\" x=\"33.48125\" y=\"235\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">problem_id</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ds = data.mean()\n", "ds1=pd.DataFrame(columns=['problem_id','count'])\n", "for i in range(len(ds)):\n", "    new=pd.DataFrame({\n", "        'problem_id':int(i),\n", "        'count':ds[i]\n", "    },index=[0]\n", "    )\n", "    ds1=ds1.append(new,ignore_index=True)\n", "\n", "ds1=ds1.sort_values(['count'])\n", "ds1['problem_id'] = (ds1['problem_id']).astype(str) + '-'\n", "fig = px.bar(\n", "    ds1,\n", "    x = 'count',\n", "    y = 'problem_id',\n", "    orientation = 'h',\n", "    title = 'Average correct rate of questions'\n", ")\n", "\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "corresponding-regular", "metadata": {}, "source": ["This figure presents the average correct rate of questions.It's obvious that students do the best on item 0 but need to improve on item 13."]}, {"cell_type": "markdown", "id": "electric-vessel", "metadata": {}, "source": ["## Sort by problem type"]}, {"cell_type": "code", "execution_count": 10, "id": "early-practitioner", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-fd1df9\"><g class=\"clips\"><clipPath id=\"clipfd1df9xyplot\" class=\"plotclip\"><rect width=\"222.75\" height=\"270\"/></clipPath><clipPath id=\"clipfd1df9x2y2plot\" class=\"plotclip\"><rect width=\"222.74999999999997\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfd1df9x\"><rect x=\"80\" y=\"0\" width=\"222.75\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfd1df9y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfd1df9xy\"><rect x=\"80\" y=\"100\" width=\"222.75\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfd1df9y2\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfd1df9xy2\"><rect x=\"80\" y=\"100\" width=\"222.75\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfd1df9x2\"><rect x=\"352.25\" y=\"0\" width=\"222.74999999999997\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfd1df9x2y\"><rect x=\"352.25\" y=\"100\" width=\"222.74999999999997\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipfd1df9x2y2\"><rect x=\"352.25\" y=\"100\" width=\"222.74999999999997\" height=\"270\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"222.75\" height=\"270\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"352.25\" y=\"100\" width=\"222.74999999999997\" height=\"270\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,319.47)\" d=\"M80,0h222.75\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,268.95)\" d=\"M80,0h222.75\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,218.42000000000002)\" d=\"M80,0h222.75\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,167.89)\" d=\"M80,0h222.75\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,117.36)\" d=\"M80,0h222.75\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,370)\" d=\"M80,0h222.75\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clipfd1df9xyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M11.14,270V21.23H100.24V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(55.69,36.230000000000004)\">49%</text></g><g class=\"point\"><path d=\"M122.51,270V13.5H211.61V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(167.06,28.5)\">51%</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><path class=\"ylines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(135.69,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">wrong</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(247.06,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">right</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,319.47)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,268.95)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,218.42000000000002)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.3</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,167.89)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.4</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,117.36)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x2y2\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x2\"/><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,332.47)\" d=\"M352.25,0h222.74999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,294.94)\" d=\"M352.25,0h222.74999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,257.41999999999996)\" d=\"M352.25,0h222.74999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,219.89)\" d=\"M352.25,0h222.74999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,182.36)\" d=\"M352.25,0h222.74999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,144.82999999999998)\" d=\"M352.25,0h222.74999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,107.31)\" d=\"M352.25,0h222.74999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y2zl zl crisp\" transform=\"translate(0,370)\" d=\"M352.25,0h222.74999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(352.25,100)\" clip-path=\"url('#clipfd1df9x2y2plot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M11.14,270V13.5H100.24V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(55.69,28.5)\">68%</text></g><g class=\"point\"><path d=\"M122.51,270V151.22H211.61V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(167.06,166.22)\">32%</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><path class=\"ylines-above crisp\" style=\"fill: none;\" d=\"M0,0\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(407.94,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">wrong</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(519.31,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">right</text></g></g><g class=\"yaxislayer-above\"><g class=\"y2tick\"><text text-anchor=\"end\" x=\"351.25\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"351.25\" y=\"4.199999999999999\" transform=\"translate(0,332.47)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.1</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"351.25\" y=\"4.199999999999999\" transform=\"translate(0,294.94)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.2</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"351.25\" y=\"4.199999999999999\" transform=\"translate(0,257.41999999999996)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.3</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"351.25\" y=\"4.199999999999999\" transform=\"translate(0,219.89)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.4</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"351.25\" y=\"4.199999999999999\" transform=\"translate(0,182.36)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"351.25\" y=\"4.199999999999999\" transform=\"translate(0,144.82999999999998)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.6</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"351.25\" y=\"4.199999999999999\" transform=\"translate(0,107.31)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.7</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-fd1df9\"><g class=\"clips\"/><clipPath id=\"legendfd1df9\"><rect width=\"103\" height=\"48\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(584.9000000000001,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"103\" height=\"48\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legendfd1df9')\"><g class=\"groups\"><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,14.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type:Obj</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"97.484375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,33.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type:Sub</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"97.484375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Average correct rate of questions for every problem type</text></g><g class=\"g-xtitle\"/><g class=\"g-x2title\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ds = data.mean()\n", "ds1=pd.DataFrame(columns=['problem_id','count'])\n", "for i in range(len(ds)):\n", "    new=pd.DataFrame({\n", "        'problem_id':int(i),\n", "        'count':ds[i]\n", "    },index=[0]\n", "    )\n", "    ds1=ds1.append(new,ignore_index=True)\n", "\n", "data2= [('Obj',ds1[ds1['problem_id']<15]['count'].mean()),\n", "    ('Sub',ds1[ds1['problem_id']>=14]['count'].mean())]\n", "ds2 = pd.DataFrame(\n", "    data=data2,\n", "    columns=['Type','Percent']\n", ")\n", "\n", "fig = make_subplots(rows=1,cols=2)\n", "traces = [\n", "    go.Bar(\n", "        x=['wrong','right'],\n", "        y=[ \n", "        1-float(ds2[ds2['Type']==item]['Percent']),\n", "        float(ds2[ds2['Type']==item]['Percent'])\n", "        ],\n", "        name='Type:' + str(item),\n", "        text=[\n", "        str(round(100*(1-float(ds2[ds2['Type']==item]['Percent'])))) + '%',\n", "        str(round(100*float(ds2[ds2['Type']==item]['Percent']))) + '%'\n", "        ],\n", "        textposition='auto'\n", "    ) for item in ds2['Type'].tolist()\n", "]\n", "for i in range(len(traces)):\n", "    fig.append_trace(\n", "        traces[i],\n", "        (i //2) + 1,\n", "        (i % 2) + 1\n", "    )    \n", "fig.update_layout(\n", "    title_text = 'Average correct rate of questions for every problem type',\n", ")\n", "\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "international-monster", "metadata": {}, "source": ["This figure shows that students do better in objective questions and their ability to answer subjective questions needs to be strengthened."]}, {"cell_type": "code", "execution_count": 11, "id": "heard-reynolds", "metadata": {}, "outputs": [], "source": ["path = \"C:/Users/<USER>/Desktop/Math2/problemdesc.txt\"\n", "data1 = pd.read_table(path,header=0)"]}, {"cell_type": "code", "execution_count": 12, "id": "outer-angel", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-d0fca6\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M344,235l0,-135a135,135 0 1 1 -16.919986531181078,268.9354846774545Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(411.36680416890835,244.03523506822864)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">52%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M344,235l-16.919986531181113,133.9354846774545a135,135 0 0 1 16.919986531181113,-268.9354846774545Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(276.56666368832634,235.55432908414758)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">48%</text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-d0fca6\"><g class=\"clips\"/><clipPath id=\"legendd0fca6\"><rect width=\"69\" height=\"48\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(618.5600000000001,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"69\" height=\"48\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legendd0fca6')\"><g class=\"groups\"><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,14.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Obj</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"63.28125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" style=\"opacity: 1;\" transform=\"translate(0,33.5)\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Sub</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"63.28125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem type</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["count = data1['Full Score'].sum()\n", "data2= [('Obj',data1[data1['Type']=='Obj']['Full Score'].sum()),\n", "    ('Sub',data1[data1['Type']=='Sub']['Full Score'].sum())]\n", "ds = pd.DataFrame(\n", "    data=data2,\n", "    columns=['Type','Percent']\n", ")\n", "\n", "ds['Percent']/=count\n", "ds=ds.sort_values('Percent')\n", "\n", "fig=px.pie(\n", "    ds,\n", "    names='Type',\n", "    values='Percent',\n", "    title='Problem type',\n", ")\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "impaired-letters", "metadata": {}, "source": ["## Sort by skills"]}, {"cell_type": "code", "execution_count": 13, "id": "protective-monthly", "metadata": {}, "outputs": [], "source": ["path1 = \"C:/Users/<USER>/Desktop/Math2/q.txt\"\n", "data1 = pd.read_table(path1,header=None)"]}, {"cell_type": "code", "execution_count": 14, "id": "vietnamese-arthritis", "metadata": {}, "outputs": [], "source": ["ds1 = data1.sum()"]}, {"cell_type": "code", "execution_count": 15, "id": "insured-abortion", "metadata": {}, "outputs": [], "source": ["path2 = \"C:/Users/<USER>/Desktop/Math2/qnames.txt\"\n", "data2 = pd.read_table(path2,header=0)"]}, {"cell_type": "code", "execution_count": 16, "id": "detailed-shadow", "metadata": {}, "outputs": [], "source": ["ds=pd.DataFrame(columns=['skill','count'])\n", "for i in range(len(ds1)):\n", "    new=pd.DataFrame({ \n", "        'skill':data2['Skill Names'][i],\n", "        'count':ds1[i]\n", "    },index=[0]\n", "    )\n", "    ds=ds.append(new,ignore_index=True)"]}, {"cell_type": "code", "execution_count": 17, "id": "royal-planner", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-661992\"><g class=\"clips\"><clipPath id=\"clip661992xyplot\" class=\"plotclip\"><rect width=\"398\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clip661992x\"><rect x=\"222\" y=\"0\" width=\"398\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clip661992y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clip661992xy\"><rect x=\"222\" y=\"100\" width=\"398\" height=\"270\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"222\" y=\"100\" width=\"398\" height=\"270\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(340.15999999999997,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(458.31,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(576.47,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(222,0)\" d=\"M0,100v270\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(222,100)\" clip-path=\"url('#clip661992xyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,268.31V254.81H23.63V268.31Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,251.44V237.94H23.63V251.44Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,234.56V221.06H23.63V234.56Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,217.69V204.19H47.26V217.69Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,200.81V187.31H47.26V200.81Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,183.94V170.44H47.26V183.94Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,167.06V153.56H70.89V167.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,150.19V136.69H70.89V150.19Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,133.31V119.81H70.89V133.31Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,116.44V102.94H70.89V116.44Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,99.56V86.06H70.89V99.56Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,82.69V69.19H94.53V82.69Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,65.81V52.31H118.16V65.81Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,48.94V35.44H141.79V48.94Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,32.06V18.56H212.68V32.06Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,15.19V1.69H378.1V15.19Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(222,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(340.15999999999997,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(458.31,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(576.47,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">15</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,361.56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Methods of data sampling,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,344.69)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Function versus equation,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,327.81)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Linear programming,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,310.94)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Classical probability theory,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,294.06)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Definitions of algorithm,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,277.19)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Algorithm logic,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,260.31)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Property of inequality,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,243.44)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Geometric progression,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,226.56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Solving triangle,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,209.69)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Principles of data analysis,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,192.81)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Data handling.</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,175.94)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Arithmetic progression,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,159.06)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Spatial imagination,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,142.19)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Abstract summarization,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,125.31)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Reasoning and demonstration,</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"221\" y=\"4.199999999999999\" transform=\"translate(0,108.44)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Calculation,</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-661992\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Skill count</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"421\" y=\"410.20625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">count</text></g><g class=\"g-ytitle\" transform=\"translate(2.3623046875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,12.043749999999989,235)\" x=\"12.043749999999989\" y=\"235\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">skill</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ds=ds.sort_values(['count'])\n", "fig = px.bar(\n", "    ds,\n", "    x='count',\n", "    y='skill',\n", "    orientation='h',\n", "    title='Skill count'\n", ")\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "nearby-jesus", "metadata": {}, "source": ["This figure shows that calculation is the most important skills in the test as almost every item is related to it. This proves that calculation is a necessary skill for math tests."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.2"}}, "nbformat": 4, "nbformat_minor": 5}