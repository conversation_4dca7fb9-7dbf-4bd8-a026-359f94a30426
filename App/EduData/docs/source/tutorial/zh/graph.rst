知识点图
===========

介绍
------------------
知识点图用于描述知识点之间的某些关系，图中的每个节点代表一个知识点，而权值的意义随图的种类不同而不同。

格式
------------------

::

	[
		['知识点编号1', '知识点编号2', '权值'],
		['知识点编号3', '知识点编号4', '权值'],
		['知识点编号5', '知识点编号6', '权值'],
		['知识点编号7', '知识点编号8', '权值'],
		...
	]

不同种类的知识点图
------------------

+----------------------------+------------------------------------------------+----------+
|            名称            |                    权值意义                    | 是否有向 |
+============================+================================================+==========+
| concurrence_graph          | 两个知识点之间相继出现的概率                   |    否    |
+----------------------------+------------------------------------------------+----------+
| transition_graph           | 一个知识点出现后，另一个知识点紧接着出现的概率 |    是    |
+----------------------------+------------------------------------------------+----------+
| correct_transition_graph   | 一个知识点掌握后，另一个知识点随之掌握的概率   |    是    |
+----------------------------+------------------------------------------------+----------+
| correct_co_influence_graph | 两个知识点之间的co_influence                   |    否    |
+----------------------------+------------------------------------------------+----------+
| similarity_graph           | 两个知识点关系之间的余弦相似度                 |    否    |
+----------------------------+------------------------------------------------+----------+

concurrence_graph(graph.con)
-----------------------

读入一个回答序列，如果两个知识点相继出现,则图中两个知识点所代表的节点之间的权值加一。

统计结束后将整个图的权值归一化得到概率。

例如：::

	[[1, 0], [2, 0], ...] ---> graph[1][2] += 1, graph[2][1] += 1

transition_graph(graph.trans)
-----------------------

读入一个回答序列，如果某个知识点在紧跟在另外一个知识点后出现，则前一个知识点到后一个知识点的边权值加一。

统计结束后将整个图的权值归一化得到概率。
例如：::

	[[1, 0], [2, 1], ...] ---> graph[1][2] += 1

correct_transition_graph(graph.trans)
------------------------

读入一个回答序列，如果某个知识点答对后紧随其后的那个知识点也同时答对，则前一个知识点到后一个知识点的边权值加一。

统计结束后将整个图的权值归一化得到概率。
例如：::

	[[1, 1], [2, 1], [3, 0], ...] ---> graph[1][2] += 1

correct_co_influence_graph(graph.ccon)
------------------------

读入一个回答序列，首先构造出未归一化的 ``correct_transition_graph`` ，然后计算两个节点间的 ``Co-influence`` 

	::
		
		Co-influence[i][j] = Co-influence[j][i] = (graph[i][j] + graph[j][i]) / |graph[i][j] - graph[i][j]|

输出 ``Co-influence``

similarity_graph(graph.sim)
------------------------

读入一个关系图，计算每两行之间的 ``余弦相似度``
例如：::
	
	[[1, 1, 0],             [[1, 0.94280904, 0.85280287],
	 [2, 2, 1],  --->        [0.94280904, 1, 0.90453403],
	 [3, 1, 1]]              [0.85280287, 0.90453403, 1]]
