#!/bin/bash
# Script to rebuild and install EduData from local source

# Activate your conda environment
# Replace 'edudata_env' with your environment name if different
conda activate edudata_env

# Navigate to the EduData directory (if not already there)
cd "$(dirname "$0")"

# Uninstall any existing EduData package
pip uninstall -y EduData

# Install in development mode
pip install -e .

# Verify installation
echo "Verifying installation..."
edudata --help

echo "EduData has been rebuilt and installed in development mode."
echo "Any changes to the source code will be immediately available without reinstallation."