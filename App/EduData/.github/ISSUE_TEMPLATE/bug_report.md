---
name: Bug report
about: Create a report to help us improve
title: ''
labels: 'Bug, needs triage'

---
## 🐛 Description
(A clear and concise description of what the bug is.)

### Error Message
(Paste the complete error message. Please also include stack trace by setting environment variable `DMLC_LOG_STACK_TRACE_DEPTH=100` before running your script.)

## To Reproduce
(If you developed your own code, please provide a short script that reproduces the error. For existing examples, please provide link.)

### Steps to reproduce
(Paste the commands you ran that produced the error.)

1.
2.

## What have you tried to solve it?

1.
2.

## Environment

<details>
<summary>Environment Information</summary>

**Operating System:** ...

**Python Version:** (e.g., python3.6, anaconda/python3.7, venv/python3.8)

</details>

## Additional context
