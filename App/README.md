# AI-Driven Adaptive Learning Platform

This is an AI-driven adaptive learning platform that can be used to build courses for any domain. The platform allows users to create modules with skill tagging and provides adaptive learning experiences.

## Project Structure

```
app/
├── frontend/         # React.js application
├── backend/          # Python FastAPI application
├── docker/           # Docker configuration files
└── documentation/    # Project documentation
```

## Features

- Domain-agnostic course creation
- Module builder with skill tagging
- Knowledge domain skills management
- AI-driven personalization
- Student progress tracking
- Adaptive assessments

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd app
```

2. Start the application using Docker Compose:
```bash
cd Docker
docker-compose up -d
```

3. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Development

### Frontend

The frontend is built with React.js and uses:
- React Router for navigation
- Bootstrap for styling
- Axios for API requests

To start the frontend development server:
```bash
cd Frontend
npm install
npm start
```

### Backend

The backend is built with Python FastAPI and uses:
- SQLAlchemy for ORM
- PyMySQL for database connection
- JWT for authentication

To start the backend development server:
```bash
cd Backend
pip install -r requirements.txt
uvicorn main:app --reload
```

## Database

The application uses MySQL as the database. The schema includes tables for:
- Users
- Courses
- Modules
- Skills
- Assessments
- User Progress
- Knowledge States

## API Endpoints

The API documentation is available at http://localhost:8000/docs when the application is running.

Key endpoints include:
- `/api/auth/register` - Register a new user
- `/api/auth/token` - Get authentication token
- `/api/courses` - Course management
- `/api/modules` - Module management
- `/api/skills` - Skill management

## Implementation Phases

### Phase 1 (Current)
- Basic frontend and backend setup
- Course, module, and skill management
- User authentication
- Docker containerization

### Phase 2 (Planned)
- Course building feature enhancements
- Module building with rich content
- Skill tagging and dependency mapping
- Knowledge domain skills set management

### Phase 3 (Planned)
- Student management
- Progress tracking
- Adaptive assessment
- AI-powered content sequencing

## Contributing

Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.