import React, { useState } from 'react';
import { Container, Row, Col, Form, Button, Card, ListGroup, Badge, Alert } from 'react-bootstrap';

const CourseBuilder = () => {
  const [course, setCourse] = useState({
    title: '',
    description: '',
    domain: '',
    skills: [],
    modules: []
  });

  const [newSkill, setNewSkill] = useState('');
  const [currentModule, setCurrentModule] = useState({
    title: '',
    description: '',
    content: '',
    skills: []
  });

  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');

  const handleCourseChange = (e) => {
    const { name, value } = e.target;
    setCourse({
      ...course,
      [name]: value
    });
  };

  const handleAddSkill = () => {
    if (newSkill.trim() === '') return;
    if (course.skills.includes(newSkill.trim())) {
      setErrors({ ...errors, skill: 'This skill already exists' });
      return;
    }
    
    setCourse({
      ...course,
      skills: [...course.skills, newSkill.trim()]
    });
    setNewSkill('');
    setErrors({ ...errors, skill: null });
  };

  const handleRemoveSkill = (skillToRemove) => {
    setCourse({
      ...course,
      skills: course.skills.filter(skill => skill !== skillToRemove)
    });
  };

  const handleModuleChange = (e) => {
    const { name, value } = e.target;
    setCurrentModule({
      ...currentModule,
      [name]: value
    });
  };

  const handleModuleSkillToggle = (skill) => {
    if (currentModule.skills.includes(skill)) {
      setCurrentModule({
        ...currentModule,
        skills: currentModule.skills.filter(s => s !== skill)
      });
    } else {
      setCurrentModule({
        ...currentModule,
        skills: [...currentModule.skills, skill]
      });
    }
  };

  const handleAddModule = () => {
    // Validate module
    if (!currentModule.title || !currentModule.description || currentModule.skills.length === 0) {
      setErrors({
        ...errors,
        module: 'Please fill all module fields and select at least one skill'
      });
      return;
    }

    setCourse({
      ...course,
      modules: [...course.modules, { ...currentModule, id: Date.now() }]
    });

    // Reset current module
    setCurrentModule({
      title: '',
      description: '',
      content: '',
      skills: []
    });
    setErrors({ ...errors, module: null });
  };

  const handleRemoveModule = (moduleId) => {
    setCourse({
      ...course,
      modules: course.modules.filter(module => module.id !== moduleId)
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate course
    const newErrors = {};
    if (!course.title) newErrors.title = 'Title is required';
    if (!course.description) newErrors.description = 'Description is required';
    if (!course.domain) newErrors.domain = 'Domain is required';
    if (course.skills.length === 0) newErrors.skills = 'At least one skill is required';
    if (course.modules.length === 0) newErrors.modules = 'At least one module is required';
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Submit course (API call would be here)
    console.log('Course data to submit:', course);
    setSuccessMessage('Course created successfully!');
    
    // Reset form after submission
    setTimeout(() => {
      setCourse({
        title: '',
        description: '',
        domain: '',
        skills: [],
        modules: []
      });
      setSuccessMessage('');
    }, 3000);
  };

  return (
    <Container>
      <h1 className="mb-4">Course Builder</h1>
      
      {successMessage && (
        <Alert variant="success" onClose={() => setSuccessMessage('')} dismissible>
          {successMessage}
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        <Row>
          <Col lg={8}>
            <Card className="mb-4">
              <Card.Header>Course Information</Card.Header>
              <Card.Body>
                <Form.Group className="mb-3">
                  <Form.Label>Course Title</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={course.title}
                    onChange={handleCourseChange}
                    isInvalid={errors.title}
                  />
                  <Form.Control.Feedback type="invalid">{errors.title}</Form.Control.Feedback>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="description"
                    value={course.description}
                    onChange={handleCourseChange}
                    isInvalid={errors.description}
                  />
                  <Form.Control.Feedback type="invalid">{errors.description}</Form.Control.Feedback>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Domain</Form.Label>
                  <Form.Control
                    type="text"
                    name="domain"
                    value={course.domain}
                    onChange={handleCourseChange}
                    isInvalid={errors.domain}
                  />
                  <Form.Control.Feedback type="invalid">{errors.domain}</Form.Control.Feedback>
                </Form.Group>
              </Card.Body>
            </Card>

            <Card className="mb-4">
              <Card.Header>Skills</Card.Header>
              <Card.Body>
                <Form.Group className="mb-3">
                  <Form.Label>Add Skills for this Course</Form.Label>
                  <div className="d-flex">
                    <Form.Control
                      type="text"
                      value={newSkill}
                      onChange={(e) => setNewSkill(e.target.value)}
                      isInvalid={errors.skill}
                      placeholder="Enter a skill"
                    />
                    <Button 
                      variant="primary" 
                      onClick={handleAddSkill} 
                      className="ms-2"
                    >
                      Add
                    </Button>
                  </div>
                  <Form.Control.Feedback type="invalid">{errors.skill}</Form.Control.Feedback>
                </Form.Group>

                {course.skills.length > 0 ? (
                  <div className="mt-3">
                    {course.skills.map((skill, index) => (
                      <Badge 
                        bg="primary" 
                        className="me-2 mb-2 p-2" 
                        key={index}
                      >
                        {skill}
                        <span 
                          className="ms-2" 
                          style={{ cursor: 'pointer' }}
                          onClick={() => handleRemoveSkill(skill)}
                        >
                          ×
                        </span>
                      </Badge>
                    ))}
                  </div>
                ) : (
                  errors.skills && <div className="text-danger">{errors.skills}</div>
                )}
              </Card.Body>
            </Card>

            <Card className="mb-4">
              <Card.Header>Module Builder</Card.Header>
              <Card.Body>
                <Form.Group className="mb-3">
                  <Form.Label>Module Title</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={currentModule.title}
                    onChange={handleModuleChange}
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Module Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    name="description"
                    value={currentModule.description}
                    onChange={handleModuleChange}
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Module Content</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    name="content"
                    value={currentModule.content}
                    onChange={handleModuleChange}
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Associated Skills</Form.Label>
                  {course.skills.length > 0 ? (
                    <div>
                      {course.skills.map((skill, index) => (
                        <Form.Check
                          type="checkbox"
                          id={`skill-${index}`}
                          label={skill}
                          key={index}
                          checked={currentModule.skills.includes(skill)}
                          onChange={() => handleModuleSkillToggle(skill)}
                        />
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted">Add course skills first</p>
                  )}
                </Form.Group>

                {errors.module && <div className="text-danger mb-3">{errors.module}</div>}

                <Button 
                  variant="success" 
                  onClick={handleAddModule}
                  disabled={course.skills.length === 0}
                >
                  Add Module
                </Button>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={4}>
            <Card className="mb-4">
              <Card.Header>Course Preview</Card.Header>
              <Card.Body>
                <h5>{course.title || 'Course Title'}</h5>
                <p>{course.description || 'Course description will appear here'}</p>
                
                {course.domain && (
                  <div className="mb-2">
                    <strong>Domain:</strong> {course.domain}
                  </div>
                )}

                <div className="mb-3">
                  <strong>Skills:</strong>
                  <div>
                    {course.skills.length > 0 ? (
                      course.skills.map((skill, index) => (
                        <Badge bg="info" className="me-1 mb-1" key={index}>
                          {skill}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-muted">No skills added</span>
                    )}
                  </div>
                </div>
              </Card.Body>
            </Card>

            <Card>
              <Card.Header>Modules ({course.modules.length})</Card.Header>
              <ListGroup variant="flush">
                {course.modules.length > 0 ? (
                  course.modules.map((module, index) => (
                    <ListGroup.Item key={module.id}>
                      <div className="d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">
                          {index + 1}. {module.title}
                        </h6>
                        <Button 
                          variant="outline-danger" 
                          size="sm"
                          onClick={() => handleRemoveModule(module.id)}
                        >
                          Remove
                        </Button>
                      </div>
                      <small className="text-muted d-block">{module.description}</small>
                      <div className="mt-1">
                        {module.skills.map((skill, idx) => (
                          <Badge bg="secondary" className="me-1 mb-1" key={idx}>
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </ListGroup.Item>
                  ))
                ) : (
                  <ListGroup.Item className="text-center text-muted">
                    No modules added yet
                  </ListGroup.Item>
                )}
              </ListGroup>
            </Card>

            {errors.modules && (
              <div className="text-danger mt-2">{errors.modules}</div>
            )}

            <div className="d-grid gap-2 mt-4">
              <Button 
                variant="primary" 
                size="lg" 
                type="submit"
              >
                Create Course
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
    </Container>
  );
};

export default CourseBuilder;