import React from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, Card, Button, Carousel } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const Home = () => {
  return (
    <Container>
      <Row className="mb-5">
        <Col>
          <Carousel>
            <Carousel.Item>
              <div className="d-block w-100 bg-secondary text-white p-5 text-center" style={{ height: '400px' }}>
                <div className="d-flex flex-column justify-content-center h-100">
                  <h2>Welcome to the Adaptive Learning Platform</h2>
                  <p className="lead">
                    Personalized learning experiences powered by AI to accelerate your progress
                  </p>
                  <div>
                    <Button as={Link} to="/courses" variant="primary" className="me-2">
                      Explore Courses
                    </Button>
                    <Button as={Link} to="/register" variant="outline-light">
                      Get Started
                    </Button>
                  </div>
                </div>
              </div>
            </Carousel.Item>
            <Carousel.Item>
              <div className="d-block w-100 bg-primary text-white p-5 text-center" style={{ height: '400px' }}>
                <div className="d-flex flex-column justify-content-center h-100">
                  <h2>Create Your Own Courses</h2>
                  <p className="lead">
                    Build domain-specific learning modules with our powerful course builder
                  </p>
                  <div>
                    <Button as={Link} to="/course-builder" variant="light" className="me-2">
                      Course Builder
                    </Button>
                    <Button as={Link} to="/courses" variant="outline-light">
                      View Examples
                    </Button>
                  </div>
                </div>
              </div>
            </Carousel.Item>
          </Carousel>
        </Col>
      </Row>

      <Row className="mb-5">
        <Col>
          <h2 className="text-center mb-4">How It Works</h2>
        </Col>
      </Row>

      <Row className="mb-5">
        <Col md={4} className="mb-4">
          <Card className="h-100 shadow-sm">
            <Card.Body className="text-center">
              <div className="mb-3">
                <i className="bi bi-journal-text" style={{ fontSize: '2.5rem', color: '#007bff' }}></i>
              </div>
              <Card.Title>Create Course Content</Card.Title>
              <Card.Text>
                Build modules for any domain with our intuitive course builder. Tag skills and set learning objectives.
              </Card.Text>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4} className="mb-4">
          <Card className="h-100 shadow-sm">
            <Card.Body className="text-center">
              <div className="mb-3">
                <i className="bi bi-diagram-3" style={{ fontSize: '2.5rem', color: '#28a745' }}></i>
              </div>
              <Card.Title>AI-Powered Adaptation</Card.Title>
              <Card.Text>
                Our AI engine analyzes learning patterns to create personalized learning paths for each student.
              </Card.Text>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4} className="mb-4">
          <Card className="h-100 shadow-sm">
            <Card.Body className="text-center">
              <div className="mb-3">
                <i className="bi bi-graph-up" style={{ fontSize: '2.5rem', color: '#6f42c1' }}></i>
              </div>
              <Card.Title>Track Progress</Card.Title>
              <Card.Text>
                Monitor skill development with detailed analytics and adaptive assessments.
              </Card.Text>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-5">
        <Col>
          <Card className="bg-light border-0">
            <Card.Body className="p-5">
              <Row>
                <Col md={6}>
                  <h3>Ready to transform learning?</h3>
                  <p className="lead">
                    Join our platform today and start creating adaptive learning experiences for any domain.
                  </p>
                  <Button as={Link} to="/register" variant="primary" size="lg">
                    Get Started
                  </Button>
                </Col>
                <Col md={6} className="d-flex align-items-center justify-content-center">
                  <div className="bg-white p-4 rounded shadow-sm">
                    <h4>Key Features</h4>
                    <ul className="mb-0">
                      <li>Domain-agnostic learning</li>
                      <li>AI-driven personalization</li>
                      <li>Skill-based modules</li>
                      <li>Detailed progress tracking</li>
                      <li>Adaptive assessments</li>
                    </ul>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Home;