import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Form, InputGroup, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const Courses = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDomain, setSelectedDomain] = useState('');

  // Sample course data - will be replaced with API calls
  const courses = [
    {
      id: 1,
      title: 'Introduction to Machine Learning',
      description: 'Learn the fundamentals of machine learning algorithms and applications.',
      domain: 'Computer Science',
      skills: ['Python', 'Statistics', 'Data Analysis', 'Algorithms'],
      modules: 8,
      image: 'https://via.placeholder.com/300x200'
    },
    {
      id: 2,
      title: 'Turkish Language A1',
      description: 'Master the basics of Turkish language with adaptive exercises and feedback.',
      domain: 'Language Learning',
      skills: ['Vocabulary', 'Grammar', 'Pronunciation', 'Reading'],
      modules: 12,
      image: 'https://via.placeholder.com/300x200'
    },
    {
      id: 3,
      title: 'Data Mining Techniques',
      description: 'Explore advanced data mining methods for extracting insights from large datasets.',
      domain: 'Computer Science',
      skills: ['SQL', 'Python', 'Data Visualization', 'Statistical Analysis'],
      modules: 10,
      image: 'https://via.placeholder.com/300x200'
    },
    {
      id: 4,
      title: 'Introduction to Psychology',
      description: 'Understand human behavior through the lens of psychological theories and research.',
      domain: 'Social Sciences',
      skills: ['Research Methods', 'Critical Thinking', 'Analysis', 'Theory Application'],
      modules: 15,
      image: 'https://via.placeholder.com/300x200'
    }
  ];

  // Get unique domains for filter
  const domains = [...new Set(courses.map(course => course.domain))];

  // Filter courses based on search term and selected domain
  const filteredCourses = courses.filter(course => {
    return (
      (course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
       course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
       course.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))) &&
      (selectedDomain === '' || course.domain === selectedDomain)
    );
  });

  return (
    <Container>
      <h1 className="mb-4">Explore Courses</h1>
      
      <Row className="mb-4">
        <Col md={8}>
          <InputGroup>
            <Form.Control
              placeholder="Search courses by title, description, or skills..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Button variant="outline-secondary">
              <i className="bi bi-search"></i>
            </Button>
          </InputGroup>
        </Col>
        <Col md={4}>
          <Form.Select
            value={selectedDomain}
            onChange={(e) => setSelectedDomain(e.target.value)}
          >
            <option value="">All Domains</option>
            {domains.map((domain, index) => (
              <option key={index} value={domain}>{domain}</option>
            ))}
          </Form.Select>
        </Col>
      </Row>

      <Row>
        {filteredCourses.length > 0 ? (
          filteredCourses.map(course => (
            <Col md={6} lg={4} className="mb-4" key={course.id}>
              <Card className="course-card h-100">
                <Card.Img variant="top" src={course.image} />
                <Card.Body>
                  <Card.Title>{course.title}</Card.Title>
                  <Badge bg="info" className="mb-2">{course.domain}</Badge>
                  <Card.Text>{course.description}</Card.Text>
                  <div className="mb-3">
                    {course.skills.map((skill, index) => (
                      <span key={index} className="skill-tag">{skill}</span>
                    ))}
                  </div>
                  <div className="d-flex justify-content-between align-items-center">
                    <small className="text-muted">{course.modules} modules</small>
                    <Button variant="primary" as={Link} to={`/courses/${course.id}`}>
                      View Course
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))
        ) : (
          <Col>
            <div className="text-center py-5">
              <h3>No courses found</h3>
              <p>Try adjusting your search or filters</p>
            </div>
          </Col>
        )}
      </Row>
    </Container>
  );
};

export default Courses;