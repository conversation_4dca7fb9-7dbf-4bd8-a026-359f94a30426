import React from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON>, Button, ProgressBar, Table, Tabs, Tab } from 'react-bootstrap';

const Dashboard = () => {
  // Sample data - will be replaced with API calls
  const studentProgress = {
    coursesEnrolled: 3,
    coursesCompleted: 1,
    avgProgress: 68,
    totalSkills: 24,
    skillsMastered: 15,
    skillsInProgress: 9
  };

  const courses = [
    {
      id: 1,
      title: 'Introduction to Machine Learning',
      progress: 85,
      lastAccessed: '2023-06-15',
      nextModule: 'Neural Networks Basics'
    },
    {
      id: 2,
      title: 'Turkish Language A1',
      progress: 62,
      lastAccessed: '2023-06-17',
      nextModule: 'Present Tense Verbs'
    },
    {
      id: 3,
      title: 'Data Mining Techniques',
      progress: 24,
      lastAccessed: '2023-06-12',
      nextModule: 'Association Rules'
    }
  ];

  const skills = [
    { name: 'Python Programming', progress: 90, level: 'Advanced' },
    { name: 'Statistical Analysis', progress: 75, level: 'Intermediate' },
    { name: 'Machine Learning Algorithms', progress: 65, level: 'Intermediate' },
    { name: 'Turkish Vocabulary', progress: 60, level: 'Beginner' },
    { name: 'Turkish Grammar', progress: 50, level: 'Beginner' }
  ];

  const recommendations = [
    {
      type: 'module',
      title: 'Neural Networks Basics',
      course: 'Introduction to Machine Learning',
      reason: 'Based on your progress in machine learning algorithms'
    },
    {
      type: 'practice',
      title: 'Turkish Present Tense Practice',
      course: 'Turkish Language A1',
      reason: 'Needs improvement based on recent assessment'
    },
    {
      type: 'course',
      title: 'Advanced Python for Data Science',
      reason: 'Complements your machine learning skills'
    }
  ];

  return (
    <Container>
      <h1 className="mb-4">Student Dashboard</h1>
      
      <Row className="mb-4">
        <Col md={4} className="mb-3">
          <Card className="dashboard-card h-100 bg-primary text-white">
            <Card.Body className="d-flex flex-column">
              <Card.Title>Courses</Card.Title>
              <div className="d-flex justify-content-between align-items-center mt-auto">
                <h2 className="mb-0">{studentProgress.coursesEnrolled}</h2>
                <div className="text-end">
                  <small>Completed: {studentProgress.coursesCompleted}</small>
                  <br />
                  <small>Avg Progress: {studentProgress.avgProgress}%</small>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4} className="mb-3">
          <Card className="dashboard-card h-100 bg-success text-white">
            <Card.Body className="d-flex flex-column">
              <Card.Title>Skills</Card.Title>
              <div className="d-flex justify-content-between align-items-center mt-auto">
                <h2 className="mb-0">{studentProgress.totalSkills}</h2>
                <div className="text-end">
                  <small>Mastered: {studentProgress.skillsMastered}</small>
                  <br />
                  <small>In Progress: {studentProgress.skillsInProgress}</small>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4} className="mb-3">
          <Card className="dashboard-card h-100 bg-info text-white">
            <Card.Body className="d-flex flex-column">
              <Card.Title>Learning Time</Card.Title>
              <div className="d-flex justify-content-between align-items-center mt-auto">
                <h2 className="mb-0">32h</h2>
                <div className="text-end">
                  <small>This Week: 5.5h</small>
                  <br />
                  <small>Today: 1.2h</small>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col lg={8}>
          <Card className="mb-4">
            <Card.Header>My Courses</Card.Header>
            <Card.Body>
              {courses.map(course => (
                <div key={course.id} className="mb-3 pb-3 border-bottom">
                  <div className="d-flex justify-content-between">
                    <h5>{course.title}</h5>
                    <Button variant="outline-primary" size="sm">Continue</Button>
                  </div>
                  <div className="d-flex align-items-center mb-1">
                    <span className="me-2">Progress: {course.progress}%</span>
                    <ProgressBar 
                      now={course.progress} 
                      className="flex-grow-1"
                      variant={course.progress < 30 ? "danger" : course.progress < 70 ? "warning" : "success"}
                    />
                  </div>
                  <small className="text-muted">
                    Last accessed: {course.lastAccessed} | Next: {course.nextModule}
                  </small>
                </div>
              ))}
              <div className="text-center mt-3">
                <Button variant="primary">View All Courses</Button>
              </div>
            </Card.Body>
          </Card>

          <Card>
            <Card.Header>Recommended For You</Card.Header>
            <Card.Body>
              <Row>
                {recommendations.map((rec, index) => (
                  <Col md={6} lg={4} className="mb-3" key={index}>
                    <Card className="h-100">
                      <Card.Body>
                        <Card.Title style={{ fontSize: '1rem' }}>{rec.title}</Card.Title>
                        {rec.course && (
                          <Card.Subtitle className="mb-2 text-muted" style={{ fontSize: '0.9rem' }}>
                            {rec.course}
                          </Card.Subtitle>
                        )}
                        <Card.Text className="text-muted" style={{ fontSize: '0.85rem' }}>
                          {rec.reason}
                        </Card.Text>
                        <div className="text-end">
                          <Button 
                            variant={rec.type === 'course' ? "primary" : "outline-primary"} 
                            size="sm"
                          >
                            {rec.type === 'course' ? 'Enroll' : 'Start'}
                          </Button>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>Skill Progress</Card.Header>
            <Card.Body>
              <Tabs defaultActiveKey="progress" className="mb-3">
                <Tab eventKey="progress" title="Progress">
                  {skills.map((skill, index) => (
                    <div key={index} className="mb-3">
                      <div className="d-flex justify-content-between mb-1">
                        <span>{skill.name}</span>
                        <span className="badge bg-secondary">{skill.level}</span>
                      </div>
                      <ProgressBar 
                        now={skill.progress} 
                        variant={skill.progress < 40 ? "danger" : skill.progress < 70 ? "warning" : "success"}
                      />
                    </div>
                  ))}
                </Tab>
                <Tab eventKey="recommendations" title="Recommended">
                  <Table size="sm">
                    <tbody>
                      <tr>
                        <td>Data Visualization</td>
                        <td>
                          <Button size="sm" variant="link">Start</Button>
                        </td>
                      </tr>
                      <tr>
                        <td>Turkish Pronunciation</td>
                        <td>
                          <Button size="sm" variant="link">Start</Button>
                        </td>
                      </tr>
                      <tr>
                        <td>SQL Basics</td>
                        <td>
                          <Button size="sm" variant="link">Start</Button>
                        </td>
                      </tr>
                    </tbody>
                  </Table>
                </Tab>
              </Tabs>
            </Card.Body>
          </Card>

          <Card>
            <Card.Header>Learning Activity</Card.Header>
            <Card.Body>
              <div className="text-center">
                {/* Simple placeholder for a graph */}
                <div className="bg-light p-4 mb-3" style={{ height: '150px', borderRadius: '4px' }}>
                  <div style={{ display: 'flex', height: '100%', alignItems: 'flex-end' }}>
                    {[40, 65, 35, 50, 75, 60, 80].map((val, i) => (
                      <div key={i} style={{ 
                        width: '12%', 
                        height: `${val}%`, 
                        background: '#007bff', 
                        margin: '0 2%',
                        borderRadius: '3px 3px 0 0'
                      }}></div>
                    ))}
                  </div>
                </div>
                <div className="d-flex justify-content-between px-2">
                  <small>Mon</small>
                  <small>Tue</small>
                  <small>Wed</small>
                  <small>Thu</small>
                  <small>Fri</small>
                  <small>Sat</small>
                  <small>Sun</small>
                </div>
              </div>
              <hr />
              <div>
                <div className="d-flex justify-content-between mb-1">
                  <span>Weekly Goal</span>
                  <span>8h / 10h</span>
                </div>
                <ProgressBar now={80} />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Dashboard;