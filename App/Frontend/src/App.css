.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

.course-card {
  transition: transform 0.3s ease;
  height: 100%;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.skill-tag {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 50px;
  font-size: 0.8rem;
  background-color: #e9ecef;
}

.module-builder {
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.dashboard-card {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
}