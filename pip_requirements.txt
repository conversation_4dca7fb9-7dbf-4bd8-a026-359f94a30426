absl-py @ file:///croot/absl-py_1714140470852/work
alembic==1.16.4
annotated-types==0.7.0
anyio @ file:///croot/anyio_1729121277521/work
arch==7.2.0
argon2-cffi @ file:///opt/conda/conda-bld/argon2-cffi_1645000214183/work
argon2-cffi-bindings @ file:///croot/argon2-cffi-bindings_1736182440035/work
asttokens @ file:///opt/conda/conda-bld/asttokens_1646925590279/work
astunparse==1.6.3
async-lru @ file:///croot/async-lru_1699554519285/work
attrs @ file:///croot/attrs_1734533101012/work
babel @ file:///croot/babel_1737454360933/work
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
beautifulsoup4 @ file:///croot/beautifulsoup4-split_1718029820055/work
bleach @ file:///croot/bleach_1732290411627/work
blinker==1.9.0
Bottleneck @ file:///croot/bottleneck_1731058641041/work
Brotli @ file:///croot/brotli-split_1736182456865/work
cachetools==5.5.2
certifi @ file:///croot/certifi_1738623731865/work/certifi
cffi @ file:///croot/cffi_1736182485317/work
charset-normalizer @ file:///croot/charset-normalizer_1721748349566/work
click==8.1.8
cloudpickle==3.1.1
comm @ file:///croot/comm_1709322850197/work
contourpy @ file:///croot/contourpy_1738160616259/work
curl_cffi==0.11.4
cycler @ file:///tmp/build/80754af9/cycler_1637851556182/work
Cython==3.1.2
dash==3.1.1
databricks-sdk==0.59.0
debugpy @ file:///croot/debugpy_1736267418885/work
decorator @ file:///opt/conda/conda-bld/decorator_1643638310831/work
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
docker==7.1.0
et_xmlfile==2.0.0
exceptiongroup @ file:///croot/exceptiongroup_1706031385326/work
executing @ file:///opt/conda/conda-bld/executing_1646925071911/work
fastapi==0.116.1
fastjsonschema @ file:///croot/python-fastjsonschema_1731939362158/work
findspark==2.0.1
Flask==3.1.1
flatbuffers @ file:///croot/python-flatbuffers_1722369094438/work
fonttools @ file:///croot/fonttools_1737039080035/work
frozendict==2.4.6
gast @ file:///tmp/build/80754af9/gast_1637837472536/work
gitdb==4.0.12
GitPython==3.1.44
google-auth==2.40.3
google-pasta @ file:///Users/<USER>/demo/mc3/conda-bld/google-pasta_1630577991354/work
graphene==3.4.3
graphql-core==3.2.6
graphql-relay==3.2.0
greenlet==3.2.3
grpcio @ file:///croot/grpc-split_1716834572340/work
gunicorn==23.0.0
h11 @ file:///croot/h11_1706652277403/work
h5py @ file:///croot/h5py_1737051113867/work
holidays==0.76
httpcore @ file:///croot/httpcore_1706728464539/work
httpx @ file:///croot/httpx_1723474802858/work
idna @ file:///croot/idna_1714398848350/work
importlib_metadata @ file:///croot/importlib_metadata-suite_1732633488278/work
importlib_resources @ file:///croot/importlib_resources-suite_1720641103994/work
iniconfig==2.1.0
ipykernel @ file:///croot/ipykernel_1737660677549/work
ipython @ file:///croot/ipython_1694181358621/work
ipywidgets @ file:///croot/ipywidgets_1733504575802/work
itsdangerous==2.2.0
jedi @ file:///croot/jedi_1733987392413/work
Jinja2 @ file:///croot/jinja2_1741710844255/work
joblib @ file:///croot/joblib_1718217211762/work
json5 @ file:///croot/json5_1730786798687/work
jsonschema @ file:///croot/jsonschema_1728486696720/work
jsonschema-specifications @ file:///croot/jsonschema-specifications_1699032386549/work
jupyter @ file:///croot/jupyter_1737645803214/work
jupyter-console @ file:///croot/jupyter_console_1679999630278/work
jupyter-events @ file:///croot/jupyter_events_1741184577592/work
jupyter-lsp @ file:///croot/jupyter-lsp-meta_1699978238815/work
jupyter_client @ file:///croot/jupyter_client_1737570961872/work
jupyter_core @ file:///croot/jupyter_core_1718818295206/work
jupyter_server @ file:///croot/jupyter_server_1741200034823/work
jupyter_server_terminals @ file:///croot/jupyter_server_terminals_1686870725608/work
jupyterlab @ file:///croot/jupyterlab_1737555423487/work
jupyterlab_pygments @ file:///croot/jupyterlab_pygments_1741124142640/work
jupyterlab_server @ file:///croot/jupyterlab_server_1725865349919/work
jupyterlab_widgets @ file:///croot/jupyterlab_widgets_1733440870459/work
keras==3.9.0
keras-tuner==1.4.7
kiwisolver @ file:///croot/kiwisolver_1672387140495/work
kneed==0.8.5
kt-legacy==1.0.5
Mako==1.3.10
Markdown @ file:///croot/markdown_1671541909495/work
markdown-it-py @ file:///croot/markdown-it-py_1684279902645/work
MarkupSafe @ file:///croot/markupsafe_1738584038848/work
matplotlib==3.9.2
matplotlib-inline @ file:///opt/conda/conda-bld/matplotlib-inline_1662014470464/work
mdurl @ file:///opt/conda/conda-bld/mdurl_1659716024347/work
mistune @ file:///croot/mistune_1741124011532/work
mkl-service==2.4.0
mkl_fft @ file:///io/mkl313/mkl_fft_1730824109137/work
mkl_random @ file:///io/mkl313/mkl_random_1730823916628/work
ml_dtypes @ file:///croot/ml_dtypes_1722369114743/work
mlflow==3.1.1
mlflow-skinny==3.1.1
multitasking==0.0.11
namex @ file:///croot/namex_1715206936162/work
narwhals==1.47.0
nbclient @ file:///croot/nbclient_1741123995822/work
nbconvert @ file:///croot/nbconvert-meta_1741184653315/work
nbformat @ file:///croot/nbformat_1728049424075/work
nest-asyncio @ file:///croot/nest-asyncio_1708532673751/work
notebook @ file:///croot/notebook_1738159946465/work
notebook_shim @ file:///croot/notebook-shim_1741707758683/work
numexpr @ file:///croot/numexpr_1730215937391/work
numpy @ file:///croot/numpy_and_numpy_base_1708638617955/work/dist/numpy-1.26.4-cp39-cp39-linux_x86_64.whl#sha256=6094eeedd869502faa0fd0a8c5ad3a70c5779be06ddd1feb7627e5c212fac420
openpyxl==3.1.5
opentelemetry-api==1.35.0
opentelemetry-sdk==1.35.0
opentelemetry-semantic-conventions==0.56b0
opt-einsum @ file:///tmp/build/80754af9/opt_einsum_1621500238896/work
optree @ file:///croot/optree_1742210181724/work
overrides @ file:///croot/overrides_1699371140756/work
packaging @ file:///croot/packaging_1734472117206/work
pandas @ file:///croot/pandas_1732735089971/work/dist/pandas-2.2.3-cp39-cp39-linux_x86_64.whl#sha256=0a51ed2e81ab863e3d00ed6c5049192ce578ecb38fb467d2f9a6585d3c25f666
pandocfilters @ file:///opt/conda/conda-bld/pandocfilters_1643405455980/work
parso @ file:///croot/parso_1733963305961/work
patsy==1.0.1
peewee==3.18.2
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
pillow @ file:///croot/pillow_1738010226202/work
platformdirs @ file:///croot/platformdirs_1692205439124/work
plotly==6.2.0
pluggy==1.6.0
pmdarima==2.0.4
prometheus_client @ file:///croot/prometheus_client_1731953121795/work
prompt-toolkit @ file:///croot/prompt-toolkit_1704404351921/work
protobuf==4.25.3
psutil @ file:///croot/psutil_1736367091698/work
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pure-eval @ file:///opt/conda/conda-bld/pure_eval_1646925070566/work
py4j==********
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pydantic==2.11.7
pydantic_core==2.33.2
Pygments @ file:///croot/pygments_1684279966437/work
pyparsing @ file:///croot/pyparsing_1731445506121/work
PyQt6==6.7.1
PyQt6_sip @ file:///croot/pyqt-split_1740498191142/work/pyqt_sip
PySocks @ file:///tmp/build/80754af9/pysocks_1605305812635/work
pyspark==4.0.0
pytest==8.4.1
python-dateutil @ file:///croot/python-dateutil_1716495738603/work
python-json-logger @ file:///croot/python-json-logger_1734370021104/work
pytz @ file:///croot/pytz_1713974312559/work
PyYAML @ file:///croot/pyyaml_1728657952215/work
pyzmq @ file:///croot/pyzmq_1734687138743/work
qtconsole @ file:///croot/qtconsole_1737590761179/work
QtPy @ file:///croot/qtpy_1700144840038/work
referencing @ file:///croot/referencing_1699012038513/work
requests @ file:///croot/requests_1730999120400/work
retrying==1.4.0
rfc3339-validator @ file:///croot/rfc3339-validator_1683077044675/work
rfc3986-validator @ file:///croot/rfc3986-validator_1683058983515/work
rich @ file:///croot/rich_1732638981168/work
rpds-py @ file:///croot/rpds-py_1736541261634/work
rsa==4.9.1
scikit-learn @ file:///croot/scikit-learn_1737988764076/work
scipy @ file:///croot/scipy_1733756309941/work/dist/scipy-1.13.1-cp39-cp39-linux_x86_64.whl#sha256=a4372e07d982a377bda57ec71fd74ac403442571e1d717de47325a14dea5c278
seaborn @ file:///croot/seaborn_1741185878286/work
Send2Trash @ file:///croot/send2trash_1736540790175/work
sip @ file:///croot/sip_1738856193618/work
six @ file:///tmp/build/80754af9/six_1644875935023/work
smmap==5.0.2
sniffio @ file:///croot/sniffio_1705431295498/work
soupsieve @ file:///croot/soupsieve_1696347547217/work
SQLAlchemy==2.0.41
sqlparse==0.5.3
stack-data @ file:///opt/conda/conda-bld/stack_data_1646927590127/work
starlette==0.47.1
statsmodels==0.14.4
tensorboard @ file:///croot/tensorboard_1723042039441/work/tensorboard-2.17.0-py3-none-any.whl#sha256=859a499a9b1fb68a058858964486627100b71fcb21646861c61d31846a6478fb
tensorboard_data_server @ file:///croot/tensorboard-data-server_1724172556187/work/tensorboard_data_server-0.7.0-py3-none-manylinux2014_x86_64.whl#sha256=79c85cfe2c0cdd115b61609d332d372681e5dac49d438683075d957adc2048f1
tensorflow @ file:///croot/tensorflow-split_1728614927061/work/tensorflow_pkg/tensorflow-2.17.0-cp39-cp39-linux_x86_64.whl#sha256=bde9b8394689a225ec9c94abe922a6bad2d95aabc79c419aa4f2c0687f206cd4
tensorflow_estimator @ file:///croot/tensorflow-split_1728614927061/work/tensorflow-estimator/wheel_dir/tensorflow_estimator-2.15.0-py2.py3-none-any.whl#sha256=31995f9efe078970e609ad150f88fdd53a886cd252d983411c4c427b95daf139
termcolor @ file:///croot/termcolor_1668084651543/work
terminado @ file:///croot/terminado_1671751832461/work
threadpoolctl @ file:///croot/threadpoolctl_1719407800858/work
tinycss2 @ file:///croot/tinycss2_1738337643607/work
tomli @ file:///opt/conda/conda-bld/tomli_1657175507142/work
tornado @ file:///croot/tornado_1733960490606/work
traitlets @ file:///croot/traitlets_1718227057033/work
typing-inspection==0.4.1
typing_extensions @ file:///croot/typing_extensions_1734714854207/work
tzdata @ file:///croot/python-tzdata_1690578112552/work
unicodedata2 @ file:///croot/unicodedata2_1736541023050/work
urllib3 @ file:///croot/urllib3_1737133630106/work
uvicorn==0.35.0
wcwidth @ file:///Users/<USER>/demo/mc3/conda-bld/wcwidth_1629357192024/work
webencodings==0.5.1
websocket-client @ file:///croot/websocket-client_1715878298792/work
websockets==15.0.1
Werkzeug @ file:///croot/werkzeug_1737448706492/work
widgetsnbextension @ file:///croot/widgetsnbextension_1733439572884/work
wrapt @ file:///croot/wrapt_1736540904746/work
yfinance==0.2.65
zipp @ file:///croot/zipp_1732630741423/work
