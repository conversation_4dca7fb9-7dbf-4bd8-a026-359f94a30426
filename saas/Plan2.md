seperate the phase 3 implementation plan in @phase3_ai_integration.md into 2 phases.
3.1 AI tasks related to course building and assessment creation
this phase should include all the AI tasks related to course building and assessment creation using LLMs. it need to include the following:
- Integration of LLMs for course content generation
- AI-assisted assessment creation
- Skill tagging and difficulty assessment
- Content validation and quality assurance
- User interface for AI-assisted content creation
- API documentation for AI services
- Testing and refinement of AI-generated content
- ability to configure integration with LLMs, allowing multiple LLMs to be used
- ability to configure the prompt for the LLMs to generate the content
- ability to use local deployed LLMs
- ability to use cloud hosted LLMs
- ability to use open source LLMs
- llms configuration should be done in the UI and stored in the database
- llms configuration should include the following:
    - llm name
    - llm type (open source, cloud hosted)
    - llm api key (if cloud hosted)
    - llm api url (if cloud hosted)
    - llm prompt (if any)
    - llm model (if open source)
    - llm other parameters (if any)

provide recommendations for LLMs to be used and their configuration, and how it can be integrated with the LMS for content generation and assessment creation.

3.2 AI tasks related to knowledge tracing and adaptive learning


Testing Content:
as created in @turkish_a1_course_specification.md create another specification for following courses each covering different skills and topics in separate files:
1- English A1 level
2- Math for grade 1
3- Math for grade 2
<!-- 4- Math for data scientists (Algebra, Calculus, Statistics, Probability, Linear Algebra, Optimization, Numerical Analysis, Differential Equations, Dynamical Systems, Chaos Theory, Game Theory, Information Theory, Cryptography, Quantum Computing, Machine Learning, Deep Learning, Reinforcement Learning, Natural Language Processing, Computer Vision, Robotics, Control Theory, Signal Processing, Image Processing, Audio Processing, Video Processing, Time Series Analysis, Forecasting, Predictive Analytics, Prescriptive Analytics, Decision Science, Operations Research,) -->
3- Machine learning advanced level
