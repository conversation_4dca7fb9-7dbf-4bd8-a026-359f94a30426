Role: Software Engineer, Solutions Architect, LMS developr

Study documents attached in /docs

Task: digest attached documents on AI driven adaptive learning system, and create a comprehensive plan for the implementation of the SaaS LMS that will offer users with full experience of taking courses have AI-backed assessments with real time skills acquiring information and learning master degreee.

we are in the phase of building the LMS where instructors can can create and publish courses, each course will have modules (reading,video,scorm...etc) and assessments connected with bank of question tagged by skills.

we need to add new AI tool to help in course and assessments building using LLMs where instructors will lead the AI to generate the course to be compatible with all of the features mentioned in the study documents of knowledge tracing, explainable AI, adaptive learning, and mastery learning measurements.

In the first launch of a course it might lack enough data for modeling the knowledge tracing, we need an alternative method to assess the latent skills knowledge acquired by student inorder to provide adaptive learning paths.

all student actions need to be tracked and stored as data for later tracking knowledge modeling and behaviour analysis 

Final SaaS LMS apps to be developed:

1- LMS Dashboard for instructors for creating courses, enroll student, view insights, analyze students results.

2- LMS platform for students to take courses, accounts managment, home page and marketing pages

3- LMS AI Dashboard for monitoring all content and results, analyzing data with dashboards, models training ..etc 

4- LMS Insights dashboard for auditing 

There should be a full RBAC auth system

1 planned course for testing is Turkish Language A1 level (details attached)

Tech stack: 

react for front, node for backend apis, python for AI and modeling

Serverless hosting is prfrefed or a dockerized solution

Terraform for CI/CD deployment

local dev enironment need to be configured with docker containers

Output format:

A comprehensive plan for developing the LMS in multiple phases 4-5, with tech stack specs . Cover all the mentioned details above and in the study documents, add all relevant suggestions to be implemented.

The expected final product is a fully functional AI-powered LMS system that can be deployed into any cloud provider


createa al