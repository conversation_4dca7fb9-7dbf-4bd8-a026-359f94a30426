# Phase 2: Basic LMS Functionality
**Duration**: 6-8 weeks  
**Team Size**: 6-8 developers (3 Backend, 3 Frontend, 1 DevOps, 1 AI/ML)

## Overview
Phase 2 builds upon the foundation established in Phase 1 to create a fully functional basic LMS with course creation, content management, assessment capabilities, and student learning interfaces. This phase focuses on core educational functionality without advanced AI features.

## Objectives
- Implement comprehensive course creation and management system
- Build content authoring tools with multiple media support
- Create assessment and quiz functionality with question banks
- Develop student learning interface with progress tracking
- Implement basic analytics and reporting
- Add file upload and SCORM support

## Deliverables

### 1. Course Management System (Week 1-2)

#### 1.1 Course Builder Interface
```typescript
// Course structure data model
interface Course {
  id: string;
  title: string;
  description: string;
  instructorId: string;
  organizationId: string;
  status: 'draft' | 'published' | 'archived';
  settings: {
    enrollmentType: 'open' | 'invite' | 'approval';
    startDate?: Date;
    endDate?: Date;
    timeLimit?: number; // minutes
    passingScore: number; // percentage
    allowRetakes: boolean;
    maxAttempts?: number;
  };
  modules: Module[];
  skills: string[]; // skill IDs
  prerequisites: string[]; // course IDs
  estimatedDuration: number; // minutes
  difficulty: 1 | 2 | 3 | 4 | 5;
  tags: string[];
  thumbnail?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Module {
  id: string;
  title: string;
  description: string;
  order: number;
  type: 'content' | 'assessment' | 'scorm';
  content: ContentItem[];
  isRequired: boolean;
  estimatedDuration: number;
}

interface ContentItem {
  id: string;
  title: string;
  type: 'text' | 'video' | 'audio' | 'pdf' | 'interactive' | 'quiz';
  content: any; // Type-specific content
  order: number;
  isRequired: boolean;
  skills: string[]; // Associated skill IDs
}
```

#### 1.2 Drag-and-Drop Course Builder
```typescript
// React component for course builder
const CourseBuilder: React.FC = () => {
  const [course, setCourse] = useState<Course>();
  const [draggedItem, setDraggedItem] = useState<ContentItem | null>(null);

  const handleDrop = (moduleId: string, position: number) => {
    // Handle content reordering
  };

  return (
    <div className="course-builder">
      <CourseHeader course={course} />
      <div className="builder-layout">
        <ContentLibrary onDragStart={setDraggedItem} />
        <ModuleEditor 
          modules={course?.modules} 
          onDrop={handleDrop}
          onModuleUpdate={updateModule}
        />
        <PropertiesPanel selectedItem={selectedItem} />
      </div>
    </div>
  );
};

// Content library with draggable items
const ContentLibrary: React.FC = ({ onDragStart }) => {
  const contentTypes = [
    { type: 'text', icon: '📝', label: 'Text Content' },
    { type: 'video', icon: '🎥', label: 'Video' },
    { type: 'quiz', icon: '❓', label: 'Quiz' },
    { type: 'interactive', icon: '🎮', label: 'Interactive' },
  ];

  return (
    <div className="content-library">
      <h3>Content Library</h3>
      {contentTypes.map(type => (
        <div
          key={type.type}
          draggable
          onDragStart={() => onDragStart(type)}
          className="content-type-item"
        >
          <span>{type.icon}</span>
          <span>{type.label}</span>
        </div>
      ))}
    </div>
  );
};
```

#### 1.3 Content Authoring Tools
```typescript
// Rich text editor for content creation
const ContentEditor: React.FC<{ content: ContentItem }> = ({ content }) => {
  switch (content.type) {
    case 'text':
      return <RichTextEditor content={content} />;
    case 'video':
      return <VideoUploader content={content} />;
    case 'quiz':
      return <QuizBuilder content={content} />;
    case 'interactive':
      return <InteractiveContentBuilder content={content} />;
    default:
      return <div>Unsupported content type</div>;
  }
};

// Rich text editor with multimedia support
const RichTextEditor: React.FC = ({ content, onChange }) => {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image,
      Link,
      CodeBlock,
      Table,
      Mathematics, // LaTeX support
    ],
    content: content.data,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  return (
    <div className="rich-text-editor">
      <EditorToolbar editor={editor} />
      <EditorContent editor={editor} />
    </div>
  );
};
```

### 2. Assessment and Quiz System (Week 2-3)

#### 2.1 Question Bank Management
```typescript
interface Question {
  id: string;
  type: 'multiple_choice' | 'true_false' | 'short_answer' | 'essay' | 'coding' | 'matching';
  question: string;
  options?: string[]; // For multiple choice
  correctAnswer: string | string[];
  explanation?: string;
  difficulty: 1 | 2 | 3 | 4 | 5;
  skills: string[]; // Associated skill IDs
  points: number;
  timeLimit?: number; // seconds
  metadata: {
    bloomsTaxonomy?: string;
    cognitiveLoad?: number;
    estimatedTime?: number;
  };
  tags: string[];
  createdBy: string;
  createdAt: Date;
}

// Question bank interface
const QuestionBank: React.FC = () => {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [filters, setFilters] = useState({
    type: '',
    difficulty: '',
    skills: [],
    tags: [],
  });

  return (
    <div className="question-bank">
      <QuestionFilters filters={filters} onChange={setFilters} />
      <QuestionList questions={filteredQuestions} />
      <QuestionEditor onSave={saveQuestion} />
    </div>
  );
};
```

#### 2.2 Quiz Builder
```typescript
interface Quiz {
  id: string;
  title: string;
  description: string;
  instructions: string;
  questions: QuizQuestion[];
  settings: {
    timeLimit?: number; // minutes
    allowBacktrack: boolean;
    shuffleQuestions: boolean;
    shuffleOptions: boolean;
    showResults: 'immediate' | 'after_submission' | 'never';
    passingScore: number; // percentage
    maxAttempts: number;
    gradingMethod: 'highest' | 'latest' | 'average';
  };
  skills: string[]; // Skills assessed by this quiz
}

interface QuizQuestion {
  questionId: string;
  order: number;
  points: number;
  required: boolean;
}

// Quiz builder component
const QuizBuilder: React.FC = () => {
  const [quiz, setQuiz] = useState<Quiz>();
  const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([]);

  const addQuestionToQuiz = (question: Question) => {
    const quizQuestion: QuizQuestion = {
      questionId: question.id,
      order: quiz.questions.length + 1,
      points: question.points,
      required: true,
    };
    setQuiz(prev => ({
      ...prev,
      questions: [...prev.questions, quizQuestion],
    }));
  };

  return (
    <div className="quiz-builder">
      <QuizSettings quiz={quiz} onChange={setQuiz} />
      <div className="quiz-content">
        <QuestionSelector onSelect={addQuestionToQuiz} />
        <QuizPreview quiz={quiz} />
      </div>
    </div>
  );
};
```

#### 2.3 Auto-Grading System
```typescript
// Grading service
class GradingService {
  static gradeSubmission(quiz: Quiz, submission: QuizSubmission): GradingResult {
    let totalPoints = 0;
    let earnedPoints = 0;
    const questionResults: QuestionResult[] = [];

    for (const question of quiz.questions) {
      const answer = submission.answers.find(a => a.questionId === question.questionId);
      const questionData = getQuestion(question.questionId);
      
      totalPoints += question.points;
      
      const isCorrect = this.gradeQuestion(questionData, answer?.response);
      if (isCorrect) {
        earnedPoints += question.points;
      }

      questionResults.push({
        questionId: question.questionId,
        isCorrect,
        points: isCorrect ? question.points : 0,
        feedback: questionData.explanation,
      });
    }

    return {
      submissionId: submission.id,
      score: (earnedPoints / totalPoints) * 100,
      passed: (earnedPoints / totalPoints) >= (quiz.settings.passingScore / 100),
      questionResults,
      completedAt: new Date(),
    };
  }

  private static gradeQuestion(question: Question, response: any): boolean {
    switch (question.type) {
      case 'multiple_choice':
        return question.correctAnswer === response;
      case 'true_false':
        return question.correctAnswer === response;
      case 'short_answer':
        return this.gradeShortAnswer(question.correctAnswer as string, response);
      case 'essay':
        return false; // Requires manual grading
      default:
        return false;
    }
  }
}
```

### 3. Student Learning Interface (Week 3-4)

#### 3.1 Course Player
```typescript
// Course player for students
const CoursePlayer: React.FC<{ courseId: string }> = ({ courseId }) => {
  const [course, setCourse] = useState<Course>();
  const [currentModule, setCurrentModule] = useState<Module>();
  const [currentContent, setCurrentContent] = useState<ContentItem>();
  const [progress, setProgress] = useState<StudentProgress>();

  const navigateToNext = () => {
    // Logic to navigate to next content item
  };

  const markAsComplete = () => {
    // Mark current content as completed
  };

  return (
    <div className="course-player">
      <CourseNavigation 
        course={course}
        currentModule={currentModule}
        progress={progress}
        onNavigate={setCurrentContent}
      />
      <ContentViewer 
        content={currentContent}
        onComplete={markAsComplete}
      />
      <ProgressBar progress={progress} />
    </div>
  );
};

// Content viewer for different content types
const ContentViewer: React.FC = ({ content, onComplete }) => {
  switch (content.type) {
    case 'text':
      return <TextContentViewer content={content} onComplete={onComplete} />;
    case 'video':
      return <VideoPlayer content={content} onComplete={onComplete} />;
    case 'quiz':
      return <QuizTaker content={content} onComplete={onComplete} />;
    default:
      return <div>Unsupported content type</div>;
  }
};
```

#### 3.2 Progress Tracking
```typescript
interface StudentProgress {
  studentId: string;
  courseId: string;
  moduleProgress: ModuleProgress[];
  overallProgress: number; // percentage
  timeSpent: number; // minutes
  lastAccessed: Date;
  completedAt?: Date;
  certificateIssued?: boolean;
}

interface ModuleProgress {
  moduleId: string;
  contentProgress: ContentProgress[];
  completed: boolean;
  timeSpent: number;
  score?: number; // For assessment modules
}

interface ContentProgress {
  contentId: string;
  status: 'not_started' | 'in_progress' | 'completed';
  timeSpent: number;
  attempts: number;
  score?: number;
  lastAccessed: Date;
  completedAt?: Date;
}

// Progress tracking service
class ProgressTrackingService {
  static async updateProgress(
    studentId: string,
    courseId: string,
    contentId: string,
    action: 'start' | 'complete' | 'time_update',
    data?: any
  ) {
    const progress = await this.getProgress(studentId, courseId);
    
    switch (action) {
      case 'start':
        await this.markContentStarted(progress, contentId);
        break;
      case 'complete':
        await this.markContentCompleted(progress, contentId, data);
        break;
      case 'time_update':
        await this.updateTimeSpent(progress, contentId, data.timeSpent);
        break;
    }

    await this.saveProgress(progress);
    await this.calculateOverallProgress(progress);
  }
}
```

### 4. File Upload and SCORM Support (Week 4-5)

#### 4.1 File Upload System
```typescript
// File upload service
class FileUploadService {
  static async uploadFile(file: File, type: 'image' | 'video' | 'document' | 'scorm'): Promise<UploadResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    // Validate file type and size
    this.validateFile(file, type);

    // Upload to cloud storage
    const response = await fetch('/api/files/upload', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();
    
    // Process file based on type
    switch (type) {
      case 'video':
        await this.processVideo(result.fileId);
        break;
      case 'scorm':
        await this.processSCORM(result.fileId);
        break;
    }

    return result;
  }

  private static validateFile(file: File, type: string) {
    const maxSizes = {
      image: 5 * 1024 * 1024, // 5MB
      video: 100 * 1024 * 1024, // 100MB
      document: 10 * 1024 * 1024, // 10MB
      scorm: 50 * 1024 * 1024, // 50MB
    };

    if (file.size > maxSizes[type]) {
      throw new Error(`File size exceeds limit for ${type}`);
    }
  }
}
```

#### 4.2 SCORM Integration
```typescript
// SCORM package handler
class SCORMHandler {
  static async processSCORMPackage(fileId: string): Promise<SCORMPackage> {
    // Extract SCORM package
    const extractedPath = await this.extractPackage(fileId);
    
    // Parse manifest
    const manifest = await this.parseManifest(extractedPath);
    
    // Validate SCORM compliance
    this.validateSCORM(manifest);
    
    // Create SCORM package record
    const scormPackage: SCORMPackage = {
      id: generateId(),
      fileId,
      title: manifest.title,
      version: manifest.version,
      launchUrl: manifest.launchUrl,
      manifest: manifest,
      extractedPath,
      createdAt: new Date(),
    };

    await this.saveSCORMPackage(scormPackage);
    return scormPackage;
  }

  static async launchSCORM(packageId: string, studentId: string): Promise<string> {
    const package = await this.getSCORMPackage(packageId);
    const session = await this.createSCORMSession(packageId, studentId);
    
    // Return launch URL with session parameters
    return `${package.launchUrl}?session=${session.id}`;
  }
}

// SCORM API implementation
class SCORMAPI {
  private session: SCORMSession;

  Initialize(parameter: string): string {
    // SCORM API Initialize
    return "true";
  }

  Terminate(parameter: string): string {
    // SCORM API Terminate
    this.saveSessionData();
    return "true";
  }

  GetValue(element: string): string {
    // SCORM API GetValue
    switch (element) {
      case "cmi.core.student_id":
        return this.session.studentId;
      case "cmi.core.student_name":
        return this.session.studentName;
      case "cmi.core.lesson_status":
        return this.session.lessonStatus;
      default:
        return "";
    }
  }

  SetValue(element: string, value: string): string {
    // SCORM API SetValue
    switch (element) {
      case "cmi.core.lesson_status":
        this.session.lessonStatus = value;
        break;
      case "cmi.core.score.raw":
        this.session.score = parseFloat(value);
        break;
    }
    return "true";
  }
}
```

### 5. Basic Analytics and Reporting (Week 5-6)

#### 5.1 Learning Analytics
```typescript
interface LearningAnalytics {
  courseId: string;
  period: 'day' | 'week' | 'month' | 'quarter';
  metrics: {
    enrollment: {
      total: number;
      new: number;
      active: number;
    };
    completion: {
      rate: number;
      average_time: number;
      drop_off_points: string[];
    };
    engagement: {
      average_session_time: number;
      pages_per_session: number;
      bounce_rate: number;
    };
    performance: {
      average_score: number;
      pass_rate: number;
      skill_mastery: SkillMastery[];
    };
  };
}

interface SkillMastery {
  skillId: string;
  skillName: string;
  masteryRate: number;
  averageAttempts: number;
  difficulty: number;
}

// Analytics service
class AnalyticsService {
  static async generateCourseAnalytics(courseId: string, period: string): Promise<LearningAnalytics> {
    const enrollments = await this.getEnrollmentData(courseId, period);
    const completions = await this.getCompletionData(courseId, period);
    const engagement = await this.getEngagementData(courseId, period);
    const performance = await this.getPerformanceData(courseId, period);

    return {
      courseId,
      period,
      metrics: {
        enrollment: this.calculateEnrollmentMetrics(enrollments),
        completion: this.calculateCompletionMetrics(completions),
        engagement: this.calculateEngagementMetrics(engagement),
        performance: this.calculatePerformanceMetrics(performance),
      },
    };
  }
}
```

#### 5.2 Reporting Dashboard
```typescript
// Analytics dashboard for instructors
const AnalyticsDashboard: React.FC = () => {
  const [analytics, setAnalytics] = useState<LearningAnalytics>();
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  return (
    <div className="analytics-dashboard">
      <DashboardHeader>
        <PeriodSelector value={selectedPeriod} onChange={setSelectedPeriod} />
        <ExportButton analytics={analytics} />
      </DashboardHeader>
      
      <div className="metrics-grid">
        <EnrollmentCard metrics={analytics?.metrics.enrollment} />
        <CompletionCard metrics={analytics?.metrics.completion} />
        <EngagementCard metrics={analytics?.metrics.engagement} />
        <PerformanceCard metrics={analytics?.metrics.performance} />
      </div>

      <div className="charts-section">
        <ProgressChart data={analytics?.progressData} />
        <SkillMasteryChart data={analytics?.metrics.performance.skill_mastery} />
        <EngagementTrendChart data={analytics?.engagementTrend} />
      </div>
    </div>
  );
};
```

### 6. User Management and Enrollment (Week 6-7)

#### 6.1 Student Enrollment System
```typescript
interface Enrollment {
  id: string;
  studentId: string;
  courseId: string;
  status: 'pending' | 'active' | 'completed' | 'dropped' | 'suspended';
  enrolledAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  progress: number;
  grade?: number;
  certificate?: string;
}

// Enrollment service
class EnrollmentService {
  static async enrollStudent(studentId: string, courseId: string): Promise<Enrollment> {
    const course = await CourseService.getCourse(courseId);
    
    // Check enrollment requirements
    await this.validateEnrollment(studentId, course);
    
    const enrollment: Enrollment = {
      id: generateId(),
      studentId,
      courseId,
      status: course.settings.enrollmentType === 'approval' ? 'pending' : 'active',
      enrolledAt: new Date(),
      progress: 0,
    };

    await this.saveEnrollment(enrollment);
    
    // Send notifications
    await NotificationService.sendEnrollmentConfirmation(enrollment);
    
    return enrollment;
  }

  static async bulkEnroll(studentIds: string[], courseId: string): Promise<Enrollment[]> {
    const enrollments = [];
    
    for (const studentId of studentIds) {
      try {
        const enrollment = await this.enrollStudent(studentId, courseId);
        enrollments.push(enrollment);
      } catch (error) {
        console.error(`Failed to enroll student ${studentId}:`, error);
      }
    }
    
    return enrollments;
  }
}
```

#### 6.2 Certificate Generation
```typescript
// Certificate service
class CertificateService {
  static async generateCertificate(enrollmentId: string): Promise<Certificate> {
    const enrollment = await EnrollmentService.getEnrollment(enrollmentId);
    const course = await CourseService.getCourse(enrollment.courseId);
    const student = await UserService.getUser(enrollment.studentId);

    if (enrollment.status !== 'completed') {
      throw new Error('Course not completed');
    }

    const certificate: Certificate = {
      id: generateId(),
      enrollmentId,
      studentId: enrollment.studentId,
      courseId: enrollment.courseId,
      studentName: `${student.firstName} ${student.lastName}`,
      courseName: course.title,
      completionDate: enrollment.completedAt!,
      grade: enrollment.grade,
      certificateUrl: '',
      issuedAt: new Date(),
    };

    // Generate PDF certificate
    certificate.certificateUrl = await this.generatePDF(certificate);
    
    await this.saveCertificate(certificate);
    
    // Send certificate to student
    await NotificationService.sendCertificate(certificate);
    
    return certificate;
  }
}
```

## Success Criteria

### Functional Requirements
- [ ] Complete course creation and management system
- [ ] Multi-media content authoring tools
- [ ] Assessment and quiz functionality with auto-grading
- [ ] Student learning interface with progress tracking
- [ ] SCORM package support
- [ ] Basic analytics and reporting
- [ ] User enrollment and certificate generation

### Technical Requirements
- [ ] File upload system with cloud storage
- [ ] Database optimizations for large datasets
- [ ] Responsive design for all interfaces
- [ ] API performance < 500ms for complex operations
- [ ] Support for 1000+ concurrent users

### Quality Gates
- [ ] 90%+ test coverage for new features
- [ ] Accessibility compliance (WCAG 2.1 AA)
- [ ] Cross-browser compatibility
- [ ] Mobile responsiveness
- [ ] Security audit passed

## Risk Mitigation

### Technical Risks
- **File Upload Performance**: Implement chunked uploads and progress tracking
- **SCORM Compatibility**: Extensive testing with various SCORM packages
- **Database Performance**: Query optimization and proper indexing
- **User Experience**: Regular usability testing and feedback incorporation

### Business Risks
- **Feature Complexity**: Prioritize MVP features and defer advanced functionality
- **User Adoption**: Comprehensive user testing and training materials
- **Data Migration**: Robust import/export tools for existing content

## Next Phase Preparation

Phase 2 establishes a fully functional basic LMS that provides the foundation for AI integration in Phase 3. The system will support traditional learning workflows while preparing the data collection infrastructure needed for knowledge tracing and adaptive learning features.
