# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
# created-by: conda 25.1.1
_libgcc_mutex=0.1=main
_openmp_mutex=5.1=1_gnu
absl-py=2.1.0=py39h06a4308_0
alembic=1.16.4=pypi_0
annotated-types=0.7.0=pypi_0
anyio=4.6.2=py39h06a4308_0
arch=7.2.0=pypi_0
argon2-cffi=21.3.0=pyhd3eb1b0_0
argon2-cffi-bindings=21.2.0=py39h5eee18b_1
asttokens=2.0.5=pyhd3eb1b0_0
astunparse=1.6.3=py_0
async-lru=2.0.4=py39h06a4308_0
attrs=24.3.0=py39h06a4308_0
babel=2.16.0=py39h06a4308_0
backcall=0.2.0=pyhd3eb1b0_0
beautifulsoup4=4.12.3=py39h06a4308_0
blas=1.0=mkl
bleach=6.2.0=py39h06a4308_0
blinker=1.9.0=pypi_0
bottleneck=1.4.2=py39ha9d4c09_0
brotli-python=1.0.9=py39h6a678d5_9
bzip2=1.0.8=h5eee18b_6
c-ares=1.19.1=h5eee18b_0
ca-certificates=2025.2.25=h06a4308_0
cachetools=5.5.2=pypi_0
certifi=2025.1.31=py39h06a4308_0
cffi=1.17.1=py39h1fdaa30_1
charset-normalizer=3.3.2=pyhd3eb1b0_0
click=8.1.8=pypi_0
cloudpickle=3.1.1=pypi_0
comm=0.2.1=py39h06a4308_0
contourpy=1.2.1=py39hdb19cb5_1
curl-cffi=0.11.4=pypi_0
cycler=0.11.0=pyhd3eb1b0_0
cyrus-sasl=2.1.28=h52b45da_1
cython=3.1.2=pypi_0
dash=3.1.1=pypi_0
databricks-sdk=0.59.0=pypi_0
debugpy=1.8.11=py39h6a678d5_0
decorator=5.1.1=pyhd3eb1b0_0
defusedxml=0.7.1=pyhd3eb1b0_0
docker=7.1.0=pypi_0
et-xmlfile=2.0.0=pypi_0
exceptiongroup=1.2.0=py39h06a4308_0
executing=0.8.3=pyhd3eb1b0_0
expat=2.6.4=h6a678d5_0
fastapi=0.116.1=pypi_0
findspark=2.0.1=pypi_0
flask=3.1.1=pypi_0
flatbuffers=24.3.25=h6a678d5_0
fontconfig=2.14.1=h55d465d_3
fonttools=4.55.3=py39h5eee18b_0
freetype=2.12.1=h4a9f257_0
frozendict=2.4.6=pypi_0
gast=0.5.3=pyhd3eb1b0_0
giflib=5.2.2=h5eee18b_0
gitdb=4.0.12=pypi_0
gitpython=3.1.44=pypi_0
google-auth=2.40.3=pypi_0
google-pasta=0.2.0=pyhd3eb1b0_0
graphene=3.4.3=pypi_0
graphql-core=3.2.6=pypi_0
graphql-relay=3.2.0=pypi_0
greenlet=3.2.3=pypi_0
grpcio=1.62.2=py39h6a678d5_0
gunicorn=23.0.0=pypi_0
h11=0.14.0=py39h06a4308_0
h5py=3.12.1=py39h5842655_1
hdf5=1.14.5=h2b7332f_2
holidays=0.76=pypi_0
httpcore=1.0.2=py39h06a4308_0
httpx=0.27.0=py39h06a4308_0
icu=73.1=h6a678d5_0
idna=3.7=py39h06a4308_0
importlib-metadata=8.5.0=py39h06a4308_0
importlib_metadata=8.5.0=hd3eb1b0_0
importlib_resources=6.4.0=py39h06a4308_0
iniconfig=2.1.0=pypi_0
intel-openmp=2023.1.0=hdb19cb5_46306
ipykernel=6.29.5=py39h06a4308_1
ipython=8.15.0=py39h06a4308_0
ipywidgets=8.1.5=py39h06a4308_0
itsdangerous=2.2.0=pypi_0
jedi=0.19.2=py39h06a4308_0
jinja2=3.1.6=py39h06a4308_0
joblib=1.4.2=py39h06a4308_0
jpeg=9e=h5eee18b_3
json5=0.9.25=py39h06a4308_0
jsonschema=4.23.0=py39h06a4308_0
jsonschema-specifications=2023.7.1=py39h06a4308_0
jupyter=1.1.1=py39h06a4308_0
jupyter-lsp=2.2.0=py39h06a4308_0
jupyter_client=8.6.3=py39h06a4308_0
jupyter_console=6.6.3=py39h06a4308_0
jupyter_core=5.7.2=py39h06a4308_0
jupyter_events=0.12.0=py39h06a4308_0
jupyter_server=2.15.0=py39h06a4308_0
jupyter_server_terminals=0.4.4=py39h06a4308_1
jupyterlab=4.3.4=py39h06a4308_0
jupyterlab_pygments=0.3.0=py39h06a4308_0
jupyterlab_server=2.27.3=py39h06a4308_0
jupyterlab_widgets=3.0.13=py39h06a4308_0
keras=3.9.0=pypi_0
keras-tuner=1.4.7=pypi_0
kiwisolver=1.4.4=py39h6a678d5_0
kneed=0.8.5=pypi_0
krb5=1.20.1=h143b758_1
kt-legacy=1.0.5=pypi_0
lcms2=2.16=hb9589c4_0
ld_impl_linux-64=2.40=h12ee557_0
lerc=4.0.0=h6a678d5_0
libabseil=20240116.2=cxx17_h6a678d5_0
libcups=2.4.2=h2d74bed_1
libcurl=8.12.1=hc9e6f67_0
libdeflate=1.22=h5eee18b_0
libedit=3.1.20230828=h5eee18b_0
libev=4.33=h7f8727e_1
libffi=3.4.4=h6a678d5_1
libgcc-ng=11.2.0=h1234567_1
libgfortran-ng=11.2.0=h00389a5_1
libgfortran5=11.2.0=h1234567_1
libglib=2.78.4=hdc74915_0
libgomp=11.2.0=h1234567_1
libgrpc=1.62.2=h2d74bed_0
libiconv=1.16=h5eee18b_3
libnghttp2=1.57.0=h2d74bed_0
libpng=1.6.39=h5eee18b_0
libpq=17.4=hdbd6064_0
libprotobuf=4.25.3=he621ea3_0
libsodium=1.0.18=h7b6447c_0
libssh2=1.11.1=h251f7ec_0
libstdcxx-ng=11.2.0=h1234567_1
libtiff=4.5.1=hffd6297_1
libuuid=1.41.5=h5eee18b_0
libwebp-base=1.3.2=h5eee18b_1
libxcb=1.15=h7f8727e_0
libxkbcommon=1.0.1=h097e994_2
libxml2=2.13.5=hfdd30dd_0
lz4-c=1.9.4=h6a678d5_1
mako=1.3.10=pypi_0
markdown=3.4.1=py39h06a4308_0
markdown-it-py=2.2.0=py39h06a4308_1
markupsafe=3.0.2=py39h5eee18b_0
matplotlib=3.9.2=py39h06a4308_1
matplotlib-base=3.9.2=py39hbfdbfaf_1
matplotlib-inline=0.1.6=py39h06a4308_0
mdurl=0.1.0=py39h06a4308_0
mistune=3.1.2=py39h06a4308_0
mkl=2023.1.0=h213fc3f_46344
mkl-service=2.4.0=py39h5eee18b_2
mkl_fft=1.3.11=py39h5eee18b_0
mkl_random=1.2.8=py39h1128e8f_0
ml_dtypes=0.4.0=py39h1128e8f_0
mlflow=3.1.1=pypi_0
mlflow-skinny=3.1.1=pypi_0
multitasking=0.0.11=pypi_0
mysql=8.4.0=h29a9f33_1
namex=0.0.7=py39h06a4308_0
narwhals=1.47.0=pypi_0
nbclient=0.10.2=py39h06a4308_0
nbconvert=7.16.6=py39h06a4308_0
nbconvert-core=7.16.6=py39h06a4308_0
nbconvert-pandoc=7.16.6=py39h06a4308_0
nbformat=5.10.4=py39h06a4308_0
ncurses=6.4=h6a678d5_0
nest-asyncio=1.6.0=py39h06a4308_0
notebook=7.3.2=py39h06a4308_0
notebook-shim=0.2.4=py39h06a4308_0
numexpr=2.10.1=py39h3c60e43_0
numpy=1.26.4=py39h5f9d8c6_0
numpy-base=1.26.4=py39hb5e798b_0
openjpeg=2.5.2=he7f1fd0_0
openldap=2.6.4=h42fbc30_0
openpyxl=3.1.5=pypi_0
openssl=3.0.16=h5eee18b_0
opentelemetry-api=1.35.0=pypi_0
opentelemetry-sdk=1.35.0=pypi_0
opentelemetry-semantic-conventions=0.56b0=pypi_0
opt_einsum=3.3.0=pyhd3eb1b0_1
optree=0.14.1=py39hdb19cb5_0
overrides=7.4.0=py39h06a4308_0
packaging=24.2=py39h06a4308_0
pandas=2.2.3=py39h6a678d5_0
pandoc=2.12=h06a4308_3
pandocfilters=1.5.0=pyhd3eb1b0_0
parso=0.8.4=py39h06a4308_0
patsy=1.0.1=pypi_0
pcre2=10.42=hebb0a14_1
peewee=3.18.2=pypi_0
pexpect=4.8.0=pyhd3eb1b0_3
pickleshare=0.7.5=pyhd3eb1b0_1003
pillow=11.1.0=py39hcea889d_0
pip=25.0.1=pypi_0
platformdirs=3.10.0=py39h06a4308_0
plotly=6.2.0=pypi_0
pluggy=1.6.0=pypi_0
pmdarima=2.0.4=pypi_0
prometheus_client=0.21.0=py39h06a4308_0
prompt-toolkit=3.0.43=py39h06a4308_0
prompt_toolkit=3.0.43=hd3eb1b0_0
protobuf=4.25.3=py39he36ed58_1
psutil=5.9.0=py39h5eee18b_1
ptyprocess=0.7.0=pyhd3eb1b0_2
pure_eval=0.2.2=pyhd3eb1b0_0
py4j=********=pypi_0
pyarrow=20.0.0=pypi_0
pyasn1=0.6.1=pypi_0
pyasn1-modules=0.4.2=pypi_0
pybind11-abi=4=hd3eb1b0_1
pycparser=2.21=pyhd3eb1b0_0
pydantic=2.11.7=pypi_0
pydantic-core=2.33.2=pypi_0
pygments=2.15.1=py39h06a4308_1
pyparsing=3.2.0=py39h06a4308_0
pyqt=6.7.1=py39h6a678d5_0
pyqt6-sip=13.9.1=py39h5eee18b_0
pysocks=1.7.1=py39h06a4308_0
pyspark=4.0.0=pypi_0
pytest=8.4.1=pypi_0
python=3.9.21=he870216_1
python-dateutil=2.9.0post0=py39h06a4308_2
python-fastjsonschema=2.20.0=py39h06a4308_0
python-flatbuffers=24.3.25=py39h06a4308_0
python-json-logger=3.2.1=py39h06a4308_0
python-tzdata=2023.3=pyhd3eb1b0_0
pytz=2024.1=py39h06a4308_0
pyyaml=6.0.2=py39h5eee18b_0
pyzmq=26.2.0=py39h6a678d5_0
qtbase=6.7.2=hdaa5aa8_1
qtconsole=5.6.1=py39h06a4308_0
qtdeclarative=6.7.2=h6a678d5_0
qtpy=2.4.1=py39h06a4308_0
qtsvg=6.7.2=he621ea3_0
qttools=6.7.2=h80c7b02_0
qtwebchannel=6.7.2=h6a678d5_0
qtwebsockets=6.7.2=h6a678d5_0
re2=2022.04.01=h295c915_0
readline=8.2=h5eee18b_0
referencing=0.30.2=py39h06a4308_0
requests=2.32.3=py39h06a4308_1
retrying=1.4.0=pypi_0
rfc3339-validator=0.1.4=py39h06a4308_0
rfc3986-validator=0.1.1=py39h06a4308_0
rich=13.9.4=py39h06a4308_0
rpds-py=0.22.3=py39h4aa5aa6_0
rsa=4.9.1=pypi_0
scikit-learn=1.6.1=py39h6a678d5_0
scipy=1.13.1=py39h5f9d8c6_1
seaborn=0.13.2=py39h06a4308_2
send2trash=1.8.2=py39h06a4308_1
setuptools=75.8.0=py39h06a4308_0
sip=6.10.0=py39h6a678d5_0
six=1.16.0=pyhd3eb1b0_1
smmap=5.0.2=pypi_0
snappy=1.2.1=h6a678d5_0
sniffio=1.3.0=py39h06a4308_0
soupsieve=2.5=py39h06a4308_0
sqlalchemy=2.0.41=pypi_0
sqlite=3.45.3=h5eee18b_0
sqlparse=0.5.3=pypi_0
stack_data=0.2.0=pyhd3eb1b0_0
starlette=0.47.1=pypi_0
statsmodels=0.14.4=pypi_0
tbb=2021.8.0=hdb19cb5_0
tensorboard=2.17.0=py39h06a4308_0
tensorboard-data-server=0.7.0=py39h52d8a92_1
tensorflow=2.17.0=cpu_py39hbca4264_0
tensorflow-base=2.17.0=cpu_py39h6deec89_0
tensorflow-cpu=2.17.0=cpu_py39hbca4264_0
tensorflow-estimator=2.17.0=cpu_py39hfedf350_0
termcolor=2.1.0=py39h06a4308_0
terminado=0.17.1=py39h06a4308_0
threadpoolctl=3.5.0=py39h2f386ee_0
tinycss2=1.4.0=py39h06a4308_0
tk=8.6.14=h39e8969_0
tomli=2.0.1=py39h06a4308_0
tornado=6.4.2=py39h5eee18b_0
traitlets=5.14.3=py39h06a4308_0
typing-extensions=4.12.2=py39h06a4308_0
typing-inspection=0.4.1=pypi_0
typing_extensions=4.12.2=py39h06a4308_0
tzdata=2025a=h04d1e81_0
unicodedata2=15.1.0=py39h5eee18b_1
urllib3=2.3.0=py39h06a4308_0
uvicorn=0.35.0=pypi_0
wcwidth=0.2.5=pyhd3eb1b0_0
webencodings=0.5.1=py39h06a4308_1
websocket-client=1.8.0=py39h06a4308_0
websockets=15.0.1=pypi_0
werkzeug=3.1.3=py39h06a4308_0
wheel=0.45.1=py39h06a4308_0
widgetsnbextension=4.0.13=py39h06a4308_0
wrapt=1.17.0=py39h5eee18b_0
xcb-util-cursor=0.1.4=h5eee18b_0
xz=5.6.4=h5eee18b_1
yaml=0.2.5=h7b6447c_0
yfinance=0.2.65=pypi_0
zeromq=4.3.5=h6a678d5_0
zipp=3.21.0=py39h06a4308_0
zlib=1.2.13=h5eee18b_1
zstd=1.5.6=hc292b87_0
