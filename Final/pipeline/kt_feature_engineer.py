#!/usr/bin/env python3
"""
Knowledge Tracing Feature Engineering
====================================

This module implements feature engineering specifically for knowledge tracing models.
It creates features for BKT, PFA, and DKT models including skill sequences,
temporal features, and student history.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import logging

logger = logging.getLogger(__name__)

class KTFeatureEngineer:
    """Feature engineering for knowledge tracing models"""
    
    def __init__(self):
        """Initialize the feature engineer"""
        self.skill_encoders = {}
        self.user_encoders = {}
        self.problem_encoders = {}
        self.is_fitted = False
    
    def create_features(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Create comprehensive features for knowledge tracing
        
        Args:
            df: Knowledge tracing dataset
            
        Returns:
            Dictionary containing different feature sets for different models
        """
        logger.info("Creating features for knowledge tracing models...")
        
        # Create basic features
        df_features = self._create_basic_features(df)
        
        # Create temporal features
        df_features = self._create_temporal_features(df_features)
        
        # Create skill-based features
        df_features = self._create_skill_features(df_features)
        
        # Create student-based features
        df_features = self._create_student_features(df_features)
        
        # Create problem-based features
        df_features = self._create_problem_features(df_features)
        
        # Prepare features for different models
        features = {
            'bkt': self._prepare_bkt_features(df_features),
            'pfa': self._prepare_pfa_features(df_features),
            'dkt': self._prepare_dkt_features(df_features),
            'raw': df_features
        }
        
        logger.info("Feature engineering completed")
        return features
    
    def _create_basic_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create basic features from the dataset"""
        df_features = df.copy()
        
        # Encode categorical variables
        if not self.is_fitted:
            # Create encoders for skills, users, and problems
            unique_skills = df['skill_name'].unique()
            unique_users = df['user_id'].unique()
            unique_problems = df['problem_id'].unique()
            
            self.skill_encoders = {skill: idx for idx, skill in enumerate(unique_skills)}
            self.user_encoders = {user: idx for idx, user in enumerate(unique_users)}
            self.problem_encoders = {problem: idx for idx, problem in enumerate(unique_problems)}
            
            self.is_fitted = True
        
        # Apply encodings
        df_features['skill_id'] = df_features['skill_name'].map(self.skill_encoders)
        df_features['user_id_encoded'] = df_features['user_id'].map(self.user_encoders)
        df_features['problem_id_encoded'] = df_features['problem_id'].map(self.problem_encoders)
        
        # Fill missing encodings with -1
        df_features['skill_id'] = df_features['skill_id'].fillna(-1).astype(int)
        df_features['user_id_encoded'] = df_features['user_id_encoded'].fillna(-1).astype(int)
        df_features['problem_id_encoded'] = df_features['problem_id_encoded'].fillna(-1).astype(int)
        
        return df_features
    
    def _create_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create temporal features"""
        df_features = df.copy()
        
        # Sort by user and order
        df_features = df_features.sort_values(['user_id', 'order_id'])
        
        # Create sequence position within user
        df_features['sequence_position'] = df_features.groupby('user_id').cumcount() + 1
        
        # Create total sequence length for each user
        user_lengths = df_features.groupby('user_id').size()
        df_features['total_sequence_length'] = df_features['user_id'].map(user_lengths)
        
        # Create relative position (position / total_length)
        df_features['relative_position'] = df_features['sequence_position'] / df_features['total_sequence_length']
        
        return df_features
    
    def _create_skill_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create skill-based features"""
        df_features = df.copy()
        
        # Sort by user and order
        df_features = df_features.sort_values(['user_id', 'order_id'])
        
        # For each user-skill combination, track attempts and successes
        df_features['skill_attempts'] = df_features.groupby(['user_id', 'skill_name']).cumcount()
        df_features['skill_successes'] = df_features.groupby(['user_id', 'skill_name'])['correct'].cumsum() - df_features['correct']
        df_features['skill_failures'] = df_features['skill_attempts'] - df_features['skill_successes']
        
        # Calculate skill success rate (for PFA)
        df_features['skill_success_rate'] = np.where(
            df_features['skill_attempts'] > 0,
            df_features['skill_successes'] / df_features['skill_attempts'],
            0
        )
        
        # Create lag features for skill performance
        df_features['prev_skill_correct'] = df_features.groupby(['user_id', 'skill_name'])['correct'].shift(1)
        df_features['prev_skill_correct'] = df_features['prev_skill_correct'].fillna(-1)
        
        return df_features
    
    def _create_student_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create student-based features"""
        df_features = df.copy()
        
        # Sort by user and order
        df_features = df_features.sort_values(['user_id', 'order_id'])
        
        # Overall student performance
        df_features['student_total_attempts'] = df_features.groupby('user_id').cumcount()
        df_features['student_total_correct'] = df_features.groupby('user_id')['correct'].cumsum() - df_features['correct']
        df_features['student_accuracy'] = np.where(
            df_features['student_total_attempts'] > 0,
            df_features['student_total_correct'] / df_features['student_total_attempts'],
            0
        )
        
        # Recent performance (last 5 attempts)
        df_features['recent_correct'] = df_features.groupby('user_id')['correct'].rolling(
            window=5, min_periods=1
        ).mean().reset_index(0, drop=True).shift(1).fillna(0)
        
        return df_features
    
    def _create_problem_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create problem-based features"""
        df_features = df.copy()
        
        # Calculate problem difficulty (global accuracy for each problem)
        problem_stats = df_features.groupby('problem_id')['correct'].agg(['mean', 'count']).reset_index()
        problem_stats.columns = ['problem_id', 'problem_difficulty', 'problem_frequency']
        
        # Merge back to main dataframe
        df_features = df_features.merge(problem_stats, on='problem_id', how='left')
        
        # Fill missing values
        df_features['problem_difficulty'] = df_features['problem_difficulty'].fillna(0.5)
        df_features['problem_frequency'] = df_features['problem_frequency'].fillna(1)
        
        return df_features
    
    def _prepare_bkt_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features specifically for BKT model"""
        # BKT primarily uses the raw sequence data
        bkt_features = df[['user_id', 'skill_name', 'correct', 'order_id']].copy()
        return bkt_features
    
    def _prepare_pfa_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features specifically for PFA model"""
        # PFA uses success/failure counts
        pfa_features = df[[
            'user_id', 'skill_name', 'problem_id', 'correct',
            'skill_attempts', 'skill_successes', 'skill_failures',
            'problem_difficulty', 'order_id'
        ]].copy()
        return pfa_features
    
    def _prepare_dkt_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features specifically for DKT model"""
        # DKT uses encoded sequences
        dkt_features = df[[
            'user_id', 'skill_id', 'correct', 'order_id',
            'sequence_position', 'total_sequence_length'
        ]].copy()
        return dkt_features
    
    def create_sequences_for_dkt(self, df: pd.DataFrame, max_seq_len: int = 200) -> List[Dict]:
        """
        Create sequences for DKT model training
        
        Args:
            df: Feature dataframe
            max_seq_len: Maximum sequence length
            
        Returns:
            List of sequences for DKT training
        """
        sequences = []
        
        for user_id, user_data in df.groupby('user_id'):
            user_data = user_data.sort_values('order_id')
            
            skills = user_data['skill_id'].values
            corrects = user_data['correct'].values
            
            # Create input sequences (skill + correct from previous step)
            # For DKT, we combine skill_id and correctness
            inputs = []
            targets = []
            
            for i in range(len(skills)):
                if i == 0:
                    # First interaction: just skill
                    input_val = skills[i]
                else:
                    # Subsequent interactions: skill + previous correctness
                    input_val = skills[i] + (len(self.skill_encoders) * corrects[i-1])
                
                inputs.append(input_val)
                targets.append(corrects[i])
            
            # Split into chunks if sequence is too long
            if len(inputs) > max_seq_len:
                for start in range(0, len(inputs), max_seq_len):
                    end = min(start + max_seq_len, len(inputs))
                    sequences.append({
                        'user_id': user_id,
                        'inputs': inputs[start:end],
                        'targets': targets[start:end],
                        'length': end - start
                    })
            else:
                sequences.append({
                    'user_id': user_id,
                    'inputs': inputs,
                    'targets': targets,
                    'length': len(inputs)
                })
        
        return sequences
    
    def get_vocab_sizes(self) -> Dict[str, int]:
        """Get vocabulary sizes for different encoders"""
        return {
            'n_skills': len(self.skill_encoders),
            'n_users': len(self.user_encoders),
            'n_problems': len(self.problem_encoders)
        }
