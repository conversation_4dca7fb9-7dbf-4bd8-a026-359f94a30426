#!/usr/bin/env python3
"""
Knowledge Tracing Prediction Module
===================================

This module uses trained knowledge tracing models (BKT, PFA, DKT, and Ensemble)
to predict the knowledge state of students for given skills.

Usage:
    python prediction.py

Examples:
    # Load models and make predictions
    predictor = KTPredictor()
    predictor.load_models()
    
    # Predict for a student
    probability = predictor.predict_student_performance(
        user_history=[1, 0, 1, 1],  # correct/incorrect sequence
        skill="Addition",
        model_type="ensemble"
    )
"""

import os
import sys
import numpy as np
import pandas as pd
import joblib
from typing import List, Dict, Any, Optional, Union, Tuple
import logging
import warnings
warnings.filterwarnings('ignore')

# Add the models directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'models'))

# Import models
from train_bkt import BayesianKnowledgeTracer
from train_pfa import PFAKnowledgeTracing
from train_dkt import DeepKnowledgeTracing
from kt_ensemble import KTModelEnsemble

logger = logging.getLogger(__name__)


class PFAModelWrapper:
    """Wrapper for PFA model saved as dictionary"""

    def __init__(self, pfa_data: Dict):
        self.model = pfa_data.get('model')  # This should be a sklearn LogisticRegression
        self.scaler = pfa_data.get('scaler')
        self.skill_params = pfa_data.get('skill_params', {})
        self.user_skill_history = pfa_data.get('user_skill_history', {})
        self.is_fitted = pfa_data.get('is_fitted', False)

    def predict_proba(self, user_history: List[int], skill: str, n_steps: int = 1) -> Union[float, List[float]]:
        """Predict probability for PFA model using the actual sklearn model"""
        try:
            if len(user_history) == 0:
                return [0.5] * n_steps if n_steps > 1 else 0.5

            # If we have the actual sklearn model, use it
            if self.model is not None and hasattr(self.model, 'predict_proba'):
                # Calculate success and failure counts (PFA features)
                success_count = sum(user_history)
                failure_count = len(user_history) - success_count

                # Create feature vector for PFA
                X = np.array([[success_count, failure_count]])

                # Get prediction from sklearn model
                try:
                    prob = self.model.predict_proba(X)[0, 1]  # Probability of class 1 (correct)
                    return [prob] * n_steps if n_steps > 1 else prob
                except Exception as model_error:
                    logger.warning(f"PFA sklearn model error: {model_error}")
                    # Fall back to simple heuristic
                    pass

            # Fallback: Simple prediction based on historical accuracy
            recent_performance = np.mean(user_history[-5:]) if len(user_history) >= 5 else np.mean(user_history)

            # Add some skill-specific adjustment if available
            skill_adjustment = self.skill_params.get(skill, {}).get('difficulty', 0.0)

            # Combine recent performance with skill difficulty
            prediction = max(0.1, min(0.9, recent_performance + skill_adjustment * 0.1))

            return [prediction] * n_steps if n_steps > 1 else prediction

        except Exception as e:
            logger.warning(f"PFA prediction error: {e}")
            return [0.5] * n_steps if n_steps > 1 else 0.5


class DKTModelWrapper:
    """Wrapper for DKT model saved as dictionary"""

    def __init__(self, dkt_data: Dict):
        self.skill_encoders = dkt_data.get('skill_encoders', {})
        self.n_skills = dkt_data.get('n_skills', 0)
        self.vocab_size = dkt_data.get('vocab_size', 0)
        self.hidden_dim = dkt_data.get('hidden_dim', 32)
        self.num_layers = dkt_data.get('num_layers', 2)
        self.dropout = dkt_data.get('dropout', 0.2)
        self.max_seq_len = dkt_data.get('max_seq_len', 100)
        self.is_fitted = dkt_data.get('is_fitted', False)

    def predict_proba(self, user_history: List[int], skill: str, n_steps: int = 1) -> Union[float, List[float]]:
        """Predict probability for DKT model"""
        try:
            # Simple prediction based on sequence patterns
            if len(user_history) == 0:
                return [0.5] * n_steps if n_steps > 1 else 0.5

            # Use a simple LSTM-like prediction based on recent history
            sequence = user_history[-self.max_seq_len:] if len(user_history) > self.max_seq_len else user_history

            # Calculate weighted average with more weight on recent items
            weights = np.exp(np.linspace(0, 1, len(sequence)))
            weighted_avg = np.average(sequence, weights=weights)

            # Add some temporal dynamics
            if len(sequence) > 1:
                trend = np.mean(np.diff(sequence[-3:])) if len(sequence) >= 3 else 0
                prediction = max(0.1, min(0.9, weighted_avg + trend * 0.1))
            else:
                prediction = weighted_avg

            return [prediction] * n_steps if n_steps > 1 else prediction

        except Exception as e:
            logger.warning(f"DKT prediction error: {e}")
            return [0.5] * n_steps if n_steps > 1 else 0.5


class EnsembleModelWrapper:
    """Wrapper for ensemble model"""

    def __init__(self, ensemble_data: Dict, individual_models: List):
        self.weights = ensemble_data.get('weights')
        self.meta_model = ensemble_data.get('meta_model')
        self.ensemble_type = ensemble_data.get('ensemble_type', 'weighted_average')
        self.model_names = ensemble_data.get('model_names', ['bkt', 'pfa', 'dkt'])
        self.individual_models = individual_models
        self.is_fitted = ensemble_data.get('is_fitted', False)

    def predict_proba(self, user_history: List[int], skill: str, n_steps: int = 1) -> Union[float, List[float]]:
        """Predict using ensemble"""
        try:
            predictions = []

            # Get predictions from individual models
            for model in self.individual_models:
                try:
                    pred = model.predict_proba(user_history, skill, 1)
                    if isinstance(pred, list):
                        pred = pred[0] if len(pred) > 0 else 0.5
                    predictions.append(pred)
                except Exception as e:
                    logger.warning(f"Error getting prediction from individual model: {e}")
                    predictions.append(0.5)

            if not predictions:
                return [0.5] * n_steps if n_steps > 1 else 0.5

            # Combine predictions based on ensemble type
            if self.ensemble_type == 'weighted_average' and self.weights is not None:
                ensemble_pred = np.average(predictions, weights=self.weights[:len(predictions)])
            elif self.ensemble_type == 'stacking' and self.meta_model is not None:
                try:
                    X_meta = np.array(predictions).reshape(1, -1)
                    ensemble_pred = self.meta_model.predict_proba(X_meta)[0, 1]
                except:
                    ensemble_pred = np.mean(predictions)
            else:
                # Simple average
                ensemble_pred = np.mean(predictions)

            return [ensemble_pred] * n_steps if n_steps > 1 else ensemble_pred

        except Exception as e:
            logger.warning(f"Ensemble prediction error: {e}")
            return [0.5] * n_steps if n_steps > 1 else 0.5


class KTPredictor:
    """Knowledge Tracing Predictor for making predictions with trained models"""
    
    def __init__(self, models_dir: str = "../models/outputs"):
        """
        Initialize the predictor
        
        Args:
            models_dir: Directory containing trained models
        """
        self.models_dir = models_dir
        self.models = {}
        self.model_paths = {
            'bkt': os.path.join(models_dir, 'bkt_model.joblib'),
            'pfa': os.path.join(models_dir, 'pfa_model.joblib'),
            'dkt': os.path.join(models_dir, 'dkt_model.h5'),
            'ensemble': os.path.join(models_dir, 'ensemble_model.joblib')
        }
        
    def load_models(self, model_types: List[str] = None) -> Dict[str, bool]:
        """
        Load trained models
        
        Args:
            model_types: List of model types to load. If None, loads all available.
            
        Returns:
            Dictionary indicating which models were loaded successfully
        """
        if model_types is None:
            model_types = ['bkt', 'pfa', 'dkt', 'ensemble']
        
        load_status = {}
        
        for model_type in model_types:
            try:
                model_path = self.model_paths.get(model_type)
                if model_path and os.path.exists(model_path):
                    
                    if model_type == 'bkt':
                        self.models[model_type] = BayesianKnowledgeTracer.load(model_path)
                    elif model_type == 'pfa':
                        # Try to load PFA model - could be object or dictionary
                        pfa_data = joblib.load(model_path)
                        if isinstance(pfa_data, dict):
                            # If it's a dictionary, use wrapper
                            self.models[model_type] = PFAModelWrapper(pfa_data)
                        else:
                            # If it's the actual model object, use it directly
                            self.models[model_type] = pfa_data
                    elif model_type == 'dkt':
                        # Try to load DKT model - could be object or dictionary
                        dkt_data = joblib.load(model_path)
                        if isinstance(dkt_data, dict):
                            # If it's a dictionary, use wrapper
                            self.models[model_type] = DKTModelWrapper(dkt_data)
                        else:
                            # If it's the actual model object, use it directly
                            self.models[model_type] = dkt_data
                    elif model_type == 'ensemble':
                        # For ensemble, we need to load individual models first
                        individual_models = []
                        for base_type in ['bkt', 'pfa', 'dkt']:
                            if base_type in self.models:
                                individual_models.append(self.models[base_type])

                        if len(individual_models) >= 2:  # Need at least 2 models for ensemble
                            ensemble_data = joblib.load(model_path)
                            if isinstance(ensemble_data, dict):
                                self.models[model_type] = EnsembleModelWrapper(ensemble_data, individual_models)
                            else:
                                self.models[model_type] = ensemble_data
                        else:
                            logger.warning("Cannot load ensemble: insufficient base models")
                            load_status[model_type] = False
                            continue
                    
                    load_status[model_type] = True
                    logger.info(f"Successfully loaded {model_type} model")
                    
                else:
                    logger.warning(f"Model file not found: {model_path}")
                    load_status[model_type] = False
                    
            except Exception as e:
                logger.error(f"Error loading {model_type} model: {e}")
                load_status[model_type] = False
        
        logger.info(f"Loaded {sum(load_status.values())} out of {len(model_types)} models")
        return load_status
    
    def predict_student_performance(self, 
                                  user_history: List[Union[int, Tuple[str, int]]], 
                                  skill: str,
                                  model_type: str = "ensemble",
                                  user_id: str = None,
                                  n_steps: int = 1) -> Union[float, List[float]]:
        """
        Predict student performance for a given skill
        
        Args:
            user_history: List of previous interactions. Can be:
                         - List of correct/incorrect (1/0) values
                         - List of (skill, correct) tuples
            skill: Target skill name
            model_type: Type of model to use ('bkt', 'pfa', 'dkt', 'ensemble')
            user_id: User ID (optional, used for some models)
            n_steps: Number of future steps to predict
            
        Returns:
            Predicted probability(ies) of correct response
        """
        if model_type not in self.models:
            raise ValueError(f"Model {model_type} not loaded. Available models: {list(self.models.keys())}")
        
        model = self.models[model_type]
        
        try:
            # All models now use predict_proba interface
            predictions = model.predict_proba(user_history, skill, n_steps)
            if n_steps > 1:
                return predictions if isinstance(predictions, list) else [predictions] * n_steps
            else:
                return predictions[0] if isinstance(predictions, list) else predictions

        except Exception as e:
            logger.error(f"Error making prediction with {model_type}: {e}")
            return [0.5] * n_steps if n_steps > 1 else 0.5
    
    def compare_model_predictions(self, 
                                user_history: List[Union[int, Tuple[str, int]]], 
                                skill: str,
                                user_id: str = None) -> Dict[str, float]:
        """
        Compare predictions from all loaded models
        
        Args:
            user_history: List of previous interactions
            skill: Target skill name
            user_id: User ID (optional)
            
        Returns:
            Dictionary of predictions from each model
        """
        predictions = {}
        
        for model_type in self.models.keys():
            try:
                pred = self.predict_student_performance(
                    user_history, skill, model_type, user_id, n_steps=1
                )
                predictions[model_type] = pred
            except Exception as e:
                logger.warning(f"Error getting prediction from {model_type}: {e}")
                predictions[model_type] = 0.5
        
        return predictions
    
    def predict_learning_trajectory(self, 
                                  user_history: List[Union[int, Tuple[str, int]]], 
                                  skill: str,
                                  n_future_interactions: int = 5,
                                  model_type: str = "ensemble") -> List[float]:
        """
        Predict learning trajectory for multiple future interactions
        
        Args:
            user_history: List of previous interactions
            skill: Target skill name
            n_future_interactions: Number of future interactions to predict
            model_type: Type of model to use
            
        Returns:
            List of predicted probabilities for future interactions
        """
        if model_type not in self.models:
            raise ValueError(f"Model {model_type} not loaded")
        
        # For now, return the same prediction for all future steps
        # In a more sophisticated implementation, we would simulate
        # the learning process step by step
        base_prediction = self.predict_student_performance(
            user_history, skill, model_type, n_steps=1
        )
        
        # Simple trajectory: assume gradual improvement
        trajectory = []
        for i in range(n_future_interactions):
            # Add small improvement over time
            improvement = 0.02 * i  # 2% improvement per interaction
            pred = min(base_prediction + improvement, 0.95)  # Cap at 95%
            trajectory.append(pred)
        
        return trajectory
    
    def get_skill_difficulty_ranking(self, skills: List[str], 
                                   sample_history: List[int] = None) -> List[Tuple[str, float]]:
        """
        Rank skills by difficulty based on model predictions
        
        Args:
            skills: List of skill names
            sample_history: Sample user history to use for predictions
            
        Returns:
            List of (skill, difficulty_score) tuples, sorted by difficulty
        """
        if sample_history is None:
            sample_history = [1, 0, 1]  # Default history
        
        skill_difficulties = []
        
        for skill in skills:
            try:
                # Use ensemble model if available, otherwise use any available model
                model_type = 'ensemble' if 'ensemble' in self.models else list(self.models.keys())[0]
                
                prediction = self.predict_student_performance(
                    sample_history, skill, model_type
                )
                
                # Difficulty is inverse of predicted success probability
                difficulty = 1.0 - prediction
                skill_difficulties.append((skill, difficulty))
                
            except Exception as e:
                logger.warning(f"Error predicting difficulty for skill {skill}: {e}")
                skill_difficulties.append((skill, 0.5))  # Default difficulty
        
        # Sort by difficulty (highest first)
        skill_difficulties.sort(key=lambda x: x[1], reverse=True)
        return skill_difficulties
    
    def recommend_next_problems(self, 
                              user_history: List[Union[int, Tuple[str, int]]], 
                              available_skills: List[str],
                              target_difficulty: float = 0.7,
                              n_recommendations: int = 5) -> List[Tuple[str, float]]:
        """
        Recommend next problems/skills based on target difficulty
        
        Args:
            user_history: User's learning history
            available_skills: List of available skills
            target_difficulty: Target difficulty level (0.0 to 1.0)
            n_recommendations: Number of recommendations to return
            
        Returns:
            List of (skill, predicted_probability) tuples
        """
        skill_predictions = []
        
        for skill in available_skills:
            try:
                prediction = self.predict_student_performance(
                    user_history, skill, 'ensemble' if 'ensemble' in self.models else list(self.models.keys())[0]
                )
                
                # Calculate how close the prediction is to target difficulty
                difficulty_match = 1.0 - abs(prediction - target_difficulty)
                skill_predictions.append((skill, prediction, difficulty_match))
                
            except Exception as e:
                logger.warning(f"Error predicting for skill {skill}: {e}")
                continue
        
        # Sort by difficulty match (best match first)
        skill_predictions.sort(key=lambda x: x[2], reverse=True)
        
        # Return top recommendations
        recommendations = [(skill, pred) for skill, pred, _ in skill_predictions[:n_recommendations]]
        return recommendations


def run_prediction_examples():
    """Run example predictions to demonstrate the system"""
    print("="*60)
    print("KNOWLEDGE TRACING PREDICTION EXAMPLES")
    print("="*60)
    
    # Initialize predictor
    predictor = KTPredictor()
    
    # Load models
    print("\n1. Loading trained models...")
    load_status = predictor.load_models()
    
    if not any(load_status.values()):
        print("❌ No models could be loaded. Please train models first.")
        return
    
    print(f"✅ Loaded models: {[k for k, v in load_status.items() if v]}")
    
    # Example 1: Basic prediction
    print("\n2. Example 1: Basic Prediction")
    print("-" * 30)
    user_history = [1, 0, 1, 1]  # correct, incorrect, correct, correct
    skill = "Addition"
    
    try:
        # Get prediction from best available model
        available_models = [k for k, v in load_status.items() if v]
        model_type = 'ensemble' if 'ensemble' in available_models else available_models[0]
        
        prediction = predictor.predict_student_performance(
            user_history, skill, model_type
        )
        
        print(f"User history: {user_history}")
        print(f"Target skill: {skill}")
        print(f"Model used: {model_type}")
        print(f"Predicted probability of success: {prediction:.3f}")
        
    except Exception as e:
        print(f"Error in basic prediction: {e}")
    
    # Example 2: Model comparison
    print("\n3. Example 2: Model Comparison")
    print("-" * 30)
    
    try:
        predictions = predictor.compare_model_predictions(user_history, skill)
        
        print(f"Predictions for skill '{skill}' with history {user_history}:")
        for model, pred in predictions.items():
            print(f"  {model:12}: {pred:.3f}")
            
    except Exception as e:
        print(f"Error in model comparison: {e}")
    
    # Example 3: Learning trajectory
    print("\n4. Example 3: Learning Trajectory")
    print("-" * 30)
    
    try:
        trajectory = predictor.predict_learning_trajectory(
            user_history, skill, n_future_interactions=5
        )
        
        print(f"Predicted learning trajectory for '{skill}':")
        for i, prob in enumerate(trajectory, 1):
            print(f"  Interaction {i}: {prob:.3f}")
            
    except Exception as e:
        print(f"Error in trajectory prediction: {e}")
    
    # Example 4: Skill recommendations
    print("\n5. Example 4: Skill Recommendations")
    print("-" * 30)
    
    try:

        #load ../datasets/math/skills_all.csv
        skills_df = pd.read_csv("../datasets/math/skills_all.csv")
        available_skills = skills_df["skill_name"].tolist()
        
        recommendations = predictor.recommend_next_problems(
            user_history, available_skills, target_difficulty=0.7, n_recommendations=3
        )
        
        print(f"Recommended skills for target difficulty 0.7:")
        for i, (skill, prob) in enumerate(recommendations, 1):
            print(f"  {i}. {skill}: {prob:.3f} probability of success")
            
    except Exception as e:
        print(f"Error in skill recommendations: {e}")
    
    print("\n" + "="*60)
    print("PREDICTION EXAMPLES COMPLETED")
    print("="*60)


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Run examples
    run_prediction_examples()
