#!/usr/bin/env python3
"""
Knowledge Tracing Pipeline Test Runner
=====================================

Simple test runner to verify the knowledge tracing pipeline works correctly.
"""

import os
import sys
import logging

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """Test that all pipeline components can be imported"""
    print("Testing imports...")
    
    try:
        # Test main pipeline import
        from kt_training_pipeline import KnowledgeTracingPipeline
        print("✓ KnowledgeTracingPipeline imported successfully")
        
        # Test feature engineer import
        from kt_feature_engineer import KTFeatureEngineer
        print("✓ KTFeatureEngineer imported successfully")
        
        # Test model trainer import
        from kt_model_trainer import KTModelTrainer
        print("✓ KTModelTrainer imported successfully")
        
        # Test ensemble import
        from kt_ensemble import KTModelEnsemble
        print("✓ KTModelEnsemble imported successfully")
        
        # Test evaluation import
        from kt_evaluation import KTEvaluator
        print("✓ KTEvaluator imported successfully")
        
        # Test prediction import
        from prediction import KTPredictor
        print("✓ KTPredictor imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of pipeline components"""
    print("\nTesting basic functionality...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # Create sample data
        sample_data = pd.DataFrame({
            'user_id': ['user1', 'user1', 'user2', 'user2'] * 5,
            'problem_id': ['prob1', 'prob2', 'prob3', 'prob4'] * 5,
            'skill_name': ['Addition', 'Subtraction', 'Multiplication', 'Division'] * 5,
            'correct': [1, 0, 1, 1] * 5,
            'order_id': list(range(1, 21))
        })
        
        # Test pipeline initialization
        from kt_training_pipeline import KnowledgeTracingPipeline
        pipeline = KnowledgeTracingPipeline()
        print("✓ Pipeline initialization successful")
        
        # Test data exploration
        stats = pipeline.explore_dataset(sample_data)
        assert 'total_interactions' in stats
        print("✓ Dataset exploration successful")
        
        # Test data preprocessing
        df_clean = pipeline.preprocess_data(sample_data)
        assert len(df_clean) > 0
        print("✓ Data preprocessing successful")
        
        # Test feature engineering
        from kt_feature_engineer import KTFeatureEngineer
        fe = KTFeatureEngineer()
        features = fe.create_features(df_clean)
        assert 'bkt' in features
        assert 'pfa' in features
        assert 'dkt' in features
        print("✓ Feature engineering successful")
        
        # Test evaluator
        from kt_evaluation import KTEvaluator
        evaluator = KTEvaluator()
        report = evaluator.generate_dataset_report(df_clean, stats)
        assert len(report) > 0
        print("✓ Evaluation module successful")
        
        # Test predictor initialization
        from prediction import KTPredictor
        predictor = KTPredictor()
        assert len(predictor.model_paths) > 0
        print("✓ Predictor initialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in basic functionality test: {e}")
        return False

def test_model_interfaces():
    """Test that model interfaces work correctly"""
    print("\nTesting model interfaces...")
    
    try:
        # Test BKT model import
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'models'))
        
        from train_bkt import BayesianKnowledgeTracer
        bkt = BayesianKnowledgeTracer()
        print("✓ BKT model interface works")
        
        from train_pfa import PFAKnowledgeTracing
        pfa = PFAKnowledgeTracing()
        print("✓ PFA model interface works")
        
        from train_dkt import DeepKnowledgeTracing
        dkt = DeepKnowledgeTracing()
        print("✓ DKT model interface works")
        
        return True
        
    except ImportError as e:
        print(f"⚠️  Model import warning: {e}")
        print("   This is expected if TensorFlow is not installed")
        return True  # Don't fail the test for missing dependencies
    except Exception as e:
        print(f"❌ Error in model interface test: {e}")
        return False

def run_integration_test():
    """Run a simple integration test"""
    print("\nRunning integration test...")
    
    try:
        import pandas as pd
        
        # Create larger sample dataset
        np.random.seed(42)
        n_users = 10
        n_interactions_per_user = 20
        skills = ['Addition', 'Subtraction', 'Multiplication', 'Division', 'Fractions']
        
        data = []
        for user_id in range(1, n_users + 1):
            for interaction in range(1, n_interactions_per_user + 1):
                skill = np.random.choice(skills)
                # Simulate learning: higher probability of correct as interaction number increases
                prob_correct = min(0.3 + (interaction / n_interactions_per_user) * 0.6, 0.9)
                correct = 1 if np.random.random() < prob_correct else 0
                
                data.append({
                    'user_id': f'user_{user_id}',
                    'problem_id': f'prob_{user_id}_{interaction}',
                    'skill_name': skill,
                    'correct': correct,
                    'order_id': interaction
                })
        
        df = pd.DataFrame(data)
        
        # Test pipeline with this data
        from kt_training_pipeline import KnowledgeTracingPipeline
        pipeline = KnowledgeTracingPipeline()
        
        # Test data loading and preprocessing
        stats = pipeline.explore_dataset(df)
        df_clean = pipeline.preprocess_data(df)
        train_df, test_df = pipeline.split_data(df_clean, test_size=0.3)
        
        print(f"✓ Integration test successful:")
        print(f"  - Total interactions: {len(df)}")
        print(f"  - Training set: {len(train_df)} interactions")
        print(f"  - Test set: {len(test_df)} interactions")
        print(f"  - Unique skills: {len(df['skill_name'].unique())}")
        print(f"  - Unique users: {len(df['user_id'].unique())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("KNOWLEDGE TRACING PIPELINE TEST SUITE")
    print("="*60)
    
    # Configure logging to reduce noise
    logging.getLogger().setLevel(logging.WARNING)
    
    tests = [
        ("Import Tests", test_imports),
        ("Basic Functionality Tests", test_basic_functionality),
        ("Model Interface Tests", test_model_interfaces),
        ("Integration Test", run_integration_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASSED" if results[i] else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! The pipeline is ready to use.")
        print("\nNext steps:")
        print("1. Prepare your knowledge tracing dataset")
        print("2. Run the main pipeline: python kt_training_pipeline.py")
        print("3. Use prediction.py to make predictions with trained models")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the issues above.")
    
    print("="*60)
    
    return passed == total

if __name__ == "__main__":
    import numpy as np
    success = main()
    sys.exit(0 if success else 1)
