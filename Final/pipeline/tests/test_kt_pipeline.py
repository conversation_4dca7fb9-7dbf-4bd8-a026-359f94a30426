#!/usr/bin/env python3
"""
Knowledge Tracing Pipeline Tests
===============================

Test suite for the knowledge tracing pipeline components.
"""

import os
import sys
import unittest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import pipeline components
from kt_training_pipeline import KnowledgeTracingPipeline
from kt_feature_engineer import KTFeatureEngineer
from kt_model_trainer import KTModelTrainer
from kt_ensemble import KTModelEnsemble
from kt_evaluation import KTEvaluator
from prediction import KTPredictor

class TestKnowledgeTracingPipeline(unittest.TestCase):
    """Test the main knowledge tracing pipeline"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.pipeline = KnowledgeTracingPipeline()
        
        # Create sample data
        self.sample_data = pd.DataFrame({
            'user_id': ['user1', 'user1', 'user2', 'user2', 'user3', 'user3'] * 10,
            'problem_id': ['prob1', 'prob2', 'prob3', 'prob4', 'prob5', 'prob6'] * 10,
            'skill_name': ['Addition', 'Subtraction', 'Addition', 'Multiplication', 'Division', 'Fractions'] * 10,
            'correct': [1, 0, 1, 1, 0, 1] * 10,
            'order_id': list(range(1, 61))
        })
    
    def test_pipeline_initialization(self):
        """Test pipeline initialization"""
        self.assertIsInstance(self.pipeline, KnowledgeTracingPipeline)
        self.assertEqual(self.pipeline.experiment_name, "knowledge-tracing")
        self.assertEqual(len(self.pipeline.models), 0)
        self.assertEqual(len(self.pipeline.metrics), 0)
    
    def test_explore_dataset(self):
        """Test dataset exploration"""
        stats = self.pipeline.explore_dataset(self.sample_data)
        
        self.assertIn('total_interactions', stats)
        self.assertIn('unique_students', stats)
        self.assertIn('unique_problems', stats)
        self.assertIn('unique_skills', stats)
        self.assertIn('overall_accuracy', stats)
        
        self.assertEqual(stats['total_interactions'], 60)
        self.assertEqual(stats['unique_students'], 3)
        self.assertEqual(stats['unique_problems'], 6)
        self.assertEqual(stats['unique_skills'], 5)
    
    def test_preprocess_data(self):
        """Test data preprocessing"""
        df_clean = self.pipeline.preprocess_data(self.sample_data)
        
        # Check that required columns exist
        required_cols = ['user_id', 'problem_id', 'skill_name', 'correct']
        for col in required_cols:
            self.assertIn(col, df_clean.columns)
        
        # Check data types
        self.assertEqual(df_clean['correct'].dtype, int)
        self.assertEqual(df_clean['user_id'].dtype, object)
        
        # Check that order_id was created/maintained
        self.assertIn('order_id', df_clean.columns)
    
    def test_split_data(self):
        """Test data splitting"""
        df_clean = self.pipeline.preprocess_data(self.sample_data)
        train_df, test_df = self.pipeline.split_data(df_clean, test_size=0.3)
        
        # Check that split maintains user separation
        train_users = set(train_df['user_id'].unique())
        test_users = set(test_df['user_id'].unique())
        
        # No user should be in both sets
        self.assertEqual(len(train_users.intersection(test_users)), 0)
        
        # Check approximate split ratio
        total_users = len(df_clean['user_id'].unique())
        expected_test_users = int(total_users * 0.3)
        self.assertAlmostEqual(len(test_users), expected_test_users, delta=1)


class TestKTFeatureEngineer(unittest.TestCase):
    """Test the feature engineering module"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.feature_engineer = KTFeatureEngineer()
        
        # Create sample data
        self.sample_data = pd.DataFrame({
            'user_id': ['user1', 'user1', 'user2', 'user2'],
            'problem_id': ['prob1', 'prob2', 'prob3', 'prob4'],
            'skill_name': ['Addition', 'Addition', 'Subtraction', 'Subtraction'],
            'correct': [1, 0, 1, 1],
            'order_id': [1, 2, 1, 2]
        })
    
    def test_create_features(self):
        """Test feature creation"""
        features = self.feature_engineer.create_features(self.sample_data)
        
        # Check that all feature sets are created
        self.assertIn('bkt', features)
        self.assertIn('pfa', features)
        self.assertIn('dkt', features)
        self.assertIn('raw', features)
        
        # Check that encoders are fitted
        self.assertTrue(self.feature_engineer.is_fitted)
        self.assertGreater(len(self.feature_engineer.skill_encoders), 0)
    
    def test_create_sequences_for_dkt(self):
        """Test DKT sequence creation"""
        # First create features to fit encoders
        features = self.feature_engineer.create_features(self.sample_data)
        
        sequences = self.feature_engineer.create_sequences_for_dkt(features['raw'])
        
        self.assertIsInstance(sequences, list)
        self.assertGreater(len(sequences), 0)
        
        # Check sequence structure
        for seq in sequences:
            self.assertIn('user_id', seq)
            self.assertIn('inputs', seq)
            self.assertIn('targets', seq)
            self.assertIn('length', seq)


class TestKTModelTrainer(unittest.TestCase):
    """Test the model trainer"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.trainer = KTModelTrainer(n_folds=2)  # Use fewer folds for testing
        
        # Create sample data
        self.sample_data = pd.DataFrame({
            'user_id': ['user1', 'user1', 'user2', 'user2', 'user3', 'user3'] * 5,
            'problem_id': ['prob1', 'prob2', 'prob3', 'prob4', 'prob5', 'prob6'] * 5,
            'skill_name': ['Addition', 'Addition', 'Subtraction', 'Subtraction', 'Multiplication', 'Multiplication'] * 5,
            'correct': [1, 0, 1, 1, 0, 1] * 5,
            'order_id': list(range(1, 31))
        })
    
    @patch('kt_model_trainer.BayesianKnowledgeTracer')
    def test_train_bkt(self, mock_bkt):
        """Test BKT training"""
        # Mock the BKT model
        mock_model = MagicMock()
        mock_bkt.return_value = mock_model
        mock_model.predict_proba.return_value = [0.7]
        
        train_df = self.sample_data.iloc[:20]
        test_df = self.sample_data.iloc[20:]
        
        model, metrics = self.trainer.train_bkt(train_df, test_df, hyperparameter_tuning=False)
        
        self.assertIsNotNone(model)
        self.assertIsInstance(metrics, dict)
        self.assertIn('accuracy', metrics)


class TestKTEvaluator(unittest.TestCase):
    """Test the evaluation module"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.evaluator = KTEvaluator()
        
        # Create sample data
        self.sample_data = pd.DataFrame({
            'user_id': ['user1', 'user1', 'user2', 'user2'],
            'problem_id': ['prob1', 'prob2', 'prob3', 'prob4'],
            'skill_name': ['Addition', 'Addition', 'Subtraction', 'Subtraction'],
            'correct': [1, 0, 1, 1],
            'order_id': [1, 2, 1, 2]
        })
        
        self.sample_stats = {
            'total_interactions': 4,
            'unique_students': 2,
            'unique_problems': 4,
            'unique_skills': 2,
            'overall_accuracy': 0.75
        }
    
    def test_generate_dataset_report(self):
        """Test dataset report generation"""
        report = self.evaluator.generate_dataset_report(self.sample_data, self.sample_stats)
        
        self.assertIsInstance(report, str)
        self.assertIn('KNOWLEDGE TRACING DATASET ANALYSIS REPORT', report)
        self.assertIn('Total interactions: 4', report)
        self.assertIn('Unique students: 2', report)


class TestKTPredictor(unittest.TestCase):
    """Test the prediction module"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.predictor = KTPredictor()
    
    def test_predictor_initialization(self):
        """Test predictor initialization"""
        self.assertIsInstance(self.predictor, KTPredictor)
        self.assertEqual(len(self.predictor.models), 0)
        self.assertIn('bkt', self.predictor.model_paths)
        self.assertIn('pfa', self.predictor.model_paths)
        self.assertIn('dkt', self.predictor.model_paths)
        self.assertIn('ensemble', self.predictor.model_paths)
    
    @patch('prediction.os.path.exists')
    def test_load_models_no_files(self, mock_exists):
        """Test loading models when no files exist"""
        mock_exists.return_value = False
        
        load_status = self.predictor.load_models()
        
        # All models should fail to load
        for status in load_status.values():
            self.assertFalse(status)


def run_pipeline_tests():
    """Run all pipeline tests"""
    print("="*60)
    print("KNOWLEDGE TRACING PIPELINE TESTS")
    print("="*60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestKnowledgeTracingPipeline,
        TestKTFeatureEngineer,
        TestKTModelTrainer,
        TestKTEvaluator,
        TestKTPredictor
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "="*60)
    if result.wasSuccessful():
        print("✅ ALL TESTS PASSED!")
    else:
        print("❌ SOME TESTS FAILED!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
    print("="*60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_pipeline_tests()
