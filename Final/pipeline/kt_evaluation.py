#!/usr/bin/env python3
"""
Knowledge Tracing Evaluation and Reporting
==========================================

This module implements comprehensive evaluation metrics and reporting
functions for knowledge tracing models.
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Tuple, Optional
from sklearn.metrics import (accuracy_score, precision_score, recall_score, 
                           f1_score, roc_auc_score, confusion_matrix, 
                           classification_report)
import logging
import json

logger = logging.getLogger(__name__)

class KTEvaluator:
    """Comprehensive evaluator for knowledge tracing models"""
    
    def __init__(self, output_dir: str = "App/Training/pipeline/outputs"):
        """
        Initialize evaluator
        
        Args:
            output_dir: Directory to save reports and plots
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def evaluate_model_performance(self, models: Dict[str, Any], test_df: pd.DataFrame) -> Dict[str, Dict]:
        """
        Evaluate performance of all models
        
        Args:
            models: Dictionary of trained models
            test_df: Test dataset
            
        Returns:
            Dictionary of model performance metrics
        """
        logger.info("Evaluating model performance...")
        
        results = {}
        
        for model_name, model in models.items():
            logger.info(f"Evaluating {model_name} model...")
            
            try:
                if model_name == 'bkt':
                    metrics = self._evaluate_bkt(model, test_df)
                elif model_name == 'pfa':
                    metrics = self._evaluate_pfa(model, test_df)
                elif model_name == 'dkt':
                    metrics = self._evaluate_dkt(model, test_df)
                elif model_name == 'ensemble':
                    metrics = self._evaluate_ensemble(model, test_df)
                else:
                    logger.warning(f"Unknown model type: {model_name}")
                    continue
                
                results[model_name] = metrics
                
            except Exception as e:
                logger.error(f"Error evaluating {model_name}: {e}")
                results[model_name] = {'error': str(e)}
        
        # Save results
        self._save_performance_results(results)
        
        return results
    
    def generate_dataset_report(self, df: pd.DataFrame, stats: Dict[str, Any]) -> str:
        """
        Generate comprehensive dataset analysis report
        
        Args:
            df: Dataset
            stats: Dataset statistics
            
        Returns:
            Report string
        """
        logger.info("Generating dataset report...")
        
        report = self._create_dataset_report_text(df, stats)
        
        # Create visualizations
        self._create_dataset_visualizations(df)
        
        # Save report
        report_path = os.path.join(self.output_dir, "dataset_report.txt")
        with open(report_path, 'w') as f:
            f.write(report)
        
        logger.info(f"Dataset report saved to {report_path}")
        return report
    
    def generate_model_comparison_report(self, results: Dict[str, Dict]) -> str:
        """
        Generate model comparison report
        
        Args:
            results: Model evaluation results
            
        Returns:
            Report string
        """
        logger.info("Generating model comparison report...")
        
        report = self._create_model_comparison_text(results)
        
        # Create comparison visualizations
        self._create_model_comparison_plots(results)
        
        # Save report
        report_path = os.path.join(self.output_dir, "model_comparison_report.txt")
        with open(report_path, 'w') as f:
            f.write(report)
        
        logger.info(f"Model comparison report saved to {report_path}")
        return report
    
    def generate_skill_performance_report(self, models: Dict[str, Any], 
                                        test_df: pd.DataFrame) -> Dict[str, Dict]:
        """
        Generate per-skill performance analysis
        
        Args:
            models: Dictionary of trained models
            test_df: Test dataset
            
        Returns:
            Dictionary of per-skill performance metrics
        """
        logger.info("Generating skill performance report...")
        
        skill_results = {}
        
        for skill in test_df['skill_name'].unique():
            skill_data = test_df[test_df['skill_name'] == skill]
            
            if len(skill_data) < 10:  # Skip skills with too few samples
                continue
            
            skill_metrics = {}
            
            for model_name, model in models.items():
                try:
                    if model_name == 'bkt':
                        metrics = self._evaluate_bkt(model, skill_data)
                    elif model_name == 'pfa':
                        metrics = self._evaluate_pfa(model, skill_data)
                    elif model_name == 'dkt':
                        metrics = self._evaluate_dkt(model, skill_data)
                    elif model_name == 'ensemble':
                        metrics = self._evaluate_ensemble(model, skill_data)
                    else:
                        continue
                    
                    skill_metrics[model_name] = metrics
                    
                except Exception as e:
                    logger.warning(f"Error evaluating {model_name} on skill {skill}: {e}")
                    continue
            
            if skill_metrics:
                skill_results[skill] = skill_metrics
        
        # Create skill performance visualizations
        self._create_skill_performance_plots(skill_results)
        
        # Save results
        skill_report_path = os.path.join(self.output_dir, "skill_performance.json")
        with open(skill_report_path, 'w') as f:
            json.dump(skill_results, f, indent=2)
        
        logger.info(f"Skill performance report saved to {skill_report_path}")
        return skill_results
    
    def _evaluate_bkt(self, model, test_df: pd.DataFrame) -> Dict[str, float]:
        """Evaluate BKT model"""
        predictions = []
        actuals = []
        
        for user_id, user_data in test_df.groupby('user_id'):
            user_data = user_data.sort_values('order_id')
            
            for skill in user_data['skill_name'].unique():
                skill_data = user_data[user_data['skill_name'] == skill]
                if len(skill_data) < 2:
                    continue
                
                # Use all but last interaction as history
                history = skill_data['correct'].iloc[:-1].tolist()
                actual = skill_data['correct'].iloc[-1]
                
                try:
                    proba = model.predict_proba(history, skill)
                    pred = 1 if proba[-1] > 0.5 else 0
                    predictions.append(pred)
                    actuals.append(actual)
                except:
                    continue
        
        if len(predictions) == 0:
            return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'auc': 0.5}
        
        return {
            'accuracy': accuracy_score(actuals, predictions),
            'precision': precision_score(actuals, predictions, zero_division=0),
            'recall': recall_score(actuals, predictions, zero_division=0),
            'f1_score': f1_score(actuals, predictions, zero_division=0),
            'auc': roc_auc_score(actuals, predictions) if len(np.unique(actuals)) > 1 else 0.5
        }
    
    def _evaluate_pfa(self, model, test_df: pd.DataFrame) -> Dict[str, float]:
        """Evaluate PFA model"""
        try:
            X_test, y_test, _ = model.prepare_features(test_df)
            return model.evaluate(X_test, y_test)
        except Exception as e:
            logger.error(f"Error evaluating PFA: {e}")
            return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'auc': 0.5}
    
    def _evaluate_dkt(self, model, test_df: pd.DataFrame) -> Dict[str, float]:
        """Evaluate DKT model"""
        try:
            return model.evaluate(test_df)
        except Exception as e:
            logger.error(f"Error evaluating DKT: {e}")
            return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'auc': 0.5}
    
    def _evaluate_ensemble(self, model, test_df: pd.DataFrame) -> Dict[str, float]:
        """Evaluate ensemble model"""
        try:
            # This is a simplified evaluation - in practice would be more complex
            predictions = []
            actuals = test_df['correct'].values
            
            for _, row in test_df.iterrows():
                user_id = row['user_id']
                skill = row['skill_name']
                
                # Get user history
                user_data = test_df[(test_df['user_id'] == user_id) & 
                                  (test_df['order_id'] < row['order_id'])]
                history = user_data['correct'].tolist()
                
                try:
                    pred = model.predict(history, skill, user_id)
                    predictions.append(pred)
                except:
                    predictions.append(0.5)
            
            predictions = np.array(predictions)
            pred_binary = (predictions > 0.5).astype(int)
            
            return {
                'accuracy': accuracy_score(actuals, pred_binary),
                'precision': precision_score(actuals, pred_binary, zero_division=0),
                'recall': recall_score(actuals, pred_binary, zero_division=0),
                'f1_score': f1_score(actuals, pred_binary, zero_division=0),
                'auc': roc_auc_score(actuals, predictions) if len(np.unique(actuals)) > 1 else 0.5
            }
        except Exception as e:
            logger.error(f"Error evaluating ensemble: {e}")
            return {'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'auc': 0.5}
    
    def _create_dataset_report_text(self, df: pd.DataFrame, stats: Dict[str, Any]) -> str:
        """Create dataset report text"""
        report = f"""
KNOWLEDGE TRACING DATASET ANALYSIS REPORT
=========================================

Dataset Overview:
- Total interactions: {stats.get('total_interactions', 'N/A')}
- Unique students: {stats.get('unique_students', 'N/A')}
- Unique problems: {stats.get('unique_problems', 'N/A')}
- Unique skills: {stats.get('unique_skills', 'N/A')}

Performance Statistics:
- Overall accuracy: {stats.get('overall_accuracy', 'N/A'):.3f}
- Correct responses: {stats.get('correct_responses', 'N/A')}
- Incorrect responses: {stats.get('incorrect_responses', 'N/A')}

Sequence Statistics:
- Average sequence length: {stats.get('avg_sequence_length', 'N/A'):.1f}
- Maximum sequence length: {stats.get('max_sequence_length', 'N/A')}
- Minimum sequence length: {stats.get('min_sequence_length', 'N/A')}

Skill Distribution:
- Most common skill: {stats.get('most_common_skill', 'N/A')}
  ({stats.get('most_common_skill_count', 'N/A')} interactions)
- Least common skill: {stats.get('least_common_skill', 'N/A')}
  ({stats.get('least_common_skill_count', 'N/A')} interactions)

Data Quality:
- Missing values in key columns: {df[['user_id', 'skill_name', 'correct']].isnull().sum().sum()}
- Duplicate rows: {df.duplicated().sum()}
"""
        return report
    
    def _create_model_comparison_text(self, results: Dict[str, Dict]) -> str:
        """Create model comparison report text"""
        report = "\nMODEL PERFORMANCE COMPARISON\n" + "="*40 + "\n"
        
        # Create comparison table
        metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc']
        
        for metric in metrics:
            report += f"\n{metric.upper()}:\n"
            for model_name, model_results in results.items():
                if 'error' not in model_results and metric in model_results:
                    value = model_results[metric]
                    report += f"  {model_name:12}: {value:.4f}\n"
                else:
                    report += f"  {model_name:12}: N/A\n"
        
        # Find best performing model for each metric
        report += "\nBEST PERFORMING MODELS:\n"
        for metric in metrics:
            best_model = None
            best_score = -1
            
            for model_name, model_results in results.items():
                if 'error' not in model_results and metric in model_results:
                    score = model_results[metric]
                    if score > best_score:
                        best_score = score
                        best_model = model_name
            
            if best_model:
                report += f"  {metric:12}: {best_model} ({best_score:.4f})\n"
        
        return report
    
    def _create_dataset_visualizations(self, df: pd.DataFrame):
        """Create dataset visualization plots"""
        try:
            plt.style.use('default')
            
            # Create subplots
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('Dataset Analysis', fontsize=16)
            
            # 1. Skill distribution
            skill_counts = df['skill_name'].value_counts().head(20)
            axes[0, 0].bar(range(len(skill_counts)), skill_counts.values)
            axes[0, 0].set_title('Top 20 Skills by Frequency')
            axes[0, 0].set_xlabel('Skills')
            axes[0, 0].set_ylabel('Number of Interactions')
            axes[0, 0].tick_params(axis='x', rotation=45)
            
            # 2. Accuracy distribution by skill
            skill_accuracy = df.groupby('skill_name')['correct'].mean().sort_values(ascending=False).head(20)
            axes[0, 1].bar(range(len(skill_accuracy)), skill_accuracy.values)
            axes[0, 1].set_title('Top 20 Skills by Accuracy')
            axes[0, 1].set_xlabel('Skills')
            axes[0, 1].set_ylabel('Accuracy')
            axes[0, 1].tick_params(axis='x', rotation=45)
            
            # 3. Sequence length distribution
            seq_lengths = df.groupby('user_id').size()
            axes[1, 0].hist(seq_lengths, bins=50, alpha=0.7)
            axes[1, 0].set_title('Distribution of Sequence Lengths')
            axes[1, 0].set_xlabel('Sequence Length')
            axes[1, 0].set_ylabel('Number of Students')
            
            # 4. Overall accuracy over time
            df_sorted = df.sort_values(['user_id', 'order_id'])
            df_sorted['global_order'] = range(len(df_sorted))
            window_size = len(df_sorted) // 100
            if window_size > 0:
                rolling_accuracy = df_sorted['correct'].rolling(window=window_size).mean()
                axes[1, 1].plot(df_sorted['global_order'], rolling_accuracy)
                axes[1, 1].set_title('Accuracy Trend Over Time')
                axes[1, 1].set_xlabel('Interaction Number')
                axes[1, 1].set_ylabel('Rolling Accuracy')
            
            plt.tight_layout()
            plt.savefig(os.path.join(self.output_dir, 'dataset_analysis.png'), dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.warning(f"Error creating dataset visualizations: {e}")
    
    def _create_model_comparison_plots(self, results: Dict[str, Dict]):
        """Create model comparison plots"""
        try:
            metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc']
            models = list(results.keys())
            
            # Extract metric values
            metric_data = {}
            for metric in metrics:
                metric_data[metric] = []
                for model in models:
                    if 'error' not in results[model] and metric in results[model]:
                        metric_data[metric].append(results[model][metric])
                    else:
                        metric_data[metric].append(0)
            
            # Create comparison plot
            fig, ax = plt.subplots(figsize=(12, 8))
            
            x = np.arange(len(models))
            width = 0.15
            
            for i, metric in enumerate(metrics):
                ax.bar(x + i * width, metric_data[metric], width, label=metric)
            
            ax.set_xlabel('Models')
            ax.set_ylabel('Score')
            ax.set_title('Model Performance Comparison')
            ax.set_xticks(x + width * 2)
            ax.set_xticklabels(models)
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(os.path.join(self.output_dir, 'model_comparison.png'), dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.warning(f"Error creating model comparison plots: {e}")
    
    def _create_skill_performance_plots(self, skill_results: Dict[str, Dict]):
        """Create skill performance visualization"""
        try:
            # This would create detailed skill-by-skill performance plots
            # For now, just log that it would be created
            logger.info("Skill performance plots would be created here")
            
        except Exception as e:
            logger.warning(f"Error creating skill performance plots: {e}")
    
    def _save_performance_results(self, results: Dict[str, Dict]):
        """Save performance results to JSON"""
        results_path = os.path.join(self.output_dir, "model_performance.json")
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Performance results saved to {results_path}")
