#!/usr/bin/env python3
"""
Knowledge Tracing Training Pipeline
===================================

This script implements a complete ML pipeline for knowledge tracing
using BKT, PFA, and DKT models. It includes data preprocessing,
feature engineering, multiple model training, and ensemble methods.

Usage:
    python kt_training_pipeline.py

Requirements:
    - Knowledge tracing datasets in /Training/datasets/ directory
    - TensorFlow for DKT model
    - scikit-learn for BKT and PFA models
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# Add the models directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'models'))

import joblib

# Local imports
from kt_feature_engineer import KTFeatureEngineer
from kt_model_trainer import KTModelTrainer
from kt_ensemble import KTModelEnsemble

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KnowledgeTracingPipeline:
    """Complete pipeline for knowledge tracing model training and evaluation"""
    
    def __init__(self):
        """Initialize the pipeline configuration"""
        self.experiment_name = "knowledge-tracing"
        self.models = {}
        self.metrics = {}
        self.datasets = {}
        self.feature_engineer = None
        self.model_trainer = None
        self.ensemble = None
        
    def load_data(self, data_path: str = None) -> pd.DataFrame:
        """
        Load knowledge tracing dataset
        
        Args:
            data_path: Path to the dataset file
            
        Returns:
            DataFrame with knowledge tracing data
        """
        logger.info("Loading knowledge tracing dataset...")
        
        if data_path is None:
            # Try to find dataset in common locations
            possible_paths = [
                "App/Training/datasets/skill_builder_data_corrected.csv",
                "App/Dashboard/datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv",
                "../datasets/skill_builder_data_corrected.csv"
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    data_path = path
                    break
            
            if data_path is None:
                raise FileNotFoundError("No dataset found. Please provide a valid data_path.")
        
        try:
            df = pd.read_csv(data_path, encoding='latin1')
            logger.info(f"Dataset loaded successfully from {data_path}")
            logger.info(f"Dataset shape: {df.shape}")
            logger.info(f"Columns: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise
    
    def explore_dataset(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Explore the dataset and extract key statistics
        
        Args:
            df: Knowledge tracing dataset
            
        Returns:
            Dictionary with dataset statistics
        """
        logger.info("Exploring dataset...")
        
        stats = {}
        
        # Basic statistics
        stats['total_interactions'] = len(df)
        stats['unique_students'] = df['user_id'].nunique() if 'user_id' in df.columns else 0
        stats['unique_problems'] = df['problem_id'].nunique() if 'problem_id' in df.columns else 0
        stats['unique_skills'] = df['skill_name'].nunique() if 'skill_name' in df.columns else 0
        
        # Performance statistics
        if 'correct' in df.columns:
            stats['overall_accuracy'] = df['correct'].mean()
            stats['correct_responses'] = df['correct'].sum()
            stats['incorrect_responses'] = len(df) - df['correct'].sum()
        
        # Temporal statistics
        if 'order_id' in df.columns:
            stats['avg_sequence_length'] = df.groupby('user_id')['order_id'].count().mean()
            stats['max_sequence_length'] = df.groupby('user_id')['order_id'].count().max()
            stats['min_sequence_length'] = df.groupby('user_id')['order_id'].count().min()
        
        # Skill distribution
        if 'skill_name' in df.columns:
            skill_counts = df['skill_name'].value_counts()
            stats['most_common_skill'] = skill_counts.index[0]
            stats['most_common_skill_count'] = skill_counts.iloc[0]
            stats['least_common_skill'] = skill_counts.index[-1]
            stats['least_common_skill_count'] = skill_counts.iloc[-1]
        
        logger.info("Dataset exploration completed")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        return stats
    
    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and preprocess the knowledge tracing data
        
        Args:
            df: Raw knowledge tracing dataset
            
        Returns:
            Cleaned dataset
        """
        logger.info("Preprocessing data...")
        
        # Required columns for knowledge tracing
        required_cols = ['user_id', 'problem_id', 'skill_name', 'correct']
        
        # Check for required columns
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Remove rows with missing values in essential columns
        df_clean = df.dropna(subset=required_cols).copy()
        
        # Remove duplicates
        df_clean = df_clean.drop_duplicates()
        
        # Ensure correct data types
        df_clean['correct'] = df_clean['correct'].astype(int)
        df_clean['user_id'] = df_clean['user_id'].astype(str)
        df_clean['problem_id'] = df_clean['problem_id'].astype(str)
        df_clean['skill_name'] = df_clean['skill_name'].astype(str)
        
        # Sort by user and order if available
        if 'order_id' in df_clean.columns:
            df_clean = df_clean.sort_values(['user_id', 'order_id'])
        else:
            # Create a simple ordering based on row index
            df_clean = df_clean.sort_values(['user_id'])
            df_clean['order_id'] = df_clean.groupby('user_id').cumcount() + 1
        
        logger.info(f"Data preprocessing completed")
        logger.info(f"Original size: {len(df)}, Cleaned size: {len(df_clean)}")
        logger.info(f"Removed {len(df) - len(df_clean)} rows")
        
        return df_clean
    
    def split_data(self, df: pd.DataFrame, test_size: float = 0.2, 
                   random_state: int = 42) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Split data into train and test sets
        
        Args:
            df: Preprocessed dataset
            test_size: Proportion of data for testing
            random_state: Random seed for reproducibility
            
        Returns:
            Tuple of (train_df, test_df)
        """
        logger.info(f"Splitting data (test_size={test_size})...")
        
        # Split by users to avoid data leakage
        unique_users = df['user_id'].unique()
        np.random.seed(random_state)
        np.random.shuffle(unique_users)
        
        n_test_users = int(len(unique_users) * test_size)
        test_users = unique_users[:n_test_users]
        train_users = unique_users[n_test_users:]
        
        train_df = df[df['user_id'].isin(train_users)].copy()
        test_df = df[df['user_id'].isin(test_users)].copy()
        
        logger.info(f"Train set: {len(train_df)} interactions from {len(train_users)} users")
        logger.info(f"Test set: {len(test_df)} interactions from {len(test_users)} users")
        
        return train_df, test_df
    
    def engineer_features(self, train_df: pd.DataFrame, test_df: pd.DataFrame):
        """Create features for knowledge tracing models"""
        logger.info("Engineering features for knowledge tracing...")

        # Initialize feature engineer
        self.feature_engineer = KTFeatureEngineer()

        # Create features for training data
        train_features = self.feature_engineer.create_features(train_df)

        # Create features for test data
        test_features = self.feature_engineer.create_features(test_df)

        logger.info("Feature engineering completed")
        return train_features, test_features

    def train_models(self, train_df: pd.DataFrame, test_df: pd.DataFrame, models_to_train: List[str] = ['bkt']):
        """Train BKT, PFA, and DKT models"""
        logger.info("Training knowledge tracing models...")

        # Initialize model trainer
        self.model_trainer = KTModelTrainer()

        if 'bkt' in models_to_train:
            # Train BKT model
            logger.info("Training BKT model...")
            bkt_model, bkt_metrics = self.model_trainer.train_bkt(train_df, test_df)
            self.models['bkt'] = bkt_model
            self.metrics['bkt'] = bkt_metrics
            logger.info(f"  BKT metrics: {bkt_metrics}")
            logger.info(f"  BKT model: {bkt_model}")
            logger.info(f"  BKT model params: {bkt_model.get_params()}")
            logger.info(f"  BKT model skills: {bkt_model.skill_params}")
            logger.info(f"  BKT model students: {bkt_model.student_initial_knowledge}")
      

        if 'pfa' in models_to_train:
            # Train PFA model
            logger.info("Training PFA model... AG")
            pfa_model, pfa_metrics = self.model_trainer.train_pfa(train_df, test_df,self.datasets)
            self.models['pfa'] = pfa_model
            self.metrics['pfa'] = pfa_metrics
            logger.info(f"  PFA metrics: {pfa_metrics}")
            logger.info(f"  PFA model: {pfa_model}")
            # logger.info(f"  PFA model params: {pfa_model.get_params()}")
            logger.info(f"  PFA model skills: {pfa_model.skill_params}")
            # logger.info(f"  PFA model students: {pfa_model.student_skill_history}")

        if 'dkt' in models_to_train:
            # Train DKT model
            logger.info("Training DKT model...")
            dkt_model, dkt_metrics = self.model_trainer.train_dkt(train_df, test_df,self.datasets)
            self.models['dkt'] = dkt_model
            self.metrics['dkt'] = dkt_metrics
            logger.info(f"  DKT metrics: {dkt_metrics}")
            logger.info(f"  DKT model: {dkt_model}")
            # logger.info(f"  DKT model params: {dkt_model.get_params()}")
            # logger.info(f"  DKT model skills: {dkt_model.skill_params}")
            # logger.info(f"  DKT model students: {dkt_model.student_initial_knowledge}")

        logger.info("Model training completed")
        return self.models, self.metrics

    def create_ensemble(self, train_df: pd.DataFrame, test_df: pd.DataFrame):
        """Create ensemble model combining BKT, PFA, and DKT"""
        logger.info("Creating ensemble model...")

        if not self.models:
            raise ValueError("No trained models found. Train models first.")

        # Initialize ensemble
        self.ensemble = KTModelEnsemble(list(self.models.values()),['bkt', 'pfa'])

        # Train ensemble
        ensemble_metrics = self.ensemble.train_ensemble(train_df, test_df)
        self.metrics['ensemble'] = ensemble_metrics

        logger.info("Ensemble model created")
        return self.ensemble, ensemble_metrics

    def save_models(self, output_dir: str = "App/Training/models/output"):
        """Save all trained models"""
        logger.info(f"Saving models to {output_dir}...")

        os.makedirs(output_dir, exist_ok=True)

        # Save individual models
        for model_name, model in self.models.items():
            model_path = os.path.join(output_dir, f"{model_name}_model.joblib")
            model.save(model_path)
            logger.info(f"Saved {model_name} model to {model_path}")

        # Save ensemble
        if self.ensemble:
            ensemble_path = os.path.join(output_dir, "ensemble_model.joblib")
            self.ensemble.save(ensemble_path)
            logger.info(f"Saved ensemble model to {ensemble_path}")

        # Save metrics
        metrics_path = os.path.join(output_dir, "model_metrics.json")
        import json
        with open(metrics_path, 'w') as f:
            json.dump(self.metrics, f, indent=2)
        logger.info(f"Saved metrics to {metrics_path}")

    def load_models(self, output_dir: str = "App/Training/models/output"):
        """Load all trained models"""
        logger.info(f"Loading models from {output_dir}...")

        # Load individual models
        for model_name in ['bkt', 'pfa', 'dkt']:
            model_path = os.path.join(output_dir, f"{model_name}_model.joblib")
            if os.path.exists(model_path):
                model = joblib.load(model_path)
                self.models[model_name] = model
                logger.info(f"Loaded {model_name} model from {model_path}")
                print(f"Loaded {model_name} model from {model_path}")   

        return self.models           

            
    def generate_reports(self):
        """Generate comprehensive reports"""
        logger.info("Generating reports...")

        # Dataset report
        self._generate_dataset_report()

        # Model performance report
        self._generate_model_report()

        # Per-skill performance report
        self._generate_skill_report()

        logger.info("Reports generated")

    def _generate_dataset_report(self):
        """Generate dataset analysis report"""
        if 'stats' not in self.datasets:
            return

        stats = self.datasets['stats']
        report = f"""
KNOWLEDGE TRACING DATASET REPORT
===============================

Dataset Statistics:
- Total interactions: {stats.get('total_interactions', 'N/A'):,}
- Unique students: {stats.get('unique_students', 'N/A'):,}
- Unique problems: {stats.get('unique_problems', 'N/A'):,}
- Unique skills: {stats.get('unique_skills', 'N/A'):,}
- Overall accuracy: {stats.get('overall_accuracy', 'N/A'):.3f}

Sequence Statistics:
- Average sequence length: {stats.get('avg_sequence_length', 'N/A'):.1f}
- Maximum sequence length: {stats.get('max_sequence_length', 'N/A')}
- Minimum sequence length: {stats.get('min_sequence_length', 'N/A')}

Most Common Skill: {stats.get('most_common_skill', 'N/A')} ({stats.get('most_common_skill_count', 'N/A')} interactions)
Least Common Skill: {stats.get('least_common_skill', 'N/A')} ({stats.get('least_common_skill_count', 'N/A')} interactions)
"""

        print(report)

        # Save to file
        with open("App/Training/pipeline/outputs/dataset_report.txt", "w") as f:
            f.write(report)

    def _generate_model_report(self):
        """Generate model performance report"""
        if not self.metrics:
            return

        report = "\nMODEL PERFORMANCE REPORT\n" + "="*30 + "\n"

        for model_name, metrics in self.metrics.items():
            report += f"\n{model_name.upper()} Model:\n"
            for metric_name, value in metrics.items():
                if isinstance(value, float):
                    report += f"  {metric_name}: {value:.4f}\n"
                else:
                    report += f"  {metric_name}: {value}\n"

        print(report)

        # Save to file
        os.makedirs("App/Training/pipeline/outputs", exist_ok=True)
        with open("App/Training/pipeline/outputs/model_report.txt", "w") as f:
            f.write(report)

    def _generate_skill_report(self):
        """Generate per-skill performance report"""
        # This would be implemented based on specific skill analysis
        logger.info("Skill-specific report generation not yet implemented")

    def run_pipeline(self, data_path: str = None):
        """Execute the complete training pipeline"""
        try:
            logger.info("Starting Knowledge Tracing Pipeline")

            # Load and preprocess data
            df = self.load_data(data_path)
            dataset_stats = self.explore_dataset(df)
            df_clean = self.preprocess_data(df)

            # Split data
            train_df, test_df = self.split_data(df_clean)

            # Store datasets for later use
            self.datasets = {
                'raw': df,
                'clean': df_clean,
                'train': train_df,
                'test': test_df,
                'stats': dataset_stats
            }

            # Feature engineering
            train_features, test_features = self.engineer_features(train_df, test_df)

            # Train models
            models, metrics = self.train_models(train_df, test_df)

            # Create ensemble
            ensemble, ensemble_metrics = self.create_ensemble(train_df, test_df)

            # Save models
            self.save_models()

            # Generate reports
            self.generate_reports()

            logger.info("Knowledge Tracing Pipeline completed successfully!")
            logger.info("Check the outputs directory for saved models and reports")

            return True

        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            raise

if __name__ == "__main__":
    # Example usage
    pipeline = KnowledgeTracingPipeline()
    pipeline.run_pipeline()
    ensembel = pipeline.create_ensemble( pipeline.datasets['train'], pipeline.datasets['test'])
