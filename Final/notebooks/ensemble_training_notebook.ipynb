{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ensemble Knowledge Tracing Training Notebook\n", "\n", "This notebook demonstrates the training and evaluation of ensemble models for knowledge tracing using stacking and voting approaches. It combines BKT, PFA, and DKT models to create more robust prediction systems.\n", "\n", "## Overview\n", "- Load pre-trained individual models (BKT, PFA, DKT)\n", "- Create ensemble models using different strategies\n", "- Evaluate and compare performance\n", "- Generate comprehensive metrics comparison\n", "\n", "## Requirements\n", "- Pre-trained models in `../models/outputs/`\n", "- Math dataset: `skill_builder_data_corrected.csv`"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:36:25.844921: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📚 Libraries imported successfully!\n", "Current working directory: /home/<USER>/workspace/AClass/Final/notebooks\n"]}], "source": ["# Import required libraries\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import joblib\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add paths for imports\n", "sys.path.append('../pipeline')\n", "sys.path.append('../models')\n", "\n", "# Import custom modules\n", "from train_ensemble import EnsembleTrainer\n", "from kt_ensemble import KTModelEnsemble\n", "from kt_evaluation import KTEvaluator\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📚 Libraries imported successfully!\")\n", "print(f\"Current working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Initialize Ensemble Trainer and <PERSON>ad Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:36:30,751 - INFO - Loading dataset...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 Initializing Ensemble Trainer...\n", "\n", "📊 Loading dataset...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:36:32,041 - INFO - Dataset loaded: (401756, 30)\n", "2025-07-19 22:36:32,042 - INFO - Cleaning data...\n", "2025-07-19 22:36:32,255 - INFO - Data cleaned: (325637, 30)\n", "2025-07-19 22:36:32,275 - INFO - Train set: (260509, 30)\n", "2025-07-19 22:36:32,276 - INFO - Test set: (65128, 30)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ Data loaded successfully!\n", "Full dataset: (325637, 30)\n", "Training set: (260509, 30)\n", "Test set: (65128, 30)\n", "\n", "📈 Dataset Statistics:\n", "Total students: 4,151\n", "Total skills: 110\n", "Total interactions: 325,637\n", "Overall accuracy: 0.658\n"]}], "source": ["# Initialize ensemble trainer\n", "print(\"🚀 Initializing Ensemble Trainer...\")\n", "trainer = <PERSON><PERSON>rainer(\n", "    models_dir=\"../models/outputs\",\n", "    data_path=\"../datasets/math/skill_builder_data_corrected.csv\"\n", ")\n", "\n", "# Load and explore data\n", "print(\"\\n📊 Loading dataset...\")\n", "df_full, train_df, test_df = trainer.load_data()\n", "\n", "print(f\"\\n✅ Data loaded successfully!\")\n", "print(f\"Full dataset: {df_full.shape}\")\n", "print(f\"Training set: {train_df.shape}\")\n", "print(f\"Test set: {test_df.shape}\")\n", "\n", "# Display basic statistics\n", "print(\"\\n📈 Dataset Statistics:\")\n", "print(f\"Total students: {df_full['user_id'].nunique():,}\")\n", "print(f\"Total skills: {df_full['skill_name'].nunique():,}\")\n", "print(f\"Total interactions: {len(df_full):,}\")\n", "print(f\"Overall accuracy: {df_full['correct'].mean():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load Pre-trained Models"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:36:36,214 - INFO - Loading pre-trained models...\n", "2025-07-19 22:36:36,224 - INFO - ✅ BKT model loaded\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔄 Loading pre-trained models...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:36:37,775 - INFO - ✅ PFA model loaded and reconstructed\n", "2025-07-19 22:36:37,780 - WARNING - ❌ Error loading DKT model with proper method: Unknown layer: 'NotEqual'. Please ensure you are using a `keras.utils.custom_object_scope` and that this object is included in the scope. See https://www.tensorflow.org/guide/keras/save_and_serialize#registering_the_custom_object for details.\n", "2025-07-19 22:36:37,781 - INFO - ✅ DKT model loaded as dictionary with wrapper\n", "2025-07-19 22:36:37,782 - INFO - Loaded 3 models: ['bkt', 'pfa', 'dkt']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ Loaded 3 models:\n", "  - BKT\n", "  - PFA\n", "  - DKT\n", "\n", "🎯 Ready for ensemble training with 3 models!\n"]}], "source": ["# Load pre-trained models\n", "print(\"🔄 Loading pre-trained models...\")\n", "models = trainer.load_trained_models()\n", "\n", "print(f\"\\n✅ Loaded {len(models)} models:\")\n", "for model_name in models.keys():\n", "    print(f\"  - {model_name.upper()}\")\n", "\n", "# Check model availability\n", "if len(models) < 2:\n", "    print(\"\\n⚠️ Warning: Need at least 2 models for ensemble training!\")\n", "    print(\"Please ensure BKT, PFA, and DKT models are trained and saved.\")\n", "else:\n", "    print(f\"\\n🎯 Ready for ensemble training with {len(models)} models!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON>ate Individual Models"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:36:52,957 - INFO - Evaluating individual models...\n", "2025-07-19 22:36:52,969 - INFO - Using entire test set: 65128 samples for evaluation\n", "2025-07-19 22:36:52,970 - INFO - Evaluating bkt model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📊 Evaluating individual models...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:36:56,942 - INFO - bkt metrics: {'accuracy': 0.6065, 'precision': 0.7253, 'recall': 0.6993, 'f1_score': 0.7121, 'auc_roc': 0.5663}\n", "2025-07-19 22:36:56,943 - INFO - Evaluating pfa model...\n", "2025-07-19 22:41:48,445 - INFO - pfa metrics: {'accuracy': 0.6499, 'precision': 0.7552, 'recall': 0.7352, 'f1_score': 0.7451, 'auc_roc': 0.6289}\n", "2025-07-19 22:41:48,445 - INFO - Evaluating dkt model...\n", "2025-07-19 22:46:31,707 - INFO - dkt metrics: {'accuracy': 0.6447, 'precision': 0.7566, 'recall': 0.7216, 'f1_score': 0.7387, 'auc_roc': 0.622}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Individual Model Performance:\n", "==================================================\n", "\n", "BKT:\n", "  accuracy: 0.6065\n", "  precision: 0.7253\n", "  recall: 0.6993\n", "  f1_score: 0.7121\n", "  auc_roc: 0.5663\n", "\n", "PFA:\n", "  accuracy: 0.6499\n", "  precision: 0.7552\n", "  recall: 0.7352\n", "  f1_score: 0.7451\n", "  auc_roc: 0.6289\n", "\n", "DKT:\n", "  accuracy: 0.6447\n", "  precision: 0.7566\n", "  recall: 0.7216\n", "  f1_score: 0.7387\n", "  auc_roc: 0.6220\n"]}], "source": ["# Evaluate individual models on test set\n", "print(\"📊 Evaluating individual models...\")\n", "individual_metrics = trainer.evaluate_individual_models(test_df)\n", "\n", "# Display results\n", "print(\"\\n📈 Individual Model Performance:\")\n", "print(\"=\" * 50)\n", "\n", "for model_name, metrics in individual_metrics.items():\n", "    if 'error' not in metrics:\n", "        print(f\"\\n{model_name.upper()}:\")\n", "        for metric, value in metrics.items():\n", "            print(f\"  {metric}: {value:.4f}\")\n", "    else:\n", "        print(f\"\\n{model_name.upper()}: Error - {metrics['error']}\")\n", "\n", "# Store metrics for later comparison\n", "trainer.metrics.update(individual_metrics)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Train Ensemble Models"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:52:54,617 - INFO - Training ensemble models...\n", "2025-07-19 22:52:54,642 - INFO - Using entire dataset: 260509 train, 65128 test samples\n", "2025-07-19 22:52:54,643 - INFO - Training weighted average ensemble...\n", "2025-07-19 22:52:54,644 - INFO - Training ensemble model using weighted_average...\n", "2025-07-19 22:52:54,645 - INFO - Training weighted average ensemble...\n", "2025-07-19 22:52:54,647 - INFO - Getting predictions from bkt model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔧 Training ensemble models...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:59:13,814 - INFO - Getting predictions from pfa model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Creating PFA features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 23:01:47,008 - INFO - Getting predictions from dkt model...\n", "2025-07-19 23:06:48,267 - INFO - Getting predictions from bkt model...\n", "2025-07-19 23:07:54,006 - INFO - Getting predictions from pfa model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Creating PFA features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 23:08:31,130 - INFO - Getting predictions from dkt model...\n", "2025-07-19 23:09:20,570 - INFO - Optimal weights: {'bkt': 0.8, 'pfa': 0.1, 'dkt': 0.09999999999999995}\n", "2025-07-19 23:09:20,571 - INFO - Ensemble training completed\n", "2025-07-19 23:09:20,572 - INFO - Weighted average metrics: {'accuracy': 0.6825175039921385, 'precision': 0.7388396751245421, 'recall': 0.8410414827890556, 'f1_score': 0.7866348842728745, 'auc': 0.6095824961555023}\n", "2025-07-19 23:09:20,572 - INFO - Training stacking ensemble...\n", "2025-07-19 23:09:20,573 - INFO - Training ensemble model using stacking...\n", "2025-07-19 23:09:20,575 - INFO - Training stacking ensemble...\n", "2025-07-19 23:09:20,576 - INFO - Getting predictions from bkt model...\n", "2025-07-19 23:15:58,210 - INFO - Getting predictions from pfa model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Creating PFA features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 23:18:31,900 - INFO - Getting predictions from dkt model...\n", "2025-07-19 23:23:26,875 - INFO - Getting predictions from bkt model...\n", "2025-07-19 23:24:37,474 - INFO - Getting predictions from pfa model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Creating PFA features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 23:25:15,691 - INFO - Getting predictions from dkt model...\n", "2025-07-19 23:26:05,838 - INFO - Ensemble training completed\n", "2025-07-19 23:26:05,839 - INFO - Stacking metrics: {'accuracy': 0.712612086967203, 'precision': 0.7351708774597338, 'recall': 0.9175198587819947, 'f1_score': 0.8162856666110462, 'auc': 0.6150008974059177}\n", "2025-07-19 23:26:05,839 - INFO - Training voting ensemble...\n", "2025-07-19 23:26:05,840 - INFO - Training ensemble model using voting...\n", "2025-07-19 23:26:05,843 - INFO - Training voting ensemble...\n", "2025-07-19 23:26:05,844 - INFO - Getting predictions from bkt model...\n", "2025-07-19 23:27:15,241 - INFO - Getting predictions from pfa model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Creating PFA features...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 23:27:53,364 - INFO - Getting predictions from dkt model...\n", "2025-07-19 23:28:42,527 - INFO - Ensemble training completed\n", "2025-07-19 23:28:42,528 - INFO - Voting metrics: {'accuracy': 0.48049993858248374, 'precision': 0.7396911519198665, 'recall': 0.39106354810238303, 'f1_score': 0.5116339491916859, 'auc': 0.5380953847135501}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 Ensemble Model Performance:\n", "==================================================\n", "\n", "WEIGHTED_AVERAGE ENSEMBLE:\n", "  accuracy: 0.6825\n", "  precision: 0.7388\n", "  recall: 0.8410\n", "  f1_score: 0.7866\n", "  auc: 0.6096\n", "\n", "STACKING ENSEMBLE:\n", "  accuracy: 0.7126\n", "  precision: 0.7352\n", "  recall: 0.9175\n", "  f1_score: 0.8163\n", "  auc: 0.6150\n", "\n", "VOTING ENSEMBLE:\n", "  accuracy: 0.4805\n", "  precision: 0.7397\n", "  recall: 0.3911\n", "  f1_score: 0.5116\n", "  auc: 0.5381\n"]}], "source": ["# Train different ensemble models\n", "print(\"🔧 Training ensemble models...\")\n", "ensemble_metrics = trainer.train_ensemble_models(train_df, test_df)\n", "\n", "# Display ensemble results\n", "print(\"\\n🎯 Ensemble Model Performance:\")\n", "print(\"=\" * 50)\n", "\n", "for ensemble_name, metrics in ensemble_metrics.items():\n", "    print(f\"\\n{ensemble_name.upper()} ENSEMBLE:\")\n", "    for metric, value in metrics.items():\n", "        print(f\"  {metric}: {value:.4f}\")\n", "\n", "# Add ensemble metrics to trainer\n", "for ensemble_name, metrics in ensemble_metrics.items():\n", "    trainer.metrics[f'ensemble_{ensemble_name}'] = metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualize Performance Comparison"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Creating performance visualizations...\n"]}, {"data": {"image/png": "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******************************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", "text/plain": ["<Figure size 1800x1200 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Performance visualizations created!\n"]}], "source": ["# Create performance comparison visualizations\n", "print(\"📊 Creating performance visualizations...\")\n", "\n", "# Prepare data for plotting\n", "plot_data = []\n", "for model_name, metrics in trainer.metrics.items():\n", "    if 'error' not in metrics:\n", "        model_type = 'Ensemble' if model_name.startswith('ensemble_') else 'Individual'\n", "        display_name = model_name.replace('ensemble_', '').upper()\n", "        \n", "        for metric, value in metrics.items():\n", "            plot_data.append({\n", "                'Model': display_name,\n", "                'Type': model_type,\n", "                'Metric': metric.replace('_', ' ').title(),\n", "                'Value': value\n", "            })\n", "\n", "plot_df = pd.DataFrame(plot_data)\n", "\n", "# Create subplots for different metrics\n", "metrics_to_plot = ['Accuracy', 'F1 Score', 'Auc Roc', 'Precision', 'Recall']\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "for i, metric in enumerate(metrics_to_plot):\n", "    if i < len(axes):\n", "        metric_data = plot_df[plot_df['Metric'] == metric]\n", "        \n", "        if not metric_data.empty:\n", "            sns.barplot(data=metric_data, x='Model', y='Value', hue='Type', ax=axes[i])\n", "            axes[i].set_title(f'{metric} Comparison', fontsize=14, fontweight='bold')\n", "            axes[i].set_xlabel('Model', fontsize=12)\n", "            axes[i].set_ylabel(metric, fontsize=12)\n", "            axes[i].tick_params(axis='x', rotation=45)\n", "            axes[i].legend(title='Model Type')\n", "            \n", "            # Add value labels on bars\n", "            for container in axes[i].containers:\n", "                axes[i].bar_label(container, fmt='%.3f', fontsize=10)\n", "                \n", "\n", "# Remove empty subplot\n", "if len(metrics_to_plot) < len(axes):\n", "    fig.delaxes(axes[-1])\n", "\n", "plt.tight_layout()\n", "plt.suptitle('Knowledge Tracing Models Performance Comparison', fontsize=16, fontweight='bold', y=1.02)\n", "plt.show()\n", "\n", "print(\"✅ Performance visualizations created!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Models and Generate Report"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 23:42:19,264 - INFO - Saving ensemble models to ../models/outputs...\n", "2025-07-19 23:42:19,267 - INFO - Ensemble model saved to ../models/outputs/ensemble_weighted_average_model.joblib\n", "2025-07-19 23:42:19,269 - INFO - ✅ Saved weighted_average ensemble to ../models/outputs/ensemble_weighted_average_model.joblib\n", "2025-07-19 23:42:19,271 - INFO - Ensemble model saved to ../models/outputs/ensemble_stacking_model.joblib\n", "2025-07-19 23:42:19,272 - INFO - ✅ Saved stacking ensemble to ../models/outputs/ensemble_stacking_model.joblib\n", "2025-07-19 23:42:19,274 - INFO - Ensemble model saved to ../models/outputs/ensemble_voting_model.joblib\n", "2025-07-19 23:42:19,275 - INFO - ✅ Saved voting ensemble to ../models/outputs/ensemble_voting_model.joblib\n", "2025-07-19 23:42:19,278 - INFO - Ensemble model saved to ../models/outputs/ensemble_model.joblib\n", "2025-07-19 23:42:19,278 - INFO - ✅ Saved best ensemble (stacking) as main ensemble model\n", "2025-07-19 23:42:19,279 - INFO - Generating comparison report...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["💾 Saving ensemble models...\n", "\n", "📋 Generating comparison report...\n", "{'timestamp': '2025-07-19T23:42:19.281478', 'test_sample_size': 'entire_dataset', 'models_evaluated': ['bkt', 'pfa', 'dkt'], 'ensemble_methods': ['weighted_average', 'stacking', 'voting'], 'individual_model_metrics': {'bkt': {'accuracy': 0.6065, 'precision': 0.7253, 'recall': 0.6993, 'f1_score': 0.7121, 'auc_roc': 0.5663}, 'pfa': {'accuracy': 0.6499, 'precision': 0.7552, 'recall': 0.7352, 'f1_score': 0.7451, 'auc_roc': 0.6289}, 'dkt': {'accuracy': 0.6447, 'precision': 0.7566, 'recall': 0.7216, 'f1_score': 0.7387, 'auc_roc': 0.622}}, 'ensemble_metrics': {'weighted_average': {'accuracy': 0.6825175039921385, 'precision': 0.7388396751245421, 'recall': 0.8410414827890556, 'f1_score': 0.7866348842728745, 'auc': 0.6095824961555023}, 'stacking': {'accuracy': 0.712612086967203, 'precision': 0.7351708774597338, 'recall': 0.9175198587819947, 'f1_score': 0.8162856666110462, 'auc': 0.6150008974059177}, 'voting': {'accuracy': 0.48049993858248374, 'precision': 0.7396911519198665, 'recall': 0.39106354810238303, 'f1_score': 0.5116339491916859, 'auc': 0.5380953847135501}}, 'best_model': 'pfa', 'best_ensemble': 'stacking', 'summary': {'total_models_trained': 3, 'total_ensembles_trained': 3, 'best_individual_f1': 0.7451, 'best_ensemble_f1': 0.8162856666110462}}\n", "\n", "✅ Models and reports saved successfully!\n"]}], "source": ["# Save ensemble models\n", "print(\"💾 Saving ensemble models...\")\n", "trainer.save_ensemble_models()\n", "\n", "# Generate comprehensive report\n", "print(\"\\n📋 Generating comparison report...\")\n", "report = trainer.generate_comparison_report()\n", "print(report)\n", "\n", "# # Save report\n", "# trainer.save_report(report)\n", "\n", "print(\"\\n✅ Models and reports saved successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}