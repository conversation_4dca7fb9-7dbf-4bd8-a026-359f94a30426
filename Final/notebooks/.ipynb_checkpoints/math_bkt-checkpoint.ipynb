{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f0a48d31-8eb5-4911-baf2-84c8a2764d7d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:09:37.660719: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["All imports successful!\n"]}], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import json\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Add pipeline to path\n", "sys.path.append('../pipeline')\n", "\n", "# Import pipeline components\n", "from kt_training_pipeline import KnowledgeTracingPipeline\n", "from kt_evaluation import KTEvaluator\n", "from prediction import KTPredictor\n", "\n", "print(\"All imports successful!\")"]}, {"cell_type": "code", "execution_count": 2, "id": "2d7d3782-ab92-4bbe-a216-05c345427b09", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading math dataset...\n", "✅ Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the math dataset\n", "data_path = \"../datasets/math/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading math dataset...\")\n", "df_clean = pd.read_csv(data_path, encoding='latin1')\n", "\n", "print(f\"✅ Dataset loaded successfully!\")\n", "print(f\"Shape: {df_clean.shape}\")\n", "print(f\"Columns: {list(df_clean.columns)}\")\n", "\n", "# Display basic info\n", "# df_clean.info()"]}, {"cell_type": "code", "execution_count": 3, "id": "fddd4be9-a7e2-455b-b6f5-2ef56067b6e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df_clean.columns:\n", "        missing = df_clean[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df_clean)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": 4, "id": "0411ed18-a337-45c7-be25-f085fde33515", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df_clean)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df_clean.columns]\n", "df_clean = df_clean.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": 5, "id": "c35ba78e-90c1-4e72-95b1-5e396e5331fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COMPREHENSIVE SKILLS ANALYSIS\n", "==================================================\n", "Total number of unique skills: 110\n", "\n", "Top 15 skills by number of interactions:\n", "                                          interactions  accuracy  accuracy_std  unique_students  unique_problems\n", "skill_name                                                                                                      \n", "Equation Solving Two or Fewer Steps              24253     0.679         0.467              961             1040\n", "Conversion of Fraction Decimals Percents         18742     0.637         0.481             1225              488\n", "Addition and Subtraction Integers                12741     0.599         0.490             1226              413\n", "Addition and Subtraction Fractions               11334     0.677         0.468             1353              433\n", "Percent Of                                        9497     0.595         0.491             1115              465\n", "Proportion                                        9054     0.641         0.480              756              485\n", "Ordering Fractions                                8539     0.792         0.406              882              464\n", "Equation Solving More Than Two Steps              8115     0.758         0.428              412              419\n", "Probability of Two Distinct Events                7963     0.490         0.500              452              339\n", "Finding Percents                                  7694     0.538         0.499              771              371\n", "Subtraction Whole Numbers                         7669     0.641         0.480              903              242\n", "Probability of a Single Event                     7438     0.742         0.437              939              350\n", "Pattern Finding                                   7343     0.600         0.490              447              554\n", "Absolute Value                                    7340     0.757         0.429             1002              241\n", "Ordering Positive Decimals                        7317     0.750         0.433              942              543\n", "\n", "Top 15 easiest skills (highest accuracy):\n", "                                              interactions  accuracy  unique_students\n", "skill_name                                                                           \n", "Nets of 3D Figures                                     280     0.950              229\n", "Area Parallelogram                                     115     0.922               95\n", "Congruence                                             587     0.894              364\n", "Distributive Property                                   18     0.889                5\n", "Mode                                                  1926     0.876              572\n", "Scatter Plot                                          1859     0.869              354\n", "Area Rectangle                                         495     0.863              215\n", "Area Triangle                                          286     0.857              168\n", "D.4.8-understanding-concept-of-probabilities           456     0.846              202\n", "Volume Rectangular Prism                               926     0.840              345\n", "Fraction Of                                            607     0.830              288\n", "Write Linear Equation from Situation                  1447     0.822              223\n", "Linear Equations                                        89     0.820               41\n", "Slope                                                   89     0.820               41\n", "Choose an Equation from Given Information               89     0.820               41\n", "\n", "Top 15 hardest skills (lowest accuracy):\n", "                                               interactions  accuracy  unique_students\n", "skill_name                                                                            \n", "Reading a Ruler or Scale                                  5     0.000                5\n", "Quadratic Formula to Solve Quadratic Equation            32     0.125               14\n", "Rotations                                               427     0.136              163\n", "Computation with Real Numbers                            21     0.190               21\n", "Solving Systems of Linear Equations                     234     0.192               22\n", "Percent Discount                                         47     0.234               29\n", "Surface Area Cylinder                                   491     0.316              135\n", "Finding Slope From Situation                              9     0.333                2\n", "Percents                                                117     0.333               41\n", "Algebraic Solving                                       389     0.368               88\n", "Reflection                                              459     0.373              176\n", "Rate                                                     91     0.374               39\n", "Algebraic Simplification                                 90     0.400               15\n", "Finding <PERSON><PERSON><PERSON> from Ordered Pairs                          5     0.400                2\n", "Multiplication Whole Numbers                            110     0.436               45\n"]}], "source": ["# Comprehensive skills analysis\n", "print(\"📋 COMPREHENSIVE SKILLS ANALYSIS\")\n", "print(\"=\"*50)\n", "\n", "# Get all unique skills\n", "all_skills = df_clean['skill_name'].unique()\n", "print(f\"Total number of unique skills: {len(all_skills)}\")\n", "\n", "# Skills statistics\n", "skills_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean', 'std'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skills_stats.columns = ['interactions', 'accuracy', 'accuracy_std', 'unique_students', 'unique_problems']\n", "skills_stats = skills_stats.sort_values('interactions', ascending=False)\n", "\n", "print(\"\\nTop 15 skills by number of interactions:\")\n", "print(skills_stats.head(15).to_string())\n", "\n", "print(\"\\nTop 15 easiest skills (highest accuracy):\")\n", "easiest_skills = skills_stats.sort_values('accuracy', ascending=False).head(15)\n", "print(easiest_skills[['interactions', 'accuracy', 'unique_students']].to_string())\n", "\n", "print(\"\\nTop 15 hardest skills (lowest accuracy):\")\n", "hardest_skills = skills_stats.sort_values('accuracy', ascending=True).head(15)\n", "print(hardest_skills[['interactions', 'accuracy', 'unique_students']].to_string())"]}, {"cell_type": "code", "execution_count": 6, "id": "806a7aaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 SKILLS CATEGORIZATION\n", "==============================\n", "Easy skills (≥80% accuracy): 17 skills\n", "Medium skills (60-80% accuracy): 54 skills\n", "Hard skills (<60% accuracy): 39 skills\n", "\n", "High frequency skills (≥1000 interactions): 63 skills\n", "Medium frequency skills (100-1000 interactions): 29 skills\n", "Low frequency skills (<100 interactions): 18 skills\n", "\n", "💾 Skills analysis saved to /reports/math/skills_analysis.csv\n"]}], "source": ["# Skills categorization analysis\n", "print(\"\\n📊 SKILLS CATEGORIZATION\")\n", "print(\"=\"*30)\n", "\n", "# Categorize skills by difficulty\n", "easy_skills = skills_stats[skills_stats['accuracy'] >= 0.8]\n", "medium_skills = skills_stats[(skills_stats['accuracy'] >= 0.6) & (skills_stats['accuracy'] < 0.8)]\n", "hard_skills = skills_stats[skills_stats['accuracy'] < 0.6]\n", "\n", "print(f\"Easy skills (≥80% accuracy): {len(easy_skills)} skills\")\n", "print(f\"Medium skills (60-80% accuracy): {len(medium_skills)} skills\")\n", "print(f\"Hard skills (<60% accuracy): {len(hard_skills)} skills\")\n", "\n", "# Categorize by frequency\n", "high_freq = skills_stats[skills_stats['interactions'] >= 1000]\n", "medium_freq = skills_stats[(skills_stats['interactions'] >= 100) & (skills_stats['interactions'] < 1000)]\n", "low_freq = skills_stats[skills_stats['interactions'] < 100]\n", "\n", "print(f\"\\nHigh frequency skills (≥1000 interactions): {len(high_freq)} skills\")\n", "print(f\"Medium frequency skills (100-1000 interactions): {len(medium_freq)} skills\")\n", "print(f\"Low frequency skills (<100 interactions): {len(low_freq)} skills\")\n", "\n", "# Save skills analysis\n", "os.makedirs('../reports/math', exist_ok=True)\n", "skills_stats.to_csv('../reports/math/skills_analysis.csv')\n", "print(\"\\n💾 Skills analysis saved to /reports/math/skills_analysis.csv\")"]}, {"cell_type": "code", "execution_count": 7, "id": "267baa18", "metadata": {}, "outputs": [], "source": ["#export csv list of all unique skills from df\n", "skills = df_clean['skill_name'].unique()\n", "skills_df = pd.DataFrame(skills, columns=['skill_name'])\n", "skills_df.to_csv('skills_all.csv', index=False)"]}, {"cell_type": "code", "execution_count": 8, "id": "0cedca97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Splitting data into train/test sets...\n", "Training set: 255,487 interactions from 3,320 users\n", "Test set: 70,150 interactions from 831 users\n", "Train accuracy: 0.655\n", "Test accuracy: 0.670\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Training set: 255,487 interactions from 3,320 users\n", "Test set: 70,150 interactions from 831 users\n", "Train accuracy: 0.655\n", "Test accuracy: 0.670\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "# Split data by users to avoid data leakage\n", "print(\"Splitting data into train/test sets...\")\n", "\n", "# Get unique users\n", "unique_users = df_clean['user_id'].unique()\n", "train_users, test_users = train_test_split(unique_users, test_size=0.2, random_state=42)\n", "\n", "# Split data based on users\n", "train_df = df_clean[df_clean['user_id'].isin(train_users)].copy()\n", "test_df = df_clean[df_clean['user_id'].isin(test_users)].copy()\n", "\n", "print(f\"Training set: {len(train_df):,} interactions from {len(train_users):,} users\")\n", "print(f\"Test set: {len(test_df):,} interactions from {len(test_users):,} users\")\n", "print(f\"Train accuracy: {train_df['correct'].mean():.3f}\")\n", "print(f\"Test accuracy: {test_df['correct'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": 9, "id": "b6c9773e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:09:58,732 - INFO - Training knowledge tracing models...\n", "2025-07-19 22:09:58,734 - INFO - Training BKT model...\n", "2025-07-19 22:09:58,735 - INFO - Training BKT model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 INITIALIZING <PERSON><PERSON><PERSON>LEDGE TRACING PIPELINE\n", "==================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:10:37,970 - INFO - BKT training completed\n", "2025-07-19 22:10:37,971 - INFO -   BKT metrics: {'accuracy': 0.8308961922687129, 'precision': 0.8962950138504155, 'recall': 0.9009745910198399, 'f1_score': 0.8986287102933519, 'cv_accuracy': 0.8345547455004272, 'cv_accuracy_std': 0.007571865962454818}\n", "2025-07-19 22:10:37,972 - INFO -   BKT model: BayesianKnowledgeTracer()\n", "2025-07-19 22:10:37,973 - INFO -   BKT model params: {'convergence_threshold': 1e-06, 'learning_rate': 0.01, 'max_iterations': 100, 'p_guess': 0.2, 'p_init': 0.1, 'p_learn': 0.1, 'p_slip': 0.1}\n", "2025-07-19 22:10:37,974 - INFO -   BKT model skills: {'Box and Whisker': {'p_init': 0.654, 'p_learn': 0.211, 'p_guess': 0.1, 'p_slip': 0.05}, 'Circle Graph': {'p_init': 0.382, 'p_learn': 0.178, 'p_guess': 0.1, 'p_slip': 0.082}, 'Histogram as Table or Graph': {'p_init': 0.686, 'p_learn': 0.197, 'p_guess': 0.112, 'p_slip': 0.06}, 'Number Line': {'p_init': 0.533, 'p_learn': 0.153, 'p_guess': 0.136, 'p_slip': 0.05}, 'Scatter Plot': {'p_init': 0.721, 'p_learn': 0.188, 'p_guess': 0.1, 'p_slip': 0.05}, 'Stem and Leaf Plot': {'p_init': 0.581, 'p_learn': 0.187, 'p_guess': 0.1, 'p_slip': 0.071}, 'Table': {'p_init': 0.644, 'p_learn': 0.179, 'p_guess': 0.1, 'p_slip': 0.063}, 'Venn Diagram': {'p_init': 0.531, 'p_learn': 0.152, 'p_guess': 0.1, 'p_slip': 0.083}, 'Mean': {'p_init': 0.432, 'p_learn': 0.222, 'p_guess': 0.1, 'p_slip': 0.05}, 'Median': {'p_init': 0.567, 'p_learn': 0.172, 'p_guess': 0.1, 'p_slip': 0.053}, 'Mode': {'p_init': 0.8, 'p_learn': 0.117, 'p_guess': 0.1, 'p_slip': 0.05}, 'Range': {'p_init': 0.59, 'p_learn': 0.209, 'p_guess': 0.1, 'p_slip': 0.05}, 'Counting Methods': {'p_init': 0.517, 'p_learn': 0.198, 'p_guess': 0.162, 'p_slip': 0.052}, 'Probability of Two Distinct Events': {'p_init': 0.419, 'p_learn': 0.147, 'p_guess': 0.117, 'p_slip': 0.052}, 'Probability of a Single Event': {'p_init': 0.658, 'p_learn': 0.171, 'p_guess': 0.1, 'p_slip': 0.05}, 'Interior Angles Figures with More than 3 Sides': {'p_init': 0.466, 'p_learn': 0.399, 'p_guess': 0.1, 'p_slip': 0.05}, 'Interior Angles Triangle': {'p_init': 0.542, 'p_learn': 0.41, 'p_guess': 0.1, 'p_slip': 0.05}, 'Congruence': {'p_init': 0.707, 'p_learn': 0.31, 'p_guess': 0.125, 'p_slip': 0.05}, 'Complementary and Supplementary Angles': {'p_init': 0.641, 'p_learn': 0.277, 'p_guess': 0.1, 'p_slip': 0.05}, 'Angles on Parallel Lines Cut by a Transversal': {'p_init': 0.704, 'p_learn': 0.05, 'p_guess': 0.1, 'p_slip': 0.05}, 'Pythagorean Theorem': {'p_init': 0.63, 'p_learn': 0.107, 'p_guess': 0.1, 'p_slip': 0.106}, 'Nets of 3D Figures': {'p_init': 0.696, 'p_learn': 0.273, 'p_guess': 0.1, 'p_slip': 0.05}, 'Unit Conversion Within a System': {'p_init': 0.136, 'p_learn': 0.361, 'p_guess': 0.109, 'p_slip': 0.05}, 'Effect of Changing Dimensions of a Shape Prportionally': {'p_init': 0.095, 'p_learn': 0.5, 'p_guess': 0.12, 'p_slip': 0.05}, 'Area Circle': {'p_init': 0.601, 'p_learn': 0.35, 'p_guess': 0.1, 'p_slip': 0.05}, 'Circumference ': {'p_init': 0.743, 'p_learn': 0.182, 'p_guess': 0.1, 'p_slip': 0.093}, 'Perimeter of a Polygon': {'p_init': 0.326, 'p_learn': 0.362, 'p_guess': 0.123, 'p_slip': 0.05}, 'Reading a Ruler or Scale': {'p_init': 0.1, 'p_learn': 0.1, 'p_guess': 0.2, 'p_slip': 0.1}, 'Calculations with Similar Figures': {'p_init': 0.5, 'p_learn': 0.209, 'p_guess': 0.103, 'p_slip': 0.111}, 'Conversion of Fraction Decimals Percents': {'p_init': 0.619, 'p_learn': 0.143, 'p_guess': 0.138, 'p_slip': 0.073}, 'Equivalent Fractions': {'p_init': 0.399, 'p_learn': 0.204, 'p_guess': 0.1, 'p_slip': 0.05}, 'Ordering Positive Decimals': {'p_init': 0.677, 'p_learn': 0.178, 'p_guess': 0.138, 'p_slip': 0.05}, 'Ordering Fractions': {'p_init': 0.718, 'p_learn': 0.127, 'p_guess': 0.1, 'p_slip': 0.05}, 'Ordering Integers': {'p_init': 0.8, 'p_learn': 0.138, 'p_guess': 0.114, 'p_slip': 0.05}, 'Ordering Real Numbers': {'p_init': 0.564, 'p_learn': 0.175, 'p_guess': 0.1, 'p_slip': 0.05}, 'Rounding': {'p_init': 0.484, 'p_learn': 0.337, 'p_guess': 0.157, 'p_slip': 0.05}, 'Addition Whole Numbers': {'p_init': 0.604, 'p_learn': 0.247, 'p_guess': 0.1, 'p_slip': 0.05}, 'Division Fractions': {'p_init': 0.479, 'p_learn': 0.274, 'p_guess': 0.1, 'p_slip': 0.05}, 'Estimation': {'p_init': 0.743, 'p_learn': 0.175, 'p_guess': 0.1, 'p_slip': 0.068}, 'Fraction Of': {'p_init': 0.698, 'p_learn': 0.439, 'p_guess': 0.2, 'p_slip': 0.05}, 'Least Common Multiple': {'p_init': 0.368, 'p_learn': 0.221, 'p_guess': 0.1, 'p_slip': 0.096}, 'Multiplication Fractions': {'p_init': 0.475, 'p_learn': 0.246, 'p_guess': 0.1, 'p_slip': 0.053}, 'Multiplication Whole Numbers': {'p_init': 0.15, 'p_learn': 0.374, 'p_guess': 0.1, 'p_slip': 0.1}, 'Percent Of': {'p_init': 0.375, 'p_learn': 0.236, 'p_guess': 0.1, 'p_slip': 0.076}, 'Subtraction Whole Numbers': {'p_init': 0.566, 'p_learn': 0.225, 'p_guess': 0.1, 'p_slip': 0.051}, 'Square Root': {'p_init': 0.767, 'p_learn': 0.118, 'p_guess': 0.136, 'p_slip': 0.085}, 'Finding Percents': {'p_init': 0.351, 'p_learn': 0.225, 'p_guess': 0.1, 'p_slip': 0.069}, 'Proportion': {'p_init': 0.542, 'p_learn': 0.156, 'p_guess': 0.1, 'p_slip': 0.067}, 'Scale Factor': {'p_init': 0.15, 'p_learn': 0.475, 'p_guess': 0.107, 'p_slip': 0.05}, 'Unit Rate': {'p_init': 0.284, 'p_learn': 0.329, 'p_guess': 0.1, 'p_slip': 0.05}, 'Scientific Notation': {'p_init': 0.574, 'p_learn': 0.192, 'p_guess': 0.1, 'p_slip': 0.05}, 'Divisibility Rules': {'p_init': 0.33, 'p_learn': 0.261, 'p_guess': 0.1, 'p_slip': 0.086}, 'Prime Number': {'p_init': 0.25, 'p_learn': 0.438, 'p_guess': 0.2, 'p_slip': 0.05}, 'Absolute Value': {'p_init': 0.618, 'p_learn': 0.178, 'p_guess': 0.148, 'p_slip': 0.05}, 'Exponents': {'p_init': 0.528, 'p_learn': 0.25, 'p_guess': 0.1, 'p_slip': 0.05}, 'Pattern Finding ': {'p_init': 0.48, 'p_learn': 0.175, 'p_guess': 0.1, 'p_slip': 0.109}, 'D.4.8-understanding-concept-of-probabilities': {'p_init': 0.762, 'p_learn': 0.399, 'p_guess': 0.2, 'p_slip': 0.05}, 'Algebraic Simplification': {'p_init': 0.25, 'p_learn': 0.306, 'p_guess': 0.1, 'p_slip': 0.05}, 'Algebraic Solving': {'p_init': 0.271, 'p_learn': 0.178, 'p_guess': 0.1, 'p_slip': 0.05}, 'Choose an Equation from Given Information': {'p_init': 0.667, 'p_learn': 0.208, 'p_guess': 0.25, 'p_slip': 0.05}, 'Intercept': {'p_init': 0.667, 'p_learn': 0.208, 'p_guess': 0.25, 'p_slip': 0.05}, 'Linear Equations': {'p_init': 0.667, 'p_learn': 0.208, 'p_guess': 0.25, 'p_slip': 0.05}, 'Percent Discount': {'p_init': 0.25, 'p_learn': 0.05, 'p_guess': 0.1, 'p_slip': 0.1}, 'Percents': {'p_init': 0.19, 'p_learn': 0.229, 'p_guess': 0.1, 'p_slip': 0.05}, 'Rate': {'p_init': 0.222, 'p_learn': 0.222, 'p_guess': 0.1, 'p_slip': 0.05}, 'Slope': {'p_init': 0.667, 'p_learn': 0.208, 'p_guess': 0.25, 'p_slip': 0.05}, 'Multiplication and Division Positive Decimals': {'p_init': 0.704, 'p_learn': 0.163, 'p_guess': 0.1, 'p_slip': 0.05}, 'Addition and Subtraction Integers': {'p_init': 0.622, 'p_learn': 0.189, 'p_guess': 0.134, 'p_slip': 0.05}, 'Addition and Subtraction Positive Decimals': {'p_init': 0.416, 'p_learn': 0.247, 'p_guess': 0.106, 'p_slip': 0.054}, 'Multiplication and Division Integers': {'p_init': 0.744, 'p_learn': 0.129, 'p_guess': 0.1, 'p_slip': 0.05}, 'Addition and Subtraction Fractions': {'p_init': 0.501, 'p_learn': 0.205, 'p_guess': 0.1, 'p_slip': 0.066}, 'Reflection': {'p_init': 0.205, 'p_learn': 0.325, 'p_guess': 0.1, 'p_slip': 0.05}, 'Rotations': {'p_init': 0.035, 'p_learn': 0.156, 'p_guess': 0.1, 'p_slip': 0.05}, 'Translations': {'p_init': 0.337, 'p_learn': 0.375, 'p_guess': 0.1, 'p_slip': 0.05}, 'Area Irregular Figure': {'p_init': 0.168, 'p_learn': 0.312, 'p_guess': 0.114, 'p_slip': 0.097}, 'Area Parallelogram': {'p_init': 0.8, 'p_learn': 0.167, 'p_guess': 0.2, 'p_slip': 0.05}, 'Area Rectangle': {'p_init': 0.765, 'p_learn': 0.236, 'p_guess': 0.1, 'p_slip': 0.05}, 'Area Trapezoid': {'p_init': 0.355, 'p_learn': 0.207, 'p_guess': 0.1, 'p_slip': 0.092}, 'Area Triangle': {'p_init': 0.722, 'p_learn': 0.321, 'p_guess': 0.1, 'p_slip': 0.05}, 'Surface Area Cylinder': {'p_init': 0.082, 'p_learn': 0.434, 'p_guess': 0.134, 'p_slip': 0.1}, 'Surface Area Rectangular Prism': {'p_init': 0.356, 'p_learn': 0.365, 'p_guess': 0.1, 'p_slip': 0.05}, 'Volume Cylinder': {'p_init': 0.353, 'p_learn': 0.324, 'p_guess': 0.109, 'p_slip': 0.05}, 'Volume Rectangular Prism': {'p_init': 0.797, 'p_learn': 0.148, 'p_guess': 0.1, 'p_slip': 0.05}, 'Volume Sphere': {'p_init': 0.273, 'p_learn': 0.212, 'p_guess': 0.1, 'p_slip': 0.111}, 'Order of Operations +,-,/,* () positive reals': {'p_init': 0.561, 'p_learn': 0.305, 'p_guess': 0.1, 'p_slip': 0.05}, 'Order of Operations All': {'p_init': 0.414, 'p_learn': 0.27, 'p_guess': 0.1, 'p_slip': 0.05}, 'Equation Solving Two or Fewer Steps': {'p_init': 0.53, 'p_learn': 0.135, 'p_guess': 0.104, 'p_slip': 0.099}, 'Equation Solving More Than Two Steps': {'p_init': 0.531, 'p_learn': 0.124, 'p_guess': 0.1, 'p_slip': 0.087}, 'Angles - Obtuse, Acute, and Right': {'p_init': 0.5, 'p_learn': 0.137, 'p_guess': 0.2, 'p_slip': 0.056}, 'Greatest Common Factor': {'p_init': 0.705, 'p_learn': 0.244, 'p_guess': 0.1, 'p_slip': 0.05}, 'Computation with Real Numbers': {'p_init': 0.1, 'p_learn': 0.1, 'p_guess': 0.2, 'p_slip': 0.1}, 'Write Linear Equation from Ordered Pairs': {'p_init': 0.304, 'p_learn': 0.322, 'p_guess': 0.1, 'p_slip': 0.05}, 'Write Linear Equation from Situation': {'p_init': 0.772, 'p_learn': 0.136, 'p_guess': 0.1, 'p_slip': 0.05}, 'Recognize Linear Pattern': {'p_init': 0.5, 'p_learn': 0.333, 'p_guess': 0.2, 'p_slip': 0.05}, 'Write Linear Equation from Graph': {'p_init': 0.159, 'p_learn': 0.229, 'p_guess': 0.12, 'p_slip': 0.05}, 'Finding Slope From Situation': {'p_init': 0.01, 'p_learn': 0.05, 'p_guess': 0.1, 'p_slip': 0.1}, 'Finding Slope From Equation': {'p_init': 0.75, 'p_learn': 0.056, 'p_guess': 0.2, 'p_slip': 0.05}, 'Finding Slope from Ordered Pairs': {'p_init': 0.1, 'p_learn': 0.1, 'p_guess': 0.2, 'p_slip': 0.1}, 'Distributive Property': {'p_init': 0.8, 'p_learn': 0.083, 'p_guess': 0.2, 'p_slip': 0.05}, 'Midpoint': {'p_init': 0.01, 'p_learn': 0.1, 'p_guess': 0.1, 'p_slip': 0.1}, 'Polynomial Factors': {'p_init': 0.3, 'p_learn': 0.082, 'p_guess': 0.1, 'p_slip': 0.167}, 'Recognize Quadratic Pattern': {'p_init': 0.8, 'p_learn': 0.05, 'p_guess': 0.2, 'p_slip': 0.1}, 'Solving Systems of Linear Equations': {'p_init': 0.077, 'p_learn': 0.251, 'p_guess': 0.1, 'p_slip': 0.1}, 'Quadratic Formula to Solve Quadratic Equation': {'p_init': 0.125, 'p_learn': 0.05, 'p_guess': 0.1, 'p_slip': 0.1}, 'Parts of a Polyomial, Terms, Coefficient, Monomial, Exponent, Variable': {'p_init': 0.25, 'p_learn': 0.219, 'p_guess': 0.2, 'p_slip': 0.071}, 'Interpreting Coordinate Graphs ': {'p_init': 0.1, 'p_learn': 0.1, 'p_guess': 0.2, 'p_slip': 0.1}, 'Solving for a variable': {'p_init': 0.751, 'p_learn': 0.254, 'p_guess': 0.127, 'p_slip': 0.05}, 'Simplifying Expressions positive exponents': {'p_init': 0.01, 'p_learn': 0.05, 'p_guess': 0.2, 'p_slip': 0.1}, 'Solving Inequalities': {'p_init': 0.533, 'p_learn': 0.153, 'p_guess': 0.136, 'p_slip': 0.05}, 'Solving Systems of Linear Equations by Graphing': {'p_init': 0.333, 'p_learn': 0.174, 'p_guess': 0.1, 'p_slip': 0.083}}\n", "2025-07-19 22:10:37,976 - INFO -   BKT model students: {}\n", "2025-07-19 22:10:37,977 - INFO - Model training completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Individual models training completed\n", "Training completed in 39.25 seconds\n", "Each skill now has unique parameters estimated from student data!\n"]}], "source": ["# Initialize the knowledge tracing pipeline\n", "print(\"🚀 INITIALIZING K<PERSON><PERSON>LEDGE TRACING PIPELINE\")\n", "print(\"=\"*50)\n", "\n", "pipeline = KnowledgeTracingPipeline()\n", "\n", "import time\n", "start_time = time.time()\n", "\n", "models, metrics = pipeline.train_models(train_df, test_df, models_to_train=['bkt'])\n", "print(\"Individual models training completed\")\n", "\n", "training_time = time.time() - start_time\n", "print(f\"Training completed in {training_time:.2f} seconds\")\n", "# print(f\"Model trained on {len(models['bkt'].skill_params)} skills\")\n", "print(f\"Each skill now has unique parameters estimated from student data!\")"]}, {"cell_type": "code", "execution_count": 10, "id": "35ce2854", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 22:12:18,553 - INFO - Saving models to ../models/outputs...\n", "2025-07-19 22:12:18,574 - INFO - Saved bkt model to ../models/outputs/bkt_model.joblib\n", "2025-07-19 22:12:18,575 - INFO - Saved metrics to ../models/outputs/model_metrics.json\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💾 Saving models...\n", "✅ Models saved successfully\n"]}], "source": ["# Save models\n", "print(\"\\n💾 Saving models...\")\n", "# os.makedirs('App/Training/pipeline/outputs/math', exist_ok=True)\n", "OUTPUT_DIR='../models/outputs'\n", "pipeline.save_models(OUTPUT_DIR)\n", "print(\"✅ Models saved successfully\")"]}, {"cell_type": "markdown", "id": "4294bdc9", "metadata": {}, "source": ["## 📊 Comprehensive Model Metrics\n", "\n", "Display detailed metrics for the trained BKT model including accuracy, precision, recall, F1-score, and AUC-ROC."]}, {"cell_type": "code", "execution_count": 12, "id": "9c650512-7c0f-4ee8-b48d-056e8123d17c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "📊 COMPREHENSIVE MODEL METRICS\n", "============================================================\n", "\n", "=== MODEL EVALUATION ===\n", "  Accuracy:  0.8309\n", "  Precision: 0.8963\n", "  Recall:    0.9010\n", "  F1-Score:  0.8986\n", "  AUC-ROC:   0.7457\n", "\n", "📈 Additional Statistics:\n", "  Test Samples: 6,907\n", "  Mean Prediction: 0.7800\n", "  Actual Success Rate: 0.8319\n", "  Prediction Std: 0.2226\n"]}], "source": ["from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score\n", "\n", "# Display comprehensive metrics from the trained model\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"📊 COMPREHENSIVE MODEL METRICS\")\n", "print(\"=\"*60)\n", "\n", "# Get the trained BKT model\n", "bkt_model = pipeline.models['bkt']\n", "\n", "# Evaluate model performance on test data\n", "print(\"\\n=== MODEL EVALUATION ===\")\n", "\n", "# Simple evaluation: predict probability for each test interaction\n", "test_predictions = []\n", "test_actuals = []\n", "\n", "# Group test data by user and skill for sequential prediction\n", "for (user_id, skill), group in test_df.groupby(['user_id', 'skill_name']):\n", "    if skill in models['bkt'].skill_params:\n", "        user_history = group['correct'].tolist()\n", "        if len(user_history) > 1:\n", "            # Use first n-1 responses to predict the last one\n", "            history = user_history[:-1]\n", "            actual = user_history[-1]\n", "            \n", "            probas = models['bkt'].predict_proba(history, skill)\n", "            if probas:\n", "                predicted_prob = probas[-1]  # Last probability\n", "                test_predictions.append(predicted_prob)\n", "                test_actuals.append(actual)\n", "\n", "if test_predictions:\n", "    test_predictions = np.array(test_predictions)\n", "    test_actuals = np.array(test_actuals)\n", "    \n", "    # Convert probabilities to binary predictions\n", "    test_pred_binary = [1 if p > 0.5 else 0 for p in test_predictions]\n", "    \n", "    # Calculate metrics\n", "    accuracy = accuracy_score(test_actuals, test_pred_binary)\n", "    precision = precision_score(test_actuals, test_pred_binary, zero_division=0)\n", "    recall = recall_score(test_actuals, test_pred_binary, zero_division=0)\n", "    f1 = f1_score(test_actuals, test_pred_binary, zero_division=0)\n", "    auc_roc = roc_auc_score(test_actuals, test_predictions) if len(set(test_actuals)) > 1 else 0.0\n", "    \n", "    print(f\"  Accuracy:  {accuracy:.4f}\")\n", "    print(f\"  Precision: {precision:.4f}\")\n", "    print(f\"  Recall:    {recall:.4f}\")\n", "    print(f\"  F1-Score:  {f1:.4f}\")\n", "    print(f\"  AUC-ROC:   {auc_roc:.4f}\")\n", "    \n", "    print(f\"\\n📈 Additional Statistics:\")\n", "    print(f\"  Test Samples: {len(test_predictions):,}\")\n", "    print(f\"  Mean Prediction: {np.mean(test_predictions):.4f}\")\n", "    print(f\"  Actual Success Rate: {np.mean(test_actuals):.4f}\")\n", "    print(f\"  Prediction Std: {np.std(test_predictions):.4f}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "6a517d0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== TRAINING STATISTICS ===\n", "Training time: 39.25 seconds\n", "Number of skills trained: 110\n", "Training data size: 255,487 interactions\n", "Test data size: 70,150 interactions\n", "\n", "=== SKILL-SPECIFIC PARAMETERS TABLE ===\n", "Skill                               Interactions Accuracy P(Init)  P(Learn) P(Guess) P(Slip) \n", "---------------------------------------------------------------------------------------------------------\n", "Equation Solving Two or Fewer Step  24253    0.679    0.530    0.135    0.104    0.099   \n", "Conversion of Fraction Decimals Pe  18742    0.637    0.619    0.143    0.138    0.073   \n", "Addition and Subtraction Integers   12741    0.599    0.622    0.189    0.134    0.050   \n", "Addition and Subtraction Fractions  11334    0.677    0.501    0.205    0.100    0.066   \n", "Percent Of                          9497     0.595    0.375    0.236    0.100    0.076   \n", "Proportion                          9054     0.641    0.542    0.156    0.100    0.067   \n", "Ordering Fractions                  8539     0.792    0.718    0.127    0.100    0.050   \n", "Equation Solving More Than Two Ste  8115     0.758    0.531    0.124    0.100    0.087   \n", "Probability of Two Distinct Events  7963     0.490    0.419    0.147    0.117    0.052   \n", "Finding Percents                    7694     0.538    0.351    0.225    0.100    0.069   \n", "Subtraction Whole Numbers           7669     0.641    0.566    0.225    0.100    0.051   \n", "Probability of a Single Event       7438     0.742    0.658    0.171    0.100    0.050   \n", "Pattern Finding                     7343     0.600    0.480    0.175    0.100    0.109   \n", "Absolute Value                      7340     0.757    0.618    0.178    0.148    0.050   \n", "Ordering Positive Decimals          7317     0.750    0.677    0.178    0.138    0.050   \n", "\n", "=== MODEL PARAMETERS FOR TOP 10 SKILLS ===\n", "\n", "Equation Solving Two or Fewer Steps:\n", "  P(Init): 0.530\n", "  P(Learn): 0.135\n", "  P(Guess): 0.104\n", "  P(Slip): 0.099\n", "\n", "Conversion of Fraction Decimals Percents:\n", "  P(Init): 0.619\n", "  P(Learn): 0.143\n", "  P(Guess): 0.138\n", "  P(Slip): 0.073\n", "\n", "Addition and Subtraction Integers:\n", "  P(Init): 0.622\n", "  P(Learn): 0.189\n", "  P(Guess): 0.134\n", "  P(Slip): 0.050\n", "\n", "Addition and Subtraction Fractions:\n", "  P(Init): 0.501\n", "  P(Learn): 0.205\n", "  P(Guess): 0.100\n", "  P(Slip): 0.066\n", "\n", "Percent Of:\n", "  P(Init): 0.375\n", "  P(Learn): 0.236\n", "  P(Guess): 0.100\n", "  P(Slip): 0.076\n", "\n", "Proportion:\n", "  P(Init): 0.542\n", "  P(Learn): 0.156\n", "  P(Guess): 0.100\n", "  P(Slip): 0.067\n", "\n", "Ordering Fractions:\n", "  P(Init): 0.718\n", "  P(Learn): 0.127\n", "  P(Guess): 0.100\n", "  P(Slip): 0.050\n", "\n", "Equation Solving More Than Two Steps:\n", "  P(Init): 0.531\n", "  P(Learn): 0.124\n", "  P(Guess): 0.100\n", "  P(Slip): 0.087\n", "\n", "Probability of Two Distinct Events:\n", "  P(Init): 0.419\n", "  P(Learn): 0.147\n", "  P(Guess): 0.117\n", "  P(Slip): 0.052\n", "\n", "Finding Percents:\n", "  P(Init): 0.351\n", "  P(Learn): 0.225\n", "  P(Guess): 0.100\n", "  P(Slip): 0.069\n"]}], "source": ["# Display training statistics\n", "print(\"=== TRAINING STATISTICS ===\")\n", "print(f\"Training time: {training_time:.2f} seconds\")\n", "print(f\"Number of skills trained: {len(models['bkt'].skill_params)}\")\n", "print(f\"Training data size: {len(train_df):,} interactions\")\n", "print(f\"Test data size: {len(test_df):,} interactions\")\n", "\n", "\n", "\n", "# Show skill-specific parameters in a formatted table\n", "print(\"\\n=== SKILL-SPECIFIC PARAMETERS TABLE ===\")\n", "print(f\"{'Skill':<35} {'Interactions':<8} {'Accuracy':<8} {'P(Init)':<8} {'P(Learn)':<8} {'P(Guess)':<8} {'P(Slip)':<8}\")\n", "print(\"-\" * 105)\n", "\n", "top_skills = skills_stats.head(15)\n", "top_skills.head(5)\n", "for skill in top_skills.index:\n", "    if skill in models['bkt'].skill_params:\n", "        params = models['bkt'].skill_params[skill]\n", "        stats = skills_stats.loc[skill]\n", "        print(f\"{skill[:34]:<35} {stats['interactions']:<8.0f} {stats['accuracy']:<8.3f} \"\n", "              f\"{params['p_init']:<8.3f} {params['p_learn']:<8.3f} \"\n", "              f\"{params['p_guess']:<8.3f} {params['p_slip']:<8.3f}\")\n", "        \n", "# Show parameters for top skills\n", "print(\"\\n=== MODEL PARAMETERS FOR TOP 10 SKILLS ===\")\n", "\n", "top_skills_list = skills_stats.head(10).index.tolist()\n", "for skill in top_skills_list:\n", "    if skill in models['bkt'].skill_params:\n", "        params = models['bkt'].skill_params[skill]\n", "        \n", "        print(f\"\\n{skill}:\")\n", "        print(f\"  P(Init): {params['p_init']:.3f}\")\n", "        print(f\"  P(Learn): {params['p_learn']:.3f}\")\n", "        print(f\"  P(Guess): {params['p_guess']:.3f}\")\n", "        print(f\"  P(Slip): {params['p_slip']:.3f}\")\n"]}, {"cell_type": "code", "execution_count": 14, "id": "7c00c1c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💾 Saving Skills parameters to ../models/outputs/bkt_skills_parameters.csv\n"]}], "source": ["\n", "\n", "top_skills_list_df=[]\n", "top_skills_list = skills_stats.index.tolist()\n", "for skill in top_skills_list:\n", "    if skill in models['bkt'].skill_params:\n", "        params = models['bkt'].skill_params[skill]\n", "        top_skills_list_df.append([skill, params['p_init'], params['p_learn'], params['p_guess'], params['p_slip']])\n", "\n", "print(f\"\\n💾 Saving Skills parameters to {OUTPUT_DIR}/bkt_skills_parameters.csv\")\n", "#convert top_skills_list_df to dataframe with column names \n", "# column_names=['skill_name', 'p_init', 'p_learn', 'p_guess', 'p_slip']\n", "# top_skills_list_df = pd.DataFrame(top_skills_list_df, columns=column_names)\n", "\n", "top_skills_list_df = pd.DataFrame(top_skills_list_df,columns=['skill_name', 'p_init', 'p_learn', 'p_guess', 'p_slip'])\n", "\n", "#save to TSV file\n", "top_skills_list_df.to_csv(OUTPUT_DIR+'/bkt_skills_parameters.csv', sep='\\t', index=False)"]}, {"cell_type": "code", "execution_count": 24, "id": "36ad2b08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PARAMETER DISTRIBUTION ANALYSIS ===\n"]}, {"data": {"image/png": "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***********************************************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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Parameter Summary Statistics:\n", "p_init: mean=0.473, std=0.228, min=0.010, max=0.800\n", "p_learn: mean=0.218, std=0.101, min=0.050, max=0.500\n", "p_guess: mean=0.125, std=0.042, min=0.100, max=0.250\n", "p_slip: mean=0.065, std=0.023, min=0.050, max=0.167\n"]}], "source": ["# Visualize parameter distributions across all skills\n", "print(\"\\n=== PARAMETER DISTRIBUTION ANALYSIS ===\")\n", "\n", "# Extract parameters for all skills\n", "params_data = {\n", "    'p_init': [params['p_init'] for params in models['bkt'].skill_params.values()],\n", "    'p_learn': [params['p_learn'] for params in models['bkt'].skill_params.values()],\n", "    'p_guess': [params['p_guess'] for params in models['bkt'].skill_params.values()],\n", "    'p_slip': [params['p_slip'] for params in models['bkt'].skill_params.values()]\n", "}\n", "\n", "# Create visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Plot distributions\n", "axes[0,0].hist(params_data['p_init'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0,0].set_title('Distribution of P(Init) - Initial Knowledge')\n", "axes[0,0].set_xlabel('P(Init)')\n", "axes[0,0].set_ylabel('Number of Skills')\n", "axes[0,0].axvline(np.mean(params_data['p_init']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_init\"]):.3f}')\n", "axes[0,0].legend()\n", "\n", "axes[0,1].hist(params_data['p_learn'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')\n", "axes[0,1].set_title('Distribution of P(Learn) - Learning Rate')\n", "axes[0,1].set_xlabel('P(Learn)')\n", "axes[0,1].set_ylabel('Number of Skills')\n", "axes[0,1].axvline(np.mean(params_data['p_learn']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_learn\"]):.3f}')\n", "axes[0,1].legend()\n", "\n", "axes[1,0].hist(params_data['p_guess'], bins=20, alpha=0.7, color='orange', edgecolor='black')\n", "axes[1,0].set_title('Distribution of P(Guess) - Guessing Probability')\n", "axes[1,0].set_xlabel('<PERSON>(Guess)')\n", "axes[1,0].set_ylabel('Number of Skills')\n", "axes[1,0].axvline(np.mean(params_data['p_guess']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_guess\"]):.3f}')\n", "axes[1,0].legend()\n", "\n", "axes[1,1].hist(params_data['p_slip'], bins=20, alpha=0.7, color='pink', edgecolor='black')\n", "axes[1,1].set_title('Distribution of P(Slip) - Slip Probability')\n", "axes[1,1].set_xlabel('P(Slip)')\n", "axes[1,1].set_ylabel('Number of Skills')\n", "axes[1,1].axvline(np.mean(params_data['p_slip']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_slip\"]):.3f}')\n", "axes[1,1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "fig.savefig(f'../reports/bkt_fig.png')\n", "\n", "# Print summary statistics\n", "print(\"\\nParameter Summary Statistics:\")\n", "for param_name, values in params_data.items():\n", "    print(f\"{param_name}: mean={np.mean(values):.3f}, std={np.std(values):.3f}, \"\n", "          f\"min={np.min(values):.3f}, max={np.max(values):.3f}\")"]}, {"cell_type": "code", "execution_count": 16, "id": "4a1df17b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SKILL COMPARISON BY CHARACTERISTICS ===\n", "\n", "🟢 EASIEST SKILLS (Highest P(Init) - Students often know these initially):\n", "  1. Mode: P(Init)=0.800, P(Learn)=0.117\n", "  2. Ordering Integers: P(Init)=0.800, P(Learn)=0.138\n", "  3. Area Parallelogram: P(Init)=0.800, P(Learn)=0.167\n", "  4. Distributive Property: P(Init)=0.800, P(Learn)=0.083\n", "  5. Recognize Quadratic Pattern: P(Init)=0.800, P(Learn)=0.050\n", "\n", "🔴 HARDEST SKILLS (Lowest P(Init) - Students rarely know these initially):\n", "  1. Finding Slope From Situation: P(Init)=0.010, P(Learn)=0.050\n", "  2. Midpoint: P(Init)=0.010, P(Learn)=0.100\n", "  3. Simplifying Expressions positive exponents: P(Init)=0.010, P(Learn)=0.050\n", "  4. Rotations: P(Init)=0.035, P(Learn)=0.156\n", "  5. Solving Systems of Linear Equations: P(Init)=0.077, P(Learn)=0.251\n", "\n", "🚀 FASTEST LEARNING SKILLS (Highest P(Learn) - Quick to master):\n", "  1. Effect of Changing Dimensions of a Shape Prportionally: P(Learn)=0.500, P(Init)=0.095\n", "  2. Scale Factor: P(Learn)=0.475, P(Init)=0.150\n", "  3. Fraction Of: P(Learn)=0.439, P(Init)=0.698\n", "  4. Prime Number: P(Learn)=0.438, P(Init)=0.250\n", "  5. Surface Area Cylinder: P(Learn)=0.434, P(Init)=0.082\n", "\n", "🐌 SLOWEST LEARNING SKILLS (Lowest P(Learn) - Take time to master):\n", "  1. <PERSON><PERSON> on Parallel Lines Cut by a Transversal: P(Learn)=0.050, P(Init)=0.704\n", "  2. Percent Discount: P(Learn)=0.050, P(Init)=0.250\n", "  3. Finding Slope From Situation: P(Learn)=0.050, P(Init)=0.010\n", "  4. Recognize Quadratic Pattern: P(Learn)=0.050, P(Init)=0.800\n", "  5. Quadratic Formula to Solve Quadratic Equation: P(Learn)=0.050, P(Init)=0.125\n"]}], "source": ["# Compare skills with different characteristics\n", "print(\"\\n=== SKILL COMPARISON BY CHARACTERISTICS ===\")\n", "\n", "# Find skills with different parameter patterns\n", "all_skills_params = [(skill, params) for skill, params in models['bkt'].skill_params.items()]\n", "\n", "# Sort by different criteria\n", "easiest_skills = sorted(all_skills_params, key=lambda x: x[1]['p_init'], reverse=True)[:5]\n", "hardest_skills = sorted(all_skills_params, key=lambda x: x[1]['p_init'])[:5]\n", "fastest_learning = sorted(all_skills_params, key=lambda x: x[1]['p_learn'], reverse=True)[:5]\n", "slowest_learning = sorted(all_skills_params, key=lambda x: x[1]['p_learn'])[:5]\n", "\n", "print(\"\\n🟢 EASIEST SKILLS (Highest P(Init) - Students often know these initially):\")\n", "for i, (skill, params) in enumerate(easiest_skills, 1):\n", "    print(f\"  {i}. {skill}: P(Init)={params['p_init']:.3f}, P(Learn)={params['p_learn']:.3f}\")\n", "\n", "print(\"\\n🔴 HARDEST SKILLS (Lowest P(Init) - Students rarely know these initially):\")\n", "for i, (skill, params) in enumerate(hardest_skills, 1):\n", "    print(f\"  {i}. {skill}: P(Init)={params['p_init']:.3f}, P(Learn)={params['p_learn']:.3f}\")\n", "\n", "print(\"\\n🚀 FASTEST LEARNING SKILLS (Highest P(Learn) - Quick to master):\")\n", "for i, (skill, params) in enumerate(fastest_learning, 1):\n", "    print(f\"  {i}. {skill}: P(Learn)={params['p_learn']:.3f}, P(Init)={params['p_init']:.3f}\")\n", "\n", "print(\"\\n🐌 SLOWEST LEARNING SKILLS (Lowest P(Learn) - Take time to master):\")\n", "for i, (skill, params) in enumerate(slowest_learning, 1):\n", "    print(f\"  {i}. {skill}: P(Learn)={params['p_learn']:.3f}, P(Init)={params['p_init']:.3f}\")"]}, {"cell_type": "markdown", "id": "28f402d9", "metadata": {}, "source": ["##  Run Prediction Examples\n", "\n", "Demonstrate the model with various prediction scenarios."]}, {"cell_type": "code", "execution_count": 17, "id": "b77ecb6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PREDICTION EXAMPLES ===\n", "\n", "Example 1: Struggling Student\n", "Skill: <PERSON><PERSON> Finding \n", "Response history: [0, 0, 0, 1, 0, 1]\n", "Predicted probabilities: ['0.480', '0.304', '0.265', '0.259', '0.689', '0.409']\n", "Final knowledge probability: 0.409\n"]}], "source": ["# Example 1: Prediction for a struggling student\n", "print(\"=== PREDICTION EXAMPLES ===\")\n", "print(\"\\nExample 1: Struggling Student\")\n", "skill_example = list(models['bkt'].skill_params.keys())[55]  # Use first available skill\n", "struggling_history = [0, 0, 0, 1, 0, 1]  # Mostly incorrect responses\n", "\n", "probas = models['bkt'].predict_proba(struggling_history, skill_example)\n", "print(f\"Skill: {skill_example}\")\n", "print(f\"Response history: {struggling_history}\")\n", "print(f\"Predicted probabilities: {[f'{p:.3f}' for p in probas]}\")\n", "print(f\"Final knowledge probability: {probas[-1]:.3f}\")"]}, {"cell_type": "code", "execution_count": 18, "id": "422d6c91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 2: Successful Student\n", "Skill: <PERSON><PERSON> Finding \n", "Response history: [1, 1, 0, 1, 1, 1]\n", "Predicted probabilities: ['0.480', '0.820', '0.884', '0.846', '0.887', '0.891']\n", "Final knowledge probability: 0.891\n"]}], "source": ["# Example 2: Prediction for a successful student\n", "print(\"\\nExample 2: Successful Student\")\n", "successful_history = [1, 1, 0, 1, 1, 1]  # Mostly correct responses\n", "\n", "probas = models['bkt'].predict_proba(successful_history, skill_example)\n", "print(f\"Skill: {skill_example}\")\n", "print(f\"Response history: {successful_history}\")\n", "print(f\"Predicted probabilities: {[f'{p:.3f}' for p in probas]}\")\n", "print(f\"Final knowledge probability: {probas[-1]:.3f}\")"]}, {"cell_type": "code", "execution_count": 26, "id": "64fbe43d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 3: Learning Progression Visualization\n"]}, {"data": {"image/png": "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***************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Example 3: Learning progression visualization\n", "print(\"\\nExample 3: Learning Progression Visualization\")\n", "\n", "# Create different learning scenarios\n", "scenarios = {\n", "    'Quick Learner': [0, 0, 1, 1, 1, 1, 1, 1],\n", "    'Slow Learner': [0, 0, 1, 0, 1, 0, 1, 1],\n", "    'Inconsistent': [1, 0, 1, 0, 1, 0, 1, 1],\n", "    'Expert': [1, 1, 1, 1, 1, 1, 1, 1]\n", "}\n", "\n", "plt.figure(figsize=(12, 8))\n", "for scenario_name, history in scenarios.items():\n", "    probas = models['bkt'].predict_proba(history, skill_example)\n", "    plt.plot(range(1, len(probas) + 1), probas, marker='o', label=scenario_name, linewidth=2)\n", "\n", "plt.xlabel('Attempt Number')\n", "plt.ylabel('Probability of Knowing Skill')\n", "plt.title(f'Learning Progression for Skill: {skill_example}')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.ylim(0, 1)\n", "\n", "\n", "# Save the figure BEFORE showing it\n", "plt.savefig(f'../reports/bkt_progress.png', bbox_inches='tight', dpi=300)  # Added tight layout \n", "\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "b6038e4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 4: Interactive Prediction Function\n", "Prediction result:\n", "  skill: Range\n", "  history: [0, 1, 0, 1, 1]\n", "  knowledge_progression: ['0.602', '0.327', '0.800', '0.416', '0.848']\n", "  current_knowledge_probability: 0.848\n", "  next_response_probability: 0.848\n", "  recommendation: <PERSON><PERSON> mastered\n"]}], "source": ["# Example 4: Interactive prediction function\n", "def predict_next_response(user_history, skill_name, model=models['bkt']):\n", "    \"\"\"\n", "    Predict the probability of correct response for the next attempt\n", "    \n", "    Args:\n", "        user_history: List of 0s and 1s representing incorrect/correct responses\n", "        skill_name: Name of the skill\n", "        model: Trained BKT model\n", "    \n", "    Returns:\n", "        Dictionary with prediction details\n", "    \"\"\"\n", "    if skill_name not in model.skill_params:\n", "        return {\"error\": f\"Skill '{skill_name}' not found in trained model\"}\n", "    \n", "    probas = model.predict_proba(user_history, skill_name)\n", "    \n", "    if not probas:\n", "        return {\"error\": \"Could not generate predictions\"}\n", "    \n", "    current_knowledge = probas[-1]\n", "    \n", "    return {\n", "        \"skill\": skill_name,\n", "        \"history\": user_history,\n", "        \"knowledge_progression\": probas,\n", "        \"current_knowledge_probability\": current_knowledge,\n", "        \"next_response_probability\": current_knowledge,\n", "        \"recommendation\": \"Continue practicing\" if current_knowledge < 0.8 else \"Skill mastered\"\n", "    }\n", "\n", "# Test the function\n", "print(\"\\nExample 4: Interactive Prediction Function\")\n", "test_history = [0, 1, 0, 1, 1]\n", "skill_example = list(models['bkt'].skill_params.keys())[11]\n", "result = predict_next_response(test_history, skill_example)\n", "\n", "print(f\"Prediction result:\")\n", "for key, value in result.items():\n", "    if key == 'knowledge_progression':\n", "        print(f\"  {key}: {[f'{p:.3f}' for p in value]}\")\n", "    elif isinstance(value, float):\n", "        print(f\"  {key}: {value:.3f}\")\n", "    else:\n", "        print(f\"  {key}: {value}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}