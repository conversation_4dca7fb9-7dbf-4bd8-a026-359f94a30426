{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f0a48d31-8eb5-4911-baf2-84c8a2764d7d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 12:16:33.131331: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ All imports successful!\n", "Notebook started at: 2025-07-19 12:16:36.970019\n"]}], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import json\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Add pipeline to path\n", "sys.path.append('../pipeline')\n", "\n", "# Import pipeline components\n", "from kt_training_pipeline import KnowledgeTracingPipeline\n", "from kt_evaluation import KTEvaluator\n", "from prediction import KTPredictor\n", "\n", "print(\"✅ All imports successful!\")\n", "print(f\"Notebook started at: {datetime.now()}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "2d7d3782-ab92-4bbe-a216-05c345427b09", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading math dataset...\n", "✅ Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the math dataset\n", "data_path = \"../datasets/math/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading math dataset...\")\n", "df_clean = pd.read_csv(data_path, encoding='latin1')\n", "\n", "print(f\"✅ Dataset loaded successfully!\")\n", "print(f\"Shape: {df_clean.shape}\")\n", "print(f\"Columns: {list(df_clean.columns)}\")\n", "\n", "# Display basic info\n", "# df_clean.info()"]}, {"cell_type": "code", "execution_count": 3, "id": "fddd4be9-a7e2-455b-b6f5-2ef56067b6e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df_clean.columns:\n", "        missing = df_clean[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df_clean)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": 4, "id": "0411ed18-a337-45c7-be25-f085fde33515", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df_clean)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df_clean.columns]\n", "df_clean = df_clean.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": 5, "id": "84f0d662-babb-4291-9a8a-37d36ffb8931", "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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", "text/plain": ["<Figure size 2000x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📊 EDA plots saved to App/Training/plots/math/eda_analysis.png\n"]}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create comprehensive EDA plots\n", "fig, axes = plt.subplots(2, 3, figsize=(20, 12))\n", "fig.suptitle('Math Dataset Exploratory Data Analysis', fontsize=16)\n", "\n", "# 1. Skill frequency distribution\n", "skill_counts = df_clean['skill_name'].value_counts().head(20)\n", "axes[0, 0].barh(range(len(skill_counts)), skill_counts.values)\n", "axes[0, 0].set_yticks(range(len(skill_counts)))\n", "axes[0, 0].set_yticklabels(skill_counts.index, fontsize=8)\n", "axes[0, 0].set_title('Top 20 Skills by Frequency')\n", "axes[0, 0].set_xlabel('Number of Interactions')\n", "\n", "# 2. Accuracy distribution by skill\n", "skill_accuracy = df_clean.groupby('skill_name')['correct'].mean().sort_values(ascending=False).head(20)\n", "axes[0, 1].barh(range(len(skill_accuracy)), skill_accuracy.values)\n", "axes[0, 1].set_yticks(range(len(skill_accuracy)))\n", "axes[0, 1].set_yticklabels(skill_accuracy.index, fontsize=8)\n", "axes[0, 1].set_title('Top 20 Skills by Accuracy')\n", "axes[0, 1].set_xlabel('Accuracy Rate')\n", "\n", "# 3. Student sequence length distribution\n", "seq_lengths = df_clean.groupby('user_id').size()\n", "axes[0, 2].hist(seq_lengths, bins=50, alpha=0.7, edgecolor='black')\n", "axes[0, 2].set_title('Distribution of Student Sequence Lengths')\n", "axes[0, 2].set_xlabel('Number of Interactions per Student')\n", "axes[0, 2].set_ylabel('Number of Students')\n", "\n", "# 4. Overall accuracy distribution\n", "axes[1, 0].hist(df_clean['correct'], bins=2, alpha=0.7, edgecolor='black')\n", "axes[1, 0].set_title('Overall Accuracy Distribution')\n", "axes[1, 0].set_xlabel('Correct (0=Incorrect, 1=Correct)')\n", "axes[1, 0].set_ylabel('Number of Interactions')\n", "axes[1, 0].set_xticks([0, 1])\n", "\n", "# 5. Student accuracy distribution\n", "student_accuracy = df_clean.groupby('user_id')['correct'].mean()\n", "axes[1, 1].hist(student_accuracy, bins=30, alpha=0.7, edgecolor='black')\n", "axes[1, 1].set_title('Student Accuracy Distribution')\n", "axes[1, 1].set_xlabel('Student Accuracy Rate')\n", "axes[1, 1].set_ylabel('Number of Students')\n", "\n", "# 6. Problem difficulty distribution\n", "problem_difficulty = df_clean.groupby('problem_id')['correct'].mean()\n", "axes[1, 2].hist(problem_difficulty, bins=30, alpha=0.7, edgecolor='black')\n", "axes[1, 2].set_title('Problem Difficulty Distribution')\n", "axes[1, 2].set_xlabel('Problem Accuracy Rate (1-difficulty)')\n", "axes[1, 2].set_ylabel('Number of Problems')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Save the plot\n", "os.makedirs('App/Training/plots/math', exist_ok=True)\n", "plt.savefig('App/Training/plots/math/eda_analysis.png', dpi=300, bbox_inches='tight')\n", "print(\"📊 EDA plots saved to App/Training/plots/math/eda_analysis.png\")"]}, {"cell_type": "code", "execution_count": 6, "id": "c35ba78e-90c1-4e72-95b1-5e396e5331fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COMPREHENSIVE SKILLS ANALYSIS\n", "==================================================\n", "Total number of unique skills: 110\n", "\n", "Top 15 skills by number of interactions:\n", "                                          interactions  accuracy  accuracy_std  unique_students  unique_problems\n", "skill_name                                                                                                      \n", "Equation Solving Two or Fewer Steps              24253     0.679         0.467              961             1040\n", "Conversion of Fraction Decimals Percents         18742     0.637         0.481             1225              488\n", "Addition and Subtraction Integers                12741     0.599         0.490             1226              413\n", "Addition and Subtraction Fractions               11334     0.677         0.468             1353              433\n", "Percent Of                                        9497     0.595         0.491             1115              465\n", "Proportion                                        9054     0.641         0.480              756              485\n", "Ordering Fractions                                8539     0.792         0.406              882              464\n", "Equation Solving More Than Two Steps              8115     0.758         0.428              412              419\n", "Probability of Two Distinct Events                7963     0.490         0.500              452              339\n", "Finding Percents                                  7694     0.538         0.499              771              371\n", "Subtraction Whole Numbers                         7669     0.641         0.480              903              242\n", "Probability of a Single Event                     7438     0.742         0.437              939              350\n", "Pattern Finding                                   7343     0.600         0.490              447              554\n", "Absolute Value                                    7340     0.757         0.429             1002              241\n", "Ordering Positive Decimals                        7317     0.750         0.433              942              543\n", "\n", "Top 15 easiest skills (highest accuracy):\n", "                                              interactions  accuracy  unique_students\n", "skill_name                                                                           \n", "Nets of 3D Figures                                     280     0.950              229\n", "Area Parallelogram                                     115     0.922               95\n", "Congruence                                             587     0.894              364\n", "Distributive Property                                   18     0.889                5\n", "Mode                                                  1926     0.876              572\n", "Scatter Plot                                          1859     0.869              354\n", "Area Rectangle                                         495     0.863              215\n", "Area Triangle                                          286     0.857              168\n", "D.4.8-understanding-concept-of-probabilities           456     0.846              202\n", "Volume Rectangular Prism                               926     0.840              345\n", "Fraction Of                                            607     0.830              288\n", "Write Linear Equation from Situation                  1447     0.822              223\n", "Linear Equations                                        89     0.820               41\n", "Slope                                                   89     0.820               41\n", "Choose an Equation from Given Information               89     0.820               41\n", "\n", "Top 15 hardest skills (lowest accuracy):\n", "                                               interactions  accuracy  unique_students\n", "skill_name                                                                            \n", "Reading a Ruler or Scale                                  5     0.000                5\n", "Quadratic Formula to Solve Quadratic Equation            32     0.125               14\n", "Rotations                                               427     0.136              163\n", "Computation with Real Numbers                            21     0.190               21\n", "Solving Systems of Linear Equations                     234     0.192               22\n", "Percent Discount                                         47     0.234               29\n", "Surface Area Cylinder                                   491     0.316              135\n", "Finding Slope From Situation                              9     0.333                2\n", "Percents                                                117     0.333               41\n", "Algebraic Solving                                       389     0.368               88\n", "Reflection                                              459     0.373              176\n", "Rate                                                     91     0.374               39\n", "Algebraic Simplification                                 90     0.400               15\n", "Finding <PERSON><PERSON><PERSON> from Ordered Pairs                          5     0.400                2\n", "Multiplication Whole Numbers                            110     0.436               45\n"]}], "source": ["# Comprehensive skills analysis\n", "print(\"📋 COMPREHENSIVE SKILLS ANALYSIS\")\n", "print(\"=\"*50)\n", "\n", "# Get all unique skills\n", "all_skills = df_clean['skill_name'].unique()\n", "print(f\"Total number of unique skills: {len(all_skills)}\")\n", "\n", "# Skills statistics\n", "skills_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean', 'std'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skills_stats.columns = ['interactions', 'accuracy', 'accuracy_std', 'unique_students', 'unique_problems']\n", "skills_stats = skills_stats.sort_values('interactions', ascending=False)\n", "\n", "print(\"\\nTop 15 skills by number of interactions:\")\n", "print(skills_stats.head(15).to_string())\n", "\n", "print(\"\\nTop 15 easiest skills (highest accuracy):\")\n", "easiest_skills = skills_stats.sort_values('accuracy', ascending=False).head(15)\n", "print(easiest_skills[['interactions', 'accuracy', 'unique_students']].to_string())\n", "\n", "print(\"\\nTop 15 hardest skills (lowest accuracy):\")\n", "hardest_skills = skills_stats.sort_values('accuracy', ascending=True).head(15)\n", "print(hardest_skills[['interactions', 'accuracy', 'unique_students']].to_string())"]}, {"cell_type": "code", "execution_count": 7, "id": "806a7aaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 SKILLS CATEGORIZATION\n", "==============================\n", "Easy skills (≥80% accuracy): 17 skills\n", "Medium skills (60-80% accuracy): 54 skills\n", "Hard skills (<60% accuracy): 39 skills\n", "\n", "High frequency skills (≥1000 interactions): 63 skills\n", "Medium frequency skills (100-1000 interactions): 29 skills\n", "Low frequency skills (<100 interactions): 18 skills\n", "\n", "💾 Skills analysis saved to App/Training/reports/math/skills_analysis.csv\n"]}], "source": ["# Skills categorization analysis\n", "print(\"\\n📊 SKILLS CATEGORIZATION\")\n", "print(\"=\"*30)\n", "\n", "# Categorize skills by difficulty\n", "easy_skills = skills_stats[skills_stats['accuracy'] >= 0.8]\n", "medium_skills = skills_stats[(skills_stats['accuracy'] >= 0.6) & (skills_stats['accuracy'] < 0.8)]\n", "hard_skills = skills_stats[skills_stats['accuracy'] < 0.6]\n", "\n", "print(f\"Easy skills (≥80% accuracy): {len(easy_skills)} skills\")\n", "print(f\"Medium skills (60-80% accuracy): {len(medium_skills)} skills\")\n", "print(f\"Hard skills (<60% accuracy): {len(hard_skills)} skills\")\n", "\n", "# Categorize by frequency\n", "high_freq = skills_stats[skills_stats['interactions'] >= 1000]\n", "medium_freq = skills_stats[(skills_stats['interactions'] >= 100) & (skills_stats['interactions'] < 1000)]\n", "low_freq = skills_stats[skills_stats['interactions'] < 100]\n", "\n", "print(f\"\\nHigh frequency skills (≥1000 interactions): {len(high_freq)} skills\")\n", "print(f\"Medium frequency skills (100-1000 interactions): {len(medium_freq)} skills\")\n", "print(f\"Low frequency skills (<100 interactions): {len(low_freq)} skills\")\n", "\n", "# Save skills analysis\n", "os.makedirs('App/Training/reports/math', exist_ok=True)\n", "skills_stats.to_csv('App/Training/reports/math/skills_analysis.csv')\n", "print(\"\\n💾 Skills analysis saved to App/Training/reports/math/skills_analysis.csv\")"]}, {"cell_type": "code", "execution_count": 8, "id": "267baa18", "metadata": {}, "outputs": [], "source": ["#export csv list of all unique skills from df\n", "skills = df_clean['skill_name'].unique()\n", "skills_df = pd.DataFrame(skills, columns=['skill_name'])\n", "skills_df.to_csv('skills_all.csv', index=False)"]}, {"cell_type": "code", "execution_count": 9, "id": "0cedca97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Splitting data into train/test sets...\n", "Training set: 255,487 interactions from 3,320 users\n", "Test set: 70,150 interactions from 831 users\n", "Train accuracy: 0.655\n", "Test accuracy: 0.670\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Training set: 255,487 interactions from 3,320 users\n", "Test set: 70,150 interactions from 831 users\n", "Train accuracy: 0.655\n", "Test accuracy: 0.670\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "# Split data by users to avoid data leakage\n", "print(\"Splitting data into train/test sets...\")\n", "\n", "# Get unique users\n", "unique_users = df_clean['user_id'].unique()\n", "train_users, test_users = train_test_split(unique_users, test_size=0.2, random_state=42)\n", "\n", "# Split data based on users\n", "train_df = df_clean[df_clean['user_id'].isin(train_users)].copy()\n", "test_df = df_clean[df_clean['user_id'].isin(test_users)].copy()\n", "\n", "print(f\"Training set: {len(train_df):,} interactions from {len(train_users):,} users\")\n", "print(f\"Test set: {len(test_df):,} interactions from {len(test_users):,} users\")\n", "print(f\"Train accuracy: {train_df['correct'].mean():.3f}\")\n", "print(f\"Test accuracy: {test_df['correct'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": 10, "id": "b6c9773e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 INITIALIZING <PERSON><PERSON><PERSON>LEDGE TRACING PIPELINE\n", "==================================================\n"]}], "source": ["# Initialize the knowledge tracing pipeline\n", "print(\"🚀 INITIALIZING K<PERSON><PERSON>LEDGE TRACING PIPELINE\")\n", "print(\"=\"*50)\n", "\n", "pipeline = KnowledgeTracingPipeline()"]}, {"cell_type": "code", "execution_count": 11, "id": "8dfb0f09", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 12:17:45,882 - INFO - Training knowledge tracing models...\n", "2025-07-19 12:17:45,885 - INFO - Training BKT model...\n", "2025-07-19 12:17:45,888 - INFO - Training BKT model...\n", "2025-07-19 12:18:21,917 - INFO - BKT training completed\n", "2025-07-19 12:18:21,918 - INFO -   BKT metrics: {'accuracy': 0.8308961922687129, 'precision': 0.8962950138504155, 'recall': 0.9009745910198399, 'f1_score': 0.8986287102933519, 'cv_accuracy': 0.8345547455004272, 'cv_accuracy_std': 0.007571865962454818}\n", "2025-07-19 12:18:21,927 - INFO -   BKT model: BayesianKnowledgeTracer()\n", "2025-07-19 12:18:21,928 - INFO -   BKT model params: {'convergence_threshold': 1e-06, 'learning_rate': 0.01, 'max_iterations': 100, 'p_guess': 0.2, 'p_init': 0.1, 'p_learn': 0.1, 'p_slip': 0.1}\n", "2025-07-19 12:18:21,929 - INFO -   BKT model skills: {'Box and Whisker': {'p_init': 0.654, 'p_learn': 0.211, 'p_guess': 0.1, 'p_slip': 0.05}, 'Circle Graph': {'p_init': 0.382, 'p_learn': 0.178, 'p_guess': 0.1, 'p_slip': 0.082}, 'Histogram as Table or Graph': {'p_init': 0.686, 'p_learn': 0.197, 'p_guess': 0.112, 'p_slip': 0.06}, 'Number Line': {'p_init': 0.533, 'p_learn': 0.153, 'p_guess': 0.136, 'p_slip': 0.05}, 'Scatter Plot': {'p_init': 0.721, 'p_learn': 0.188, 'p_guess': 0.1, 'p_slip': 0.05}, 'Stem and Leaf Plot': {'p_init': 0.581, 'p_learn': 0.187, 'p_guess': 0.1, 'p_slip': 0.071}, 'Table': {'p_init': 0.644, 'p_learn': 0.179, 'p_guess': 0.1, 'p_slip': 0.063}, 'Venn Diagram': {'p_init': 0.531, 'p_learn': 0.152, 'p_guess': 0.1, 'p_slip': 0.083}, 'Mean': {'p_init': 0.432, 'p_learn': 0.222, 'p_guess': 0.1, 'p_slip': 0.05}, 'Median': {'p_init': 0.567, 'p_learn': 0.172, 'p_guess': 0.1, 'p_slip': 0.053}, 'Mode': {'p_init': 0.8, 'p_learn': 0.117, 'p_guess': 0.1, 'p_slip': 0.05}, 'Range': {'p_init': 0.59, 'p_learn': 0.209, 'p_guess': 0.1, 'p_slip': 0.05}, 'Counting Methods': {'p_init': 0.517, 'p_learn': 0.198, 'p_guess': 0.162, 'p_slip': 0.052}, 'Probability of Two Distinct Events': {'p_init': 0.419, 'p_learn': 0.147, 'p_guess': 0.117, 'p_slip': 0.052}, 'Probability of a Single Event': {'p_init': 0.658, 'p_learn': 0.171, 'p_guess': 0.1, 'p_slip': 0.05}, 'Interior Angles Figures with More than 3 Sides': {'p_init': 0.466, 'p_learn': 0.399, 'p_guess': 0.1, 'p_slip': 0.05}, 'Interior Angles Triangle': {'p_init': 0.542, 'p_learn': 0.41, 'p_guess': 0.1, 'p_slip': 0.05}, 'Congruence': {'p_init': 0.707, 'p_learn': 0.31, 'p_guess': 0.125, 'p_slip': 0.05}, 'Complementary and Supplementary Angles': {'p_init': 0.641, 'p_learn': 0.277, 'p_guess': 0.1, 'p_slip': 0.05}, 'Angles on Parallel Lines Cut by a Transversal': {'p_init': 0.704, 'p_learn': 0.05, 'p_guess': 0.1, 'p_slip': 0.05}, 'Pythagorean Theorem': {'p_init': 0.63, 'p_learn': 0.107, 'p_guess': 0.1, 'p_slip': 0.106}, 'Nets of 3D Figures': {'p_init': 0.696, 'p_learn': 0.273, 'p_guess': 0.1, 'p_slip': 0.05}, 'Unit Conversion Within a System': {'p_init': 0.136, 'p_learn': 0.361, 'p_guess': 0.109, 'p_slip': 0.05}, 'Effect of Changing Dimensions of a Shape Prportionally': {'p_init': 0.095, 'p_learn': 0.5, 'p_guess': 0.12, 'p_slip': 0.05}, 'Area Circle': {'p_init': 0.601, 'p_learn': 0.35, 'p_guess': 0.1, 'p_slip': 0.05}, 'Circumference ': {'p_init': 0.743, 'p_learn': 0.182, 'p_guess': 0.1, 'p_slip': 0.093}, 'Perimeter of a Polygon': {'p_init': 0.326, 'p_learn': 0.362, 'p_guess': 0.123, 'p_slip': 0.05}, 'Reading a Ruler or Scale': {'p_init': 0.1, 'p_learn': 0.1, 'p_guess': 0.2, 'p_slip': 0.1}, 'Calculations with Similar Figures': {'p_init': 0.5, 'p_learn': 0.209, 'p_guess': 0.103, 'p_slip': 0.111}, 'Conversion of Fraction Decimals Percents': {'p_init': 0.619, 'p_learn': 0.143, 'p_guess': 0.138, 'p_slip': 0.073}, 'Equivalent Fractions': {'p_init': 0.399, 'p_learn': 0.204, 'p_guess': 0.1, 'p_slip': 0.05}, 'Ordering Positive Decimals': {'p_init': 0.677, 'p_learn': 0.178, 'p_guess': 0.138, 'p_slip': 0.05}, 'Ordering Fractions': {'p_init': 0.718, 'p_learn': 0.127, 'p_guess': 0.1, 'p_slip': 0.05}, 'Ordering Integers': {'p_init': 0.8, 'p_learn': 0.138, 'p_guess': 0.114, 'p_slip': 0.05}, 'Ordering Real Numbers': {'p_init': 0.564, 'p_learn': 0.175, 'p_guess': 0.1, 'p_slip': 0.05}, 'Rounding': {'p_init': 0.484, 'p_learn': 0.337, 'p_guess': 0.157, 'p_slip': 0.05}, 'Addition Whole Numbers': {'p_init': 0.604, 'p_learn': 0.247, 'p_guess': 0.1, 'p_slip': 0.05}, 'Division Fractions': {'p_init': 0.479, 'p_learn': 0.274, 'p_guess': 0.1, 'p_slip': 0.05}, 'Estimation': {'p_init': 0.743, 'p_learn': 0.175, 'p_guess': 0.1, 'p_slip': 0.068}, 'Fraction Of': {'p_init': 0.698, 'p_learn': 0.439, 'p_guess': 0.2, 'p_slip': 0.05}, 'Least Common Multiple': {'p_init': 0.368, 'p_learn': 0.221, 'p_guess': 0.1, 'p_slip': 0.096}, 'Multiplication Fractions': {'p_init': 0.475, 'p_learn': 0.246, 'p_guess': 0.1, 'p_slip': 0.053}, 'Multiplication Whole Numbers': {'p_init': 0.15, 'p_learn': 0.374, 'p_guess': 0.1, 'p_slip': 0.1}, 'Percent Of': {'p_init': 0.375, 'p_learn': 0.236, 'p_guess': 0.1, 'p_slip': 0.076}, 'Subtraction Whole Numbers': {'p_init': 0.566, 'p_learn': 0.225, 'p_guess': 0.1, 'p_slip': 0.051}, 'Square Root': {'p_init': 0.767, 'p_learn': 0.118, 'p_guess': 0.136, 'p_slip': 0.085}, 'Finding Percents': {'p_init': 0.351, 'p_learn': 0.225, 'p_guess': 0.1, 'p_slip': 0.069}, 'Proportion': {'p_init': 0.542, 'p_learn': 0.156, 'p_guess': 0.1, 'p_slip': 0.067}, 'Scale Factor': {'p_init': 0.15, 'p_learn': 0.475, 'p_guess': 0.107, 'p_slip': 0.05}, 'Unit Rate': {'p_init': 0.284, 'p_learn': 0.329, 'p_guess': 0.1, 'p_slip': 0.05}, 'Scientific Notation': {'p_init': 0.574, 'p_learn': 0.192, 'p_guess': 0.1, 'p_slip': 0.05}, 'Divisibility Rules': {'p_init': 0.33, 'p_learn': 0.261, 'p_guess': 0.1, 'p_slip': 0.086}, 'Prime Number': {'p_init': 0.25, 'p_learn': 0.438, 'p_guess': 0.2, 'p_slip': 0.05}, 'Absolute Value': {'p_init': 0.618, 'p_learn': 0.178, 'p_guess': 0.148, 'p_slip': 0.05}, 'Exponents': {'p_init': 0.528, 'p_learn': 0.25, 'p_guess': 0.1, 'p_slip': 0.05}, 'Pattern Finding ': {'p_init': 0.48, 'p_learn': 0.175, 'p_guess': 0.1, 'p_slip': 0.109}, 'D.4.8-understanding-concept-of-probabilities': {'p_init': 0.762, 'p_learn': 0.399, 'p_guess': 0.2, 'p_slip': 0.05}, 'Algebraic Simplification': {'p_init': 0.25, 'p_learn': 0.306, 'p_guess': 0.1, 'p_slip': 0.05}, 'Algebraic Solving': {'p_init': 0.271, 'p_learn': 0.178, 'p_guess': 0.1, 'p_slip': 0.05}, 'Choose an Equation from Given Information': {'p_init': 0.667, 'p_learn': 0.208, 'p_guess': 0.25, 'p_slip': 0.05}, 'Intercept': {'p_init': 0.667, 'p_learn': 0.208, 'p_guess': 0.25, 'p_slip': 0.05}, 'Linear Equations': {'p_init': 0.667, 'p_learn': 0.208, 'p_guess': 0.25, 'p_slip': 0.05}, 'Percent Discount': {'p_init': 0.25, 'p_learn': 0.05, 'p_guess': 0.1, 'p_slip': 0.1}, 'Percents': {'p_init': 0.19, 'p_learn': 0.229, 'p_guess': 0.1, 'p_slip': 0.05}, 'Rate': {'p_init': 0.222, 'p_learn': 0.222, 'p_guess': 0.1, 'p_slip': 0.05}, 'Slope': {'p_init': 0.667, 'p_learn': 0.208, 'p_guess': 0.25, 'p_slip': 0.05}, 'Multiplication and Division Positive Decimals': {'p_init': 0.704, 'p_learn': 0.163, 'p_guess': 0.1, 'p_slip': 0.05}, 'Addition and Subtraction Integers': {'p_init': 0.622, 'p_learn': 0.189, 'p_guess': 0.134, 'p_slip': 0.05}, 'Addition and Subtraction Positive Decimals': {'p_init': 0.416, 'p_learn': 0.247, 'p_guess': 0.106, 'p_slip': 0.054}, 'Multiplication and Division Integers': {'p_init': 0.744, 'p_learn': 0.129, 'p_guess': 0.1, 'p_slip': 0.05}, 'Addition and Subtraction Fractions': {'p_init': 0.501, 'p_learn': 0.205, 'p_guess': 0.1, 'p_slip': 0.066}, 'Reflection': {'p_init': 0.205, 'p_learn': 0.325, 'p_guess': 0.1, 'p_slip': 0.05}, 'Rotations': {'p_init': 0.035, 'p_learn': 0.156, 'p_guess': 0.1, 'p_slip': 0.05}, 'Translations': {'p_init': 0.337, 'p_learn': 0.375, 'p_guess': 0.1, 'p_slip': 0.05}, 'Area Irregular Figure': {'p_init': 0.168, 'p_learn': 0.312, 'p_guess': 0.114, 'p_slip': 0.097}, 'Area Parallelogram': {'p_init': 0.8, 'p_learn': 0.167, 'p_guess': 0.2, 'p_slip': 0.05}, 'Area Rectangle': {'p_init': 0.765, 'p_learn': 0.236, 'p_guess': 0.1, 'p_slip': 0.05}, 'Area Trapezoid': {'p_init': 0.355, 'p_learn': 0.207, 'p_guess': 0.1, 'p_slip': 0.092}, 'Area Triangle': {'p_init': 0.722, 'p_learn': 0.321, 'p_guess': 0.1, 'p_slip': 0.05}, 'Surface Area Cylinder': {'p_init': 0.082, 'p_learn': 0.434, 'p_guess': 0.134, 'p_slip': 0.1}, 'Surface Area Rectangular Prism': {'p_init': 0.356, 'p_learn': 0.365, 'p_guess': 0.1, 'p_slip': 0.05}, 'Volume Cylinder': {'p_init': 0.353, 'p_learn': 0.324, 'p_guess': 0.109, 'p_slip': 0.05}, 'Volume Rectangular Prism': {'p_init': 0.797, 'p_learn': 0.148, 'p_guess': 0.1, 'p_slip': 0.05}, 'Volume Sphere': {'p_init': 0.273, 'p_learn': 0.212, 'p_guess': 0.1, 'p_slip': 0.111}, 'Order of Operations +,-,/,* () positive reals': {'p_init': 0.561, 'p_learn': 0.305, 'p_guess': 0.1, 'p_slip': 0.05}, 'Order of Operations All': {'p_init': 0.414, 'p_learn': 0.27, 'p_guess': 0.1, 'p_slip': 0.05}, 'Equation Solving Two or Fewer Steps': {'p_init': 0.53, 'p_learn': 0.135, 'p_guess': 0.104, 'p_slip': 0.099}, 'Equation Solving More Than Two Steps': {'p_init': 0.531, 'p_learn': 0.124, 'p_guess': 0.1, 'p_slip': 0.087}, 'Angles - Obtuse, Acute, and Right': {'p_init': 0.5, 'p_learn': 0.137, 'p_guess': 0.2, 'p_slip': 0.056}, 'Greatest Common Factor': {'p_init': 0.705, 'p_learn': 0.244, 'p_guess': 0.1, 'p_slip': 0.05}, 'Computation with Real Numbers': {'p_init': 0.1, 'p_learn': 0.1, 'p_guess': 0.2, 'p_slip': 0.1}, 'Write Linear Equation from Ordered Pairs': {'p_init': 0.304, 'p_learn': 0.322, 'p_guess': 0.1, 'p_slip': 0.05}, 'Write Linear Equation from Situation': {'p_init': 0.772, 'p_learn': 0.136, 'p_guess': 0.1, 'p_slip': 0.05}, 'Recognize Linear Pattern': {'p_init': 0.5, 'p_learn': 0.333, 'p_guess': 0.2, 'p_slip': 0.05}, 'Write Linear Equation from Graph': {'p_init': 0.159, 'p_learn': 0.229, 'p_guess': 0.12, 'p_slip': 0.05}, 'Finding Slope From Situation': {'p_init': 0.01, 'p_learn': 0.05, 'p_guess': 0.1, 'p_slip': 0.1}, 'Finding Slope From Equation': {'p_init': 0.75, 'p_learn': 0.056, 'p_guess': 0.2, 'p_slip': 0.05}, 'Finding Slope from Ordered Pairs': {'p_init': 0.1, 'p_learn': 0.1, 'p_guess': 0.2, 'p_slip': 0.1}, 'Distributive Property': {'p_init': 0.8, 'p_learn': 0.083, 'p_guess': 0.2, 'p_slip': 0.05}, 'Midpoint': {'p_init': 0.01, 'p_learn': 0.1, 'p_guess': 0.1, 'p_slip': 0.1}, 'Polynomial Factors': {'p_init': 0.3, 'p_learn': 0.082, 'p_guess': 0.1, 'p_slip': 0.167}, 'Recognize Quadratic Pattern': {'p_init': 0.8, 'p_learn': 0.05, 'p_guess': 0.2, 'p_slip': 0.1}, 'Solving Systems of Linear Equations': {'p_init': 0.077, 'p_learn': 0.251, 'p_guess': 0.1, 'p_slip': 0.1}, 'Quadratic Formula to Solve Quadratic Equation': {'p_init': 0.125, 'p_learn': 0.05, 'p_guess': 0.1, 'p_slip': 0.1}, 'Parts of a Polyomial, Terms, Coefficient, Monomial, Exponent, Variable': {'p_init': 0.25, 'p_learn': 0.219, 'p_guess': 0.2, 'p_slip': 0.071}, 'Interpreting Coordinate Graphs ': {'p_init': 0.1, 'p_learn': 0.1, 'p_guess': 0.2, 'p_slip': 0.1}, 'Solving for a variable': {'p_init': 0.751, 'p_learn': 0.254, 'p_guess': 0.127, 'p_slip': 0.05}, 'Simplifying Expressions positive exponents': {'p_init': 0.01, 'p_learn': 0.05, 'p_guess': 0.2, 'p_slip': 0.1}, 'Solving Inequalities': {'p_init': 0.533, 'p_learn': 0.153, 'p_guess': 0.136, 'p_slip': 0.05}, 'Solving Systems of Linear Equations by Graphing': {'p_init': 0.333, 'p_learn': 0.174, 'p_guess': 0.1, 'p_slip': 0.083}}\n", "2025-07-19 12:18:21,930 - INFO -   BKT model students: {}\n", "2025-07-19 12:18:21,930 - INFO - Model training completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Individual models training completed\n", "Training completed in 36.05 seconds\n", "Model trained on 110 skills\n", "Each skill now has unique parameters estimated from student data!\n"]}], "source": ["import time\n", "start_time = time.time()\n", "\n", "models, metrics = pipeline.train_models(train_df, test_df, models_to_train=['bkt'])\n", "print(\"✅ Individual models training completed\")\n", "\n", "training_time = time.time() - start_time\n", "print(f\"Training completed in {training_time:.2f} seconds\")\n", "print(f\"Model trained on {len(models['bkt'].skill_params)} skills\")\n", "print(f\"Each skill now has unique parameters estimated from student data!\")"]}, {"cell_type": "code", "execution_count": 29, "id": "35ce2854", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 12:40:01,490 - INFO - Saving models to /home/<USER>/workspace/AClass/App/Training/models/outputs...\n", "2025-07-17 12:40:01,501 - INFO - Saved bkt model to /home/<USER>/workspace/AClass/App/Training/models/outputs/bkt_model.joblib\n", "2025-07-17 12:40:01,503 - INFO - Saved metrics to /home/<USER>/workspace/AClass/App/Training/models/outputs/model_metrics.json\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💾 Saving models...\n", "✅ Models saved successfully\n"]}], "source": ["# Save models\n", "print(\"\\n💾 Saving models...\")\n", "# os.makedirs('App/Training/pipeline/outputs/math', exist_ok=True)\n", "OUTPUT_DIR='/home/<USER>/workspace/AClass/App/Training/models/outputs'\n", "pipeline.save_models(OUTPUT_DIR)\n", "print(\"✅ Models saved successfully\")"]}, {"cell_type": "code", "execution_count": 14, "id": "67510012", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== MODEL EVALUATION ===\n", "Test Accuracy: 0.831\n", "Test AUC: 0.746\n", "Number of predictions: 6907\n", "Mean predicted probability: 0.780\n", "Actual success rate: 0.832\n"]}], "source": ["from sklearn.metrics import accuracy_score, roc_auc_score, classification_report\n", "\n", "# Evaluate model performance on test data\n", "print(\"\\n=== MODEL EVALUATION ===\")\n", "\n", "# Simple evaluation: predict probability for each test interaction\n", "test_predictions = []\n", "test_actuals = []\n", "\n", "# Group test data by user and skill for sequential prediction\n", "for (user_id, skill), group in test_df.groupby(['user_id', 'skill_name']):\n", "    if skill in models['bkt'].skill_params:\n", "        user_history = group['correct'].tolist()\n", "        if len(user_history) > 1:\n", "            # Use first n-1 responses to predict the last one\n", "            history = user_history[:-1]\n", "            actual = user_history[-1]\n", "            \n", "            probas = models['bkt'].predict_proba(history, skill)\n", "            if probas:\n", "                predicted_prob = probas[-1]  # Last probability\n", "                test_predictions.append(predicted_prob)\n", "                test_actuals.append(actual)\n", "\n", "if test_predictions:\n", "    test_predictions = np.array(test_predictions)\n", "    test_actuals = np.array(test_actuals)\n", "    \n", "    # Calculate metrics\n", "    binary_predictions = (test_predictions > 0.5).astype(int)\n", "    accuracy = accuracy_score(test_actuals, binary_predictions)\n", "    auc = roc_auc_score(test_actuals, test_predictions)\n", "    \n", "    print(f\"Test Accuracy: {accuracy:.3f}\")\n", "    print(f\"Test AUC: {auc:.3f}\")\n", "    print(f\"Number of predictions: {len(test_predictions)}\")\n", "    print(f\"Mean predicted probability: {test_predictions.mean():.3f}\")\n", "    print(f\"Actual success rate: {test_actuals.mean():.3f}\")\n", "else:\n", "    print(\"No valid predictions could be made on test data\")"]}, {"cell_type": "code", "execution_count": 15, "id": "6a517d0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== TRAINING STATISTICS ===\n", "Training time: 36.05 seconds\n", "Number of skills trained: 110\n", "Training data size: 255,487 interactions\n", "Test data size: 70,150 interactions\n", "\n", "=== SKILL-SPECIFIC PARAMETERS TABLE ===\n", "Skill                               Interactions Accuracy P(Init)  P(Learn) P(Guess) P(Slip) \n", "---------------------------------------------------------------------------------------------------------\n", "Equation Solving Two or Fewer Step  24253    0.679    0.530    0.135    0.104    0.099   \n", "Conversion of Fraction Decimals Pe  18742    0.637    0.619    0.143    0.138    0.073   \n", "Addition and Subtraction Integers   12741    0.599    0.622    0.189    0.134    0.050   \n", "Addition and Subtraction Fractions  11334    0.677    0.501    0.205    0.100    0.066   \n", "Percent Of                          9497     0.595    0.375    0.236    0.100    0.076   \n", "Proportion                          9054     0.641    0.542    0.156    0.100    0.067   \n", "Ordering Fractions                  8539     0.792    0.718    0.127    0.100    0.050   \n", "Equation Solving More Than Two Ste  8115     0.758    0.531    0.124    0.100    0.087   \n", "Probability of Two Distinct Events  7963     0.490    0.419    0.147    0.117    0.052   \n", "Finding Percents                    7694     0.538    0.351    0.225    0.100    0.069   \n", "Subtraction Whole Numbers           7669     0.641    0.566    0.225    0.100    0.051   \n", "Probability of a Single Event       7438     0.742    0.658    0.171    0.100    0.050   \n", "Pattern Finding                     7343     0.600    0.480    0.175    0.100    0.109   \n", "Absolute Value                      7340     0.757    0.618    0.178    0.148    0.050   \n", "Ordering Positive Decimals          7317     0.750    0.677    0.178    0.138    0.050   \n", "\n", "=== MODEL PARAMETERS FOR TOP 10 SKILLS ===\n", "\n", "Equation Solving Two or Fewer Steps:\n", "  P(Init): 0.530\n", "  P(Learn): 0.135\n", "  P(Guess): 0.104\n", "  P(Slip): 0.099\n", "\n", "Conversion of Fraction Decimals Percents:\n", "  P(Init): 0.619\n", "  P(Learn): 0.143\n", "  P(Guess): 0.138\n", "  P(Slip): 0.073\n", "\n", "Addition and Subtraction Integers:\n", "  P(Init): 0.622\n", "  P(Learn): 0.189\n", "  P(Guess): 0.134\n", "  P(Slip): 0.050\n", "\n", "Addition and Subtraction Fractions:\n", "  P(Init): 0.501\n", "  P(Learn): 0.205\n", "  P(Guess): 0.100\n", "  P(Slip): 0.066\n", "\n", "Percent Of:\n", "  P(Init): 0.375\n", "  P(Learn): 0.236\n", "  P(Guess): 0.100\n", "  P(Slip): 0.076\n", "\n", "Proportion:\n", "  P(Init): 0.542\n", "  P(Learn): 0.156\n", "  P(Guess): 0.100\n", "  P(Slip): 0.067\n", "\n", "Ordering Fractions:\n", "  P(Init): 0.718\n", "  P(Learn): 0.127\n", "  P(Guess): 0.100\n", "  P(Slip): 0.050\n", "\n", "Equation Solving More Than Two Steps:\n", "  P(Init): 0.531\n", "  P(Learn): 0.124\n", "  P(Guess): 0.100\n", "  P(Slip): 0.087\n", "\n", "Probability of Two Distinct Events:\n", "  P(Init): 0.419\n", "  P(Learn): 0.147\n", "  P(Guess): 0.117\n", "  P(Slip): 0.052\n", "\n", "Finding Percents:\n", "  P(Init): 0.351\n", "  P(Learn): 0.225\n", "  P(Guess): 0.100\n", "  P(Slip): 0.069\n"]}], "source": ["# Display training statistics\n", "print(\"=== TRAINING STATISTICS ===\")\n", "print(f\"Training time: {training_time:.2f} seconds\")\n", "print(f\"Number of skills trained: {len(models['bkt'].skill_params)}\")\n", "print(f\"Training data size: {len(train_df):,} interactions\")\n", "print(f\"Test data size: {len(test_df):,} interactions\")\n", "\n", "\n", "\n", "# Show skill-specific parameters in a formatted table\n", "print(\"\\n=== SKILL-SPECIFIC PARAMETERS TABLE ===\")\n", "print(f\"{'Skill':<35} {'Interactions':<8} {'Accuracy':<8} {'P(Init)':<8} {'P(Learn)':<8} {'P(Guess)':<8} {'P(Slip)':<8}\")\n", "print(\"-\" * 105)\n", "\n", "top_skills = skills_stats.head(15)\n", "top_skills.head(5)\n", "for skill in top_skills.index:\n", "    if skill in models['bkt'].skill_params:\n", "        params = models['bkt'].skill_params[skill]\n", "        stats = skills_stats.loc[skill]\n", "        print(f\"{skill[:34]:<35} {stats['interactions']:<8.0f} {stats['accuracy']:<8.3f} \"\n", "              f\"{params['p_init']:<8.3f} {params['p_learn']:<8.3f} \"\n", "              f\"{params['p_guess']:<8.3f} {params['p_slip']:<8.3f}\")\n", "        \n", "# Show parameters for top skills\n", "print(\"\\n=== MODEL PARAMETERS FOR TOP 10 SKILLS ===\")\n", "\n", "top_skills_list = skills_stats.head(10).index.tolist()\n", "for skill in top_skills_list:\n", "    if skill in models['bkt'].skill_params:\n", "        params = models['bkt'].skill_params[skill]\n", "        \n", "        print(f\"\\n{skill}:\")\n", "        print(f\"  P(Init): {params['p_init']:.3f}\")\n", "        print(f\"  P(Learn): {params['p_learn']:.3f}\")\n", "        print(f\"  P(Guess): {params['p_guess']:.3f}\")\n", "        print(f\"  P(Slip): {params['p_slip']:.3f}\")\n"]}, {"cell_type": "code", "execution_count": 16, "id": "7c00c1c3", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'OUTPUT_DIR' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[16], line 8\u001b[0m\n\u001b[1;32m      5\u001b[0m         params \u001b[38;5;241m=\u001b[39m models[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbkt\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mskill_params[skill]\n\u001b[1;32m      6\u001b[0m         top_skills_list_df\u001b[38;5;241m.\u001b[39mappend([skill, params[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mp_init\u001b[39m\u001b[38;5;124m'\u001b[39m], params[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mp_learn\u001b[39m\u001b[38;5;124m'\u001b[39m], params[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mp_guess\u001b[39m\u001b[38;5;124m'\u001b[39m], params[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mp_slip\u001b[39m\u001b[38;5;124m'\u001b[39m]])\n\u001b[0;32m----> 8\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m💾 Saving Skills parameters to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mOUTPUT_DIR\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m/bkt_skills_parameters.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m#convert top_skills_list_df to dataframe with column names \u001b[39;00m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;66;03m# column_names=['skill_name', 'p_init', 'p_learn', 'p_guess', 'p_slip']\u001b[39;00m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# top_skills_list_df = pd.DataFrame(top_skills_list_df, columns=column_names)\u001b[39;00m\n\u001b[1;32m     13\u001b[0m top_skills_list_df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame(top_skills_list_df,columns\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mskill_name\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mp_init\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mp_learn\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mp_guess\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mp_slip\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "\u001b[0;31mNameError\u001b[0m: name 'OUTPUT_DIR' is not defined"]}], "source": ["\n", "\n", "top_skills_list_df=[]\n", "top_skills_list = skills_stats.index.tolist()\n", "for skill in top_skills_list:\n", "    if skill in models['bkt'].skill_params:\n", "        params = models['bkt'].skill_params[skill]\n", "        top_skills_list_df.append([skill, params['p_init'], params['p_learn'], params['p_guess'], params['p_slip']])\n", "\n", "print(f\"\\n💾 Saving Skills parameters to {OUTPUT_DIR}/bkt_skills_parameters.csv\")\n", "#convert top_skills_list_df to dataframe with column names \n", "# column_names=['skill_name', 'p_init', 'p_learn', 'p_guess', 'p_slip']\n", "# top_skills_list_df = pd.DataFrame(top_skills_list_df, columns=column_names)\n", "\n", "top_skills_list_df = pd.DataFrame(top_skills_list_df,columns=['skill_name', 'p_init', 'p_learn', 'p_guess', 'p_slip'])\n", "\n", "#save to TSV file\n", "top_skills_list_df.to_csv(OUTPUT_DIR+'/bkt_skills_parameters.csv', sep='\\t', index=False)"]}, {"cell_type": "code", "execution_count": 17, "id": "36ad2b08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PARAMETER DISTRIBUTION ANALYSIS ===\n"]}, {"data": {"image/png": "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***********************************************//M+ePeuD6Pybu323Zs0ao3Tp0sbp06d9EZ7futjPuJUrVxoWi8U4fPiwN8LzW+7224svvmjUqVMnz7oZM2YY1apV81qMgDeRk6MonHvAFZxroSicV8IVl8J5NNO5OJGZmalvv/1WXbt2zbO+a9eu+vLLLwvcZ/v27fm2v+6667Rz505lZWV5LVZ/Upx+w3me6LucnBwlJyerXLly3gjRL3mi377//nt9+eWX6tixozdC9EvF7bdFixbpwIEDmjBhgrdD9EsXc7y1aNFCcXFx6ty5szZt2uTNMP1Scfpu1apVatWqlaZOnaqqVavq8ssv12OPPaa0tDRfhOwXPPEZt2DBAnXp0kU1a9b0Roh+qTj91rZtW/3222/65JNPZBiG/vjjD7333nvq3r27L0IGPIqcHEXh3AOu4FwLReG8Eq64VM6jQ0xt3Y+dOnVKdrtdlSpVyrO+UqVKOnHiRIH7nDhxosDts7OzderUKcXFxXktXn9RnH7DeZ7ou5dfflkpKSm68847vRGiX7qYfqtWrZqSkpKUnZ2tiRMn6oEHHvBmqH6lOP3266+/asyYMdq6datCQgLz30dx+i0uLk7z5s3TlVdeqYyMDL355pvq3LmzNm/erA4dOvgibL9QnL47ePCgtm3bpvDwcK1cuVKnTp3SkCFDdObMmYCZF/1i/zccP35ca9as0dKlS70Vol8qTr+1bdtWb7/9tu666y6lp6crOztbPXr00GuvveaLkAGPIidHUTj3gCs410JROK+EKy6V82iO1iJYLJY89w3DyLeuqO0LWn+pc7ff8Lfi9t0777yjiRMn6sMPP1TFihW9FZ7fKk6/bd26VefOndNXX32lMWPGqF69errnnnu8GabfcbXf7Ha7evXqpUmTJunyyy/3VXh+y53jrX79+qpfv77j/tVXX62jR4/qpZdeCqgiei53+i4nJ0cWi0Vvv/22SpcuLUmaNm2abr/9ds2aNUsRERFej9dfFPd/w+LFi1WmTBn17NnTS5H5N3f67eeff9awYcM0fvx4XXfddTp+/Lgef/xxDR48WAsWLPBFuIDHkZOjKJx7wBWca6EonFfCFSX9PJoiuhPly5dXcHBwvm9ETp48me+bk1yVK1cucPuQkBDFxsZ6LVZ/Upx+w3kX03fLli3TgAED9N///lddunTxZph+52L6rXbt2pKkK664Qn/88YcmTpwYMImdu/2WnJysnTt36vvvv9fDDz8s6XyB0zAMhYSEaN26dfr3v//tk9jN5KnPuKuuukpvvfWWp8Pza8Xpu7i4OFWtWtVRQJekhg0byjAM/fbbb7rsssu8GrM/uJhjzjAMLVy4UL1791ZYWJg3w/Q7xem3hIQEtWvXTo8//rgkqWnTpoqKitI111yj5557LiB+UYhLBzk5isK5B1zBuRaKwnklXHGpnEczJ7oTYWFhuvLKK7V+/fo869evX6+2bdsWuM/VV1+db/t169apVatWCg0N9Vqs/qQ4/Ybzitt377zzjvr27aulS5cG5LytnjrmDMNQRkaGp8PzW+72W0xMjH788Uft2rXLcRs8eLDq16+vXbt2qU2bNr4K3VSeOt6+//77gCvIFafv2rVrp2PHjuncuXOOdfv27VNQUJCqVavm1Xj9xcUcc1u2bNH+/fs1YMAAb4bol4rTb6mpqQoKypsaBwcHS/r7l4VASUFOjqJw7gFXcK6FonBeCVdcMufRPryIaYnz7rvvGqGhocaCBQuMn3/+2RgxYoQRFRVlHD582DAMwxgzZozRu3dvx/YHDx40IiMjjZEjRxo///yzsWDBAiM0NNR47733zHoJpnC33wzDML7//nvj+++/N6688kqjV69exvfff2/s2bPHjPBN5W7fLV261AgJCTFmzZplHD9+3HH7888/zXoJpnC332bOnGmsWrXK2Ldvn7Fv3z5j4cKFRkxMjDFu3DizXoIpivNevVCgXkXd3X575ZVXjJUrVxr79u0zfvrpJ2PMmDGGJOP999836yWYxt2+S05ONqpVq2bcfvvtxp49e4wtW7YYl112mfHAAw+Y9RJMUdz36n333We0adPG1+H6DXf7bdGiRUZISIgxe/Zs48CBA8a2bduMVq1aGf/617/MegnARSEnR1E494ArONdCUTivhCsuhfNoiuhFmDVrllGzZk0jLCzMaNmypbFlyxbHY3369DE6duyYZ/vNmzcbLVq0MMLCwoxatWoZc+bM8XHE/sHdfpOU71azZk3fBu0n3Om7jh07Fth3ffr08X3gJnOn32bMmGE0btzYiIyMNGJiYowWLVoYs2fPNux2uwmRm8vd9+qFAjnZcaffpkyZYtStW9cIDw83ypYta7Rv395YvXq1CVH7B3ePuV9++cXo0qWLERERYVSrVs0YNWqUkZqa6uOozeduv/35559GRESEMW/ePB9H6l/c7bcZM2YYjRo1MiIiIoy4uDjj3nvvNX777TcfRw14Djk5isK5B1zBuRaKwnklXFHSz6MthsHvUwEAAAAAAAAAKAhzogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAD/x959h0dRrn0c/23aphA6aRBCCCBNpIqAmCAC0pSDggIi2EABaXIQ5AjBg6GoHPRQFETAQrGADWlKEQUFUQ5SjoUSOEoEEQgkIXXeP/JmYckupOxmN9nv57rmumZnZmfufXZ2c8+dZ58BAAAAAACwgyI6AAAAAAAAAAB2UEQHAAAAAAAAAMAOiugAAAAAAAAAANhBER0AoGeffVZDhgwp1j5MJpM+/PDDa24zbtw4jRw5sljHAQAAAEorR+TdJaVVq1ZavXq1q8MAALdAER0AyrjBgwfLZDLJZDLJ19dXtWvX1rhx45SSkiJJ+uOPP/Tyyy/rmWeesXpOr169CnWckydPqmvXrpKkY8eOyWQyae/evVbbjB8/XkuWLNHRo0eL9ZoAAAAAd1NSeXdJefbZZzVhwgTl5OS4OhQAcDmK6ADgAe68806dPHlSR44c0bRp0zR//nyNGzdOkrR48WK1adNGtWrVKtYxwsLCZDabr7lNSEiIOnfurFdffbVYxwIAAADcUUnk3cWVmZlZoO26d++u8+fPa8OGDU6OCADcH0V0APAAZrNZYWFhioyMVP/+/TVgwADL0CsrV67UXXfddc3nx8XFaeTIkRo/frwqV66ssLAwxcfHW21z5XAu0dHRkqRmzZrJZDIpLi7Ost1dd92lFStWOOqlAQAAAG6juHn31Q4ePKhu3bqpXLlyCg0N1cCBA/Xnn39a1q9fv1633nqrKlasqCpVqqhHjx46fPiwZX3eL0TfffddxcXFyd/fX2+//balB/yLL76o8PBwValSRcOHD7cqsHt7e6tbt27k7gAgiugA4JECAgKUmZmps2fPav/+/WrZsuV1n7Ns2TIFBQXp22+/1axZs/Tcc89p06ZNNrfdtWuXJOnzzz/XyZMnrcZSvPnmm3XixAklJiY65sUAAAAAbqooeXeekydPKjY2Vk2bNtV3332n9evX648//lDfvn0t26SkpGjs2LHavXu3vvjiC3l5eelvf/tbviFYnn76aY0cOVKHDh1Sly5dJElbtmzR4cOHtWXLFi1btkxLly7V0qVLrZ538803a/v27UVvAAAoI3xcHQAAoGTt2rVLy5cvV8eOHZWYmCjDMBQREXHd5zVp0kRTpkyRJNWtW1dz587VF198oU6dOuXbtlq1apKkKlWqKCwszGpd9erVJeX2iomKiiruywEAAADcUlHz7jwLFixQ8+bNlZCQYFn2xhtvKDIyUj///LPq1aune+65x+o5ixcvVkhIiA4ePKjGjRtblo8ePVq9e/e22rZSpUqaO3euvL29Vb9+fXXv3l1ffPGFHnvsMcs21atX1/Hjx5WTkyMvL/phAvBcfAMCgAf49NNPVa5cOfn7+6tNmza67bbb9O9//1tpaWmSJH9//+vuo0mTJlaPw8PDderUqULHEhAQIElKTU0t9HMBAAAAd+aIvDvPnj17tGXLFpUrV84y1a9fX5IsQ7YcPnxY/fv3V+3atVW+fHnLsIrHjx+32petHvCNGjWSt7e35bGt/D4gIEA5OTlKT08vcNwAUBbREx0APECHDh20YMEC+fr6KiIiQr6+vpKk8+fPS5LOnj1r6T1uT95z8phMpnw/Ey2Iv/76S5KuezwAAACgtHFE3p0nJydHPXv21MyZM/OtCw8PlyT17NlTkZGRWrRokSIiIpSTk6PGjRsrIyPDavugoKB8+yhIfv/XX38pMDDQ0hEGADwVRXQA8ABBQUGqU6dOvuUxMTEqX768Dh48qHr16jnseH5+fpKk7OzsfOv2798vX19fNWrUyGHHAwAAANyBI/Pu5s2b64MPPlCtWrXk45O/fHPmzBkdOnRIr732mtq3by9J+uqrr4r3Aq6yf/9+NW/e3KH7BIDSiOFcAMCDeXl56Y477nB4sh0SEqKAgADLzY/yet5I0vbt29W+fXt6swAAAMBjXCvvPn/+vPbu3Ws1HT9+XMOHD9dff/2lfv36adeuXTpy5Ig2btyohx9+WNnZ2apUqZKqVKmihQsX6tdff9XmzZs1duxYh8a9fft2de7c2aH7BIDSiCI6AHi4IUOGaOXKlUUamsUeHx8fvfLKK3rttdcUERGhu+++27JuxYoVVjcrAgAAADyBvbx769atatasmdU0efJkRURE6Ouvv1Z2dra6dOmixo0ba9SoUapQoYK8vLzk5eWllStXas+ePWrcuLHGjBmjF154wWHx/vbbb9qxY4ceeughh+0TAEork2EYhquDAAC4jmEYuuWWWzR69Gj169fPqcdau3at/v73v2vfvn02f5IKAAAAlFUlmXc7wt///nedP39eCxcudHUoAOBy9EQHAA9nMpm0cOFCZWVlOf1YKSkpWrJkCQV0AAAAeJySzLsdISQkRP/85z9dHQYAuAV6ogMAAAAAAAAAYAc90QEAAAAAAAAAsIMiOgAAAAAAAAAAdlBEBwAAAAAAAADADoroAAAAAAAAAADYQREdAAAAAAAAAAA7KKIDAAAAAAAAAGAHRXQAAAAAAAAAAOygiA4AAAAAAAAAgB0U0QEAAAAAAAAAsIMiOgAAAAAAAAAAdlBEBwAAAAAAAADADoroAAAAAAAAAADYQREdAAAAAAAAAAA7KKIDAAAAAAAAAGAHRXSgkJYuXSqTyWSZ/P39FRYWpg4dOmj69Ok6depUvufEx8fLZDIV6jipqamKj4/X1q1bC/U8W8eqVauWevToUaj9XM/y5cs1Z84cm+tMJpPi4+MdejxH++KLL9SyZUsFBQXJZDLpww8/tLndsWPHrN5vLy8vValSRd26ddPOnTvzbX/48GGZzWab6z799FPdfffdioiIkJ+fn4KDg9WsWTNNmTJFx48fd/RLdIpnn31WzZs3V05OjtOOsW/fPj3yyCOKiYlRQECAAgICVLduXQ0dOlTfffed047rSFu3bpXJZCr059eRx86bvL29FRoaqj59+ujQoUMOPZYzvltMJpNGjBhx3e1stbGt77+4uDjFxcVZHhf1uxUAAHu4PsjF9UHBrw8Mw9DKlSvVvn17hYSEyN/fXzVq1FCXLl30+uuvWz3/6rYrTp6ZmZmpmJgYu+9TcWVmZuq1115Tq1atVLlyZQUGBioqKkp333231qxZY9kurw2XLl1qWZb3OTp27JjD4omLi7N6rwICAnTTTTdpzpw5Dr2eyYvdkdcqeZ/bP//887rbXp3vSgU7bz777DO3/1wC7oQiOlBES5Ys0c6dO7Vp0ybNmzdPTZs21cyZM9WgQQN9/vnnVts++uijNhOqa0lNTdXUqVMLnRwV5VhFca0keefOnXr00UedHkNRGYahvn37ytfXVx9//LF27typ2NjYaz7nySef1M6dO7V9+3ZNnz5d//nPf9ShQwf98MMPVtuNGzdOnTp1Ups2bSzLcnJyNGjQIPXs2VOZmZmaPn26Nm3apPfee0+9e/fWW2+9pXbt2jnltTrauHHjdPToUS1btswp+3/ttdfUokULffvttxo1apQ+/fRTrV27VqNHj9aBAwfUqlUrHT582CnHdqTmzZtr586dat68uctiSEhI0M6dO7VlyxY9/fTT2rRpk9q1a6fffvvNZTE5UkHbeP78+Zo/f77lcVG/WwEAuB6uD7g+KOj1wcSJE9WvXz81aNBAr7/+utatW6dp06YpNDRUH3300TWPW5w809fXV5MnT9Zzzz2nM2fOFPr51zNw4EA9+eST6tChg95++2198skn+sc//iEfHx9t2LDhms/t3r27du7cqfDwcIfGVLt2be3cuVM7d+7UqlWrVL16dY0ZM0YTJ0506HFc6ep81xZb581nn32mqVOnOjs8oOwwABTKkiVLDEnG7t27861LTEw0IiMjjeDgYCMpKalYxzl9+rQhyZgyZUqBtk9JSbG7LioqyujevXux4rla9+7djaioKIfus6T873//MyQZM2fOvO62R48eNSQZL7zwgtXyL774wpBkPProo5ZlBw8eNCQZ69evt9o2ISHBkGRMnz7d5jEyMzONuXPnFuGVuMaIESOMevXqGTk5OQ7d71dffWV4eXkZPXv2NNLT021u8+677xq//fabQ49b1mzZssWQZLz33ntWyxcvXmxIMqZNm2b3udf6HrHFGd8tkozhw4cX6blTpkwxrpfaFPa7FQCA6+H6IBfXBwW7PkhNTTXMZrPx4IMP2tx/dna21WNH5y3p6elG5cqVjeeff95h+zQMwzhy5IghyZg8ebLN9Ve+rrw2XLJkiUNjuFpsbKzRqFEjq2UZGRlG7dq1jcDAQCMjI8Pm83JycozU1NQCH+da3wFFlZfXnj59ukjPL8h5M3z48OvmzgAuoyc64EA1a9bUSy+9pAsXLui1116zLLf1E8rNmzcrLi5OVapUUUBAgGrWrKl77rlHqampOnbsmKpVqyZJmjp1quXnZ4MHD7ba3/fff697771XlSpVUkxMjN1j5VmzZo2aNGkif39/1a5dW6+88orVens/obv6p19xcXFau3atEhMTrX4el8fWzzX379+vu+++W5UqVZK/v7+aNm2arzdz3nFWrFihSZMmKSIiQuXLl9cdd9yhn376yX7DX+Grr75Sx44dFRwcrMDAQLVt21Zr1661rI+Pj1eNGjUkSU8//bRMJpNq1apVoH1f6ZZbbpEkJSYmWpYtWLBAYWFh6tSpk2VZRkaGZs2apcaNG2vChAk29+Xj46Phw4dbLbP3k9datWpZzoM8SUlJGjp0qGrUqCE/Pz9FR0dr6tSpysrKstpuwYIFuummm1SuXDkFBwerfv36euaZZyzrU1NTNW7cOEVHR8vf31+VK1dWy5YttWLFCqv9DBw4UD///LO2bNliv4GKICEhQd7e3nrttdfk5+dnc5s+ffooIiLC8tjWTxclafDgwfne14yMDE2bNk3169eX2WxWtWrV9NBDD+n06dNW213rs5nnem1p6+eSgwcPVrly5fTrr7+qW7duKleunCIjI/XUU08pPT3dKob//e9/uvfeexUcHKyKFStqwIAB2r17d76fvRbG1efstb5HLl26pIkTJyo6Olp+fn6qXr26hg8frnPnztnc9/W+Wy5duqSnnnpKTZs2VYUKFVS5cmW1adPmmj2tXnvtNdWrV09ms1kNGzbUypUrrdYX9KfMV54j1/pu3b59u+X752pvvvmmTCaTdu/efc1jAQBwNa4PcnF9cPn6ICUlRenp6XZ7XHt5XbtMc60888CBA+rYsaOCgoJUrVo1jRgxwiqHlSQ/Pz/dd999WrhwoQzDKOzLtCuvZ3tRX5etcy0uLk6NGzfW9u3bdcsttyggIEDVq1fXs88+q+zs7CLF6evrqxYtWig1NdVyHZA3nOCrr76qBg0ayGw2W87F650/Vzp79qweeughVa5cWUFBQerZs6eOHDlitc2mTZt09913q0aNGvL391edOnU0dOhQu8O2nDhxQr1791b58uVVoUIFPfDAA/muX+xdE13p6vNm8ODBmjdvnuX1503Hjh1Tx44dVb9+/Xznh2EYqlOnjrp3737NYwFlFUV0wMG6desmb29vffnll3a3OXbsmLp37y4/Pz+98cYbWr9+vWbMmKGgoCBlZGQoPDxc69evlyQ98sgjlp+fPfvss1b76d27t+rUqaP33ntPr7766jXj2rt3r0aPHq0xY8ZozZo1atu2rUaNGqUXX3yx0K9x/vz5ateuncLCwiyxXesnoj/99JPatm2rAwcO6JVXXtHq1avVsGFDDR48WLNmzcq3/TPPPKPExES9/vrrWrhwoX755Rf17NnzuonStm3bdPvtt+v8+fNavHixVqxYoeDgYPXs2VOrVq2SlPtz1tWrV0u6/BPMK8fnK6hff/1VkiwXM5K0du1a3XbbbVYJ4nfffadz586pZ8+ehT5GQSQlJenmm2/Whg0bNHnyZK1bt06PPPKIpk+frscee8yy3cqVKzVs2DDFxsZqzZo1+vDDDzVmzBilpKRYthk7dqwWLFigkSNHav369XrrrbfUp0+ffD/1bNGihcqVK2c3eSyK7OxsbdmyRS1btnT4Tzil3CF17r77bs2YMUP9+/fX2rVrNWPGDG3atElxcXFKS0uTdP3PplSwtrQnMzNTd911lzp27KiPPvpIDz/8sP71r39p5syZlm1SUlLUoUMHbdmyRTNnztS7776r0NBQ3XfffcVqA1vnrJT/e8QwDPXq1UsvvviiBg4cqLVr12rs2LFatmyZbr/99nwF/4J8t6Snp+uvv/7SuHHj9OGHH2rFihW69dZb1bt3b7355pv5Yv3444/1yiuv6LnnntP777+vqKgo9evXT++//36x2uBa363t27dXs2bNLBcTV5o7d65atWqlVq1aFev4AADPxPVBfp58fVC1alXVqVNH8+fP1+zZs/Xf//7XIcXszMxMdevWTR07dtSHH36oESNG6LXXXrOZQ8bFxSkxMVH79+8v9nHzNGjQQBUrVtTUqVO1cOFCh41tnpSUpPvvv18DBgzQRx99pHvvvVfTpk3TqFGjirzPw4cPy8fHR5UqVbIs+/DDD7VgwQJNnjxZGzZsUPv27Qt0/lzpkUcekZeXl2Voo127dikuLs6qI8rhw4fVpk0bLViwQBs3btTkyZP17bff6tZbb1VmZma+ff7tb39TnTp19P777ys+Pl4ffvihunTpYnPbwnj22Wd17733SpLVZzY8PFyjRo3STz/9pC+++MLqOevWrdPhw4fzdQADPIZL+8EDpVBBfqoVGhpqNGjQwPL46iEG3n//fUOSsXfvXrv7uNbPNfP2Z+uncraGM4iKijJMJlO+43Xq1MkoX7685aeeea/t6NGjVtvlDQ+xZcsWy7Jr/Vzz6rjvv/9+w2w2G8ePH7farmvXrkZgYKBx7tw5q+N069bNart3333XkGTs3LnT5vHy3HLLLUZISIhx4cIFy7KsrCyjcePGRo0aNSzDj9j7CaYtedvOnDnTyMzMNC5dumTs2bPHaNWqlSHJWLt2rWEYhvHHH38YkowZM2ZYPX/lypWGJOPVV1/Nt+/MzEyr6Ur23vuoqChj0KBBlsdDhw41ypUrZyQmJlpt9+KLLxqSjAMHDhiGkTsES8WKFa/5Whs3bmz06tXrmtvkadeundG6desCbVsQSUlJhiTj/vvvz7cuKyvLqp2uHEYmNjbWiI2NzfecQYMGWZ2fK1asMCQZH3zwgdV2u3fvNiQZ8+fPNwyjYJ/NgrSlrc/MoEGDDEnGu+++a7Vtt27djBtuuMHyeN68eYYkY926dVbbDR06tEA/e8079qpVq4zMzEwjNTXV+PLLL406deoY3t7exn/+8x/DMOx/j6xfv96QZMyaNctq+apVqwxJxsKFCy3LCvrdcrW89/SRRx4xmjVrZrVOkhEQEGD1k/esrCyjfv36Rp06dfK9zivb2Nb339XnyLW+W/O+A3/44QfLsl27dhmSjGXLltl8LQAAcH2Qi+uDgl0fGEZuflGzZk1DkiHJCA4ONnr06GG8+eab+YZMvLrtrpVnvvzyy1bPff755w1JxldffWW1/JdffjEkGQsWLLju6y2MtWvXGlWrVrW8ripVqhh9+vQxPv74Y6vtbA3nYutci42NNSQZH330kdXzH3vsMcPLyyvfNdDV8oZzybuO+P33340JEyYYkow+ffpYtpNkVKhQwfjrr7+snl/Q8ycv9r/97W9Wz//666+vOZxiTk6OkZmZaSQmJuZ7nXmf2zFjxlg955133jEkGW+//bbV67z6mqgg54294Vyys7ON2rVrG3fffbfV8q5duxoxMTEOH9YTKC3oiQ44gXGdngRNmzaVn5+fhgwZomXLluX7iVdB3XPPPQXetlGjRrrpppuslvXv31/Jycn6/vvvi3T8gtq8ebM6duyoyMhIq+WDBw9Wampqvl4qd911l9XjJk2aSLL+aeTVUlJS9O233+ree+9VuXLlLMu9vb01cOBA/e9//yvwTz5tefrpp+Xr6yt/f3+1aNFCx48f12uvvaZu3bpJkn7//XdJUkhISIH2d+7cOfn6+lpNRbmb+6effqoOHTooIiJCWVlZlqlr166ScnvfSNLNN9+sc+fOqV+/fvroo49s/lzw5ptv1rp16zRhwgRt3brV0jvblpCQkOveoDInJ8cqpqL+5LJFixZW7fTSSy8Veh+ffvqpKlasqJ49e1rF1LRpU4WFhVl+1liQz2ZB2tIek8mU71cJTZo0sTq3t23bpuDgYN15551W2/Xr168Qr1i677775Ovrq8DAQN12223Kzs7W+++/b/k85bn6e2Tz5s2SlG/YoD59+igoKChfj5SCfre89957ateuncqVKycfHx/5+vpq8eLFOnToUL7YO3bsqNDQUMtjb29v3Xffffr111/1v//9r+CNUEj9+vVTSEiIVW/0f//736pWrVqxfwkAAPBsXB9Y8/Trg1atWunXX3/V+vXr9cwzz6hNmzb64osv9OCDD+quu+4qcs/0AQMGWD3u37+/JOUbhjEvpuvl89nZ2Va5c05OzjW379atm44fP641a9Zo3LhxatSokT788EPdddddGjFiRGFfjiQpODg43/vfv39/5eTkXPPXHXkOHDhguY6IiIjQSy+9pAEDBmjRokVW291+++1WPdOLcv5c3f5t27ZVVFSUVfufOnVKjz/+uCIjIy05cVRUlCTZzIuv3mffvn3l4+Pj8KE1r+Tl5aURI0bo008/1fHjxyXl9qBfv369hg0bZnd4KKCso4gOOFhKSorOnDljNW7z1WJiYvT5558rJCREw4cPV0xMjGJiYvTyyy8X6liFGfYiLCzM7jJn3Jn9SmfOnLEZa14bXX38KlWqWD02m82SdM2i7tmzZ2UYRqGOUxijRo3S7t27tWfPHh0+fFgnT57UkCFDLOvzYvP397d6Xs2aNSXlT/CDg4O1e/du7d69W1OmTClyXH/88Yc++eSTfAX5Ro0aSZKlwDtw4EC98cYbSkxM1D333KOQkBC1bt1amzZtsuzrlVde0dNPP60PP/xQHTp0UOXKldWrVy/98ssv+Y7r7+9/zfdDkp577jmrmPLG5bSlatWqCggIsHkhtHz5cu3evVsff/xxgdrElj/++EPnzp2Tn59fvrZKSkqytFNBPpsFaUt7AgMD850jZrNZly5dsjw+c+aMVQE5j61l1zJz5kzt3r1b33//vY4fP64jR46oV69e+ba7+jNz5swZ+fj45Bv2xWQyKSwsLN/nqCDfLatXr1bfvn1VvXp1vf3229q5c6d2796thx9+2Oq1F2afzmA2mzV06FAtX75c586d0+nTp/Xuu+/q0UcftXwPAQBQWFwf5OfJ1wd5fH191aVLFz3//PPasGGDTpw4obi4OH366adat25doePx8fHJ10723s+8mK6Xz8fExFjlzc8999x14wgICFCvXr30wgsvaNu2bfr111/VsGFDzZs3TwcOHCjMS5JkOwcuzHkaExOj3bt367vvvtP+/ft17tw5vf3226pQoYLVdlefJ0U5f+x9pvK2y8nJUefOnbV69WqNHz9eX3zxhXbt2qVvvvlGku334+p95r3Pzv6MPvzwwwoICLAMCzVv3jwFBATo4YcfdupxAXfm4+oAgLJm7dq1ys7Ovu6NPdq3b6/27dsrOztb3333nf79739r9OjRCg0N1f3331+gYxXmP8BJSUl2l+UlW3nJ1NVjHhemp60tVapU0cmTJ/Mtz+udUbVq1WLtX5IqVaokLy8vpx2nRo0aatmypd31efv+66+/rJa3aNFClSpV0ieffKKEhATLcm9vb8v+bI1FaDab870PUv5ErWrVqmrSpImef/55m3FdebH20EMP6aGHHlJKSoq+/PJLTZkyRT169NDPP/+sqKgoBQUFaerUqZo6dar++OMPS6/0nj176r///a/Vfv/666/rtueQIUPUo0cPq9dkj7e3t26//XZt3LhRJ0+etEpWGzZsKEk2x1X09/fX+fPn8y2/+pytWrWqqlSpYhlL9GrBwcGW+YJ8Nq/XlsVRpUoV7dq1K99yW5/ha6ldu/Y1z9k8V3+PVKlSRVlZWTp9+rRVId0wDCUlJeUbF7wg3y1vv/22oqOjtWrVKqvj2TrHC7pPZ3niiSc0Y8YMvfHGG7p06ZKysrL0+OOPO/WYAICyjeuD/Dz5+sCeKlWqaPTo0dq6dav2799v6dFeUFlZWTpz5oxVrmQvf8qL6Xqv/5NPPrF676/1jyB7atasqSFDhmj06NE6cOCApbNPQf3xxx/5lhUmL/T39y9STlyU88feZ6pOnTqScq/7/vOf/2jp0qUaNGiQZZu88fRtSUpKUvXq1S2Pbb3PzlChQgUNGjRIr7/+usaNG6clS5aof//+qlixolOPC7gzeqIDDnT8+HGNGzdOFSpU0NChQwv0HG9vb7Vu3doyfEDeTycL0ruiMA4cOKD//Oc/VsuWL1+u4OBgNW/eXJIsd6Hft2+f1Xa2egCbzeYCx9axY0dt3rzZkmzkefPNNxUYGGi5k31xBAUFqXXr1lq9erVVXDk5OXr77bdVo0YN1atXr9jHsScqKkoBAQE6fPiw1XI/Pz/9/e9/1/79+61uHnk9tWrVyvc+bN68WRcvXrRa1qNHD+3fv18xMTFq2bJlvslWohsUFKSuXbtq0qRJysjIsNkjJDQ0VIMHD1a/fv30008/KTU11Wr9kSNHLMVteyIiIqxiufHGG6+5/cSJE5Wdna3HH3+8wDfKqVWrln7++Wer5P7MmTPasWOH1XY9evTQmTNnlJ2dbbOdbrjhhnz7tvfZvFJB2rKwYmNjdeHChXw9kFauXFnsfRdEx44dJeUWvq/0wQcfKCUlxbI+T0G+W0wmk/z8/KwuTpKSkvTRRx/ZjOGLL76wumDKzs7WqlWrFBMToxo1ahT9xen6363h4eHq06eP5s+fr1dffVU9e/a0/KIEAIDC4vrANk++PsjMzLTbizhvOI+iFKsl6Z133rF6vHz5cknK9w+cvOGCrpfP33jjjde9tshz4cKFfNcqeYrzui5cuJDvfFu+fLm8vLx02223FXp/BVWU8+fq9t+xY4cSExMt7Z+XC1/duei1116zG8fV+3z33XeVlZV13X/KFcT1vlNGjhypP//8U/fee6/OnTtX5CF5gLKCnuhAEe3fv98yNtypU6e0fft2LVmyRN7e3lqzZk2+oRCu9Oqrr2rz5s3q3r27atasqUuXLumNN96QJN1xxx2ScnvGRkVF6aOPPlLHjh1VuXJlVa1a1ZLIFlZERITuuusuxcfHKzw8XG+//bY2bdqkmTNnKjAwUFLu2Hw33HCDxo0bp6ysLFWqVElr1qzRV199lW9/N954o1avXq0FCxaoRYsW8vLysvsf/ilTpljG7p48ebIqV66sd955R2vXrtWsWbPy/ZSuqKZPn65OnTqpQ4cOGjdunPz8/DR//nzt379fK1ascOrYbX5+fmrTpo3lp3hXevrpp/Xf//5XEyZM0Jdffqn77rtPtWrVUnp6uo4cOaLXX39d3t7elvdByh0y5Nlnn9XkyZMVGxurgwcPau7cufna6rnnntOmTZvUtm1bjRw5UjfccIMuXbqkY8eO6bPPPtOrr76qGjVq6LHHHlNAQIDatWun8PBwJSUlafr06apQoYKlZ3Hr1q3Vo0cPNWnSRJUqVdKhQ4f01ltvqU2bNlaxnTlzRr/88ouefPJJh7Zhu3btNG/ePD355JNq3ry5hgwZokaNGll6gHzwwQeSpPLly1u102uvvaYHHnhAjz32mM6cOaNZs2ZZbSNJ999/v9555x1169ZNo0aN0s033yxfX1/973//05YtW3T33Xfrb3/7W4E+mwVpy+IYNGiQ/vWvf+mBBx7QtGnTVKdOHa1bt04bNmyQlDtGoTN16tRJXbp00dNPP63k5GS1a9dO+/bt05QpU9SsWTMNHDjQavuCfLf06NFDq1ev1rBhw3TvvffqxIkT+uc//6nw8HCbwwVVrVpVt99+u5599lkFBQVp/vz5+u9//+uQfyQU5Lt11KhRat26tSRpyZIlxT4mAMAzcH3A9cGV7F0fnD9/XrVq1VKfPn10xx13KDIyUhcvXtTWrVv18ssvq0GDBurdu3eRjvfSSy/p4sWLatWqlXbs2KFp06apa9euuvXWW622/eabb+Tt7e3QIvRPP/2kLl266P7771dsbKzCw8N19uxZrV27VgsXLlRcXJzatm1b6P1WqVJFTzzxhI4fP6569erps88+06JFi/TEE084vaNDYc+f7777To8++qj69OmjEydOaNKkSapevbqGDRsmSapfv75iYmI0YcIEGYahypUr65NPPrnmsJCrV6+Wj4+POnXqpAMHDujZZ5/VTTfdpL59+xb79eV1cpo5c6a6du0qb29vNWnSRH5+fpKkevXq6c4779S6det066235ruHAuBxXHdPU6B0yrvzdt7k5+dnhISEGLGxsUZCQoJx6tSpfM/Ju7N2np07dxp/+9vfjKioKMNsNhtVqlQxYmNj8921/PPPPzeaNWtmmM1mQ5IxaNAgq/2dPn36uscyDMOIiooyunfvbrz//vtGo0aNDD8/P6NWrVrG7Nmz8z3/559/Njp37myUL1/eqFatmvHkk08aa9euzXcn77/++su49957jYoVKxomk8nqmLrqTuCGYRg//vij0bNnT6NChQqGn5+fcdNNN1ndjd0wLt8x/L333rNabuvu7fZs377duP32242goCAjICDAuOWWW4xPPvnE5v5eeOGF6+6vMNsuXrzY8Pb2Nn7//Xeb6z/++GOjZ8+eRmhoqOHj42MEBwcbTZs2NZ566injv//9r9W26enpxvjx443IyEgjICDAiI2NNfbu3WtERUVZzoM8p0+fNkaOHGlER0cbvr6+RuXKlY0WLVoYkyZNMi5evGgYhmEsW7bM6NChgxEaGmr4+fkZERERRt++fY19+/ZZ9jNhwgSjZcuWRqVKlQyz2WzUrl3bGDNmjPHnn3/me52+vr5GUlLSddukKPbu3Ws89NBDRnR0tGE2mw1/f3+jTp06xoMPPmh88cUX+bZftmyZ0aBBA8Pf399o2LChsWrVKmPQoEFGVFSU1XaZmZnGiy++aNx0002Gv7+/Ua5cOaN+/frG0KFDjV9++cUwjIJ9NgvSlnnn8pWfmUGDBhlBQUH54rf1mT1+/LjRu3dvo1y5ckZwcLBxzz33GJ999pkhyfjoo4+u2X72Pkf2jmvreyQtLc14+umnjaioKMPX19cIDw83nnjiCePs2bNW2xXmu2XGjBlGrVq1DLPZbDRo0MBYtGiRzdcuyRg+fLgxf/58IyYmxvD19TXq169vvPPOOzZf55VtbGt/sbGxRmxsrNUye9+tV6pVq5bRoEEDGy0HAIA1rg9ycX2Qn63rg/T0dOPFF180unbtatSsWdOS7zZo0MAYP368cebMGat9XN1218oz9+3bZ8TFxRkBAQFG5cqVjSeeeMJyPXCl9u3bGz179rxu/IVx9uxZY9q0acbtt99uVK9e3fDz8zOCgoKMpk2bGtOmTTNSU1Mt29p6//I+R0ePHrUsi42NNRo1amRs3brVaNmypWE2m43w8HDjmWeeMTIzM68bU97zrycv/7SlIOdPXuwbN240Bg4caFSsWNEICAgwunXrZrnOyHPw4EGjU6dORnBwsFGpUiWjT58+xvHjx/O9z3mf2z179hg9e/a0XBf069fP+OOPP/K9zqvz3YKcN+np6cajjz5qVKtWzfKZvbL9DcMwli5dakgyVq5cee1GBDyAyTCKeNtnAICVS5cuqWbNmnrqqaf09NNPuzocp2nfvr1q1qyZ76eFcK6EhAT94x//0PHjx4s9pAmubd++fbrppps0b948S88hAACAwiqp64PBgwfr/ffftzucypUOHz6sunXrasOGDerUqZPTYnKEuLg4/fnnnzbvIYWScc899+ibb77RsWPH5Ovr6+pwAJdiOBcAcBB/f39NnTpV8fHxGjFihIKCglwdksN9+eWX2r17t5YtW+bqUMq0uXPnSsr9yWdmZqY2b96sV155RQ888AAFdCc6fPiwEhMT9cwzzyg8PFyDBw92dUgAAKAUc8frg2nTpqljx45uX0CH66Snp+v777/Xrl27tGbNGs2ePZsCOiCK6ADgUEOGDNG5c+d05MiR695IszQ6c+aM3nzzTdWuXdvVoZRpgYGB+te//qVjx44pPT1dNWvW1NNPP61//OMfrg6tTPvnP/+pt956Sw0aNNB7771ndS8AAACAonCn64OsrCzFxMRo4sSJLo0D7u3kyZNq27atypcvr6FDhzr8XlhAacVwLgAAAAAAAAAA2OHl6gAAAAAAAAAAAHBXFNEBAAAAAAAAALCDIjoAAAAAAAAAAHaU+RuL5uTk6Pfff1dwcLBMJpOrwwEAAABkGIYuXLigiIgIeXl5Tr8WcnMAAAC4k4Lm5WW+iP77778rMjLS1WEAAAAA+Zw4cUI1atRwdRglhtwcAAAA7uh6eXmZL6IHBwdLym2I8uXLuzgauFxKihQRkTv/++9SUJBr4wEAAB4pOTlZkZGRllzVU5CblzLkzgAAoIwraF5e5ovoeT8TLV++PIk6JG/vy/Ply3MhAAAAXMrThjQhNy9lyJ0BAICHuF5e7jkDMAIAAAAAAAAAUEgU0QEAAAAAAAAAsKPMD+cCWPH1lWbNujwPAAAAwDZyZwAAAEkU0eFp/Pykv//d1VEAAMqY7OxsZWZmujoMuBFfX195XzmeNFAakTsDAEoZ8nJczVF5OUV0AACAIjIMQ0lJSTp37pyrQ4EbqlixosLCwjzu5qEAAAAljbwc1+KIvJwiOjxLdrb0/fe5882bS/QQAwAUQ16iHhISosDAQIqlkJR7EZeamqpTp05JksLDw10cEVBE5M4AgFKCvBy2ODIvp4gOz3LpknTzzbnzFy9KQUGujQcAUGplZ2dbEvUqVaq4Ohy4mYCAAEnSqVOnFBISwtAuKJ3InQEApQB5Oa7FUXm5lyODAgAA8BR5Yy0GBga6OBK4q7xzg3E5AQAAnIe8HNfjiLycIjoAAEAx8FNR2MO5AQAAUHLIvWCPI84NiugAAAAAAAAAANhBER0AAAAAAAAAADsoogMAAHiQwYMHy2Qy6fHHH8+3btiwYTKZTBo8eHDJB1YAhmEoPj5eERERCggIUFxcnA4cOHDN5xw4cED33HOPatWqJZPJpDlz5uTbZvr06WrVqpWCg4MVEhKiXr166aeffrLaJq/drpxuueUWR748AAAAeBhPy80l6YMPPlDDhg1lNpvVsGFDrVmzJt82v/32mx544AFVqVJFgYGBatq0qfbs2WNZ74rcnCI6AACAh4mMjNTKlSuVlpZmWXbp0iWtWLFCNWvWdGFk1zZr1izNnj1bc+fO1e7duxUWFqZOnTrpwoULdp+Tmpqq2rVra8aMGQoLC7O5zbZt2zR8+HB988032rRpk7KystS5c2elpKRYbXfnnXfq5MmTlumzzz5z6OsDAACA5/Gk3Hznzp267777NHDgQP3nP//RwIED1bdvX3377beWbc6ePat27drJ19dX69at08GDB/XSSy+pYsWKVvsq6dzcpUX0vB5BV0/Dhw+XVPT/aAB2+fpKU6bkTr6+ro4GAFAWpaTYny5dKvi2VyTR19y2CJo3b66aNWtq9erVlmWrV69WZGSkmjVrZrWtYRiaNWuWateurYCAAN100016//33Leuzs7P1yCOPKDo6WgEBAbrhhhv08ssvW+1j8ODB6tWrl1588UWFh4erSpUqGj58uDIzMwscs2EYmjNnjiZNmqTevXurcePGWrZsmVJTU7V8+XK7z2vVqpVeeOEF3X///TKbzTa3Wb9+vQYPHqxGjRrppptu0pIlS3T8+HGr3i6SZDabFRYWZpkqV65c4PjdHXk5bCJ3BgCUduTmbpWbz5kzR506ddLEiRNVv359TZw4UR07drT6tejMmTMVGRmpJUuW6Oabb1atWrXUsWNHxcTEWO2rpHNzlxbRd+/ebfUfg02bNkmS+vTpI6lo/9EArsnPT4qPz538/FwdDQCgLCpXzv50zz3W24aE2N+2a1frbWvVsr1dET300ENasmSJ5fEbb7yhhx9+ON92//jHP7RkyRItWLBABw4c0JgxY/TAAw9o27ZtkqScnBzVqFFD7777rg4ePKjJkyfrmWee0bvvvmu1ny1btujw4cPasmWLli1bpqVLl2rp0qWW9fHx8apVq5bdeI8ePaqkpCR17tzZssxsNis2NlY7duwoYivYdv78eUnKl4hv3bpVISEhqlevnh577DGdOnXKocd1JfJy2ETuDAAo7cjN3So337lzp9VzJKlLly5Wz/n444/VsmVL9enTRyEhIWrWrJkWLVqUb18lnZu7tIherVo1q/8YfPrpp4qJiVFsbGyR/6MBAACA6xs4cKC++uorHTt2TImJifr666/1wAMPWG2TkpKi2bNn64033lCXLl1Uu3ZtDR48WA888IBee+01SZKvr6+mTp2qVq1aKTo6WgMGDNDgwYPzJeqVKlXS3LlzVb9+ffXo0UPdu3fXF198YVlftWrVfL1LrpSUlCRJCg0NtVoeGhpqWecIhmFo7NixuvXWW9W4cWPL8q5du+qdd97R5s2b9dJLL2n37t26/fbblZ6e7rBjuxJ5OQAAgOt4Sm6elJR03eccOXJECxYsUN26dbVhwwY9/vjjGjlypN58803LNq7IzX2ctudCysjI0Ntvv62xY8fKZDLpyJEj1/yPxtChQ23uJz093arBkpOTnR77tZw+fdplMZQvX17VqlVzybHdVk6OdOhQ7nyDBpIXtwUAADjYxYv213l7Wz++Vm+Jq/9GHTtW5JBsqVq1qrp3765ly5bJMAx1795dVatWtdrm4MGDunTpkjp16mS1PCMjw+qnpa+++qpef/11JSYmKi0tTRkZGWratKnVcxo1aiTvK15/eHi4fvzxR8vjESNGaMSIEdeN22QyWT02DCPfsuIYMWKE9u3bp6+++spq+X333WeZb9y4sVq2bKmoqCitXbtWvXv3dtjx3YGj8nLJvXJz8vIiIHcGAJR25OZul5tf7zk5OTlq2bKlEhISJEnNmjXTgQMHtGDBAj344IOSXJObu00R/cMPP9S5c+csd5y91n80EhMT7e5n+vTpmjp1qtPiLIzTp0/riUf7K/3iGZcc31yuiha8vrx0JuzOkpYm5fUqu3hRCgpybTwAgLKnMH9bnLVtAT388MOW5HjevHn51ufk5EiS1q5dq+rVq1utyxtf/N1339WYMWP00ksvqU2bNgoODtYLL7xgdXMgKbdXzJVMJpNl/wWRd1PQpKQkhYeHW5afOnUqX75YVE8++aQ+/vhjffnll6pRo8Y1tw0PD1dUVJR++eUXhxzbnTgqL5fcJzc/ffq0nnhsiNJTU11yfHNgoBYsWlj68nJyZwBAaUdu7la5eVhYWL6e6lc/Jzw8XA0bNrTapkGDBvrggw/s7rckcnO3KaIvXrxYXbt2VUREhNXywv5HY+LEiRo7dqzlcXJysiIjIx0bbAElJycr/eIZPdXTrMhqASV67BOn0/TSJ2eUnJxc+pJ1AABQIu68805lZGRIyh2L8GoNGzaU2WzW8ePHFRsba3Mf27dvV9u2bTVs2DDLssOHDzs81ujoaIWFhWnTpk2WnjYZGRnatm2bZs6cWax9G4ahJ598UmvWrNHWrVsVHR193eecOXNGJ06csLpoKCsclZdL7pObJycnKz01VU/dP1CRYSX7np1IOqmXVr5FXg4AAK7JE3LzNm3aaNOmTRozZoxl2caNG9W2bVvL43bt2umnn36yet7PP/+sqKgou/stidzcLYroiYmJ+vzzz63uQlvU/2iYzWbLf1/cRWS1AMVUd0WvjbIxRicAAHAOb29vHfr/oRq8r/45q6Tg4GCNGzdOY8aMUU5Ojm699VYlJydrx44dKleunAYNGqQ6derozTff1IYNGxQdHa233npLu3fvLlAh+kpz587VmjVrrMZivJLJZNLo0aOVkJCgunXrqm7dukpISFBgYKD69+9v2e7BBx9U9erVNX36dEm5yfzBgwct87/99pv27t2rcuXKqU6dOpKk4cOHa/ny5froo48UHBxs6R1ToUIFBQQE6OLFi4qPj9c999yj8PBwHTt2TM8884yqVq2qv/3tb4V6ne7OkXm55H65eWRYuGIia7o6DAAAgHw8ITcfNWqUbrvtNs2cOVN33323PvroI33++edWQymOGTNGbdu2VUJCgvr27atdu3Zp4cKFWrhwoSS5LDd3iyL6kiVLFBISou7du1uWObO3EQAAAHKVL1/+muv/+c9/KiQkRNOnT9eRI0dUsWJFNW/eXM8884wk6fHHH9fevXt13333yWQyqV+/fho2bJjWrVtXqDj+/PPP6/aSGT9+vNLS0jRs2DCdPXtWrVu31saNGxUcHGzZ5vjx4/K6YszK33//3WqMyBdffFEvvviiYmNjtXXrVknSggULJElxcXFWx1uyZIkGDx4sb29v/fjjj3rzzTd17tw5hYeHq0OHDlq1apXVscsC8nIAAADXKeu5edu2bbVy5Ur94x//0LPPPquYmBitWrVKrVu3tmzTqlUrrVmzRhMnTtRzzz2n6OhozZkzRwMGDJAkl+XmJsMwDKftvQBycnIUHR2tfv36acaMGVbrZs6cqenTp2vJkiWW/2hs3bpVP/30U4EbJTk5WRUqVND58+eveyI62uHDhzV6SB/NebhiifdEP/xbika/cU5zFr53zbvpepyUFKlcudx5xnUEABTDpUuXdPToUUVHR8vf39/V4cANXesccWWOao+z83LJda/78OHDGv3EMM0ZPb7Ee6IfPnFco+fM0pwF80tfXk7uDAAoBcjLcT2OyMtd3hP9888/1/Hjx/Xwww/nW1eQ/2gAAAAAKD7ycgAAAMA2lxfRO3fuLHud4U0mk+Lj4xUfH1+yQQEAAAAehrwcAAAAsM3lRXSgRPn6SuPGXZ4HAAAAYBu5MwAAgCSK6PA0fn7SCy+4OgoAAADA/ZE7AwAASJK8rr8JAAAA7HHxPdrhxjg3AAAASg65F+xxxLlBER2eJSdHOnYsd8rJcXU0AIBSzPf/hzZITU11cSRwV3nnhi/DYKC0IncGAJQC5OW4Hkfk5QznAs+SliZFR+fOX7woBQW5Nh4AQKnl7e2tihUr6tSpU5KkwMBAmUwmF0cFd2AYhlJTU3Xq1ClVrFhR3t7erg4JKBpyZwBAKUBeDnscmZdTRAcAACiisLAwSbIk7MCVKlasaDlHAAAA4Dzk5bgWR+TlFNEBAACKyGQyKTw8XCEhIcrMzHR1OHAjvr6+9EAHAAAoIeTlsMdReTlFdAAAgGLy9vamYAoAAAC4GHk5nIUbiwIAAAAAAAAAYAdFdAAAAAAAAAAA7KCIDgAAAAAAAACAHYyJDs/i4yMNG3Z5HgAAAIBt5M4AAACSKKLD05jN0rx5ro4CAAAAcH/kzgAAAJIYzgUAAAAAAAAAALvoiQ7PYhjSn3/mzletKplMro0HAAAAcFfkzgAAAJIoosPTpKZKISG58xcvSkFBro0HAAAAcFfkzgAAAJIYzgUAAAAAAAAAALsoogMAAAAAAAAAYAdFdAAAAAAAAAAA7KCIDgAAAAAAAACAHRTRAQAAAAAAAACwgyI6AAAAAAAAAAB2+Lg6AKBE+fhIgwZdngcAAABgG7kzAACAJIro8DRms7R0qaujAAAAANwfuTMAAIAkhnMBAAAAAAAAAMAueqLDsxiGlJqaOx8YKJlMro0HAAAAcFfkzgAAAJLoiQ5Pk5oqlSuXO+VdEAAAAADIj9wZAABAEkV0AAAAAAAAAADsoogOAAAAAAAAAIAdFNEBAAAAAAAAALCDIjoAAAAAAAAAAHZQRAcAAAAAAAAAwA6K6AAAAAAAAAAA2OHj6gCAEuXtLd177+V5AAAAALaROwMAAEiiiA5P4+8vvfeeq6MAAAAA3B+5MwAAgCSGcwEAAAAAAAAAwC6K6AAAAAAAAAAA2EERHZ4lJUUymXKnlBRXRwMAAAC4L3JnAAAASRTRAQAAAAAAAACwiyI6AAAAAAAAAAB2UEQHAAAAAAAAAMAOiugAAAAAAAAAANhBER0AAAAAAAAAADsoogMAAAAAAAAAYIePqwMASpS3t9St2+V5AAAAALaROwMAAEhyg57ov/32mx544AFVqVJFgYGBatq0qfbs2WNZbxiG4uPjFRERoYCAAMXFxenAgQMujBilmr+/tHZt7uTv7+poAAAA3AZ5OfIhdwYAAJDk4iL62bNn1a5dO/n6+mrdunU6ePCgXnrpJVWsWNGyzaxZszR79mzNnTtXu3fvVlhYmDp16qQLFy64LnAAAACgDCEvBwAAAOxz6XAuM2fOVGRkpJYsWWJZVqtWLcu8YRiaM2eOJk2apN69e0uSli1bptDQUC1fvlxDhw4t6ZABAACAMoe8HAAAALDPpT3RP/74Y7Vs2VJ9+vRRSEiImjVrpkWLFlnWHz16VElJSercubNlmdlsVmxsrHbs2GFzn+np6UpOTraaAIuUFCkoKHdKSXF1NAAAAG7BGXm5RG5e6pE7AwAASHJxEf3IkSNasGCB6tatqw0bNujxxx/XyJEj9eabb0qSkpKSJEmhoaFWzwsNDbWsu9r06dNVoUIFyxQZGencF4HSJzU1dwIAAIAk5+TlErl5mUDuDAAA4Noiek5Ojpo3b66EhAQ1a9ZMQ4cO1WOPPaYFCxZYbWcymaweG4aRb1meiRMn6vz585bpxIkTTosfAAAAKAuckZdL5OYAAAAoG1xaRA8PD1fDhg2tljVo0EDHjx+XJIWFhUlSvt4tp06dytcLJo/ZbFb58uWtJgAAAAD2OSMvl8jNAQAAUDa4tIjerl07/fTTT1bLfv75Z0VFRUmSoqOjFRYWpk2bNlnWZ2RkaNu2bWrbtm2JxgoAAACUVeTlAAAAgH0+rjz4mDFj1LZtWyUkJKhv377atWuXFi5cqIULF0rK/bno6NGjlZCQoLp166pu3bpKSEhQYGCg+vfv78rQAQAAgDKDvBwAAACwz6VF9FatWmnNmjWaOHGinnvuOUVHR2vOnDkaMGCAZZvx48crLS1Nw4YN09mzZ9W6dWtt3LhRwcHBLowcAAAAKDvIywEAAAD7XFpEl6QePXqoR48edtebTCbFx8crPj6+5IJC2eXlJcXGXp4HAACAJPJy2EDuDAAAIMkNiuhAiQoIkLZudXUUAAAAgPsjdwYAAJDk4huLAgAAAAAAAADgziiiAwAAAAAAAABgB0V0eJaUFKlatdwpJcXV0QAAAADui9wZAABAEmOiwxP9+aerIwAAAABKB3JnAAAAeqIDAAAAAAAAAGAPRXQAAAAAAAAAAOygiA4AAAAAAAAAgB0U0QEAAAAAAAAAsIMiOgAAAAAAAAAAdvi4OgCgRHl5SS1bXp4HAAAAYBu5MwAAgCSK6PA0AQHS7t2ujgIAAABwf+TOAAAAkhjOBQAAAAAAAAAAuyiiAwAAAAAAAABgB0V0eJbUVKlWrdwpNdXV0QAAAADui9wZAABAEmOiw9MYhpSYeHkeAAAAgG3kzgAAAJLoiQ4AAAAAAAAAgF0U0QEAAAAAAAAAsIMiOgAAAAAAAAAAdlBEBwAAAAAAAADADoroAAAAAAAAAADY4ePqAIASZTJJDRtengcAAABgG7kzAACAJIro8DSBgdKBA66OAgAAAHB/5M4AAACSGM4FAAAAAAAAAAC7KKIDAAAAAAAAAGAHRXR4ltRUqVGj3Ck11dXRAAAAAO6L3BkAAEASY6LD0xiGdPDg5XkAAAAAtpE7AwAASKInOgAAAAAAAAAAdlFEBwAAAAAAAADADoroAAAAAAAAAADYQREdAAAAAAAAAAA7KKIDAAAAAAAAAGCHj6sDAEqUySRFRV2eBwAAAGAbuTMAAIAkiujwNIGB0rFjro4CAAAAcH/kzgAAAJIYzgUAAAAAAAAAALsoogMAAAAAAAAAYAdFdHiWtDSpVavcKS3N1dEAAAAA7ovcGQAAQBJjosPT5ORI3313eR4AAACAbeTOAAAAkuiJDgAAAAAAAACAXRTRAQAAAAAAAACwgyI6AAAAAAAAAAB2UEQHAAAAAAAAAMAOiugAAAAAAAAAANjh4+oAgBJXtaqrIwAAAABKB3JnAAAAiujwMEFB0unTro4CAAAAcH/kzgAAAJIYzgUAAAAAAAAAALtcWkSPj4+XyWSymsLCwizrDcNQfHy8IiIiFBAQoLi4OB04cMCFEQMAAABlD3k5AAAAYJ/Le6I3atRIJ0+etEw//vijZd2sWbM0e/ZszZ07V7t371ZYWJg6deqkCxcuuDBilGppaVJcXO6UlubqaAAAANwGeTnyIXcGAACQ5AZjovv4+Fj1csljGIbmzJmjSZMmqXfv3pKkZcuWKTQ0VMuXL9fQoUNt7i89PV3p6emWx8nJyc4JHKVTTo60bdvleQAAAEhyfF4ukZuXeuTOAAAAktygJ/ovv/yiiIgIRUdH6/7779eRI0ckSUePHlVSUpI6d+5s2dZsNis2NlY7duywu7/p06erQoUKlikyMtLprwEAAAAo7Rydl0vk5gAAACgbXFpEb926td58801t2LBBixYtUlJSktq2baszZ84oKSlJkhQaGmr1nNDQUMs6WyZOnKjz589bphMnTjj1NQAAAAClnTPyconcHAAAAGWDS4dz6dq1q2X+xhtvVJs2bRQTE6Nly5bplltukSSZTCar5xiGkW/Zlcxms8xms3MCBgAAAMogZ+TlErk5AAAAygaXD+dypaCgIN1444365ZdfLOMxXt275dSpU/l6wQAAAABwHPJyAAAA4DK3KqKnp6fr0KFDCg8PV3R0tMLCwrRp0ybL+oyMDG3btk1t27Z1YZQAAABA2UZeDgAAAFzm0uFcxo0bp549e6pmzZo6deqUpk2bpuTkZA0aNEgmk0mjR49WQkKC6tatq7p16yohIUGBgYHq37+/K8NGaRcY6OoIAAAA3Ap5OewidwYAAHBtEf1///uf+vXrpz///FPVqlXTLbfcom+++UZRUVGSpPHjxystLU3Dhg3T2bNn1bp1a23cuFHBwcGuDBulWVCQlJLi6igAAADcCnk5bCJ3BgAAkOTiIvrKlSuvud5kMik+Pl7x8fElExAAAADggcjLAQAAAPvcakx0AAAAAAAAAADcCUV0eJZLl6Tu3XOnS5dcHQ0AAADgvsidAQAAJLl4OBegxGVnS599dnkeAAAAgG3kzgAAAJLoiQ4AAAAAAAAAgF0U0QEAAAAAAAAAsIMiOgAAAAAAAAAAdlBEBwAAAAAAAADADoroAAAAAAAAAADYQREdAAAAAAAAAAA7fFwdAFCigoIkw3B1FAAAAID7I3cGAACQRE90AAAAAAAAAADsoogOAAAAAAAAAIAdFNHhWS5dkvr0yZ0uXXJ1NAAAAID7IncGAACQVIQi+rJly7R27VrL4/Hjx6tixYpq27atEhMTHRoc4HDZ2dL77+dO2dmujgYAAKBYyM3hVOTOAAAAkopQRE9ISFBAQIAkaefOnZo7d65mzZqlqlWrasyYMQ4PEAAAAIBt5OYAAACA8/kU9gknTpxQnTp1JEkffvih7r33Xg0ZMkTt2rVTXFyco+MDAAAAYAe5OQAAAOB8he6JXq5cOZ05c0aStHHjRt1xxx2SJH9/f6WlpTk2OgAAAAB2kZsDAAAAzlfonuidOnXSo48+qmbNmunnn39W9+7dJUkHDhxQrVq1HB0fAAAAADvIzQEAAADnK3RP9Hnz5qlNmzY6ffq0PvjgA1WpUkWStGfPHvXr18/hAQIAAACwjdwcAAAAcL5C90SvWLGi5s6dm2/51KlTHRIQAAAAgIIhNwcAAACcr0BF9H379hV4h02aNClyMIDTBQZKFy9engcAAChlyM1RYsidAQAAJBWwiN60aVOZTCYZhmFzfd46k8mk7OxshwYIOJTJJAUFuToKAACAIiM3R4khdwYAAJBUwCL60aNHnR0HAAAAgAIgNwcAAABKVoGK6FFRUc6OAygZ6enS0KG586+9JpnNro0HAACgkMjNUWLInQEAACQVsIj+8ccfF3iHd911V5GDAZwuK0tatix3ft48LgQAAECpQ26OEkPuDAAAIKmARfRevXoVaGeMuwgAAAA4F7k5AAAAULIKVETPyclxdhwAAAAACoDcHAAAAChZXq4OAAAAAAAAAAAAd1WgnuivvPKKhgwZIn9/f73yyivX3HbkyJEOCQwAAABAfuTmAAAAQMkqUBH9X//6lwYMGCB/f3/961//srudyWQiUQcAAACciNwcAAAAKFkFKqIfPXrU5jwAAACAkkVuDgAAAJSsQo+Jvm/fPrvrPvzww+LEAjhfYKB06lTuFBjo6mgAAACKhdwcTkXuDAAAIKkIRfQuXbroyJEj+ZZ/8MEHGjBggEOCApzGZJKqVcudTCZXRwMAAFAs5OZwKnJnAAAASUUooj/xxBPq2LGjTp48aVm2atUqPfjgg1q6dKkjYwMAAABwDeTmAAAAgPMVaEz0K02ePFlnzpzRHXfcoe3bt2v9+vV69NFH9dZbb+mee+5xRoyA46SnS2PH5s7Pni2Zza6NBwAAoBjIzeFU5M4AAACSilBEl6SXX35ZAwcO1C233KLffvtNK1as0N133+3o2ADHy8qS5s/PnZ81iwsBAABQ6pGbw2nInQEAACQVsIj+8ccf51vWq1cvbdu2Tf369ZPJZLJsc9dddzk2QgAAAAAW5OYAAABAySpQEb1Xr152173xxht64403JEkmk0nZ2dkOCQwAAABAfuTmAAAAQMkqUBE9JyfH2XEAAAAAKABycwAAAKBkebk6AAAAAAAAAAAA3FWBi+jffvut1q1bZ7XszTffVHR0tEJCQjRkyBClp6c7PEAAAAAA1sjNAQAAgJJT4CJ6fHy89u3bZ3n8448/6pFHHtEdd9yhCRMm6JNPPtH06dOdEiQAAACAy8jNAQAAgJJT4CL63r171bFjR8vjlStXqnXr1lq0aJHGjh2rV155Re+++65TggQcJiBAOno0dwoIcHU0AAAARUJujhJB7gwAACCpgDcWlaSzZ88qNDTU8njbtm268847LY9btWqlEydOODY6wNG8vKRatVwdBQAAQLGQm6NEkDsDAABIKkRP9NDQUB09elSSlJGRoe+//15t2rSxrL9w4YJ8fX0dHyEAAAAAK+TmAAAAQMkpcBH9zjvv1IQJE7R9+3ZNnDhRgYGBat++vWX9vn37FBMTU+RApk+fLpPJpNGjR1uWGYah+Ph4RUREKCAgQHFxcTpw4ECRjwEoI0P6+99zp4wMV0cDAABQJOTmKBHkzgAAAJIKUUSfNm2avL29FRsbq0WLFmnRokXy8/OzrH/jjTfUuXPnIgWxe/duLVy4UE2aNLFaPmvWLM2ePVtz587V7t27FRYWpk6dOunChQtFOg6gzEzpxRdzp8xMV0cDAABQJOTmKBHkzgAAAJIKMSZ6tWrVtH37dp0/f17lypWTt7e31fr33ntP5cqVK3QAFy9e1IABA7Ro0SJNmzbNstwwDM2ZM0eTJk1S7969JUnLli1TaGioli9frqFDh9rcX3p6utLT0y2Pk5OTCx0TAAAA4M7IzQEAAICSU+Ce6HkqVKiQL0mXpMqVK1v1fimo4cOHq3v37rrjjjuslh89elRJSUlWPWjMZrNiY2O1Y8cOu/ubPn26KlSoYJkiIyMLHRMAAABQGpCbAwAAAM5X6CK6I61cuVLff/+9pk+fnm9dUlKSpNybJl0pNDTUss6WiRMn6vz585bpxIkTjg0aAAAAKIPIzQEAAADbCjyci6OdOHFCo0aN0saNG+Xv7293O5PJZPXYMIx8y65kNptlNpsdFicAAABQ1pGbAwAAAPa5rCf6nj17dOrUKbVo0UI+Pj7y8fHRtm3b9Morr8jHx8fSy+Xqni2nTp3K1wMGAAAAQNGRmwMAAAD2FaiI3rx5c509e1aS9Nxzzyk1NbXYB+7YsaN+/PFH7d271zK1bNlSAwYM0N69e1W7dm2FhYVp06ZNludkZGRo27Ztatu2bbGPDwAAAJRG5OYAAABAySrQcC6HDh1SSkqKKlWqpKlTp+rxxx9XYGBgsQ4cHBysxo0bWy0LCgpSlSpVLMtHjx6thIQE1a1bV3Xr1lVCQoICAwPVv3//Yh0bHiwgQNq///I8AABAKUNujhJD7gwAACCpgEX0pk2b6qGHHtKtt94qwzD04osvqly5cja3nTx5ssOCGz9+vNLS0jRs2DCdPXtWrVu31saNGxUcHOywY8DDeHlJjRq5OgoAAIAiIzdHiSF3BgAAkFTAIvrSpUs1ZcoUffrppzKZTFq3bp18fPI/1WQyFStR37p1a779xcfHKz4+vsj7BAAAAMoScnMAAACgZBWoiH7DDTdo5cqVkiQvLy998cUXCgkJcWpggFNkZEgJCbnzzzwj+fm5Nh4AAIBCIjdHiSF3BgAAkFTAIvqVcnJynBEHUDIyM6WpU3Pn//53LgQAAECpRm4OpyJ3BgAAkFSEIrokHT58WHPmzNGhQ4dkMpnUoEEDjRo1SjExMY6ODwAAAMA1kJsDAAAAzuVV2Cds2LBBDRs21K5du9SkSRM1btxY3377rRo1aqRNmzY5I0YAAAAANpCbAwAAAM5X6J7oEyZM0JgxYzRjxox8y59++ml16tTJYcEBAAAAsI/cHAAAAHC+QvdEP3TokB555JF8yx9++GEdPHjQIUEBAAAAuD5ycwAAAMD5Cl1Er1atmvbu3Ztv+d69exUSEuKImAAAAAAUALk5AAAA4HyFHs7lscce05AhQ3TkyBG1bdtWJpNJX331lWbOnKmnnnrKGTECAAAAsIHcHAAAAHC+QhfRn332WQUHB+ull17SxIkTJUkRERGKj4/XyJEjHR4g4FD+/tKuXZfnAQAASjFyczgVuTMAAICkIhTRTSaTxowZozFjxujChQuSpODgYIcHBjiFt7fUqpWrowAAAHAIcnM4FbkzAACApCIU0a9Egg4AAAC4B3JzAAAAwDmKVUQHSp2MDOnll3PnR42S/PxcGw8AAADgrsidAQAAJFFEh6fJzJTGj8+dHzaMCwEAAADAHnJnAAAASZKXqwMAAAAAAAAAAMBdFaqInpmZqQ4dOujnn392VjwAAAAACoDcHAAAACgZhSqi+/r6av/+/TKZTM6KBwAAAEABkJsDAAAAJaPQw7k8+OCDWrx4sTNiAQAAAFAI5OYAAACA8xX6xqIZGRl6/fXXtWnTJrVs2VJBQUFW62fPnu2w4AAAAADYR24OAAAAOF+hi+j79+9X8+bNJSnf+Iv8lBQAAAAoOeTmAAAAgPMVuoi+ZcsWZ8QBlAx/fynvHPb3d20sAAAAxURuDqcidwYAAJBUhCJ6nl9//VWHDx/WbbfdpoCAABmGQW8XuD9vbykuztVRAAAAOBS5OZyC3BkAAEBSEW4seubMGXXs2FH16tVTt27ddPLkSUnSo48+qqeeesrhAQIAAACwjdwcAAAAcL5CF9HHjBkjX19fHT9+XIGBgZbl9913n9avX+/Q4ACHy8yU5s3LnTIzXR0NAABAsZCbw6nInQEAACQVYTiXjRs3asOGDapRo4bV8rp16yoxMdFhgQFOkZEhjRiROz94sOTr69JwAAAAioPcHE5F7gwAACCpCD3RU1JSrHq55Pnzzz9lNpsdEhQAAACA6yM3BwAAAJyv0EX02267TW+++ablsclkUk5Ojl544QV16NDBocEBAAAAsI/cHAAAAHC+Qg/n8sILLyguLk7fffedMjIyNH78eB04cEB//fWXvv76a2fECAAAAMAGcnMAAADA+QrdE71hw4bat2+fbr75ZnXq1EkpKSnq3bu3fvjhB8XExDgjRgAAAAA2kJsDAAAAzlfonuiSFBYWpqlTpzo6FgAAAACFRG4OAAAAOFeRiuhnz57V4sWLdejQIZlMJjVo0EAPPfSQKleu7Oj4AAAAAFwDuTkAAADgXIUezmXbtm2Kjo7WK6+8orNnz+qvv/7SK6+8oujoaG3bts0ZMQKOYzZLn36aO5nNro4GAACgWMjN4VTkzgAAAJKK0BN9+PDh6tu3rxYsWCBvb29JUnZ2toYNG6bhw4dr//79Dg8ScBgfH6l7d1dHAQAA4BDk5nAqcmcAAABJReiJfvjwYT311FOWJF2SvL29NXbsWB0+fNihwQEAAACwj9wcAAAAcL5CF9GbN2+uQ4cO5Vt+6NAhNW3a1BExAc6TmSktXZo7ZWa6OhoAAIBiITeHU5E7AwAASCrgcC779u2zzI8cOVKjRo3Sr7/+qltuuUWS9M0332jevHmaMWOGc6IEHCUjQ3roodz5Pn0kX1/XxgMAAFBI5OYoMeTOAAAAkgpYRG/atKlMJpMMw7AsGz9+fL7t+vfvr/vuu89x0QEAAACwQm4OAAAAlKwCFdGPHj3q7DgAAAAAFAC5OQAAAFCyClREj4qKcnYcAAAAAAqA3BwAAAAoWQUqol/tt99+09dff61Tp04pJyfHat3IkSMdEhgAAACA6yM3BwAAAJyr0EX0JUuW6PHHH5efn5+qVKkik8lkWWcymUjUAQAAgBJCbg4AAAA4X6GL6JMnT9bkyZM1ceJEeXl5OSMmAAAAAAVAbg4AAAA4X6GL6Kmpqbr//vtJ0lE6mc3Su+9engcAACjFyM3hVOTOAAAAkqRCZ9uPPPKI3nvvPWfEAjifj4/Up0/u5FOkWwIAAAC4DXJzOBW5MwAAgKQi9ESfPn26evToofXr1+vGG2+Ur6+v1frZs2c7LDgAAAAA9pGbAwAAAM5X6CJ6QkKCNmzYoBtuuEGS8t28CHBrWVnSmjW583/7Gz1qAABAqUZuDqcidwYAAJBUhOFcZs+erTfeeEOHDh3S1q1btWXLFsu0efPmQu1rwYIFatKkicqXL6/y5curTZs2WrdunWW9YRiKj49XRESEAgICFBcXpwMHDhQ2ZOCy9HSpb9/cKT3d1dEAAAAUi6Nyc/Jy2ETuDAAAIKkIRXSz2ax27do55OA1atTQjBkz9N133+m7777T7bffrrvvvtuSkM+aNUuzZ8/W3LlztXv3boWFhalTp066cOGCQ44PAAAAlGaOys3JywEAAAD7Cl1EHzVqlP7973875OA9e/ZUt27dVK9ePdWrV0/PP/+8ypUrp2+++UaGYWjOnDmaNGmSevfurcaNG2vZsmVKTU3V8uXLHXJ8AAAAoDRzVG5OXg4AAADYV+hB7Xbt2qXNmzfr008/VaNGjfLdvGj16tVFCiQ7O1vvvfeeUlJS1KZNGx09elRJSUnq3LmzZRuz2azY2Fjt2LFDQ4cOtbmf9PR0pV/xU8Pk5OQixQMAAAC4O2fk5o7KyyVycwAAAJQNhS6iV6xYUb1793ZYAD/++KPatGmjS5cuqVy5clqzZo0aNmyoHTt2SJJCQ0Ottg8NDVViYqLd/U2fPl1Tp051WHwAAACAu3Jkbu7ovFwiNwcAAEDZUOgi+pIlSxwawA033KC9e/fq3Llz+uCDDzRo0CBt27bNst5kMlltbxhGvmVXmjhxosaOHWt5nJycrMjISIfGDAAAALgDR+bmjs7LJXJzAAAAlA2FLqI7mp+fn+rUqSNJatmypXbv3q2XX35ZTz/9tCQpKSlJ4eHhlu1PnTqVrxfMlcxms8xms3ODBgAAAMoYR+flErk5AAAAyoZCF9Gjo6Ov2ePkyJEjxQrIMAylp6crOjpaYWFh2rRpk5o1ayZJysjI0LZt2zRz5sxiHQMezM9Pyuux5efn2lgAAACKyZm5OXk5yJ0BAAByFbqIPnr0aKvHmZmZ+uGHH7R+/Xr9/e9/L9S+nnnmGXXt2lWRkZG6cOGCVq5cqa1bt2r9+vUymUwaPXq0EhISVLduXdWtW1cJCQkKDAxU//79Cxs2kMvXVxo82NVRAAAAOISjcnPycthE7gwAACCpCEX0UaNG2Vw+b948fffdd4Xa1x9//KGBAwfq5MmTqlChgpo0aaL169erU6dOkqTx48crLS1Nw4YN09mzZ9W6dWtt3LhRwcHBhQ0bAAAAKHMclZuTlwMAAAD2OWxM9K5du2rixImFurnR4sWLr7neZDIpPj5e8fHxxYwO+H9ZWdKGDbnzXbpIPi6/LQAAAIDDFTY3Jy+HTeTOAAAAkhxYRH///fdVuXJlR+0OcI70dKlHj9z5ixe5EAAAAGUSuTkcgtwZAABAUhGK6M2aNbO6eZFhGEpKStLp06c1f/58hwYHAAAAwD5ycwAAAMD5Cl1E79Wrl9VjLy8vVatWTXFxcapfv76j4gIAAABwHeTmAAAAgPMVuog+ZcoUZ8QBAAAAoJDIzQEAAADn83J1AAAAAAAAAAAAuKsC90T38vKyGm/RFpPJpKysrGIHBQAAAMA+cnMAAACg5BS4iL5mzRq763bs2KF///vfMgzDIUEBAAAAsI/cHAAAACg5BS6i33333fmW/fe//9XEiRP1ySefaMCAAfrnP//p0OAAh/Pzk+bOvTwPAABQCpGbo0SQOwMAAEgqwo1FJen333/XlClTtGzZMnXp0kV79+5V48aNHR0b4Hi+vtLw4a6OAgAAwGHIzeE05M4AAACSCnlj0fPnz+vpp59WnTp1dODAAX3xxRf65JNPSNIBAACAEkZuDgAAAJSMAvdEnzVrlmbOnKmwsDCtWLHC5k9IAbeXnS1t354737695O3t2ngAAACKgNwcJYLcGQAAQFIhiugTJkxQQECA6tSpo2XLlmnZsmU2t1u9erXDggMc7tIlqUOH3PmLF6WgINfGAwAAUATk5igR5M4AAACSClFEf/DBB2UymZwZCwAAAIACIDcHAAAASk6Bi+hLly51YhgAAAAACorcHAAAACg5hbqxKAAAAAAAAAAAnoQiOgAAAAAAAAAAdlBEBwAAAAAAAADADoroAAAAAAAAAADYUeAbiwJlgq+vNGvW5XkAAAAAtpE7AwAASKKIDk/j5yf9/e+ujgIAAABwf+TOAAAAkhjOBQAAAAAAAAAAu+iJDs+SnS19/33ufPPmkre3a+MBAAAA3BW5MwAAgCSK6PA0ly5JN9+cO3/xohQU5Np4AAAAAHdF7gwAACCJ4VwAAAAAAAAAALCLIjoAAAAAAAAAAHZQRAcAAAAAAAAAwA6K6AAAAAAAAAAA2EERHQAAAAAAAAAAOyiiAwAAAAAAAABgh4+rAwBKlK+vNGXK5XkAAAAAtpE7AwAASKKIDk/j5yfFx7s6CgAAAMD9kTsDAABIYjgXAAAAAAAAAADsoic6PEtOjnToUO58gwaSF/9HAgAAAGwidwYAAJBEER2eJi1Natw4d/7iRSkoyLXxAAAAAO6K3BkAAEASw7kAAAAAAAAAAGAXRXQAAAAAAAAAAOygiA4AAAAAAAAAgB0U0QEAAAAAAAAAsIMiOgAAAAAAAAAAdlBEBwAAAAAAAADADh9XBwCUKF9fady4y/MAAAAAbCN3BgAAkEQRHZ7Gz0964QVXRwEAAAC4P3JnAAAASQznAgAAAAAAAACAXfREh2fJyZGOH8+dr1lT8uL/SAAAAIBN5M4AAACSKKLD06SlSdHRufMXL0pBQa6NBwAAAHBX5M4AAACSGM4FAAAAAAAAAAC7XFpEnz59ulq1aqXg4GCFhISoV69e+umnn6y2MQxD8fHxioiIUEBAgOLi4nTgwAEXRQwAAACUPeTlAAAAgH0uLaJv27ZNw4cP1zfffKNNmzYpKytLnTt3VkpKimWbWbNmafbs2Zo7d652796tsLAwderUSRcuXHBh5AAAAEDZQV4OAAAA2OfSMdHXr19v9XjJkiUKCQnRnj17dNttt8kwDM2ZM0eTJk1S7969JUnLli1TaGioli9frqFDh+bbZ3p6utLT0y2Pk5OTnfsiAAAAgFLOGXm5RG4OAACAssGtxkQ/f/68JKly5cqSpKNHjyopKUmdO3e2bGM2mxUbG6sdO3bY3Mf06dNVoUIFyxQZGen8wAEAAIAyxBF5uURuDgAAgLLBbYrohmFo7NixuvXWW9W4cWNJUlJSkiQpNDTUatvQ0FDLuqtNnDhR58+ft0wnTpxwbuAAAABAGeKovFwiNwcAAEDZ4NLhXK40YsQI7du3T1999VW+dSaTyeqxYRj5luUxm80ym81OiRFlgI+PNGzY5XkAAABYcVReLpGbl3rkzgAAAJLcpIj+5JNP6uOPP9aXX36pGjVqWJaHhYVJyu35Eh4ebll+6tSpfL1ggAIxm6V581wdBQAAgFsiL4cVcmcAAABJLh7OxTAMjRgxQqtXr9bmzZsVHR1ttT46OlphYWHatGmTZVlGRoa2bdumtm3blnS4AAAAQJlEXg4AAADY59Ke6MOHD9fy5cv10UcfKTg42DKeYoUKFRQQECCTyaTRo0crISFBdevWVd26dZWQkKDAwED179/flaGjtDIM6c8/c+erVpWu8fNjAAAAT0FeDpvInQEAACS5uIi+YMECSVJcXJzV8iVLlmjw4MGSpPHjxystLU3Dhg3T2bNn1bp1a23cuFHBwcElHC3KhNRUKSQkd/7iRSkoyLXxAAAAuAHycthE7gwAACDJxUV0wzCuu43JZFJ8fLzi4+OdHxAAAADggcjLAQAAAPtcOiY6AAAAAAAAAADujCI6AAAAAAAAAAB2UEQHAAAAAAAAAMAOiugAAAAAAAAAANhBER0AAAAAAAAAADt8XB0AUKJ8fKRBgy7PAwAAALCN3BkAAEASRXR4GrNZWrrU1VEAAAAA7o/cGQAAQBLDuQAAAAAAAAAAYBc90eFZDENKTc2dDwyUTCbXxgMAAAC4K3JnAAAASfREh6dJTZXKlcud8i4IAAAAAORH7gwAACCJIjoAAAAAAAAAAHZRRAcAAAAAAAAAwA6K6AAAAAAAAAAA2EERHQAAAAAAAAAAOyiiAwAAAAAAAABgB0V0AAAAAAAAAADs8HF1AECJ8vaW7r338jwAAAAA28idAQAAJFFEh6fx95fee8/VUQAAAADuj9wZAABAEsO5AAAAAAAAAABgFz3RAQBwY6dPn1ZycrJLjl2+fHlVq1bNJccGAAAoKPIlAICzUUSHZ0lJkcqVy52/eFEKCnJtPABwDadPn9YTj/ZX+sUzLjm+uVwVLXh9OReGAOCpyJ1RCpw+fVpPPDZE6ampLjm+OTBQCxYtJF8CgDKOIjoAAG4qOTlZ6RfP6KmeZkVWCyjRY584naaXPjmj5ORkLgoBAIDbSk5OVnpqqp66f6Aiw8JL9Ngnkk7qpZVvkS8BgAegiA4AgJuLrBagmOqu6P2X7oJjAgAAFF5kWLhiImu6OgwAQBnFjUUBAAAAAAAAALCDIjoAAAAAAAAAAHZQRAcAAAAAAAAAwA6K6AAAAAAAAAAA2MGNReFZvL2lbt0uzwMAAACwjdwZAABAEkV0eBp/f2ntWldHAQAAALg/cmcAAABJDOcCAAAAAAAAAIBdFNEBAAAAAAAAALCDIjo8S0qKFBSUO6WkuDoaAAAAwH2ROwMAAEhiTHR4otRUV0cAAAAAlA7kzgAAAPREBwAAAAAAAADAHoroAAAAAAAAAADYQREdAAAAAAAAAAA7KKIDAAAAAAAAAGAHRXQAAAAAAAAAAOzwcXUAQIny8pJiYy/PAwAAALCN3BkAAEASRXR4moAAaetWV0cBAAAAuD9yZwAAAEkM5wIAAAAAAAAAgF0U0QEAAAAAAAAAsIMiOjxLSopUrVrulJLi6mgAAAAA90XuDAAAIIkx0eGJ/vzT1REAAAAApQO5MwAAAD3RAQAAAAAAAACwx6VF9C+//FI9e/ZURESETCaTPvzwQ6v1hmEoPj5eERERCggIUFxcnA4cOOCaYAEAAIAyjNwcAAAAsM2lRfSUlBTddNNNmjt3rs31s2bN0uzZszV37lzt3r1bYWFh6tSpky5cuFDCkQIAAABlG7k5AAAAYJtLx0Tv2rWrunbtanOdYRiaM2eOJk2apN69e0uSli1bptDQUC1fvlxDhw61+bz09HSlp6dbHicnJzs+cAAAAKCMITcHAAAAbHPbMdGPHj2qpKQkde7c2bLMbDYrNjZWO3bssPu86dOnq0KFCpYpMjKyJMIFAAAAyixycwAAAHgyty2iJyUlSZJCQ0OtloeGhlrW2TJx4kSdP3/eMp04ccKpcaKU8fKSWrbMnbzc9vQHAABwK+TmHorcGQAAQJKLh3MpCJPJZPXYMIx8y65kNptlNpudHRZKq4AAafduV0cBAABQKpGbexhyZwAAAElu3BM9LCxMkvL1bDl16lS+HjAAAAAAnIfcHAAAAJ7MbYvo0dHRCgsL06ZNmyzLMjIytG3bNrVt29aFkQEAAACehdwcAAAAnsylw7lcvHhRv/76q+Xx0aNHtXfvXlWuXFk1a9bU6NGjlZCQoLp166pu3bpKSEhQYGCg+vfv78KoUaqlpkoNG+bOHzwoBQa6Nh4AAAA3QW6OfMidAQAAJLm4iP7dd9+pQ4cOlsdjx46VJA0aNEhLly7V+PHjlZaWpmHDhuns2bNq3bq1Nm7cqODgYFeFjNLOMKTExMvzAAAAkERuDhvInQEAACS5uIgeFxcn4xrJmMlkUnx8vOLj40suKAAAAMADkZsDAAAAtrntmOgAAAAAAAAAALgaRXQAAAAAAAAAAOygiA4AAAAAAAAAgB0U0QEAAAAAAAAAsMOlNxYFSpzJJDVseHkeAAAAgG3kzgAAAJIoosPTBAZKBw64OgoAAADA/ZE7AwAASGI4FwAAAAAAAAAA7KKIDgAAAAAAAACAHRTR4VlSU6VGjXKn1FRXRwMAAAC4L3JnAAAASYyJDk9jGNLBg5fnAQAAANhG7gwAACCJnugAAAAAAAAAANhFER0AAAAAAAAAADsoogMAAAAAAAAAYAdFdAAAAAAAAAAA7KCIDgAAAAAAAACAHT6uDgAoUSaTFBV1eR4AAACAbeTOAAAAkiiiw9MEBkrHjrk6CgAAAMD9kTsDAABIYjgXAAAAAAAAAADsoogOAAAAAAAAAIAdFNHhWdLSpFatcqe0NFdHAwAAALgvcmcAAABJjIlepqVnZCoxMdElxy5fvryqVavmkmNfU06O9N13l+cd7PTp00pOTnb4fgvCbdscAAAApVMxc2dyYwAAUFZQRC+jziRn6MjRRM2Y/KTMZnOJH99crooWvL7coxLX06dP64lH+yv94hmXHN8T2xwAAADu6fTp03risSFKT011yfHNgYFasGghuTEAAHAIiuhl1MW0bPl5ZWlMDz/Vi6xYosc+cTpNL31yRsnJyR6VtCYnJyv94hk91dOsyGoBJXpsT21zAAAAuKfk5GSlp6bqqfsHKjIsvESPfSLppF5a+Ra5MQAAcBiK6GVcjWr+iqke5IIjp7vgmO4hsloAbQ4AAABIigwLV0xkTVeHAQAAUCzcWBQAAAAAAAAAADvoiQ4AAACX4uaDAMoSvtMAACh7KKLD81St6uoIAADA/+PG3ICbI3cuFG6oCgBA2UQRHZ4lKEg6fdrVUQAAgP/HjbkBN0buXGjcUBUAgLKJIjoAAABcjhtzAyhLuKEqAABlCzcWBQAAAAAAAADADoro8CxpaVJcXO6UlubqaAAAAAD3Re4MAAAgieFc4CTpGZlKTEx0ybEzMjLk5+dnc50pNVW1t22TJB359VcZgYEOO25iYqKysrIctj8AAACguE6fPq3k5OQiPbc4uTO5secpzrlWHJ58rrmqzSWpfPnyjL0PwKNQRIfDnUnO0JGjiZox+UmZzeYSPXZ6RqaOHv9ddWpVl49P/tPbnJ2t9/9/fvyTDyjd29thx05JTdcfSSeUnlnBYfsEAAAAiur06dN64rEhSk9NLdLzzdlZl3Pn0aOV7l3wy8eU1FT9kZSk9MyMIh0bpUtxz7Xi8NRzzZVtLknmwEAtWLSQQjoAj0ERHQ53MS1bfl5ZGtPDT/UiK5bosb85dFbPv5mmkV29bR7bdClbyu1Mo1kDK8rwd1wRPffYWcrO9sxeEAAAAHAvycnJSk9N1VP3D1RkWHihn2+6lCZt3ixJmjV8jAz/gAI/95t9e/X8wvnKzsou9HFR+hT3XCsOTz3XXNnmJ5JO6qWVbyk5OZkiOgCPQREdTlOjmr9iqgeV6DET/0i79rHTLhe4a0cESgGO+wjkHRsAAABwJ5Fh4YqJrFn4J17Rw7V29UipMMO5nPy98MdDqVfkc60YPP1cc0WbA4An4saiAAAAAAAAAADYQU90oIxw5c1cuakMAACAfekZGS7J0zz5houeinMNAADnoIgOz+PAcdDdhStv5ipJ5nJVtOD15RTSAQAArnLm/DkdOXJEM6b+U2azX4ke2yE3XCzEOOhwrVJ/rgEA4MYoosOzBPhI67q6OgqHc+XNXE+cTtNLn5zhpjIAAAA2XExNlZ+Pj8bcN0D1akWX6LGLfcPFwEBp138cGxScplSfawAAuDmK6EAZ4oqbueZKd8ExAQAASo8aoWHccBElgnMNAADH48aiAAAAAAAAAADYQREdniUjW5qwK3fK4KeGAAAAgF3p6dLwx3KndH55CAAAPBfDucCzZBvSt6cuz8Mh0jMylZiYWOLHzcjIkJ9fyd40yR2OXb58ecafBwAAzpedLW3fdnm+FEnPyHBJfpqYmKisrKwSPy5ch3MNQFly+vRpJScnu+TY7l7roIgOoFjOJGfoyNFEzZj8pMxmc4kdNz0jU0eP/646tarLx6dkv8pceWxJMperogWvL3frPy4AAACucub8OR05ckQzpv5TZnPJdnpISU3VH0lJSs/MKNHjwjU41wCUJadPn9YTjw1RemqqS45vDgzUgkUL3bbWQREdQLFcTMuWn1eWxvTwU73IiiV23G8OndXzb6ZpZFfvEj2uq4994nSaXvrkjJKTk932DwsAAIArXUxNlZ+Pj8bcN0D1akWX6LG/2bdXzy+cr+ys0tVzH0XDuQagLElOTlZ6aqqeun+gIsPCS/TYJ5JO6qWVb7l1raNUFNHnz5+vF154QSdPnlSjRo00Z84ctW/f3tVhAbhCjWr+iqkeVGLHS/wjzSXHdfWxczEmKQDAdcjNUVrUCA1TTGTNEj1m4snfS/R4cA+cawDKksiw8BL/TisN3P7GoqtWrdLo0aM1adIk/fDDD2rfvr26du2q48ePuzo0AAAAwKOQmwMAAMATuX0Rffbs2XrkkUf06KOPqkGDBpozZ44iIyO1YMECV4cGAAAAeBRycwAAAHgitx7OJSMjQ3v27NGECROslnfu3Fk7duyw+Zz09HSlp18e6uD8+fOS5JI7y164cEGZWdn674kLupBasnfNPnwyRdk5hn4+kaLsHF+O/f9M6dnKG6nu6OFkGWbvEju2M3nisT3xNUvSb2fSlJqWroMHD+rChQslemyUvBMnTuhSerpL/o5wrqGkuPo8z8zK1oULF0o8V8w7nmEYJXrc4ijNuXluXp6l/x49ogupKSV67MMnjis7J0c/HzuqbCOnVB3bdOnS5dz5l59k+PuX2LGLg2NzbI7tXL/98YdS09LIE4EyxpKXuyBf+u2PP5SZleXeebnhxn777TdDkvH1119bLX/++eeNevXq2XzOlClTDElMTExMTExMTExMbj+dOHGiJNJqhyA3Z2JiYmJiYmJiKqvT9fJyt+6JnsdkMlk9Ngwj37I8EydO1NixYy2Pc3Jy9Ndff6lKlSp2n+MsycnJioyM1IkTJ1S+fPkSPXZZQjs6Dm3pGLSj49CWjkE7Og5t6Ri04/UZhqELFy4oIiLC1aEUWmnNzcsaPmelA+9T6cD7VDrwPrk/3qPSgffJWkHzcrcuoletWlXe3t5KSkqyWn7q1CmFhobafI7ZbJbZbLZaVrFiRWeFWCDly5fnpHQA2tFxaEvHoB0dh7Z0DNrRcWhLx6Adr61ChQquDqFQykpuXtbwOSsdeJ9KB96n0oH3yf3xHpUOvE+XFSQvd+sbi/r5+alFixbatGmT1fJNmzapbdu2LooKAAAA8Dzk5gAAAPBUbt0TXZLGjh2rgQMHqmXLlmrTpo0WLlyo48eP6/HHH3d1aAAAAIBHITcHAACAJ3L7Ivp9992nM2fO6LnnntPJkyfVuHFjffbZZ4qKinJ1aNdlNps1ZcqUfD9hReHQjo5DWzoG7eg4tKVj0I6OQ1s6Bu1YdpXm3Lys4XNWOvA+lQ68T6UD75P74z0qHXifisZkGIbh6iAAAAAAAAAAAHBHbj0mOgAAAAAAAAAArkQRHQAAAAAAAAAAOyiiAwAAAAAAAABgB0V0AAAAAAAAAADsoIheCPPnz1d0dLT8/f3VokULbd++3e62J0+eVP/+/XXDDTfIy8tLo0ePtrndBx98oIYNG8psNqthw4Zas2aNk6J3L45uy0WLFql9+/aqVKmSKlWqpDvuuEO7du1y4itwD844J/OsXLlSJpNJvXr1cmzQbsoZbXnu3DkNHz5c4eHh8vf3V4MGDfTZZ5856RW4B2e045w5c3TDDTcoICBAkZGRGjNmjC5duuSkV+A+CtOWq1evVqdOnVStWjWVL19ebdq00YYNG/Jt54l/cxzdjp7690ZyzjmZx9P+5gD2FOZzJknbtm1TixYt5O/vr9q1a+vVV1/Nt40n5iPO5Iz3yFNzHWfi2r10oC5QOlB3cH/UM0oGRfQCWrVqlUaPHq1Jkybphx9+UPv27dW1a1cdP37c5vbp6emqVq2aJk2apJtuusnmNjt37tR9992ngQMH6j//+Y8GDhyovn376ttvv3XmS3E5Z7Tl1q1b1a9fP23ZskU7d+5UzZo11blzZ/3222/OfCku5Yx2zJOYmKhx48apffv2zgjd7TijLTMyMtSpUycdO3ZM77//vn766SctWrRI1atXd+ZLcSlntOM777yjCRMmaMqUKTp06JAWL16sVatWaeLEic58KS5X2Lb88ssv1alTJ3322Wfas2ePOnTooJ49e+qHH36wbOOJf3Oc0Y6e+PdGck5b5vG0vzmAPYX9nB09elTdunVT+/bt9cMPP+iZZ57RyJEj9cEHH1i28cR8xJmc8R55aq7jTFy7lw7UBUoH6g7uj3pGCTJQIDfffLPx+OOPWy2rX7++MWHChOs+NzY21hg1alS+5X379jXuvPNOq2VdunQx7r///mLF6u6c0ZZXy8rKMoKDg41ly5YVNUy356x2zMrKMtq1a2e8/vrrxqBBg4y7777bAdG6N2e05YIFC4zatWsbGRkZjgrT7TmjHYcPH27cfvvtVsvGjh1r3HrrrcWK1d0Vpy3zNGzY0Jg6darlsSf+zXFGO17NE/7eGIbz2tIT/+YA9hT2czZ+/Hijfv36VsuGDh1q3HLLLZbHnpiPOJMz3iNPzXWciWv30oG6QOlA3cH9Uc8oOfREL4CMjAzt2bNHnTt3tlreuXNn7dixo8j73blzZ759dunSpVj7dHfOasurpaamKjMzU5UrV3bYPt2JM9vxueeeU7Vq1fTII48Uaz+lhbPa8uOPP1abNm00fPhwhYaGqnHjxkpISFB2dnZxQ3ZLzmrHW2+9VXv27LH8DPPIkSP67LPP1L1792LF684c0ZY5OTm6cOGC1Xegp/3NcVY7Xq2s/72RnNuWnvY3B7CnKJ8ze9/r3333nTIzMyV5Xj7iTM56jzwx13Emrt1LB+oCpQN1B/dHPaNk+bg6gNLgzz//VHZ2tkJDQ62Wh4aGKikpqcj7TUpKcvg+3Z2z2vJqEyZMUPXq1XXHHXc4bJ/uxFnt+PXXX2vx4sXau3dvMSMsPZzVlkeOHNHmzZs1YMAAffbZZ/rll180fPhwZWVlafLkycUN2+04qx3vv/9+nT59WrfeeqsMw1BWVpaeeOIJTZgwobghuy1HtOVLL72klJQU9e3b17LM0/7mOKsdr1bW/95IzmtLT/ybA9hTlM+Zve/1rKws/fnnnwoPD/e4fMSZnPUeeWKu40xcu5cO1AVKB+oO7o96RsmiiF4IJpPJ6rFhGPmWucM+SwNnvu5Zs2ZpxYoV2rp1q/z9/R2yT3flyHa8cOGCHnjgAS1atEhVq1Z1RHiliqPPyZycHIWEhGjhwoXy9vZWixYt9Pvvv+uFF14o0390HN2OW7du1fPPP6/58+erdevW+vXXXzVq1CiFh4fr2WefLW64bq2obblixQrFx8fro48+UkhIiEP2WZo5ox3zeNLfG8mxbenpf3MAewr7ObO1/ZXLPTUfcSZHv0eenOs4E9fupQN1gdKBuoP7o55RMiiiF0DVqlXl7e2d7784p06dyvffnsIICwtz+D7dnbPaMs+LL76ohIQEff7552rSpEmx9+eunNGOhw8f1rFjx9SzZ0/LspycHEmSj4+PfvrpJ8XExBQ9aDflrHMyPDxcvr6+8vb2tixr0KCBkpKSlJGRIT8/vyLv2x05qx2fffZZDRw4UI8++qgk6cYbb1RKSoqGDBmiSZMmycur7I1KVpy2XLVqlR555BG99957+XrceNrfHGe1Yx5P+XsjOactPfVvDmBPUT5n9r7XfXx8VKVKFUmel484k7PeI0/MdZyJa/fSgbpA6UDdwf1RzyhZ/EUuAD8/P7Vo0UKbNm2yWr5p0ya1bdu2yPtt06ZNvn1u3LixWPt0d85qS0l64YUX9M9//lPr169Xy5Yti7Uvd+eMdqxfv75+/PFH7d271zLddddd6tChg/bu3avIyEhHhO52nHVOtmvXTr/++qslIZCkn3/+WeHh4WXyD46z2jE1NTXfxaO3t7cMw7D05CpritqWK1as0ODBg7V8+XKb46h62t8cZ7Wj5Fl/byTntKWn/s0B7CnK58ze93rLli3l6+sryfPyEWdy1nvkibmOM3HtXjpQFygdqDu4P+oZJazEbmFayq1cudLw9fU1Fi9ebBw8eNAYPXq0ERQUZBw7dswwDMOYMGGCMXDgQKvn/PDDD8YPP/xgtGjRwujfv7/xww8/GAcOHLCs//rrrw1vb29jxowZxqFDh4wZM2YYPj4+xjfffFOir62kOaMtZ86cafj5+Rnvv/++cfLkSct04cKFEn1tJckZ7Xg1T7lLtjPa8vjx40a5cuWMESNGGD/99JPx6aefGiEhIca0adNK9LWVJGe045QpU4zg4GBjxYoVxpEjR4yNGzcaMTExRt++fUv0tZW0wrbl8uXLDR8fH2PevHlW34Hnzp2zbOOJf3Oc0Y6e+PfGMJzTllfzlL85gD2F/ZwdOXLECAwMNMaMGWMcPHjQWLx4seHr62u8//77lm08MR9xJme8R56a6zgT1+6lA3WB0oG6g/ujnlFyKKIXwrx584yoqCjDz8/PaN68ubFt2zbLukGDBhmxsbFW20vKN0VFRVlt89577xk33HCD4evra9SvX9/44IMPSuCVuJ6j2zIqKsrmNlOmTCmZF+Qizjgnr+RJf8yc0ZY7duwwWrdubZjNZqN27drG888/b2RlZZXAq3EdR7djZmamER8fb8TExBj+/v5GZGSkMWzYMOPs2bMl84JcqDBtGRsba7MtBw0aZLVPT/yb4+h29NS/N4bhnHPySp70Nwewp7B/R7du3Wo0a9bM8PPzM2rVqmUsWLAg3z49MR9xJke/R56c6zgT1+6lA3WB0oG6g/ujnlEyTIbBb8QAAAAAAAAAALCFMdEBAMD/tXf/MVHXcRzHX19QOeSmTAkdRRjcQIyLoJtKOBgBo0zIyXKhtZEL3KKN/rA/aJrrx5i2WnPH1NVCbQ51NWVOV6O5bkJiJ01mBHjFr/6QyATbsmh3cv3R/M5Tzzx/hF7Px3Yb+3y+nx/f7z+89977+/kCAAAAAIAgSKIDAAAAAAAAABAESXQAAAAAAAAAAIIgiQ4AAAAAAAAAQBAk0QEAAAAAAAAACIIkOgAAAAAAAAAAQZBEBwAAAAAAAAAgCJLoAAAAAAAAAAAEQRIdAMLYhg0bVF1dPdnbCFlDQ4PKysomexsAAADAf+ZmYnfDMNTc3CxJGhwclGEY6uzsvOHxhw4dUlZWliYmJkJaFwD+b0iiA8A9qrKyUoZhyDAMTZ06VcnJyVq3bp0uXLggSRoZGdGWLVv0+uuvB4z7+eefVVtbK5vNJovFojlz5mjJkiXavn27/vjjj8m4latUVVXpxIkTamtrm+ytAAAAALfsZmL3X375RWvXrtWDDz6oqKgozZ07VyUlJWpvb7/mGomJiRoeHlZGRsYN72vZsmUyDENNTU23doMAEOamTPYGAAA378knn9SOHTvk9XrV2tqql156SRcuXNC2bdv08ccfKycnR/PmzTOv7+/vV25urmJjY1VfXy+73S6fzyePx6PGxkYlJCTcFRXgUVFRWrVqlZxOp5YsWTLZ2wEAAABuWaixe3l5ubxer3bt2qXk5GSNjIzoyJEjGh0dveb8kZGRmjt3bsj7evHFF+V0OvX888/f7K0BQNijEh0A7mGXKlISExO1atUqrV692nydc+/evVclxF9++WVNmTJFHR0dWrlypdLT02W321VeXq7Dhw+rtLRU0rVfBT1//rwMw5DL5TLburu7tXTpUlmtVs2ZM0cvvPCCfv31V7P/s88+k91uV3R0tGbPnq2ioiKz2sblcmnhwoWKiYlRbGyscnNzNTQ0ZI4tKytTc3Oz/vzzz9v81AAAAID/Xiix+/nz59XW1qbNmzeroKBASUlJWrhwoerq6vT0009fc/4rY3iXyyXDMHT48GFlZmbKYrFo0aJF+u677wLGlZWVye12q7+//47cNwCEA5LoABBGoqOj5fV6NTY2pq6uLjkcDrPv3LlzamlpUU1NjWJiYq453jCMG15reHhY+fn5evTRR9XR0aEvvvhCIyMjWrlypdlfUVGhNWvWqKenRy6XSytWrJDf75fP59Py5cuVn5+vU6dOqb29XdXV1QHrOxwOeb1eud3um3waAAAAwN3rerG71WqV1WpVc3Oz/vrrr1ta57XXXtN7772nEydOKD4+XmVlZfJ6vWZ/UlKS4uPj1draekvrAEA44zgXAAgTbrdbTU1NKiws1NDQkPx+vxISEsz+H3/8UX6/X2lpaQHj4uLiND4+LkmqqanR5s2bb2i9bdu2KTs7W/X19WZbY2OjEhMT5fF49Pvvv8vn82nFihVKSkqSJNntdknS6OiofvvtNy1btkwpKSmSpPT09ID5L1WoDw4OKj8/P8SnAQAAANy9/i12nzJlinbu3Kmqqipt375d2dnZys/P13PPPadHHnkkpLU2btyo4uJiSdKuXbv0wAMP6MCBA2bxiyTdf//9GhwcvC33BgDhiEp0ALiHHTp0SFarVRaLRTk5OcrLy5PT6TSPQLFYLFeNubLa3O12q7OzUw8//HBIVS7ffvutvvrqK7NKxmq1av78+ZKkvr4+ZWZmqrCwUHa7Xc8++6w++ugjjY2NSZJmzZqlyspKlZSUqLS0VFu2bNHw8PBVa0RHR981HzsFAAAAbkWosXt5ebnOnDmjgwcPqqSkRC6XS9nZ2dq5c2dI6+bk5Jh/z5o1S2lpaerp6Qm4hrgbAK6PJDoA3MMKCgrU2dmp06dPa3x8XPv371d8fLzi4uIkyUxaS5LNZpNhGOrt7Q2YIzk5WTabTdHR0WZbRMQ//x78fr/Zdvkrn5I0MTGh0tJSdXZ2Bvx++OEH5eXlKTIyUl9++aU+//xzLViwQE6nU2lpaRoYGJAk7dixQ+3t7Xr88ce1b98+paam6vjx4wFrjI6O6r777rsNTwoAAACYXKHE7pdYLBYVFxfrjTfe0LFjx1RZWamNGzfe8l6uLKwh7gaA6yOJDgD3sJiYGNlsNiUlJWnq1Klme0pKimbMmKHu7m6zbfbs2SouLlZDQ4P5cc9gLgXQl1eHX/6RUUnKzs7W999/r3nz5slmswX8Lp25bhiGcnNz9eabb+rkyZOaNm2aDhw4YM6RlZWluro6HTt2TBkZGWpqajL7+vr6ND4+rqysrNAfDAAAAHCXCSV2D2bBggX/Gstf6fJClbGxMXk8HvMNUkkaHx9XX18fcTcAXAdJdAAIQxERESoqKlJbW1tA+9atW+Xz+eRwOLRv3z719PTo9OnT2r17t3p7exUZGSnpn9c5Fy9erE2bNqm7u1tHjx7V+vXrA+aqqanR6OioKioq5Ha71d/fr5aWFq1Zs0YXL17UN998o/r6enV0dOinn37S/v37dfbsWaWnp2tgYEB1dXVqb2/X0NCQWlpa5PF4As5Fb21tVXJysnlmOgAAABCOrhW7nzt3Tk888YR2796tU6dOaWBgQJ9++qneffddPfPMMyHN/9Zbb+nIkSPq6upSZWWl4uLitHz5crP/+PHjioqKCjj2BQAQiCQ6AISp6upq7d27VxMTE2ZbSkqKTp48qaKiItXV1SkzM1MOh0NOp1Pr1q3T22+/bV7b2Ngor9crh8Oh2tpavfPOOwHzJyQk6Ouvv9bFixdVUlKijIwM1dbWaubMmYqIiNCMGTN09OhRLV26VKmpqVq/fr3ef/99PfXUU5o+fbp6e3tVXl6u1NRUVVdX65VXXtHatWvN+ffs2aOqqqo7/6AAAACASXZl7G61WrVo0SJ98MEHysvLU0ZGhjZs2KCqqio1NDSENPemTZtUW1urxx57TMPDwzp48KCmTZtm9u/Zs0erV6/W9OnTb+s9AUA4MfyXH3gLAAgbfr9fixcv1quvvqqKiorJ3k5Iurq6VFhYKI/Ho5kzZ072dgAAAIA76k7E7i6XSwUFBRobG1NsbOw1rzl79qzmz5+vjo4OPfTQQ7dlXQAIR1SiA0CYMgxDH374oXw+32RvJWRnzpzRJ598QgIdAAAA/wuTFbsPDAxo69atJNAB4F9QiQ4AAAAAABBmbqQSHQBwY0iiAwAAAAAAAAAQBMe5AAAAAAAAAAAQBEl0AAAAAAAAAACCIIkOAAAAAAAAAEAQJNEBAAAAAAAAAAiCJDoAAAAAAAAAAEGQRAcAAAAAAAAAIAiS6AAAAAAAAAAABEESHQAAAAAAAACAIP4GoBIxJ7Zg2JwAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Parameter Summary Statistics:\n", "p_init: mean=0.473, std=0.228, min=0.010, max=0.800\n", "p_learn: mean=0.218, std=0.101, min=0.050, max=0.500\n", "p_guess: mean=0.125, std=0.042, min=0.100, max=0.250\n", "p_slip: mean=0.065, std=0.023, min=0.050, max=0.167\n"]}], "source": ["# Visualize parameter distributions across all skills\n", "print(\"\\n=== PARAMETER DISTRIBUTION ANALYSIS ===\")\n", "\n", "# Extract parameters for all skills\n", "params_data = {\n", "    'p_init': [params['p_init'] for params in models['bkt'].skill_params.values()],\n", "    'p_learn': [params['p_learn'] for params in models['bkt'].skill_params.values()],\n", "    'p_guess': [params['p_guess'] for params in models['bkt'].skill_params.values()],\n", "    'p_slip': [params['p_slip'] for params in models['bkt'].skill_params.values()]\n", "}\n", "\n", "# Create visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Plot distributions\n", "axes[0,0].hist(params_data['p_init'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0,0].set_title('Distribution of P(Init) - Initial Knowledge')\n", "axes[0,0].set_xlabel('P(Init)')\n", "axes[0,0].set_ylabel('Number of Skills')\n", "axes[0,0].axvline(np.mean(params_data['p_init']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_init\"]):.3f}')\n", "axes[0,0].legend()\n", "\n", "axes[0,1].hist(params_data['p_learn'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')\n", "axes[0,1].set_title('Distribution of P(Learn) - Learning Rate')\n", "axes[0,1].set_xlabel('P(Learn)')\n", "axes[0,1].set_ylabel('Number of Skills')\n", "axes[0,1].axvline(np.mean(params_data['p_learn']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_learn\"]):.3f}')\n", "axes[0,1].legend()\n", "\n", "axes[1,0].hist(params_data['p_guess'], bins=20, alpha=0.7, color='orange', edgecolor='black')\n", "axes[1,0].set_title('Distribution of P(Guess) - Guessing Probability')\n", "axes[1,0].set_xlabel('<PERSON>(Guess)')\n", "axes[1,0].set_ylabel('Number of Skills')\n", "axes[1,0].axvline(np.mean(params_data['p_guess']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_guess\"]):.3f}')\n", "axes[1,0].legend()\n", "\n", "axes[1,1].hist(params_data['p_slip'], bins=20, alpha=0.7, color='pink', edgecolor='black')\n", "axes[1,1].set_title('Distribution of P(Slip) - Slip Probability')\n", "axes[1,1].set_xlabel('P(Slip)')\n", "axes[1,1].set_ylabel('Number of Skills')\n", "axes[1,1].axvline(np.mean(params_data['p_slip']), color='red', linestyle='--', label=f'Mean: {np.mean(params_data[\"p_slip\"]):.3f}')\n", "axes[1,1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print summary statistics\n", "print(\"\\nParameter Summary Statistics:\")\n", "for param_name, values in params_data.items():\n", "    print(f\"{param_name}: mean={np.mean(values):.3f}, std={np.std(values):.3f}, \"\n", "          f\"min={np.min(values):.3f}, max={np.max(values):.3f}\")"]}, {"cell_type": "code", "execution_count": 18, "id": "4a1df17b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SKILL COMPARISON BY CHARACTERISTICS ===\n", "\n", "🟢 EASIEST SKILLS (Highest P(Init) - Students often know these initially):\n", "  1. Mode: P(Init)=0.800, P(Learn)=0.117\n", "  2. Ordering Integers: P(Init)=0.800, P(Learn)=0.138\n", "  3. Area Parallelogram: P(Init)=0.800, P(Learn)=0.167\n", "  4. Distributive Property: P(Init)=0.800, P(Learn)=0.083\n", "  5. Recognize Quadratic Pattern: P(Init)=0.800, P(Learn)=0.050\n", "\n", "🔴 HARDEST SKILLS (Lowest P(Init) - Students rarely know these initially):\n", "  1. Finding Slope From Situation: P(Init)=0.010, P(Learn)=0.050\n", "  2. Midpoint: P(Init)=0.010, P(Learn)=0.100\n", "  3. Simplifying Expressions positive exponents: P(Init)=0.010, P(Learn)=0.050\n", "  4. Rotations: P(Init)=0.035, P(Learn)=0.156\n", "  5. Solving Systems of Linear Equations: P(Init)=0.077, P(Learn)=0.251\n", "\n", "🚀 FASTEST LEARNING SKILLS (Highest P(Learn) - Quick to master):\n", "  1. Effect of Changing Dimensions of a Shape Prportionally: P(Learn)=0.500, P(Init)=0.095\n", "  2. Scale Factor: P(Learn)=0.475, P(Init)=0.150\n", "  3. Fraction Of: P(Learn)=0.439, P(Init)=0.698\n", "  4. Prime Number: P(Learn)=0.438, P(Init)=0.250\n", "  5. Surface Area Cylinder: P(Learn)=0.434, P(Init)=0.082\n", "\n", "🐌 SLOWEST LEARNING SKILLS (Lowest P(Learn) - Take time to master):\n", "  1. <PERSON><PERSON> on Parallel Lines Cut by a Transversal: P(Learn)=0.050, P(Init)=0.704\n", "  2. Percent Discount: P(Learn)=0.050, P(Init)=0.250\n", "  3. Finding Slope From Situation: P(Learn)=0.050, P(Init)=0.010\n", "  4. Recognize Quadratic Pattern: P(Learn)=0.050, P(Init)=0.800\n", "  5. Quadratic Formula to Solve Quadratic Equation: P(Learn)=0.050, P(Init)=0.125\n"]}], "source": ["# Compare skills with different characteristics\n", "print(\"\\n=== SKILL COMPARISON BY CHARACTERISTICS ===\")\n", "\n", "# Find skills with different parameter patterns\n", "all_skills_params = [(skill, params) for skill, params in models['bkt'].skill_params.items()]\n", "\n", "# Sort by different criteria\n", "easiest_skills = sorted(all_skills_params, key=lambda x: x[1]['p_init'], reverse=True)[:5]\n", "hardest_skills = sorted(all_skills_params, key=lambda x: x[1]['p_init'])[:5]\n", "fastest_learning = sorted(all_skills_params, key=lambda x: x[1]['p_learn'], reverse=True)[:5]\n", "slowest_learning = sorted(all_skills_params, key=lambda x: x[1]['p_learn'])[:5]\n", "\n", "print(\"\\n🟢 EASIEST SKILLS (Highest P(Init) - Students often know these initially):\")\n", "for i, (skill, params) in enumerate(easiest_skills, 1):\n", "    print(f\"  {i}. {skill}: P(Init)={params['p_init']:.3f}, P(Learn)={params['p_learn']:.3f}\")\n", "\n", "print(\"\\n🔴 HARDEST SKILLS (Lowest P(Init) - Students rarely know these initially):\")\n", "for i, (skill, params) in enumerate(hardest_skills, 1):\n", "    print(f\"  {i}. {skill}: P(Init)={params['p_init']:.3f}, P(Learn)={params['p_learn']:.3f}\")\n", "\n", "print(\"\\n🚀 FASTEST LEARNING SKILLS (Highest P(Learn) - Quick to master):\")\n", "for i, (skill, params) in enumerate(fastest_learning, 1):\n", "    print(f\"  {i}. {skill}: P(Learn)={params['p_learn']:.3f}, P(Init)={params['p_init']:.3f}\")\n", "\n", "print(\"\\n🐌 SLOWEST LEARNING SKILLS (Lowest P(Learn) - Take time to master):\")\n", "for i, (skill, params) in enumerate(slowest_learning, 1):\n", "    print(f\"  {i}. {skill}: P(Learn)={params['p_learn']:.3f}, P(Init)={params['p_init']:.3f}\")"]}, {"cell_type": "markdown", "id": "28f402d9", "metadata": {}, "source": ["##  Run Prediction Examples\n", "\n", "Demonstrate the model with various prediction scenarios."]}, {"cell_type": "code", "execution_count": 19, "id": "b77ecb6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PREDICTION EXAMPLES ===\n", "\n", "Example 1: Struggling Student\n", "Skill: <PERSON><PERSON> Finding \n", "Response history: [0, 0, 0, 1, 0, 1]\n", "Predicted probabilities: ['0.480', '0.304', '0.265', '0.259', '0.689', '0.409']\n", "Final knowledge probability: 0.409\n"]}], "source": ["# Example 1: Prediction for a struggling student\n", "print(\"=== PREDICTION EXAMPLES ===\")\n", "print(\"\\nExample 1: Struggling Student\")\n", "skill_example = list(models['bkt'].skill_params.keys())[55]  # Use first available skill\n", "struggling_history = [0, 0, 0, 1, 0, 1]  # Mostly incorrect responses\n", "\n", "probas = models['bkt'].predict_proba(struggling_history, skill_example)\n", "print(f\"Skill: {skill_example}\")\n", "print(f\"Response history: {struggling_history}\")\n", "print(f\"Predicted probabilities: {[f'{p:.3f}' for p in probas]}\")\n", "print(f\"Final knowledge probability: {probas[-1]:.3f}\")"]}, {"cell_type": "code", "execution_count": 20, "id": "422d6c91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 2: Successful Student\n", "Skill: <PERSON><PERSON> Finding \n", "Response history: [1, 1, 0, 1, 1, 1]\n", "Predicted probabilities: ['0.480', '0.820', '0.884', '0.846', '0.887', '0.891']\n", "Final knowledge probability: 0.891\n"]}], "source": ["# Example 2: Prediction for a successful student\n", "print(\"\\nExample 2: Successful Student\")\n", "successful_history = [1, 1, 0, 1, 1, 1]  # Mostly correct responses\n", "\n", "probas = models['bkt'].predict_proba(successful_history, skill_example)\n", "print(f\"Skill: {skill_example}\")\n", "print(f\"Response history: {successful_history}\")\n", "print(f\"Predicted probabilities: {[f'{p:.3f}' for p in probas]}\")\n", "print(f\"Final knowledge probability: {probas[-1]:.3f}\")"]}, {"cell_type": "code", "execution_count": 21, "id": "64fbe43d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 3: Learning Progression Visualization\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA+kAAAK7CAYAAACODM43AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3xT5RrA8V+S7g0tnbTQsldZZc+CbAeIishWAVG2TNlDkK1sB0tARERQkeuCguy99ygUKKWlpXsmOfeP0EBpgQJt05bn+/l4L+fNe855kpOkec67VIqiKAghhBBCCCGEEMLk1KYOQAghhBBCCCGEEAaSpAshhBBCCCGEEPmEJOlCCCGEEEIIIUQ+IUm6EEIIIYQQQgiRT0iSLoQQQgghhBBC5BOSpAshhBBCCCGEEPmEJOlCCCGEEEIIIUQ+IUm6EEIIIYQQQgiRT0iSLoQQQgghhBBC5BOSpAshxHNauXIlKpWKw4cPmzqUZ9a0aVOaNm1qsnOrVCrjf9bW1lStWpUvv/wSvV5vkpjyi4kTJ6JSqUxy7qioKN59911cXV1RqVS0b98+V8+XlpbG119/Ta1atShatCg2NjaUKFGCN954g02bNhnr7dixA5VKxc8///zE46V/Hq9du2Ys69mzJyVLlsxQr2TJkvTs2fO5Yr527VqG965arcbZ2Zm2bduyb9++Zz7e1q1bmThxYpaPTZs2jc2bNz9XnLmtZ8+eGV6Hh//bsmVLltfiRaW/D3bs2JEhjkevrxBCFAZmpg5ACCFE3lu8eLFJz+/n58fatWsBCA8PZ+nSpQwZMoTbt28zY8YMk8ZmSh9++CGtW7c2ybmnTJnCpk2bWL58OaVKlaJo0aK5er5u3brxyy+/MHjwYCZNmoSlpSVXr17lzz//5K+//qJDhw7PdLx27dqxb98+PDw8ciniBwYMGMB7772HTqfjzJkzTJo0icDAQPbt20f16tWzfZytW7eyaNGiLBP1adOm8dZbb+X6zZLnZW1tzfbt2zOVly9fnrS0tDy5FuPGjWPQoEG5eg4hhDAFSdKFEKKAUxSF5ORkrK2ts71PxYoVczGip7O2tqZu3brG7TZt2lC+fHkWLlzI1KlTMTc3z7TP8zzPF5GUlJRn50pXvHhxihcvnqfnTHf69GlKlSpFly5dcuR4T7pewcHBrF+/nvHjxzNp0iRjefPmzendu/dz9agoVqwYxYoVe6GYs8vHx8f4/m3QoAGlS5emefPmLF68mG+//TZPYngeOp0OrVaLpaXlCx9LrVZn+Aw/Ki+uRalSpXL9HEIIYQrS3V0IIXLZpUuXeO+993B1dcXS0pIKFSqwaNGiDHWSk5P59NNPqVatGo6OjhQtWpR69erx66+/ZjqeSqWif//+LF26lAoVKmBpacmqVauMXUyDgoLo168fLi4uODs78+abbxIaGprhGI92d0/vxjt79mzmzp2Lr68vdnZ21KtXj/3792eK4dtvv6Vs2bJYWlpSsWJFfvjhhxfqempubk7NmjVJTEwkIiLiic8TYPfu3TRv3hx7e3tsbGyoX78+f/zxR6bj7t69m3r16mFlZYWXlxfjxo3ju+++y9QVt2TJkrz66qv88ssvVK9eHSsrK2PyGBYWRt++fSlevDgWFhb4+voyadIktFpthnMtWbKEqlWrYmdnh729PeXLl+ezzz4zPp6YmMiwYcPw9fXFysqKokWLEhAQwLp164x1sururtfrmTlzJuXLl8fS0hJXV1e6d+/OzZs3M9Rr2rQplStX5tChQzRq1AgbGxv8/Pz44osvnpj0pl/7f//9l3Pnzhm7Lad3K46KiuLjjz/Gy8sLCwsL/Pz8GDNmDCkpKRmO86Tr9ajIyEiAx7a0qtVP/nkSGxtLq1atcHNz4+DBg0DW3d3zSnqyev36dQDWr19Py5Yt8fDwwNramgoVKjBq1CgSEhKM+/Ts2dP4PfBwd/H065GQkMCqVauM5Q9/XrPznkw/zsyZM5k6dSq+vr5YWloSFBRkfJ+dOXOGzp074+joiJubG++//z4xMTEv/HpkdS2e5f15/vx5WrdujY2NDS4uLnz00UfExcVlOk9W3znp78PVq1dToUIFbGxsqFq1Klu2bMm0/6+//oq/vz+Wlpb4+fnx1VdfmXTIiRBCpJOWdCGEyEVnz56lfv36+Pj4MGfOHNzd3fnrr78YOHAgd+/eZcKECQCkpKQQFRXFsGHD8PLyIjU1lX///Zc333yTFStW0L179wzH3bx5M7t27WL8+PG4u7vj6urKoUOHAEOX6Xbt2vHDDz9w48YNhg8fTteuXbPsmvqoRYsWUb58eb788kvA0J20bdu2BAcH4+joCMA333xD37596dixI/PmzSMmJoZJkyZlStqe1ZUrVzAzM6NIkSJPfJ47d+6kRYsW+Pv7s2zZMiwtLVm8eDGvvfYa69ato1OnTgCcPHmSFi1aULZsWVatWoWNjQ1Lly5lzZo1WZ7/6NGjnDt3jrFjx+Lr64utrS1hYWHUrl0btVrN+PHjKVWqFPv27WPq1Klcu3aNFStWAPDjjz/y8ccfM2DAAGbPno1areby5cucPXvWePyhQ4eyevVqpk6dSvXq1UlISOD06dPGhPVx+vXrxzfffEP//v159dVXuXbtGuPGjWPHjh0cPXoUFxcXY92wsDC6dOnCp59+yoQJE9i0aROjR4/G09Mz03sonYeHB/v27ePjjz8mJibGOAyhYsWKJCcnExgYyJUrV5g0aRL+/v7s2rWL6dOnc/z48Uw3RrK6XlmpUKECTk5OTJo0CbVaTcuWLbN9g+fmzZu0bduW1NRU9u3bh5+fX7b2e5KmTZuyc+dOFEV5rv0vX74MPGg9vnTpEm3btmXw4MHY2tpy/vx5ZsyYwcGDB42fw3HjxpGQkMDPP/+cYTx7+vVo1qwZgYGBjBs3DgAHBweAbL8n082fP5+yZcsye/ZsHBwcKFOmjPHGW8eOHenUqRMffPABp06dYvTo0QAsX748W8/70RtVKpUKjUbz2PrZeX/euXOHJk2aYG5uzuLFi3Fzc2Pt2rX0798/WzEB/PHHHxw6dIjJkydjZ2fHzJkz6dChAxcuXDC+X/7880/efPNNGjduzPr169FqtcyePZs7d+5k+zxCCJFrFCGEEM9lxYoVCqAcOnTosXVatWqlFC9eXImJiclQ3r9/f8XKykqJiorKcj+tVqukpaUpH3zwgVK9evUMjwGKo6Njpn3T4/n4448zlM+cOVMBlNu3bxvLmjRpojRp0sS4HRwcrABKlSpVFK1Wayw/ePCgAijr1q1TFEVRdDqd4u7urtSpUyfDOa5fv66Ym5srJUqUeOxr8fC5K1WqpKSlpSlpaWlKaGioMmrUKAVQ3n777ac+z7p16yqurq5KXFxchtercuXKSvHixRW9Xq8oiqK8/fbbiq2trRIREWGsp9PplIoVKyqAEhwcbCwvUaKEotFolAsXLmQ4V9++fRU7Ozvl+vXrGcpnz56tAMqZM2cURTFcTycnpyc+78qVKyvt27d/Yp0JEyYoD/9pPnfuXJbX9MCBAwqgfPbZZ8ayJk2aKIBy4MCBDHUrVqyotGrV6onnTd+/UqVKGcqWLl2qAMpPP/2UoXzGjBkKoPz999/Gssddr8f5448/FBcXFwVQAMXZ2Vl5++23ld9++y1DvaCgIAVQNmzYoBw7dkzx9PRUGjVqpERGRmaol/7+f/i69ujRI9N7skSJEkqPHj0ylDVr1kzRaDRPjTn9czJjxgwlLS1NSU5OVo4cOaLUqlVLAZQ//vgj0z56vV5JS0tTdu7cqQDKiRMnjI998sknyuN+itna2maKU1Gy/55Mj7VUqVJKampqhrrp77OZM2dmKP/4448VKysr42focXr06GG8bg//16BBA0VRsr4W2X1/jhw5UlGpVMrx48cz1GvRooUCKEFBQRniePT6Aoqbm5sSGxtrLAsLC1PUarUyffp0Y1mtWrUUb29vJSUlxVgWFxenODs7P/aaCCFEXpHu7kIIkUuSk5PZtm0bHTp0wMbGBq1Wa/yvbdu2JCcnZ+hKvmHDBho0aICdnR1mZmaYm5uzbNkyzp07l+nYzZo1y9Di/LDXX389w7a/vz/woCvuk7Rr1y5DS9ij+164cIGwsDDeeeedDPv5+PjQoEGDpx4/3ZkzZzA3N8fc3BxPT0/mzJlDly5dMo3nffR5JiQkcODAAd566y3s7OyM5RqNhm7dunHz5k0uXLgAwM6dO2nWrFmGlma1Wp0p9oefa9myZTOUbdmyhcDAQDw9PTNcvzZt2hjPAVC7dm2io6Pp3Lkzv/76K3fv3s10/Nq1a/O///2PUaNGsWPHDpKSkp76OgUFBQFkmo28du3aVKhQgW3btmUod3d3p3bt2pmeV3aufVa2b9+Ora0tb731Voby9HgePf+T3pePatu2LSEhIWzatIlhw4ZRqVIlNm/ezOuvv55lq+lff/1Fo0aNaNy4Mf/880+OTmy3bdu2TK3CTzJy5EjMzc2xsrKiZs2ahISE8PXXX9O2bVsArl69ynvvvYe7uzsajQZzc3OaNGkCkOXn+Vlk9z2Z7vXXX89yjof0xx7m7+9PcnIy4eHhT43D2tqaQ4cOZfhv2bJlT9wnO+/PoKAgKlWqRNWqVTPUe++9954aU7rAwEDs7e2N225ubri6uhrPk5CQwOHDh2nfvj0WFhbGenZ2drz22mvZPo8QQuQW6e4uhBC5JDIyEq1Wy4IFC1iwYEGWddKTuV9++YV33nmHt99+m+HDh+Pu7o6ZmRlLlizJsuvpk2ZNdnZ2zrCdPklUdpLCp+2b3jXbzc0t075ubm4EBwc/9RxgmPDpxx9/RKVSYWVlha+vLzY2NpnqPfo87927h6IoWT5/T0/PDDFGRkY+Ns6sZHXMO3fu8Pvvvz82yUm/ft26dUOr1fLtt9/SsWNH9Ho9tWrVYurUqbRo0QIwdDsuXrw469evZ8aMGVhZWdGqVStmzZpFmTJlsjz+k8Zue3p6Zkq+H71+YLiG2bn2jzu/u7t7pjG6rq6umJmZZeqq/6yzeVtbW9O+fXvjDOYhISG0adOGRYsW0a9fPypVqmSsu3nzZpKSkujXr1+OTHz2IgYNGkTXrl1Rq9U4OTnh6+trfI3i4+Np1KgRVlZWTJ06lbJly2JjY8ONGzd48803n/tapMvuezJdbn1XqNVqAgICnlrvSedLP+fD54uMjMTX1zdTPXd39xw7T/r3yLN8PwghRF6SJF0IIXJJkSJFjC28n3zySZZ10n+MrlmzBl9fX9avX58hIXrcOG9TTWyU/uM3q3GbYWFh2T6OlZVVtn7gP/o8ixQpglqt5vbt25nqpk+Ol95y7uzs/ExxZvWauri44O/vz+eff57lPuk3BgB69epFr169SEhI4L///mPChAm8+uqrXLx4kRIlSmBra8ukSZOYNGkSd+7cMbaqv/baa5w/fz7L46e/3rdv384063toaGiGXgK5wdnZmQMHDqAoSobXJzw8HK1Wm+n8L/q+9PHxoU+fPgwePJgzZ85kSNLnzZvH+vXradOmDZs2baJly5YvdK4XUbx48ce+f7dv305oaCg7duwwtp4DREdH58i5n+U9Cab7rnhezs7OWX5Gn+X75WmKFCmCSqV64e8xIYTILdLdXQghcomNjQ2BgYEcO3YMf39/AgICMv2XnoSpVCosLCwy/KAOCwvLcnZ3UypXrhzu7u789NNPGcpDQkLYu3dvrp/f1taWOnXq8Msvv2RofdPr9axZs4bixYsbu6w3adKE7du3Z2hZ1Ov1bNiwIdvne/XVV41Lk2V1/R5NiNJjbNOmDWPGjCE1NZUzZ85kquPm5kbPnj3p3LkzFy5cIDExMcvzN2vWDCDTZHeHDh3i3LlzNG/ePNvP5Xk0b96c+Ph4Nm/enKH8+++/Nz7+POLi4oiPj8/ysfTu4I++tlZWVvzyyy+8+uqrvP766/nus5Eu/TP8aGv/119/nanuk1quH9cD4nnekwVJYGAgZ86c4cSJExnKf/jhhxw7h62tLQEBAWzevJnU1FRjeXx8fJazwAshRF6TlnQhhHhB27dvz3LZp7Zt2/LVV1/RsGFDGjVqRL9+/ShZsiRxcXFcvnyZ33//3TjTc/ryXx9//DFvvfUWN27cYMqUKXh4eHDp0qU8fkaPp1armTRpEn379uWtt97i/fffJzo6mkmTJuHh4fHUpbNywvTp02nRogWBgYEMGzYMCwsLFi9ezOnTp1m3bp0xSRozZgy///47zZs3Z8yYMVhbW7N06VLjMljZiXXy5Mn8888/1K9fn4EDB1KuXDmSk5O5du0aW7duZenSpRQvXpzevXtjbW1NgwYN8PDwICwsjOnTp+Po6EitWrUAqFOnDq+++ir+/v4UKVKEc+fOsXr1aurVq5dlV38w3BTp06cPCxYsQK1W06ZNG+Ps7t7e3gwZMiSHXtWsde/enUWLFtGjRw+uXbtGlSpV2L17N9OmTaNt27a88sorz3XcCxcu0KpVK959912aNGmCh4cH9+7d448//uCbb76hadOm1K9fP9N+5ubmrFu3jg8//JC33nqL77//ns6dO7/o06R58+bs3LnzmcalP079+vUpUqQIH330ERMmTMDc3Jy1a9dmSjoBqlSpAsCMGTNo06YNGo0Gf39/LCwsqFKlCjt27OD333/Hw8MDe3t7ypUrl+33ZEE1ePBgli9fTrt27Zg6dapxdvfH9TZ5XpMnT6Zdu3a0atWKQYMGodPpmDVrFnZ2dkRFReXouYQQ4llJki6EEC9o5MiRWZYHBwdTsWJFjh49ypQpUxg7dizh4eE4OTlRpkwZ4yRTYOgqHR4eztKlS1m+fDl+fn6MGjWKmzdvGtfrzi/69OljXH+5Q4cOlCxZklGjRvHrr78SEhKS6+dPbyGfMGECPXv2RK/XU7VqVX777TdeffVVY72qVavyzz//MGzYMLp3706RIkXo1q0bTZo0YeTIkcYl5Z7Ew8ODw4cPM2XKFGbNmsXNmzext7fH19eX1q1bGydJa9SoEStXruSnn37i3r17uLi40LBhQ77//nvjslzNmjXjt99+Y968eSQmJuLl5UX37t0ZM2bME2NYsmQJpUqVYtmyZSxatAhHR0dat27N9OnTsxx7m5OsrKwICgpizJgxzJo1i4iICLy8vBg2bJhx+cDnUbp0aYYOHcr27dv59ddfiYiIwNzcnDJlyjB16lSGDh362JsoarWaZcuWYW9vT9euXUlISODDDz987lgAdDodOp3uhY6RztnZmT/++INPP/2Url27YmtryxtvvMH69eupUaNGhrrvvfcee/bsYfHixUyePBlFUQgODqZkyZJ89dVXfPLJJ7z77rskJibSpEkTduzYke33ZEHl7u7Ozp07GTRoEP369cPGxoYOHTqwcOFC3njjjRw7T+vWrdm4cSPjx4+nU6dOuLu78/HHHxMaGsrq1atz7DxCCPE8VIrynIuCCiGEEPdFR0dTtmxZ2rdvzzfffGPqcJ6oZcuWXLt2jYsXL5o6FCFEPpKWlka1atXw8vLi77//NnU4QoiXmLSkCyGEeCZhYWF8/vnnBAYG4uzszPXr15k3bx5xcXEMGjTI1OFlMHToUKpXr463tzdRUVGsXbuWf/7556lLRQkhCr8PPviAFi1aGIeoLF26lHPnzvHVV1+ZOjQhxEtOknQhhBDPxNLSkmvXrvHxxx8TFRWFjY0NdevWZenSpRlm484PdDod48ePJywsDJVKRcWKFVm9ejVdu3Y1dWhCCBOLi4tj2LBhxuEWNWrUYOvWrc8914IQQuQU6e4uhBBCCCGEEELkEyZdgu2///7jtddew9PTE5VKlWmJl6zs3LmTmjVrYmVlhZ+fH0uXLs39QIUQQgghhBBCiDxg0iQ9ISGBqlWrsnDhwmzVDw4Opm3btjRq1Ihjx47x2WefMXDgQDZu3JjLkQohhBBCCCGEELkv33R3V6lUbNq0ifbt2z+2zsiRI/ntt984d+6cseyjjz7ixIkT7Nu3Lw+iFEIIIYQQQgghck+Bmjhu3759tGzZMkNZq1atWLZsGWlpaZibm2faJyUlhZSUFOO2Xq8nKioKZ2dnVCpVrscshBBCCCGEEOLlpigKcXFxeHp6olY/uUN7gUrSw8LCcHNzy1Dm5uaGVqvl7t27eHh4ZNpn+vTpTJo0Ka9CFEIIIYQQQgghsnTjxg2KFy/+xDoFKkkHMrV+p/fWf1yr+OjRoxk6dKhxOyYmBh8fH65fv46Dg0PuBZoD9Ho9d+/excXF5al3W0TBI9e38JNrXPjJNS785BoXfnKNCze5voVfQbnGsbGxlChRAnt7+6fWLVBJuru7O2FhYRnKwsPDMTMzw9nZOct9LC0tsbS0zFTu5ORUIJL01NRUnJyc8vUbTjwfub6Fn1zjwk+uceEn17jwk2tcuMn1LfwKyjVOjy07Q67z77PIQr169fjnn38ylP39998EBARkOR5dCCGEEEIIIYQoSEyapMfHx3P8+HGOHz8OGJZYO378OCEhIYChq3r37t2N9T/66COuX7/O0KFDOXfuHMuXL2fZsmUMGzbMFOELIYQQQgghhBA5yqTd3Q8fPkxgYKBxO33seI8ePVi5ciW3b982JuwAvr6+bN26lSFDhrBo0SI8PT2ZP38+HTt2zPPYhRBCCCGEEEKInGbSJL1p06Y8aZn2lStXZipr0qQJR48ezcWoDJPRabVadDpdrp7nafR6PWlpaSQnJ+fr8RUiI41Gg5mZmSzxJ4QQQgghhHhmBWriuLyQmprK7du3SUxMNHUoKIqCXq8nLi5OEr4CxsbGBg8PDywsLEwdihBCCCGEEKIAkST9IXq9nuDgYDQaDZ6enlhYWJg0OU5v0ZdW2YJDURRSU1OJiIggODiYMmXKSC8IIYQQQgghRLZJkv6Q1NRU9Ho93t7e2NjYmDocSdILKGtra8zNzbl+/TqpqalYWVmZOiQhhBBCCCFEASFNfFmQlk/xouQ9JIQQQgghhHgekkkIIYQQQgghhBD5hCTpQgghhBBCCCFEPiFJei5R9Hp0l0PQHT2L7nIIil5v6pCy7dq1a6hUKo4fP56t+j179qR9+/a5GpMQQgghhBBCvAwkSc8FupMXSZnyNWmLfyRtzRbSFv9IypSv0Z28mKvnvXHjBh988IFxZvoSJUowaNAgIiMjn+k43t7e3L59m8qVK+dSpKBSqdi8eXOuHV8IIYQQQgghCiJJ0nOY7uRF0lZuhpi4jA/ExJG2cnOuJepXr14lICCAixcvsm7dOi5fvszSpUvZtm0b9erVIyoqKtvH0mg0uLu7Y2ZWeCb/T0tLy7Vjp6am5tqxhRBCCCGEEC8XSdJzkKLXk7Zp2xPrpG3elitd3z/55BMsLCz4+++/adKkCT4+PrRp04Z///2XW7duMWbMGGPdrFqxnZycWLlyJZB1d/czZ87Qrl07HBwcsLe3p1GjRly5ciXLWI4cOYKrqyuff/75cz+fFStWUKFCBaysrChfvjyLFy/O8PjIkSMpW7YsNjY2+Pn5MW7cuAyJ+MSJE6lWrRrLly/Hz88PS0tLFEVBpVLx3Xff0aFDB2xsbChTpgy//fZbhmOfPXuWtm3bYmdnh5ubG926dePu3bvGx5s2bUr//v0ZOnQoLi4utGjR4rmfpxBCCCGEEEI8rPA0leailLmrUOISnl5Rq4WE5CfXiY4jZcJCyEYrtcreFvWA955aLyoqir/++ovPP/8ca2vrDI+5u7vTpUsX1q9fz+LFi59rvfVbt27RuHFjmjZtyvbt23FwcGDPnj1otdpMdXfs2EH79u2ZPn06/fr1e+ZzAXz77bdMmDCBhQsXUr16dY4dO0bv3r2xtbWlR48eANjb27Ny5Uo8PT05deoUvXv3xt7enhEjRhiPc/nyZX766Sc2btyIRqMxlk+aNImZM2cya9YsFixYQJcuXbh+/TpFixbl9u3bNGnShN69ezN37lySkpIYOXIk77zzDtu3bzceY9WqVfTr1489e/agKMpzPU8hhBBCCCGEeJQk6dmgxCVATHzOHfBpiXz6ebN5uEuXLqEoChUqVMjy8QoVKnDv3j0iIiJwdXXN5lEfWLRoEY6Ojvz444+Ym5sDULZs2Uz1fv31V7p168bXX39N586dn/k86aZMmcKcOXN48803AfD19eXs2bN8/fXXxiR97NixxvolS5bk008/Zf369RmS9NTUVFavXk2xYsUyHL9nz57G+KZNm8aCBQs4ePAgrVu3ZsmSJdSoUYNp06YZ6y9fvhxvb28uXrxofN6lS5dm5syZz/0chRBCCCGEECIrkqRng8reNnsJc3Za0gFsrbLdkp4T0lt6LSwsnmv/48eP06hRI2OCnpUDBw6wZcsWNmzYQIcOHZ7rPAARERHGCfB69+5tLNdqtTg6Ohq3f/75Z7788ksuX75MfHw8Wq0WBweHDMcqUaJEpgQdwN/f3/hvW1tb7O3tCQ8PBwxd9YOCgrCzs8u035UrV4xJekBAwHM/RyGEEEIIIYR4HEnSs8FyaI9s1VP0elKmfJ150riHOdljObYvKvXTpwNQFCXLLuWPKl26NCqVirNnz2a5FNr58+cpVqwYTk5OgGFM+qNdtJ80sdqjXeizUqpUKZydnVm+fDnt2rV77hsC+vvj9b/99lvq1KmT4bH0Luv79+/n3XffZdKkSbRq1crYyj9nzpwM9W1ts77J8ejNBpVKZTyvXq/ntddeY8aMGZn28/DweOqxhRBCCCGEEOJFyMRxOUilVmPeofkT65i3b56tBP1ZODs706JFCxYvXkxSUlKGx8LCwli7di09e/Y0lhUrVozbt28bty9dukRiYuJjj+/v78+uXbuemMi7uLiwfft2rly5QqdOnZ57NnU3Nze8vLy4evUqpUuXzvCfr68vAHv27KFEiRKMGTOGgIAAypQpw/Xr15/rfI+qUaMGZ86coWTJkpnOL4m5EEIIIYQQIrdJkp7DNP5lMe/ZHhztMz7gZI95z/Zo/DOP5c4JCxcuJCUlhVatWvHff/9x48YN/vzzT1q0aEHZsmUZP368sW6zZs1YuHAhR48e5fDhw3z00UdP7Mrev39/YmNjeffddzl8+DCXLl1i9erVXLhwIUM9V1dXtm/fzvnz5+ncufNTewEEBwdz/PjxDP/Fx8czceJEpk+fzldffcXFixc5deoUK1asYO7cuYCh50BISAg//vgjV65cYf78+WzatOkFXr0HPvnkE6KioujcuTMHDx7k6tWr/P3337z//vvodLocOYcQQgghhBBCPI50d88FGv+yqCuXRn/1JsTGg4Mdar/iOd6C/rAyZcpw6NAhJk6cyDvvvEN4eDiKovDmm2+yevVqbGxsjHXnzJlDr169aNy4MZ6ennz11VccOXLkscd2dnZm+/btDB8+nCZNmqDRaKhWrRoNGjTIVNfd3Z3t27fTtGlTunTpwg8//JBhZvWHDR06NFNZUFAQH374ITY2NsyaNYsRI0Zga2tLlSpVGDx4MABvvPEGQ4YMoX///qSkpNCuXTvGjRvHxIkTn+1Fy4Knpyd79uxh5MiRtGrVipSUFEqUKEHr1q1R5+L1E0IIIYQQQggAlfKSrR8VGxuLo6MjMTExmSYaS05OJjg4GF9fX6ysrEwU4QPpY9LNzMyea+m0CRMmMHfuXP7++2/q1auXCxGKx8nOe0mv1xMeHo6rq6vcACik5BoXfnKNCz+5xoWfXOPCTa5v4VdQrvGT8tBHSUt6ITZp0iRKlizJgQMHqFOnTr5+0wohhBBCCCGEkCS90OvVq5epQxBCCCGEEEIIkU3StCqEEEIIIYQQQuQTkqQLIYQQQgghhBD5hCTpQgghhBBCCCFEPiFJuhBCCCGEEEIIkU9Iki6EEEIIIYQQQuQTkqQLIYQQQgghhBD5hCTpQgghhBBCCCFEPiFJei5R9DqiQg8TdvlPokIPo+h1pg4JlUrF5s2bTR2GEEIIIYQQQojHkCQ9F4Rf3c7uH17l6O99Ob1tDEd/78vuH14l/Or23DtneDh9+/bFx8cHS0tL3N3dadWqFfv27cu1cz5N06ZNGTx4sMnOL4QQQgghhBAFjZmpAyhswq9u5+Q/wzOVpySEc/Kf4fi3mIWrX7McP2/Hjh1JS0tj1apV+Pn5cefOHbZt20ZUVFSOnyu/UhQFnU6HmVnOv63T0tIwNzfP8eMKIYQQQgghxMOkJT0HKXodF/bOemKdC3tn53jX9+joaHbv3s2MGTMIDAykRIkS1K5dm9GjR9OuXbvH7nfq1CmaNWuGtbU1zs7O9OnTh/j4eONjarWau3fvAnDv3j3UajVvv/22cf/p06dTr16954577969NG7cGGtra7y9vRk4cCAJCQnGx9esWUNAQAD29va4u7vz3nvvER4ebnx8x44dqFQq/vrrLwICArC0tGTXrl00bdqUgQMHMmLECIoWLYq7uzsTJ07McO6YmBj69OmDq6srDg4ONGvWjBMnThgfnzhxItWqVWP58uX4+flhaWmJoijP/VyFEEIIIYQQIjukJT0bDmzsSmpS5FPr6XWppCVHP7FOSsId/lvdErXG4qnHs7B2psbrK55az87ODjs7OzZv3kzdunWxtLR86j6JiYm0bt2aunXrcujQIcLDw/nwww/p378/K1eupHLlyjg7O7Nz5046duzIf//9h7OzM//995/xGDt27KBJkyZPPVdWTp06RatWrZgyZQrLli0jIiKC/v37079/f1asMDzn1NRUpkyZQrly5QgPD2fIkCH07NmTrVu3ZjjWiBEjmD17Nn5+fjg5OQGwatUqhg4dyoEDB9i3bx89e/akQYMGtGjRAkVRaNeuHUWLFmXr1q04Ojry9ddf07x5cy5evEjRokUBuHz5Mj/99BMbN25Eo9E81/MUQgghhBBCiGchSXo2pCZFkpIQ/vSK2fS0RP5ZmZmZsXLlSnr37s3SpUupUaMGTZo04d1338Xf3z/LfdauXUtSUhLff/89tra2ACxcuJDXXnuNGTNm4ObmRuPGjdmxYwcdO3Zkx44d9OjRg1WrVnH27FnKli3L3r17GTJkyHPFPGvWLN577z3jmPUyZcowf/58mjRpwpIlS7CysuL999831vfz82P+/PnUrl2b+Ph47OzsjI9NnjyZFi1aZDi+v78/EyZMMB574cKFbNu2jRYtWhAUFMSpU6cIDw833tCYPXs2mzdv5ueff6ZPnz6A4SbB6tWrKVas2HM9RyGEEEIIIYR4VpKkZ4OFtXO26mWnJR3A3Mop2y3p2dWxY0fatWvHrl272LdvH3/++SczZ87ku+++o2fPnpnqnzt3jqpVqxoTdIAGDRqg1+u5cOECbm5uNG3alG+++QaAnTt3MmXKFIKDg9m5cycxMTEkJSXRoEGDbMf4sCNHjnD58mXWrl1rLFMUBb1eT3BwMBUqVODYsWNMnDiR48ePExUVhV6vByAkJISKFSsa9wsICMh0/EdvTnh4eBi7yh85coT4+HicnTO+vklJSVy5csW4XaJECUnQhRBCCCGEEHlKkvRsqNNxTbbqKXodu3949Ymt7pa2bjR873dU6qd3n1YUBa1Wm+04raysaNGiBS1atGD8+PF8+OGHTJgwIcskXVEUVCpVlsdJL2/atCmDBg3i8uXLnD59mkaNGnHlyhV27txJdHQ0NWvWxN7ePtvxPUyv19O3b18GDhyY6TEfHx8SEhJo2bIlLVu2ZM2aNRQrVoyQkBBatWpFampqhvoP32hI9+gkbyqVypjk6/V6PDw82LFjR6b90rvLP+64QgghhBBCCJGbJEnPQSq1hnL1h2c5u/v9GpSrPyxbCXpOqFix4mPXRa9YsSKrVq0iISHBmIzu2bMHtVpN2bJlAYzj0qdOnUrVqlVxcHCgSZMmTJ8+nXv37j33eHSAGjVqcObMGUqXLp3l46dOneLu3bt88cUXeHt7A3D48OHnPt+j5w4LC8PMzIySJUvmyDGFEEIIIYQQIifI7O45zNWvGf4tZmFp65qh3NLWDf8WM3Nl+bXIyEiaNWvGmjVrOHnyJMHBwWzYsIGZM2fyxhtvZLlPly5dsLKyokePHpw+fZqgoCAGDBhAt27dcHNzAwytz40bN2bNmjU0bdoUMHQjT01NZdu2bcayJ4mIiOD48eMZ/gsLC2PkyJHs27ePTz75hOPHj3Pp0iV+++03BgwYABha0y0sLFiwYAFXr17lt99+Y8qUKTnyer3yyivUq1eP9u3b89dff3Ht2jX27t3L2LFjc+xGgBBCCCGEEEI8D2lJzwWufs0oVrIJ98KOkZp4FwsbF4q4V8+1FnQ7Ozvq1KnDvHnzuHLlCmlpaXh7e9O7d28+++yzLPexsbHhr7/+YtCgQdSqVQsbGxs6duzI3LlzM9QLDAzkl19+MSbkKpWKRo0asWXLFho2bPjU2H744Qd++OGHDGUTJkxg4sSJ7Ny5kzFjxtCoUSMURaFUqVJ06tQJgGLFirFy5Uo+++wz5s+fT40aNZg9ezavv/76c7xCGalUKrZu3cqYMWN4//33iYiIwN3dncaNGxtvUAghhBBCCCGEKaiUl2zx59jYWBwdHYmJicHBwSHDY8nJyQQHB+Pr64uVlZWJInwgfUy6mZnZY8ePi/wpO+8lvV5PeHg4rq6uqNXSqaUwkmtc+Mk1LvzkGhd+co0LN7m+hV9BucZPykMflX+fhRBCCCGEEEII8ZKRJF0IIYQQQgghhMgnJEkXQgghhBBCCCHyCUnShRBCCCGEEEKIfEKSdCGEEEIIIYQQIp+QJF0IIYQQQgghhMgnJEkXQgghhBBCCCHyCUnShRBCCCGEEEKIfEKSdCGEEEIIIYQQIp+QJD2X6BQdp+8eZtfNPzl99zA6RWfqkPIVlUrF5s2bTR2GEEIIIYQQQuQrZqYOoDDaF7qNZadnEZkcbixztnLlg8rDqefZPFfO2bNnT6KjowtM4nv79m2KFCmSrboqlYpNmzbRvn37HI1hx44dBAYGcu/ePZycnHL02EIIIYQQQgjxPKQlPYftC93GzMMjMiToAJHJEcw8PIJ9odtMFFn+4u7ujqWlpanDEEIIIYQQQoh8RZL0HKRTdCw7PQtQsnjUULb89Oxc7/retGlTBg4cyIgRIyhatCju7u5MnDgxQ53o6Gj69OmDm5sbVlZWVK5cmS1bthgf37hxI5UqVcLS0pKSJUsyZ86cDPuXLFmSadOm8f7772Nvb4+Pjw/ffPON8fHU1FT69++Ph4cHVlZWlCxZkunTpxsff7i7+5PqlixZEoAOHTqgUqmM2wC///47NWvWxMrKCj8/PyZNmoRWq81wju+++44OHTpgY2NDmTJl+O233wC4du0agYGBABQpUgSVSkXPnj2f6/UWQgghhBBCiJwi3d2zYdjOLkSnRD61Xpouldi06CfUULibfIf3/2yBucbiqcdzsnRmev2V2Y7zYatWrWLo0KEcOHCAffv20bNnTxo0aECLFi3Q6/W0adOGuLg41qxZQ6lSpTh79iwajQaAI0eO8M477zBx4kQ6derE3r17+fjjj3F2ds6QyM6ZM4cpU6bw2Wef8fPPP9OvXz8aN25M+fLlmT9/Pr/99hs//fQTPj4+3Lhxgxs3bmQZ65PqHjp0CFdXV1asWEHr1q2NMf7111907dqV+fPn06hRI65cuUKfPn0AmDBhgvHYkyZNYubMmcyaNYsFCxbQpUsXrl+/jre3Nxs3bqRjx45cuHABBwcHrK2tn+u1FkIIIYQQQoicIkl6NkSnRGbqvv4iYtOiIe3p9bJqj88uf39/Y7JapkwZFi5cyLZt22jRogX//vsvBw8e5Ny5c5QtWxYAPz8/475z586lefPmjBs3DoCyZcty9uxZZs2alSFJb9u2LR9//DEAI0eOZN68eezYsYPy5csTEhJCmTJlaNiwISqVihIlSjw21ifVLVasGABOTk64u7sbyz///HNGjRpFjx49jPFPmTKFESNGZEjSe/bsSefOnQGYNm0aCxYs4ODBg7Ru3ZqiRYsC4OrqKmPShRBCCCGEEPmCJOnZ4GTpnK16T29JN3Awd8p2S/rz8vf3z7Dt4eFBeLjhRsPx48cpXry4MUF/1Llz53jjjTcylDVo0IAvv/wSnU5nbM1++BwqlQp3d3fjOXr27EmLFi0oV64crVu35tVXX6Vly5ZZnu9Z6qY7cuQIhw4d4vPPPzeW6XQ6kpOTSUxMxMbGJlOMtra22NvbG2MUQgghhBBCiPxGkvRsmN1kbbbq6RQdff9pR2RyBFm3g6twsXJlaYstaFSapx5PUZQMY6yfhbm5ecYzq1To9XqAp3brVhQFlUqVqexZzlGjRg2Cg4P53//+x7///ss777zDK6+8ws8//5zpOM9SN51er2fSpEm8+eabmR6zsrLKVoxCCPEidIqeYxF3uBpxBz+VQnVXNzQqmeqlMJFrXPjJNS7ctDotR0+c4UZ4ON6urtSoWgkzjaQ/hUlhvcYF/xnkIxqVhg8qD2fm4RGAioyJuiHpfb/ysGwl6LnJ39+fmzdvcvHixSxb0ytWrMju3bszlO3du5eyZcsaW9Gzw8HBgU6dOtGpUyfeeustWrduTVRUlLGbeXbrmpubo9NlnGyvRo0aXLhwgdKlS2c7nkdZWBh6Mzx6bCGEeJqgWyHMPXmE8KREQ8GlM7ha2zDUvyaBXj6mDU7kCLnGhV/QrRDmnjhMeHKSoeDSGVytrBlaNUCucSGwbe9+5oVcIsLi/k2Xa+EUu3iSIT5laF6/rmmDEzmiMF9juVWYw+p5NmdEwEycrYplKHexcmVEwMxcWyf9WTRp0oTGjRvTsWNH/vnnH2Mr9p9//gnAp59+yrZt25gyZQoXL15k1apVLFy4kGHDhmX7HPPmzePHH3/k/PnzXLx4kQ0bNuDu7p7l2O+n1S1ZsiTbtm0jLCyMe/fuATB+/Hi+//57Jk6cyJkzZzh37hzr169n7Nix2Y6xRIkSqFQqtmzZQkREBPHx8dneVwjx8gq6FcKoA7seJG/3hSclMurALoJuhZgoMpFT5BoXfnKNC7dte/fz2e3LRJhn7BkaYa7is9uX2bZ3v4kiEzmlsF9jaUnPBfU8m1PboynnIo9xL/kuRaxcqOBc3eQt6A/buHEjw4YNo3PnziQkJFC6dGm++OILwNBK/dNPPzF+/HimTJmCh4cHkydPfqYlyuzs7JgxYwaXLl1Co9FQq1Yttm7dilqd+b7Q0+rOmTOHoUOH8u233+Ll5cW1a9do1aoVW7ZsYfLkycycORNzc3PKly/Phx9+mO0Yvby8mDRpEqNGjaJXr150796dlStXZnt/IcTLR6fomXviyBPrzD5xmFKOTqhRoQBKeq8qBeO28qDIUGLcNtQ2bCvGOory0HEe2k4vUe4XPnw+Rcnq+Pcff2T7Qd0H9TMcXwEe2X74PBlif6jwSc/3QexKhtfmabE/GH314LXK8NpgGKKV1WuT+fGM51NQ0OsV1lw6x5NMObKfyzHRqB8ZGvaw55v89Tn3eo7dnudMSh7Gl5uvoF5R+PnSeUNgj15DlQoUhckH93CmdOQTr7F4VPoHV3noQ5X+AX74McO28vBjxjqP1FeUh/Z5pC5ZnUNBp9OzKeYOqFWPvb5Tb13izP/uoVarjKE/z9PN9Z0yVc/G/s8cVxY7mOIYz/BFodPr2ZwW+8Rr/GXIJZrUCSiwXd9VSlaDjQux2NhYHB0diYmJwcHBIcNjycnJBAcH4+vrm2Fcs6mkj0k3MzPLNEZc5G/ZeS/p9XrCw8NxdXXN8uaFKNh0ip5j4Xe4Gn4HP1c3GeeYC7R6Pal6HSk6Hak6HSkP/1unMz728L/TH0vRZ10vffvheg8fP1mrRfty/dkUQgghCqSFflWoVc3/6RXzyJPy0EcVzFsLQgiRj71MY1kVRSFNr39iUpz8SOKbqr9f9khSnPzIfsbjPSb51kmyLIQQQojHuBsba+oQnpsk6UIIkYPSxzk+Kn2c4xd1GuVKoq5T9KTq9FkmxSlZJb5PaE1+NFF+NKF+uGU5Vad7zk6p+Z+ZSo2FRo2lxgxLteH/tYqeWwlPn78iwMUNZ2tr0vtApfeGUgEqHvSMUqkM2yoe7rH3YDu9rur+/zy8/fAx08+hMtZ9+HwPHnv4DOnnS6/56PlUqgexPBpreqXHx/7Qfvd3fnj7cfE8/IwePd/D8TyIXfWY1+ZpsT94rVQPnQ/gakwMS8+d4Gk+rlSV0o5FnljnefrAqZ5rr8w9PrN7ttzf4/5+z7Hj414LRatFiU+EhCRISEJJSEJJSESJT4aERMN/icmP7T572dacRX5OTz3/J1ejKZ2Q9uyBFwQq1SP/ASr1gw9Q+r/V9z8lKpVhJiuV+kF9VA+6G6tUxrqoQJXeQ1D96HkePt/986jvX+ss6z4ST1bHSu+yrjbUuxh7j4U8/Xt6gNqBss7FHl8hO+/ZTHWysdPTPgxPffzpp3hqpUxdxF/8kLny3B9T/WLYHRYk333q6Vye0lqdn0mSLoQQOUSn6Jl78snjlb84dhBFgTRFR4pOT4pOS6r+/v/rdKQY//3wY1l12daSotcbk2etUniXFrRUa7DUaLDQ3P//h7cf89jj66mxVJsZ61uqNcZE3EKtwep+fQuNOsvhCTpFT/s/f8002dTD3KxtmN+omQxvKKAaenjxy7VLT73GXctWlGucwxRFgcRklOg4lJg4lJh4lJg4SP93tOH/SUp+ofNUjUnlJy87Iiw0WScNioJrio63i3liVq7Ig0RQpTIknw9tG/79SJk6Pak0/FuVXpZe7+HkMsO+6gcJ7sNlDyfHxn3ulxmPn55Uqx+J636ZMd775yjEwyhr6rSs37DOMKHY465vmsK7b7cpsOOVX3Y1dFp+zMY1rl6lYt4Hl0PknSmEEDnk+N2IJ/6wB4hOTWH0wcwt7fmdRqXKnPgak9yH/v8JibLV/e2H6xmT4of2eXg/c7U6X/2Y1KjUDPWvmWVviXRD/GtK8laAyTXOHYpOB7EJGZLv9KTbkIjHGxJwrfbFTqQC7GxROdqhcrR/8P9O9nD/39jbMPDrNYzzsc48edz91vcB4alYDXzjQYuwKDDMNGYM8SnDZ7cvP/b6DvYpIwl6AfYyXOOCG7kQQuQT8Wmp7L59ix8vn8/1c1mo1ZmS4uwkyOn1LJ+SFD+uddpMfqgaBXr58EWdRhnnHcDQujqkEM478DKSa/xslJTU+0l3/INW75iHWsOj4yA+4XknbH9Ao0HlaAeO9qicHknC7/8bBztUZk9fTad58ybw+5/M93MkwvLBz2HXFB0DgmNo/lprSdALsOb16zJtL/fX0H6QwLmmKQwuBGtoi8df42JpSqFYJ12SdCGEeA4xKSn8d/smQaEhHAwPI02f/e7m7UuWws/BKcvk+UmJtoVaI8sB5ROBXj409iwuM/gXYnKNQdErkJCYKekmOmMiTnLqi5/M2jJD0s1DLeDGMlvrHOtZo/EvS3OgwaZtnCSFSHMNzmk6/FWWWLVvjca/bI6cR5hO8/p1aVIngKMnznAjPBxvV1dqVK1UoFtXRUY2JRNwi/sBfZI5OuzQEI9bkTRsSg4zdWgvTN6lQgiRTZHJSewMvUFQ6A2ORNzJcnZxFU9uLHKztmFE9dov1Q/9wkqjUlOjmBvFFRWuxVxRyzUtdArzNVa0OpTY+y3f0Zlbv0nvhq57wfkuVCqwv9/93Mk+60Tc0Q6VpUXOPLFnoPEvi03l0tS6HELMzds4FvdAU9pHWtALETONGQHVquAjS94WOvtCtzHz8AhAwfKhyxqVomLm4RGMCJhJPc/mJovvRUmSLoQQT3AnMYGg0BsE3QrhRGRElgm4q7UNTT29aeblTVRyMp8d3P3Y48lYViFEblIUBZJTnzDx2v0kPP7J82dki7nZg+7nj4z/Nibi9raoNPn3O0+lVqMu7UOagxVqV1dJ0IUoAHSKjmWnZ5F1s4gCqFh+eja1PZqiUT19+Et+JEm6EEI84mZ8HEGhIQTdusGZe5FZ1vG0sSPQy5tmXj5ULOKcoRv6FyqVjGUVQuQ4Ra+H+ETj+O8sE/GYOEjJgWXDbK1ROaQn3Y+2fN8f/21jla8mdhRCvBzORR4jMjn8CTUU7ibf4VzkMSq7BORZXDlJkvRcolP0HL8bwd3kJFysrKnmUkxaz4TIx4JjYwgKDWH7rRtcirmXZZ2S9g4EenoT6OVDWccij/1xKmNZhSgcFL0e/eUQzG/eRh+bjCoXu0IradoHs5w/sgSZMfmOjQf9C86+plYZJlfLMOnaIzOgO9ihsjDPmScmhBA57Hb8jWzVu5eNtdTzK0nSc0HQrZBMrWiu1jYMzcVWtJ49e7Jq1apM5a1ateLPP//MlXNmV8+ePYmOjmbz5s0mjUOIhymKwqWYe2y/dYOg0BCuxcVmWa+MYxFjYu7n4Jjt4xfmsaxCvAx0Jy+StmkbxMRhC2gBraM95h2aP9OkYsa1vx+eaM24BNn9/4+Nh4SkFw/awtzY1TxzF/T7reF2NtKl+yE6RceZu0e4HnmVEmo/KhWrWWC7xwpR2OkVPTtubGHl2XnZql/EyiWXI8o9kqTnsKBbIVmurRqelMioA7v4ok6jXEvUW7duzYoVKzKUWVpa5sq5skOn00k3OJGvKIrCmXuRBN0KISj0BrcS4rOsV6mIM4Fe3jT19MHbzj6PoxRCmJru5EXSVm7O/EBMnKG8Z3s0/mVRdHqIe2jt70dawNO7oJP2gmt/gyG5frjl28n+fiL+oAs6Vhbyd/cZ7AvdxrLTsx50m70CzlaufFB5eIGecEqIwuh67CW+Pjmdc1HHs1FbhYuVKxWcq+d2WLlGkvQcpFP0zD155Il15p08QmPP4rnS7dXS0hJ3d/dM5Tt27KBly5Zs27aNRo0aATBnzhymT5/OqVOn8PDwoGnTplSuXBmANWvWoNFo6NevH1OmTDH+wU9NTWXs2LGsXbuW6OhoKleuzIwZM2jatCkAK1euZPDgwaxZs4YRI0Zw8eJFunTpwvfffw9gPE5QUJBxHyFym07RczIygu23brAj9EaGHi7pVIC/czECvXwI9PTG3cY27wMVQuQLil5vaEF/grTVv5FmY22YfC2LVR6eiUZt6H7ulHnNb5WjPTjZo3KwRWUmP9ly0sMzQz8sMjmiUMwMLURhkaRNYN35pfwR/CN6RWcsL1fEnwv3TpJ5XR1DvvF+5WEFuleMfONnQ4/t/yMy+end0FJ1OmLSnrxW6J2kRNps2YiF5ulvGmcra75r9Eq243ycpk2bMnjwYLp168aJEye4du0aY8aMYd26dXh4eBjrrVq1ig8++IADBw5w+PBh+vTpQ4kSJejduzcAvXr14tq1a/z44494enqyadMmWrduzalTpyhTpgwAiYmJTJ8+ne+++w5nZ2fc3d1JTk4mNjbW2MpftGjRF35OQjyJVq/n6N07bL8Vws7Qm0SlJGeqo1GpqOHidr/F3BtnK2sTRCqEyG/0V29CTNyTK91vQX8qK4uME6091O38wdrfNqjU0vqdl16GmaGFKOgURWFP6N+sODOXqOQIY7mHrQ99qoykmmu9zL1hABcrV96vPKzA32STJD0bIpOTiMhGkp5dMWmpkAMTrz5qy5Yt2NnZZSgbOXIk48aNY+rUqfz777/06dOHM2fO0K1bNzp06JChrre3N/PmzUOlUlGuXDlOnTrFvHnz6N27N1euXGHdunXcvHkTT09PAIYNG8aff/7JihUrmDZtGgBpaWksXryYqlWrGo9rbW1NSkpKlq38QuSUVJ2Og+FhBIWG8N/tm8SmZr5hZqZSU9vV3TCxm4cXTpZWJohUCJGvxWY9DCYTK0tULk5ZrP2d/m87VFamG3ImHu9lmBlaiILsVvw1vj05gxN3DxjLLNSWvFX2A9qX6o65xgKAep7Nqe3RlDMRR7gecZUSxQrPvBKSpGdDdlvYstOSDuBobpHtlvRnERgYyJIlSzKUpbdaW1hYsGbNGvz9/SlRogRffvllpv3r1q2bYSxbvXr1mDNnDjqdjqNHj6IoCmXLZpwsJyUlBWdnZ+O2hYUF/v7+zxS3EM8rWatl351QgkJvsPv2LRK0me9+Wao11HP3JNDTm4YeXtiZW5ggUiFEgZGNv88A5u93QFNallQsiB5ulXuSgjwztBAFUYo2iZ8vLWfz5VVolQdzeQS4NeLDyiNws/XKtI9GpaGySwCueh9cXQrPRL2SpGfDqmZtslVPp+hp/+evWY55TedmbcOm1m9ka0y6oihotdmfbMbW1pbSpUs/9vG9e/cCEBUVRVRUFLa22R93q9fr0Wg0HDlyBM0jP2Aebr23traWSWtEropPS2NP2C2CboWw704oyTpdpjo2ZmbUd/eimac39dw9sTGTpYSEEE+nD7tL2q/bn17RyR61X/HcD0jkOJ1ey86b/8tW3f9d20Bpp4p42MnNGCFy26GwnXx7aiYRSbeNZcWsPfiwyghquzcxYWSmIUl6DtKo1Az1r5nl7O7phvjXNMlayVeuXGHIkCF8++23/PTTT3Tv3p1t27ahfmgZlv3792fYZ//+/ZQpUwaNRkP16tXR6XSEh4cbJ5/LLgsLC3RZJFJCZFdMagq7bt8k6NYNDoTfJk2vz1TH3tyCRh5eBHr6UMfNA8tstoYJIQSA/nIIqSs2QVLKU+uat28uy5gVQCnaJOYc/Yyj4buzVf9c1DEGBr1FG99OvFO2N3YWDrkcoRAvnzsJt1h2ehaH7vxnLDNTmfFG6e68XeYDLM1ezjmDJEnPYYFePnxRp1GmddLdrG0YkovrpIOh63lYWFiGMjMzM4oUKUK3bt1o2bIlvXr1ok2bNlSpUoU5c+YwfPhwY90bN24wdOhQ+vbty9GjR1mwYAFz5swBoGzZsnTp0oXu3bszZ84cqlevzt27d9m+fTtVqlShbdu2j42rZMmS/PXXX1y4cAFnZ2ccHR0xN5eWTfFkkclJ7Ay9SVBoCEci7qDLYgblIpaWNPbwppmXNzWLuWGulsRcCPHsdEfPkrbuf3D/hrLKyxVNg+po/9qbcRI5J3vM2z/bOukif4hLjWHagcGcv3cCADVq9OjJPDO0gY2ZHYnaeLSKlt+vriXoxu+8U7Y3rX3fwVwtv2GEeFFpulQ2X/meny8uI1X/4Oaov0ttelcZSXF7XxNGZ3qSpOeCQC8fGnsW5/jdCO4mJ+FiZU01l2K53oL+559/ZpitHaBcuXK89957XLt2jd9//x0Ad3d3vvvuO9555x1atGhBtWrVAOjevTtJSUnUrl0bjUbDgAED6NOnj/FYK1asYOrUqXz66afcunULZ2dn6tWr98QEHaB3797s2LGDgIAA4uPjZQk28VjhSYnsCL1B0K0Qjt+NQJ/FD6diVtY09fQm0MuHqs7FMJPWLCHEc1IUBd32g2j/2GksU5f3w7zH66gsLdDUroLucggxN2/jWNwDTWkfaUEvgCISbzN5f39uxgcDYG1my6hac0hIi8tiZmg33q88jGquddl0eRW/XllDqi6Z+LRYlp+Zw/+u/UT3ioOo4x4ow/uEeE4nIg7wzckvCE24biwrYulCr8qf0tCzpXy2AJWivOgCnwVLbGwsjo6OxMTE4OCQsdtScnIywcHB+Pr6YmVl+lmf08ekm5mZ5fqbtWnTplSrVi3LCeXEs8vOe0mv1xMeHo6rq2uGYQcvm9CEeLbfCiEo9Aano7KepMfDxpZAT2+aeflQqagL6gLy5S3XuHDTKbpCOaPsy0LR6dFu+hfd3uPGMk0df8zeaolK8+DzKp/jgu167GWm7O9vTMSdLJ0ZV3cBfo7lgad/ju8mhbH23CJ23Pwjw3ErOtegV6WhlHaqmHdPRjwX+QznH5FJ4aw8M5fdoX8by9QqDe18O/FuuY+wMbd7wt6PV1Cu8ZPy0EdJS7oQIs9di4sh6JahxfxCzL0s6/jY2RPo6UOglzflnYrKXVWRr2Ram/UKOFu58kHl4QV+bdaXgZKSStrq39GfvWIsM2vbCE3zuvJdU4iciTzCtANDSNQaltXzsPVhQt1FGWaIftrM0C7W7gyqMYVX/Tqz4sw8zkQeAeBs5FGG/9eVJsXb0rVCf1ysZZlZIR5Hq0/jj+D1/Hh+Kcm6B8OByxepSh//0fg6yhCiR0mSLoTIdYqicDk22tBifusGwXExWdYr7eBEoJc3gZ4++Dk4yo9lkS/tC93GzMMjeHQca2RyBDMPj2BEwExJ1PMxJS6B1O82oty4P4eLWo35u23QBFQybWAiR+0L3ca8o2NI0xuWxi3tVImxdebjaFnkuY5XyqkiU+p/w8Gwnaw6+yW3E0IA2HlzK/tCt/FaqS50LNMLa7Psr5wjxMvgbOQxvjk5netxl41lDhZOdK84iEDv1wrNkmk5TZJ0AcCOHTtMHYIoZBRF4dy9KIJCQ9h+6wY3E+KyrFfBqSiBXj4EenrjYy8z54r8TafoWHZ6FllNNGUoU7H89GxqezSVru/5kD48krRvfkaJun+j0MoC854d0JQtYdrARI76M3gD35z6AuX+57SGawOGBczA2szmhY6rUqmo49GUGm4N+Ovaz6y/8A3xaTGk6lPYeGk5/17fzHvl+9Hc5w00avmJLV5u0SlRrD77Fdtv/G4sU6GiRYk36VqhP/YWjiaMLv+TbxAhRI7RKwonIyMICr3BjlshhD20wsHD/Iu6EOjlQ1NPbzxtn2/8kRCmcC7yWIZJpjJTuJt8h3ORx6jsEpBncYmn01+9SeryXyAx2VDgZI/Fh2+h9ixm2sBEjlEUhXUXlrDh4nfGskDv1/i46ljMcnBGdnO1Oa/6daZp8Xb8fGkZf1xdh1bREpMaxZKTn7Ml+Ed6VhpMDdcGOXZOIQoKnaLjn+ubWHNuAQlpDxpo/BzL09f/M8oWqWzC6AoOSdKz8JLNpSdywcv0HtLq9Ry7G07QrRB2hN4gMiU5Ux01KqoXcyXQ05umnt4Us36x1gwhTOVectaTGz5vPZE3dMfPk/bDH6C9v8SaRzEser+FysnexJGJnKLTa1l6chr/hmw2lr1ZuhddK/TPtaFTdhYO9Kw0hFYl32L12QXsu/0vADfirjBl/wCqFatLz0pDKOFQJlfOL0R+czn6LF+fnMbl6LPGMhszO7pU+IRWJd+SHmbPQJL0h6Sv3Z2YmIi1tbWJoxEFWWKioQW5sK4Hn6bXcTA8jKBbN/jv9k1iUlMy1TFTqanl6kagp2FJwiKWpl8xQYgXVcTKJVv1nCydczkSkR2KoqDbeQjtbzuMZeqyJTHv+QYqK0vTBSZyVIo2idlHRnH4zi7A0KX2/crDeNWvc56c38PWmxG1ZnIu8jgrzszlUvRpAI5H7Gfojs40L/EGncv1y/b3hxAFTXxqLGvPL+Kvaz8bh5kANC3+Kj0qDsLJSv4mPitJ0h+i0WhwcnIiPNzQldHGxsakE1fl5RJsImcoikJiYiLh4eE4OTmh0RSeO4bJOi0H7txm+60Qdt2+RYI2LVMdC7Waum6eBHp508i9OPYWFiaIVIjcU9yuJGrU6NE/sd4/1zdRtmgVLDVyc8pUFL0e7ebt6HYfNZZpalfB7O2WqArRd/PLLjY1mmkHBnPh3kkAzNTmDKo+mYZerfI8lgrO1fii0Ur23Pqb1ecWEJF0Gz16/rm+iV03/+LNMj153a8LlmbSECQKB0VRCLqxhVVnvyQ29cFqPd72pejrP4pKzjVNGF3BJkn6I9zdDUtopCfqpqQoCnq9HrVaLUl6AePk5GR8LxVkCWlp7L1zi6BbN9gbFkqSTpupjrXGjPruhsS8vpsXtoW094AQOkXHV8fGPzVBB9gV+iehCdcZVXsuLtZueRCdeJiSmkbami3oT18ylpm1aoCmZX35e1qIhCeGMnl/f27FXwPA2syWUbXm4F+stsliUqvUNCremjoegWy5uo6fLy0jSZtAsi6RH84v5q9rG+la4RMaF28rs1qLAu167CW+Pjmdc1HHjWVWGms6levLq36dc3QeiJeRJOmPUKlUeHh44OrqSlpa5pbCvKTX64mMjMTZ2Rm1Wr7ICwpzc/MC3YIem5rC7tu32B56gwN3QknVZ05I7MzNaejuRTMvH+q4eWClka8SUfitv/ANxyP2A4YxdpYaK+6lPBh77mLlRkOvVvx5bQPJuiSuxJxj+H9dGVlrNuWLVjVV2C8dJT7RsMRayG1DgVqN2TutMKtdxbSBiRx1PfYSk/f3Jyo5AoAili6Mq7sAX8dyJo7MwEJjyZtletLM53XWX/iav6//gl7REZl8h6+OjWfL1XX0rDSUyi7S0igKliRtAj+e/5otwevQKzpjeT2PV3i/8qdyYzqHyC/rx9BoNCZPtPR6Pebm5lhZWUmSLnLVvZRkdobeJOhWCIciwtBlMfGdo4UlTTyKE+jlTS1Xd8zVBfdGhBDP6lDYf2y4+C0AapWG0bXnUsG5OmcijnA94iolivlRqVhNNCoNTb3bMf3gUO4k3iI6JZJxe/vQ1380r/i0N+2TeAnoI6IMS6xFRhsKLC0w7/kGmnK+Jo1L5KzTd48w/eAQErXxAHjY+jCh7iLcbL1MHFlmTpZF6es/mra+nVh19kuO3NkNwJWYc4zb25s67oF0qzgQLztZBlDkb4qisDf0H5afmWO8OQaGORl6VxlJddf6Joyu8JEkXYiXVERSIjtCb7D91g2O3w1Hn8W6z86WVjT19KaZlw/VXFwxk5tF4iUUlnCTr46NM253rdDfuLxaZZcAXPU+uLq4GruulnAow8xG3zPryEhO3z2MVp/GouOTuRZziV6Vhsj6yblEf+0Wqct+gYQkQ4GDHRa9O6L2kladwmRf6DbmHR1Dmj4VgDJOlRlT5yscLYuYOLIn87b3Y2yd+ZyIOMDKM3O5FmsYinEgLIjDd3bRuuTbvFOuNw4WTqYNVIgs3Iq/zrenZnDifm8yAAu1JR3LvE/70t2x0MhEnDlNfikI8RIJTYgnKPQGQbdCOBWV9RJR7tY2NPXyIdDTG3/nYqhl/KZ4iaXokpl5aLhxrde6Hs1oX6r7U/dzsCzChLqLWHFmLluD1wPwR/A6bsRd4dOAL+SHeA7TnbxI2potoDXMm6FydzEssVbEwcSRiZz0v+Cf+PbUDOPs0TVcGzA8YCZWBWgitqrF6jC7yQ8E3fidH84t5l7KXXSKlj+C17Hj5hbeLtubtiXfwVwjE68K00vRJrHx8go2XV6FVv9gGHBNt4Z8WHkE7rbFTRhd4SZJuhCFXEhcLNtDQwi6dYPz0VFZ1ilua08zL28CPX2oUKSoTKwkBIaufd+cnE5w7AUAvOxKMqDaxGx/PszU5vSuMpISDmX49uQXaBUtJ+8eZMR/3fms9jx8HErlZvgvDe1/R9D+uo30zkDqMj6Y92yPylpm1i8sFEXhh/OL+fnSMmNZM+/X6Fd1bIGcnEqj0vCKT3saeLZk8+VVbL6ymlRdMglpcaw8M5f/Bf9E94oDqefRXP4eC5M5FPYf352eSXhiqLGsmLU7H1QeQW33JvLezGWSpAtRyCiKwpXYaIJu3SAoNIQrsTFZ1vNzcCTQ04dAL29KOzjJl60Qj/jn+i9sv/E7AJYaK0YEzMLG3O6Zj9OyxJsUt/Nl5qHhxKRGcSfxJiN39WBwjanU8Wiaw1G/PBS9gvb3IHQ7DxvL1AGVMH+nNSozmTOjsNDptSw5+TnbQn41lnUs04su5fsX+L9b1mY2dC7fj5YlOvLD+UUE3diCgsKdxJvMOjyC8kWr0avSEMoWkUkPRd4JTwzlu9OzOBS201hmpjLj9VLdeLvshwWq50pBJkm6EIWAoiicj45i+60QgkJvcCM+Lst65ZyK0szTm0AvH0rYSzdQIR7n0r0zfHt6pnH7k2oTXqjlu6JzdWY1Xs0Xhz7lasx5knWJfHFoKO+V/5i3ynxQ4JONvKakppH2wx/oT140lmla1MOsdUN5LQuRFG0Ss4+M4vCdXQCoUPFB5eG083vXxJHlLGdrVwZUn0Q7v86sPDOPU3cPAXA+6jgjd/WgkVdrulboj6uNp4kjFYVZmi6VX6+sZsOlZaTqko3lVVxq0afKKIrbywSceUmSdCEKKL2icDrqLkH3E/PbiQlZ1qtc1IXA+5O/edo+eyugEC+b2JR7zDw83Dj+rp1vZxp5tXrh4xaz8WBag2UsPD6J3aF/A/DD+cVci73EgGoTpXUim5T4RFKXb0K5dstQoFZh1rElZvVkmbvCJDY1ms8PDOLivVOAYfjI4OpTaODV0sSR5R4/x/JMqreUw3f+Y9XZr4zrv++69Sf7b2/nVb/36FimF7bm9qYNVBQ6JyMO8M2pGcb3HBiWNexVaSgNvVrJzU8TkCRdiAJEq9dzPDKcoFs32Bl6g4jkpEx11Kio5lKMQC8fmnh442ZjY4JIhSiYdIqOuUfHcDcpDIDyRavRo9LgHDu+pZk1Q2tOp6RjWdaeW4SCYUmb0PjrjK49V1rKnkIfGU3aNxtQIu4ZCizMMe/xBpoKfqYNTOSo8MRQJu/vb0wYbMzsGFV7DlVcapk2sDygUqmo5d6E6q71+fv6L6y/8DWxqdGk6VPZdHkl20J+pXO5j2hRooOsFCFeWFRyBCvOzGX3rb+MZWrUtPXrxLvlPpIbQiYkn24hTCBVn8avl/dwOTKU0rGevFG6ARaPmfwmTa/jcPgdgkJD2Bl6k+jUlEx1NCoVAcXcCfTypomHN0WtZMIkIZ7H+gtfG5eYcbJ0ZnjADMxzeGIqlUpFxzLv42NfinlHx5KkTeBa7EWG/9eNEbVmUsm5Zo6er7DQX79N6rKNEJ9oKLC3NSyxVtzdtIGJHHUt5iKT9/fnXophBZIili6Mq7sQX8eyJo4sb5mpzWnr24kmxdvy88VlbAleh1afRmzqPb4+NZ0/gn+kR6XB1HSVIR7i2en0WrYGr2fdhaUkaR/0xCxXxJ++/qPxdSxnwugEgEpRlMyLIxdisbGxODo6EhMTg4ND/h6Tq9frCQ8Px9XVFbWsT11oLD39B2su3SJNsTWWmasS6FrGi48qtwMgWaflwJ3bBIXeYPftW8SlpWY6joVaTR1XDwK9fGjo4YWjhaxRmd/IZ7hgORT2H9MODgZArdIwqd5SKrs8OWF+0Wt8I+4q0w8O4XbCDQA0KjM+rDKC1iXfeuZjFWa605dIW/07pN1fYs3NGfPeb6Eu6pjr55bPcd45ffcw0w8OJVEbD4CnbQkm1FuU6z1MCsI1vpNwizXnFhiHyqSr6lKHHpWGvHQ3MZ5FQbi+eelc5HG+OTWda7GXjGX2Fk50rziQZt6vo1YVvNeooFzjZ8lDJUnPxwrKG05k39LTf7Di4v1umjx859vwMWzqYYuZ2oU9YaEk6bSZ9rfSaKjn5kmglw8N3L2wMy94S8+8TOQzXHDcTrjB8P+6GtdD71FxMO1LP3099Jy4xvGpscw+MsrYgg/QuuTbfFB5WIFcXiqnafccQ/vLv3D/54qqlDcWvTqgssmbHkPyOc4be0P/Zd7RMca5IMoWqcyY2l/hYFkk189dkK7x+agTrDgz1zhWHwwT6jXzeZ33yn9MUatiJowufypI1zc3xaTc4/uzX7H9xm/GMhUqWpToQJcK/XGwcDJdcC+ooFzjZ8lDpbu7EHkkVZ/Gmku3ABsyJujc31bYcTsRCMnwiI2ZGY08ihPo6U09N0+szORjK0ROStEmMfPQcGOCXs+jOW+U6pZn57ezcGBcnfmsOvsVv19dC8Cf1zYQEneFEQGzcMyDJCU/UvQK2j92ogs6aCxTVy+Peee2qOR7sFDZGrye707NRLl/w7qma0OGBcyQyRSzUL5oVb5ouJK9of+w+twC7iTeQkFhW8iv7L71Fx1K9+SNUt3ktRNGekXPP9d/Yc25hcSnxRrL/RzL09d/tCzxl0/JXzkh8shvV/dl6OKe2YPE3cHCgsYexQn09KG2qzsWGlnzV4jcoCgKX5/6gmuxhqW8vOxK0r/ahDwf46lRm/F+5U8p6VCWJSenotWncTbyKMP/68ro2vNeuq6sSpqWtHVb0R8/byzTNKuDWdvGqNQy/rawUBSFtecXsfHScmNZM+/X6Fd1rPQieQKVSkUDr5bUdm/KH8E/suHidyRq40nRJfPjhaX8fX0jXcp/QlPvVwtk12WRc65En+Xrk19wKfq0sczGzI4uFT6hVcm30Kjk92V+JUm6EHkkNOHe0ysBzTysmFKnA2b5uLuOEIXF39c3EnTjdwCsNNaMrDUbG3PTLVXYzOc1vOxKMOPQMO6l3CUi6Tajd/dkYPXJ1Pd8xWRx5SUlIYnUFZtQrt40FKhUmL35CmYNqps2MJGjtPo0lpz4PEPX27fKfMB75T+WidCyyVxjQfvS3Wnm/RrrL37Dn9d+Rq/oiEqOYMHxiWy5uo6elYbgX6y2qUMVeSw+NZa15xfx17WfjT1UAJoWb0ePioNxsnI2YXQiOyQLECKPeNpmr8tqzWLFJEEXIg9cvHea707PMm5/Um083vamX8qrXFF/ZjVeQ2mnSgCk6JKZdXgEP5xfgl7Rmzi63KWPiiF1wdoHCbqFOebvd5AEvZBJ1ibxxcFPjQm6ChW9q4ygS4VPJEF/Dg6WRehdZSTzAzdQy72JsTw49gIT9n3EtAODuRkXbMIIRV5RFIWgG1vov/1N/ry2wZige9v7MaX+twyqMUUS9AJCMgEh8sjrfvUwVyUAj5urUcFMFc/rfvXyMiwhXkqxKfeYdXiEcZKqV/0609CrlYmjesDZ2pXPG3xH0+LtjGUbLn7LzEPDMyyXU5job4SR+tUalPAoQ4GdDRYfv4umUmnTBiZyVGzKPSbs68uR8N2AYamxYQEzaOv7rokjK/i87EryWe15TK7/NX6O5Y3lh+78x6Ad7/DNyS+ITclerz5R8FyPvczYvb2Zf2w8MamG71ErjTXdKw5ibpN1T12tROQvkqQLkUcs1Oa84+f2mEcNiXsxs71EJ0fkXVBCvIR0io65Rz7jblIYAOWLVqNHxcGmDSoLFhpLBlafTM+KQ1Df/3N9ICyIkbt6EpZw08TR5Szd2SukLloHcYYbEKpiRbAY1BW1j4eJIxM5KTwxlM/2fMDFe4bxsTZmdkyou+ilGcqRV6q41GJW4zUMqDbJONu7XtHxv2s/0W/bG2y6vIpUXYqJoxQ5JUmbyMoz8xi6szNnI48ay+t5NGdBs410KN1D5ngogCRJFyIPBcdGkXlmd9AQTxH1b6iUU0zZP4C41Ji8D06Il8SP55dy4u4BAJwsnRkeMCPf/oBRqVS8UbobY+p+hY2ZYaz8jbgrDP+vGycjDj5l74JBu+84act/gVRDrwaVrxcWA7uidnYybWAiRwXHXGTUrp7cir8GQBFLFz5v8B2VXQJMG1ghpVapaebzGouabebdch9hpTHM9p6ojef7s18xIKgju2/9xUu2EnOhoigKe0L/YcD2N/n1ymr0ig4Ad5vijKu7gBG1ZuFi7W7iKMXzkiRdiDxyPuo6+yLSE3Q9HUtY0t5DxbDKXvzepiN+9qkA3IwP5ouDQ+UutxC54FDYTn6+tAwAtUrDsIAvCsS6wjVcGzCz8Wq87EoCEJ8Ww6T9n/DH1R8L7I9sRVFI2/of2g1/g97wHNRVy2HxUSdUtrJ8VGFy6u4hxu75kHspdwFDt+wvGq2k5Eu2aoEpWJlZ06lcHxY138wrPu1R3W8oCE8MZc6R0Yze3YvzUSdMHKV4VqHxIUze/wmzD48kMjkcAHO1Be+W+4ivAjdQw7WBiSMUL0qSdCHyyPhDW1AwtNZVckxkWPU36eXbjI6lG+NsXYzxdRfiaFEUgLNRx/jq2PhCP0mUEHnpdnwIXx4dZ9zuXmEglZwLzhg9L7sSzGi0ipquDQFD99XvTs9k8YkppOlSTRzds1G0OtLW/oHu3/3GMk3TWph3ex2VuSw8U5jsCf2Hyfv7k6iNB6BskSpMa7gcVxtPE0f2cilqVYxPqo1nTpN1VHWpYyy/cO8ko3f3YvbhkdxJuGXCCEV2pOiS+eH8EgbteJvjEQ++P2u6NuSrwA10KtcHC42lCSMUOUWSdCHywB/B+7me4ASAmmSm1HkzUx132+KMrTvf2CVtb+g/rDwzLy/DFKLQStEmMfPwcGOiUM/jFV4v1dXEUT07W3N7RteZR4fSPY1l/4ZsZvzevkQnR5ousGegJCWT9s0G9EfPGgpUYNahOeavB8oa6IXMH1d/ZM7hUcYJGmu6NWRSvaU4WDiZNrCXmK9jWSbUW8zYOvMpbudrLN8T+g/9g95k1ZkvSUiLM2GE4nEO39nFoKC32XDxW+NnysXanZG1ZjOmzld42HqbOEKRkyRJFyKXafU65p06YtxuVdwGL7usu9eWdqrIsICZqFUaAH6/upbfrqzJkziFKKwURWHpyelci70EGLraDqg+ocAu9aRRaehecSBDanyOhdrQYnL+3gmG/deVK9FnTRzdkyn3Ykld8AP6yyGGAjMzzHt2wKxRwenRIJ5OURTWnFvAd6dnGpeAau7zBqNrzcXKTIYymJpKpaKmW0O+bLqevv6f4WBhWCJWq09j85Xv6bftDbYG/2hMBIVphSeGMv3gUD4/MIg7iYbeDhqVGW+W7sWCwI3U9WhWYP+eicczeZK+ePFifH19sbKyombNmuzateuJ9deuXUvVqlWxsbHBw8ODXr16ERlZMFoPxMvpyxO/Eqd1AsBaHcvoGh2fWL+mWwP6+Y8xbq84M5c9t/7OzRCFKNT+ur6RHTe3AIblaEbWmo21ma2Jo3pxjYu34fOGy3C2cgUgMvkOn+3+gF03/zRxZFnT37pDylerUcIM45KxtTYssValjGkDEzlKq09j4fGJbLy0wlj2VpkP+KTqeDRqGcqQn2jUZrQu+RZLmv/Km6V7Ya62ACAuNZpvT81k8I5OHAzbWWDnvSjo0vRpbLy0ggFBb3EwbIexvLJLAPOa/ki3igPkplchZtIkff369QwePJgxY8Zw7NgxGjVqRJs2bQgJCcmy/u7du+nevTsffPABZ86cYcOGDRw6dIgPP/wwjyMXInsik2PZdO3BTaS+FctjaWbx1P1eKdGeTmX7GLe/PDaOM5FHnrCHECIrF++dZtnpWcbt/tUm4G3vZ8KIclZpp4rMaryGckX8AUjVpzD36GesPrsA3f2ZfvMD3flgUhf+ALH3l1hzcTLM4F5SxiUXJsnaJKYfHMr2G78DoEJFnyqj6FLhE2npy8dszO3oVnEAC5v9QmOvNsbyW/HXmH5wCOP39eVqzHkTRvjyORlxkCE7OrHm3AJSdcmAYUWEITU+Z3K9rwvV3zGRNZMm6XPnzuWDDz7gww8/pEKFCnz55Zd4e3uzZMmSLOvv37+fkiVLMnDgQHx9fWnYsCF9+/bl8OHDeRy5ENkz/sBPaBUbANyt7tG5bGC29+1Uri/Nfd4ADC0T0w8OJST2Sq7EKURhFJNyj1mHRxi7bL7m9x4NvFqaOKqcV8TKhSn1vzF+XwD8cnkF0w8OyRdjS7UHTpL23c+Qcn+JtRKehgS9WBETRyZyUmzKPcbv7cvR8D0AmKnNGR4wkza+75g4MpFdrjaeDKn5OTMafU/5otWM5afvHmbYzi7MPzaByKRw0wX4EohKjmDukc+YsO8j43KFatS08+3MgmYbaVy8jdzwekmYrN9RamoqR44cYdSoURnKW7Zsyd69e7Pcp379+owZM4atW7fSpk0bwsPD+fnnn2nXrt1jz5OSkkJKyoOlrGJjYwHQ6/Xo9fl75my9Xo+iKPk+TpG1k3cvczgyfe1lHWNqNM9wLbNzfftUHk1UUgTHIvaSkBbHlP39mdZgBc7WrrkcvcgJ8hk2HZ2iY+6R0dxNCgOgQtFqdC0/MMevRX65xhqVGf2qjKWkfRlWnJ2HXtFx5M5uRu7qwaiAOXjalcjzmBRFQff3XvT/7DOWqSqXwey9tigW5igF5HORX65xfnYn8RZTDwwgNMHQE9LGzI5RteZQyblmgXjd5BpnVNqxIlPrfcv+sO2sObeAsMSbKCgE3fidPbf+5o1S3XmjVDeszWxMHWq2FITrq9Nr+d+1Dfx4cSlJ2gRjeVmnKvSpMgpfx3IA+fo5mIqi1xF1+yjRYcFo0nwp6lEDlVpj6rCy9CzXz2RJ+t27d9HpdLi5uWUod3NzIywsLMt96tevz9q1a+nUqRPJyclotVpef/11FixY8NjzTJ8+nUmTJmUqj4iIIDk5+cWeRC7T6/XExMSgKApqtcmnDxDPaOLhPwFnACrZxeNDEcLDH9yBzu717e7zKZEJ4YQkXuZu8h0m7f2ETyvOwlpTMP44vszkM2w6v95Yxcm7BwFwMC9CD59hRN2NyvHz5LdrHGDbDLuyRfn28jQSdfHcir/GiF09+LDUSCo65eHkbDodNn/tx+L0g94/KTXLkxQYANH38i6OHJDfrnF+czPxKgsujCM2zXBdHc2dGVBuMsV03hn+5uVnco2zVkpThc8qLmLnnd/ZGvojSbp4UvUpbLj0LX9f28hrxbtRz+UV42S3+VV+v75X4s7y4/XF3Ey8aiyzNXOgg3cv6rm0QJ2iLjCfpbwWE7qH26e+QZtsmOvkJmBm5YJHlT44eua/teLj4rLfu02lmGg2iNDQULy8vNi7dy/16tUzln/++eesXr2a8+czj305e/Ysr7zyCkOGDKFVq1bcvn2b4cOHU6tWLZYtW5blebJqSff29ubevXs4ODjk/BPLQXq9noiICIoVK5Yvv1TE4/1yZRezTt0EQEMSm1p2oJhNxq6dz3J9o1MiGb3nfcLvz+pZxaU2Y2p/hbna/In7CdOSz7BpHArbyReHPwVArdIwqe5SKjpXz5Vz5ddrHJZwky8OD+VGnOFHnxo13SoM5DW/LrneVVJJTkG76jeUS9cNBSrQvNYUTeOAXD1vbsmv1zg/OHX3MDMPf0ri/ZY/L7uSjK29AFcbDxNH9mzkGj9dXGo0Gy4t489rP2WY76KEfRl6VBxM1WJ1nrC3aeXX6xubGs3qc/PZfuO3DOWv+HSga/lPsJelCp8oPHg7p/8d+djHK78yA1ffZnkY0dPFxsZSpEgRYmJinpqHmqwl3cXFBY1Gk6nVPDw8PFPrerrp06fToEEDhg8fDoC/vz+2trY0atSIqVOn4uGR+Y+CpaUllpaWmcrVanW++qA+jkqlKjCxCoNUfRqLzp4BHAF4rYQjbnbOWdbN7vUtal2M8XUXMHr3+8SlRnPq7kGWnJzCoOpTZGxSPief4bx1Oz6E+ccnGLe7VxxE5WK524KcH6+xp70PMxqt4suj4zgYtgM9elad+5LrcZfoV3UsFprMfxdzghIdR9q3P6PcjjAUmGkw7/IqmqrlcuV8eSU/XmNT23Prb748Ns4450O5Iv58VufLArsGulzjJ3O0KsqHVYbTxvcdVp+dz4GwIACux11i8oFPqOnakB6VBufbCc3y0/XVK3r+vb6J1ecWEp8WYyz3cyxPnyqjKFfU34TRFQyKXselfXOeWOfSvrm4+Qbmq67vz/L+M9k71cLCgpo1a/LPP/9kKP/nn3+oX79+lvskJiZmenIajeGFl+UhRH4x++gmEnWGBN1WE8On1dvnyHG97EryWe15xnWRd97cytrzC3Pk2EIUBinaJGYeHk6iNh6Aeh6v8LpfFxNHZTrWZraMrDWbt8v2NpbtuPkHY/f0Jio5IsfPpw+NMCyxlp6g21hh0a9TgU/QRWZbrq5jzpHRxgQ9wK0Rk+otKbAJusg+L7sSjKo9hyn1v6WUYwVj+ZHw3Qze0YmvT04nOiXnhxYVFleizzFqV0+WnPzcmKDbmNnRu8oIZjZeLQl6Nt0LO0ZKwpOHAKQk3OFe2LE8iijnmfR20tChQ/nuu+9Yvnw5586dY8iQIYSEhPDRRx8BMHr0aLp3726s/9prr/HLL7+wZMkSrl69yp49exg4cCC1a9fG01OWcRGmdycxii0hscbtgZX9scjBLunli1ZlaM1pqDC0nm+8tII/gzfk2PGFKKgURWHpyWlci70EGG5qDag+4aXvaaJWqXmvfD+GBczAUmMFwKXo0wzb2YWL907l2Hl0F6+TunAtxBhukKic7y+x5ls8x84hTE9RFFafXcCy07NQMDSOvOLTnlG15mAp6zW/VCq71GRm49UMqj4FZytDD1i9ouPPaxv4eNsbbLy0glRdylOO8vJISIvjm5NfMOK/blyKPm0sb1K8LQub/UJb33fR5POx/flJauLdHK2XH5msuztAp06diIyMZPLkydy+fZvKlSuzdetWSpQwzEJ7+/btDGum9+zZk7i4OBYuXMinn36Kk5MTzZo1Y8aMGaZ6CkJkMO7ARnQYJnQrbhNN+1I534pXxyOQD6uM4NtThvf9t6dmUMSqGHU8mub4uYQoKP669jM7bv4BgJXGhpG1ZmNtZmviqPKPBp4t8LT1YfrBIUQkhXEv5S5j9/SmX9WxBHq/+kLH1h06Tdr6P+H+rLUqb3csPuyIyl5e/8JEq09j0fEp7Li5xVj2dtkP6Vyu30t/M+xlpVapaerdjnoezfjt6lp+ubSSZF0iSdoE1pxbwF/XNtCtwkAaerV6ad8jiqKw8+ZWVp6ZR0zqgx4G3vZ+9KkyisouBXOuDlOzsHHJ0Xr5kckmjjOV2NhYHB0dszVg39T0ej3h4eG4urrmizE04skO3TlP/z0HAQ0qtHzXpBGVnR8/NutFr+/3Z+ez6fJKACw0Vkyut1S6SeUz8hnOGxfvnWLM7g/QKloAhgXMoIFnizw5d0G7xjEp95h5eDhnI48ay17360r3igPRqJ/tvr2iKOj+3Yf2f7uNZepKpTDv+hoqS4sci9nUCto1zg3J2iRmHR5hXANdhYreVUYWmjXQ5RrnjHvJd/nxwlL+vb4ZPQ+WmirjVJlelYZSwbmaSeIy1fUNib3CN6e+4EzkEWOZpcaKTmX78GqpLjL57wtQ9Dp2//DqE7u8W9q60fC93/PVmPRnyUPlm0iIHDLlyL+A4Yuglov2iQl6TuhaoT+NvdoAkKpL5vODg7kVfz1XzylEfhOTco+Zh0YYE/TX/LrkWYJeEDlaFmFivSW0KvGWsey3q2uYemAg8amxT9gzI0WnQ/vTXxkSdE2D6pj36lCoEnRh+IyN29vHmKCbqy0YHjCz0CToIucUsXKhX9WxzG36I9WLPVi56VL0aT7b8z4zDw3ndsINE0aYN5K0iaw68yVDd3bOkKDX9WjGgsCNdCjTUxL0F6RSayheqdOTalCu/rB8laA/K0nShcgBay78y51kwxJrZqoEJtfO/R8vapWa/tUnUsWlFmBYHmXK/gFEJ0fm+rmFyA90io65R0YTmXwHgApFq9G94kATR5X/mavN+ajqZ/T1/wyNytB6fjxiPyN2dTMu2fYkSnIKact+QXfgpLHM7NUmmL35CipphSxU7iTcYvTuXlyOPgMYJriaUG8R9TybmzgykZ+VcCjN+HqLGF93Id72pYzl+25vY+D2jqw4PfeZbgoWFIqisC90GwO2d2Tzle/R3b957G5TnLF1FjCy1myKFbDlCfOz6NtHsiy3tHXDv8VMXP3y1/Jrz0r+mgrxgpK1KXx77pJxu2PJYhSxss+Tc5urzRlZazYl7EsDcCfxJp8fHESSNjFPzi+EKa07v5iTdw8CUMTShWEBMzCT1olsa13yLSY+NCP37YQbjNzVg0Nh/z12HyUmjtRF69CfDzYUaDSYd3sNs2Z1Xtoxp4XV1ZjzjN7di9sJhrmBnK1cmdZwGZWcc3dJQ1F4VHetz7wm6+jnPwZHi6IAaBUtv11dQ79tb/D71R9Iu79CQEF3Oz6EKfv7M/PwcOONY3O1Be+W68tXgRuo6dbAxBEWLtFhJ4i8sRcASzsPqrZZSPGaw6nebikN3/u9wCfoIEm6EC9s+pGNJOsN40oczKIZWPX1PD2/rbk9Y+suMM6uejn6LHMOj0Kn1+ZpHELkpQO3d7Dx0goA1CoNwwK+oKhVMRNHVfBUdqnJrMZrKOlQFoAkbQLTDw5h46XlmZY21YfdJWX+WpRb98cAWlti8dE7aKpXePSwooA7GXGQsXt6cy/FMDOyl11JpjdcQQmHMiaOTBQ0GrUZLUt2ZMkrv/JWmQ+My8jGp8Ww/PRsBgW9zYHbQQV2KeUUXTLrzi9h0I53OBaxz1he3bU+XwVuoFO5vlhoLE0YYeF09fBS47/9avbGuXgdnIo3pYhnzQLdxf1hkqQL8QJuxofz960k4/bQqrUwM8GXg4u1G+PrLsTGzA4wrFe69OT0AvtHT4gnuR0fwvxj443bPSoOoqJzDRNGVLC52ngyveEK6nm8AoCCwppzC5l79DNStIbvN93lEFLnr4V797uoFnEwLLFWyttUYYtcsvvWX0zZ358kbQIA5Yr4M63hcummK16ItZktXSp8wqLmm2hSvK2x/HZCCF8c+pSxe3tzJfqsCSN8dkfu7GZQ0Dv8dPFb0vSpALhYuzMiYBbj6izAw1a+H3PDvdAjRN0y9KKzdiiOR9l2Jo4od0iSLsQLGHvgF/QY1h4uaRtDmxK1TRaLj0MpRteea+zu+2/IJn66+K3J4hEiN6Rok5hxaBiJWsN63A08W/CaX84vdfiysTKzZnjADDqX62cs233rLz7b8wF3Duwi7esNkGxY81hV3A3LQV1RuzmbKlyRS36/+gNzjow2TsRYy70Jkx4aEiHEi3KxdmdwjanMarwmw83Vs5FHGfZfV746Oo67SWEmjPDpIhJv88XBT5l6YCB3Em8CoFGZ0aF0TxYEbqSeZ3MZ/pNLFEXhSoZW9D6on3FlkoJCknQhntOu0JOcizGsA6wijcm1X2yt4ZxQ2SWAgdUnG7d/vLCUbSG/mjAiIXKOoigsOfk51+MuA4YuuJ9UGy8/hnKISqXinXK9GVVrLlYaG8AwLnnEjRFcsDX8aFZX8MPik86oHOxMGarIYYqi8P3Zr1h+erax7BWfDowMmIWlmbUJIxOFVWmnikyt/y2jas3J0OK84+YffLKtA2vPLTL25sgv0vRp/HJpJQOCOnIgLMhYXsm5JvOa/kj3igOxks9LrroXeojo24YlRG2cSuBeurWJI8o9kqQL8Rz0ej3Tj/5H+keogSuUK+Jj2qDua+TVip4Vhxi3F5+Yalw6R4iC7M9rG9h5cysAVhobRtaajbWZrYmjKnzqeDRleoPluCmGiZ5iLVKYUi2IHQ1SMX//TVlirZDR6tOYf2wCmy6vMpa9U7Y3H1cdi6aQtlCJ/EGlUlHHI5CvAn/m/crDsDM3zO+Tqk/h50vL+Hhbe/6+tjFfzLFz6u4hhu54l9Xn5pOiSwbAydKZwTWmMqX+N3jb5+6yu+J+K/qhh1vR+xaa8edZkSRdiOew4vzfRKYallwzV8UzofbbJo4oo9dLdaWdb2cA9IqOWYdGcCX6nImjEuL5XYg6maGVr3/1CfKjKJcoKal4/nySKXuaUOmeKwA6tZ6vzX/hu7Oz0BaS2ZiFYT3naQcGs+PmFgBUqOjr/xmdy/eTHioiz5irzXnN7z2WNP+N1/26YnZ/acjolEiWnPycoTvf41j4XpPEFpUcwbwjYxi/ty834w2rWqhR0873XRY2+4UmxdvKZyWPRN3cR8ydEwDYFimFm98rJo4od0mSLsQzSkhNYtXFa8bt90p74WCRv1rzVCoVvSoPpZ6HYS3bZF2SYexUwi0TRybEs4tOiWLW4ZHGcbKv+3WlgWcLE0dVOCmx8YYl1s5exV5ryejTzWhr29L4+Nbg9Uza/wmxqdGmC1LkiJiUe4zf29c4I7W52oLhATNpXfItE0cmXlZ2Fg70qjyU+c02Gn+/AITEXWby/v5M3vcJ12Mv50ksOr2WLVfXMWB7R/679T9jedkilZnVZA0fVhmBrXneLLcrshiLHtCnULeigyTpQjyzKYc3kKI3fDE7mUfzUaW2T9nDNDQqDYNrTKVC0WqA4Y70lAMD5Me1KFB0ei1zj4w2rjtbsWh1ulUcYOKoCif9nUhS569FuWl4rbGyxKpPJ3o3/4JPqo03tm6dvnuYEf9143rsJRNGK15EWMJNRu/uyeXoM4BhKc+J9RZTz7P5U/YUIvd52HozotYsPm+wjDJOlY3lxyL2MXTHuyw5MZXo5MhcO//5qBMM/68by07PMk5SamfuyMdVxzG94Ur8HMvn2rlF1u6G7CI23PB9ZVe0DK6+BX8d9KeRJF2IZxAcE8qOMN39LYVR1eujVuffj5GFxpLRtefhZVcSgFvx15h+YIhxPJUQ+d0P55dw6u4hAIpYuvBpwBfGFQxEztFfvWlI0KNiDAVO9lgMeA9NmRIAvOLTnikNvsXJ0jCj+53EW4za1ZP9t7ebKmTxnK7GnGf07l7cTrgBgLOVK583WCbLGIp8p6Jzdb5otJKhNaZRzNodAD16/r7+C/22vcGGi9/l6O+Z2JR7LDo+mdG7exEce8FY/opPBxY1+4UWJTqgVuXf33yFlaIoGddFr/URqpfgOhT+ZyhEDhp78FcUDJMmlbGPI7B4dRNH9HT2Fo6Mr7uQIpYuAJy/d4J5R8agU3RP2VMI0zpwewe/XF4BGJa3GRYwg6JWxUwcVeGjO36e1KXrIcnwY1fl5WpYYs0j42tdvmhVZjVeTSnHCoBhGM2MQ8NYf+Eb9Io+z+MWz+5ExAHG7ulNdIqhFbK4nS9fNFpJCYfSJo5MiKypVWoaFW/Ngma/0LXCAONkocm6RH44v5hPtnVgx40/Xug7SK8YEv9Ptr/JvyGbjeW+DuWY3nAFn1Qbh4NlkRd9KuI5RVzbQdxdw00Te5fyFCvRxMQR5Q1J0oXIpm03j3I5ztDNXU0KU+u0N21Az8DVxpOxdecbl1U6EBbE8tOzURTFxJEJkbXQ+BDmHxtv3O5RcRAVnfP/TbGCRFEUtEEHSfv+N9Aabtqpy/kallhzzHqspYu1O583XEZjrzbGsh8vLGX24ZEkaRPzJG7xfHbd+oup+wcYl7UqX6Qq0xoux+V+C6UQ+ZmlxoqOZXqxuPmvtC75Nur7KUxk8h2+OjaOkbu6cybyyDMf19CzpCdLTkwlPs3Qk8jGzI4PKg9nVuPVlC9aNUefh3g2iqLn6uGvjdulAj56aSbqkyRdiGzQ6/XMPLYPMHwxBHqYU9LBw7RBPSM/x/KMrDULzf1xpVuD17P5yvcmjkqIzJK1Scw8NMw4FrChZ0te9XvPxFEVLopej3bTNrS/7zCWaWpXwfzDN1FZWT5xX0uNFYNrTKV7hYGo7n8n7ru9jc929yI8MTQXoxbP6/cra5l7ZLRx8sVa7k2YWH8J9haOJo5MiGfjZFmUvv6j+TLwJ2q6NjSWX44+y9g9vfni4KeExocYy3WKjtN3D3Mocgen7x429iJMSIvju1MzGb6zKxfvnTbWb+zVhgXNNvKqX2dZgjAfCL+6jfgow/wnDq6VcfZp+JQ9Cg959wmRDUtObyE6zQkAS3UcY2v1NGk8z6uaaz0+rjqOBccnAPD92a9wtnKlcfE2T9lTiLyhKApLTkzlepxhBt/idr58XG38S3PnPC8oqWmkrfkd/ekHsySbtWqApmX9bL/OKpWKDmV64u1QinlHxpCojeda7CWG/9eV4QGzqOxSM7fCF89Ar+hZfXZ+hhuyLUu8SZ8qoyQBEQWat70fY+vO53j4flaemWv8m3EgLIjDd3bRxvdtfB3K88P5RUQmhxt2umKYg6GeR3N2h/5tHPYBhr81ffxHUcWllimejsiCotdx9cg3xu2XqRUdpCVdiKeKTU1g3ZUw43bPsr7YmFmZMKIX08znNd4r/7Fxe8GxCcaJuYQwtf9d+8m43I2VxoaRtWZjbWZj4qgKDyUugdTFPz5I0NVqzDu3xaxVg+f68RPg1ogZjVbhaWuYYC42NZqJ+/rxZ/CGnAxbPAetPo35x8ZnSNA7le3DR/5jJEEXhUY117rMabqOT6qON869o1MMy6ctOD7hQYJ+X2RyOFuC1xkTdEuNFd0qDGRu0x8lQc9n7lz9h4R7VwFwdK9K0eJ1TRxR3pIkXYinmHhwA2mKYaISF4t79Cxf8NdnfqvMB7Qs8SYAWkXLFwc/leWUhMldiDrJitNzjNsDqk+kuL2vCSMqXPThUYYZ3ENuGwosLTDv/RaaWpWfvONTFLf3ZWbj76lerB5g+IH89anpLDnxOWn6tBcNWzyHJG0i0w4MZufNrQCoUfOR/2e8W/7laokSLweNSsMrJdqzqPlm3inbG3P1k4fspKvt1pQFgRt5s0xPzGXVkHxFr9dy9fDL24oOkqQL8UQX7oWw13gTVs+Ymk3z9ZJr2aVSqehTZRQBbo0ASNTGM2X/AO4mhT1lTyFyR3RKFLMOjzCOmX2jVDfqe75i4qgKD33wLVIXrEWJjDYUONoZllgrVzJHjm9rbs+YuvN5o1Q3Y9nf1zcyce9HRKdE5cg5RPZEp0Qxfm8fjkXsA8BcbcGIWrNoVfItE0cmRO6yNrOhc/l+DKo+OVv1Xyv1HsVsCtb8Qi+LsMt/khhzHYAinjUp6lXbxBHlvYKfbQiRi8Yf3IKC4e5qJcdE6nu8WItTfqJRm/FpzS8o42R4TpHJ4UzeP4CEtDgTRyZeNjq9ljlHRhm7JVZ0rkG3CgNMHFXhoTt5kdQl6yEhCQCVRzEsB3VD7emao+fRqDT0rDSEQdUnY642LFV5NuoYI/7rSnDMhafsLXJCWMJNPtvdi8vRZwHDzZOJ9ZZQxyPQxJEJkXf02Vxi9l7y3VyORDwPvS6N4CPfGrf9an5kwmhMR5J0IR7jj+D9XEswzHyrJpkpdd40cUQ5z8rMms/qfIm7TXEAbsRd4YuDn5KmSzVxZOJlsvb8Yk7fPQxAEUsXhtX8QsbM5hDtf4dJW7UZtIYeCuoyJbDo/x4qp6yXWMsJTb1fZWqD74xr2kckhTFqdy/23Po7184p4Er0OUbt6snthBsAOFu5Ma3Bclm6ULx0ili55Gg9kbduX9pKUuxNAIp61aGIZw0TR2QakqQLkQWtXse8Uw/W22xV3Bovu2ImjCj3OFkWZXzdhThYOAFwOvIw849PQK/oTRuYeCkcuB3EpssrAdCozBgWMEN+OOUARa+Qtnkb2s3bQTGUqQMqYd77LVTW2Ruv+SLKFqnMrMZrKFvE0FMnVZfM7COjWHtukXy35IITEQcYu6c3MamGoQXe9n580WgFPg6lTByZEHmvgnN1nK1cSV82NzMVLlZuVJAbWPlOplb0gJezFR0kSRciS/NP/Eqc1gkAa3Uso2sU7rF8HnY+jKnzFRYaw6z1u2/9xeqz800clSjsQuNDmH9sgnG7R6XB0uqXA5TUNNK+/xXdfw9uNGpa1se8c1tUZpo8i6OoVTGm1P+WQO/XjGU/X1rGF4c+JTEtPs/iKOz+u/k/pu4fQLIuEYAKRavxeYNluFi7mzgyIUxDo9LwQeXh97ceTdQN2+9XHoZGlXffhyJ7Qi/8RnK8YXJTZ+8GOLn7mzgi05EkXYhHRCbHsvHag7Uz+1Qoh6WZhQkjyhtli1RhWM3pqO9/LWy+8j1/XP3RxFGJwipZm8SMQ8NI1BqStYaeLXnVt7OJoyr4lPhEUpf+hP7kRUOBWoXZO60xb93QJDPjWmgsGVBtIr0qDTV+txwK28mo3Q+6ZYvn9+uVNcw7OsY44WJt96ZMqLcYewtHE0cmhGnV82zOiICZOFtl7AXpYuXKiICZ1PNsbqLIxOPodakEH11m3PYL6GvCaExPBv0J8YgJBzagVQzrMrtbRfNeuS4mjijv1HJvQh//USw9OQ2AZadnUdSqmPwxEzlKURSWnJhKSJxhrW5vez8+rjb+pVteJafp794j7dufUSLuGQoszTHv/gaaCn4mjUulUvF6qa742Jdi9pFRJKTFcSPuKiP+68awgBlULVbHpPEVRHpFz/dnv+LXK6uNZS1LvEmfKqNkPgch7qvn2ZzaHk05E3GE6xFXKVHMj0rFakoLej5169xmUhLuAOBSojGOrpVMHJFpSUu6EA85dfcKhyLTf+DoGFfz5VsCqlXJt3irzAcAKCh8eXQs5yKPmzYoUaj879p6/rv1PwCsNDaMCJiFtZmNiaMq2PTXbxvWQE9P0O1tsfjkPZMn6A+r5lqPmY1WU9zOF4D4tFgm7+/P71d/QFEUE0dXcKTp0/jq6LgMCfq75T7iI/8xkqAL8QiNSkNllwBqOTelskuAJOj5lE6bTPAxaUV/mCTpQjxkwqE/Se9gUrVIMgFu5UwbkIm8V/5jmhZ/FYBUfQrTDg7mZlywiaMShcH5qBOsOD3XuD2g+kSK2/uaMKKCT3f6EqmL10G8YUyyys0Zy0FdURd3M3FkmXna+TCj0SoC3BoBhqWSlp+ezcLjk2RViWxI0iYw7cAg400uNWr6+Y+hU7k+0hNFCFFg3Tr7C6mJhiXxivk2w8GlvIkjMj1J0oW4b+PlXdxKcgJAQxJT6hTuyeKeRKVS8XG1cVQtVhd40OIla4qKFxGdEsXswyON42ffKNWN+p4vX2+VnKTdfZS0FZsh7f4Sa6W8sRjQBVXR/Dsm2cbcjlG159KxTC9j2fYbvzFubx/5jnmC6ORIxu3pw/GI/QBYqC0ZUWs2LUt2NHFkQgjx/HRpSVw7vsK4XapmHxNGk39Iki4EkKpPY+GZU8bt10o44mZT1IQRmZ652pwRATPxdTD0JohIus3UAwNJ0iaYODJREOn0WuYcGUVkcjgAlZxr0q3CABNHVXApeoW033eg/eVfuN9VXF2jAuZ930ZlY2Xa4LJBo9LQtcIAPq053biqxIV7Jxn+X1cuR581cXT5z+2EG4ze3YsrMecAsDN3YGK9JdTxaGrawIQQ4gXdOLOB1CTD8pFufi2wcy5j4ojyB0nShQBmH91Eos7Q8mSrieHT6u1NG1A+YWNux9i68yl2fymfqzHnmXVoBFp9mokjEwXN2vOLOH33MABFLF34tOZ0GT/7nJQ0LWlrfkcXdNBYpmleF/P3XkVlVrBe04ZerZj20HJhkcnhjNn9ATtvbjVxZPnHleizjN7Vi7DEmwA4W7nxeYNlVHCuZtrAhBDiBWlTE7h+YuX9LRW+AdKKnk6SdPHSu5MYxZaQWON2/8pVsFCbmzCi/KWoVTHG1V2InbkDAMci9rHkxFSZ6Elk2/7b29l0eRUAGpUZwwNmUsTKxcRRFUxKQhKpX/+E/vh5Q4FKhdlbLTFv1xiVumCOSS7lVIFZjVZTvmg1wDAPxpdHx/L92a/QKTrTBmdix8P3M3ZPH2JSDa1M3val+KLRSnwcSpk4MiGEeHE3zqwnLTkGAPfSrbErkn8mOzU1SdLFS2/cgY3osAaguE00b5ZqZOKI8h9vez9G156HudqwXvz2G7+z7sJSE0clCoJb8deZf2yCcbtHpcHSAvic9JHRpC5Yi3LV0KKKhTnmH7yJWf1qJo0rJzhZOTO5/te0KNHBWLbp8iqmHRhMQlqcCSMznZ03tzL1wECSdYYJASsUrXa/10H+mxBQCCGelTY1nusn7q9SoVLjW7O3aQPKZyRJFy+1w3cucOKe5f0tLRMDWps0nvysonN1BteYigpDa92Gi9/y9/VfTByVyM+StUnMPDTcOI9BQ69WvOrb2cRRFUz6G/eXWAs3tKhiZ4PFJ++iqVh4WlTN1eb08x9L7yojUd9fJulo+B5G7urBrfhrpg0uj/16eTVfHh2L7v4ki3XcA5lQbzF2Fg4mjkwIIXJGyKkf0KYYerJ6lGmLrVMJE0eUv0iSLl5qU478Axh+DNZ20VLFpfD84M0N9T1foVflT43bX5+czuE7u0wYkcivFEVhyYmphMRdBgy9MT6uOk6WiXoOurNXSF30I8QZbnaoXItiMagram8PE0eW81QqFW19OzGx3mLsLZwAuBV/jRH/defInT2mDS4P6BU9K07PZeXZecayliU6MrzWTCw1+X9CQCGEyI60lFhCTq4FQKXSSCt6FiRJFy+tHy5sIyy5CABmqkQm1n7bxBEVDK/5vccbpboBhjWOZx8eyaV7Z0wclchvtgavN67lbG1my8has7E2szFxVAWPdt9x0pb9AqmGyRpVvsWxGNAFtbOTaQPLZVVcajGr0WpK2JcGIFEbz7QDg9h0eVWhnQ8jTZ/GV0fH8dvVNcayzuX68ZH/Z2ju9ywQQojCIOTkWrSp8QB4lHsNG4fiJo4o/5EkXbyUkrUpfHPuonG7Y0kXnK2kG2F2da84iIaeLYH/s3ff4VGVaR/Hv2dm0nsvpNFbIIEkVJFm2bV3LIC9vmtfFF1RFHtbe91VFLCsde0rq4AUKQklhE4SUiAhhfQ+c877x8AgC2ISkjwzyf25Lq7rPCdnZn5wSDLPPOWGJlsjj665jaK6AsWphLPYfmAT72553tG+NXkuvXwT1AVyQYZu0PLtL1g/+fFwibXkgbjfdAmaj5fidF0jwqcXT0yYz+jIyQDo6Ly/9UVe3DCHJluj4nQdq8Fax2Nrbnd8sGXCxC1Jc7hk4PUy+0QI0a00N1aSv/lDADSThd4jr1WcyDlJJ130SE9kfEaDbu+U+1kquS3pHMWJXItJM3HbiEcYGpICQHVzBfNW/4WqpgrFyYRqlY3lPJN+j2Mt7Xl9ZzI2eqriVK7FsFpp+eAbbD+tdpwzTx6F2/Rz0Nxcq8TaifKyeHNP2jNMG3C4LM+ywu94YOV1lDeUKEzWcSoby3lg5fVsKrXfb3eTB/eOeu6ITfSEEKK7yN+0AFuLfflW9KBz8fKLVpzIOUknXfQ4hbUl/Li3wdG+a3gqFpNMJWwrN7M7s9OeI9bPvo6/qK6Ax9feTpO14Q8eKborm27luYz7ONBYCkBiSCrTB/9FcSrXYjQ00vLmJ+jrt9lPaBqWC07B7exJLlti7USZNBOXDrqJe1KfwdNsn0Wwu3Irs36Zzo4DmYrTnZii2nxmr7ianCp7ST1fN38eHvcGoyInKk4mhBAdr7nhAAVZHwOgmdzoPUJG0X+PdNJFjzNnzRfo2DfgSfCp4oyE0YoTuS5fd38eHPMywZ5hAOysyOK59fdj062KkwkVFm1/lazydACCPcO4O+UJzKaeNfJ7IoyKappfWoSefXDpiJsFt6vPw3LSSLXBnMTY6Kk8cdK7hHvbR10qmsp4YNX1/Jz/leJk7bO7ciuzV1zN/np7Sb1Qr0geP+kdBgUnKU4mhBCdI2/j+9gODubEDLkAT18pKfl7pJMuepSV+zaztcq+eZVGC4+MOktxItcX6hXJnNEv42XxAWBd8TLe3vx0t93cSRzbr/t+4ovd7wFg1izMSn2aQM8Qxalch753P00vLsDYX24/4euN+y2XYk7srzaYk0kIGMAzExaQGJIKgFVv4eWNc3kn61mX+nBwY8mvzFl5PdXN9iVCcX79eOKkd4n166M4mRBCdI6m+jIKtv4LAJPZg4TkqxUncm7SSRc9hq7rPLZ+GYf+248Lh4FBcWpDdRMJAQOYnfYsFs0+avqfvE/5fPe7ilOJrrK3dg8vb5zraF819E4ZDWwD2/Zcml/5AKoPllgLC8L9tiswxcs6vWPx9wjiobGv8ueESxznvs75gHlrbqOmuUphstZZVvgdj665nUabfTRpSPAIHhv/D0K9ZERJCNF97dkwH93aBEDMkIvw8AlTnMi5SSdd9Bjzt/9IebO95JqbVisl1zrY8LDR/GXEXEd74bZXWFrwjbpAoks0Wht4at0sGqz2DuaEXn/izN6XKk7lOqyrM2n5x6fQdLDEWkK0vcRaaJDiZM7NYnLjhuGzuXn43zAf/HBwU+lq7vllBvnV2YrT/b5/717AC+sfcGysOCZqCg+NfQ1fd6kuIoTovhrrSti77TMATBZP4pOvVJzI+UknXfQIdc0NzN+5x9G+rF80/u4+6gJ1UxNjzmD64Fsd7Vc2PsLGktXHeYRwZYZh8NqmeRTU2DtFsX59uSVpjpSMagXDMGj5fjnWf/0A+sESa8MG4H7TNDRfqSffWqclXMgj497A393+oUZxfSH3Lr+SdcXLFCc7km7ovJP1HPO3/t1x7k8JF/PX1KdwN3soTCaEEJ1vz4Z30W3NAMQOnYaHtyyH+yPSSRc9wqMZn9Kk+wEQ6FbJzUPPVJyo+7qg31X8KcE+S8FmWHk6fRa5VTsUpxKd4dvcj1i+9wcAvCw+3Jv2LJ6WnlHD+0QYVhstH36HbfGvjnPmk1Nwu/IcNHc3hclc05CQkTx78kL6BAwCoNFWzxNr7+LTnf90ir0xWvQWXlj/AF/nLHKcu3zQLdwwbDZmTSqLCCG6t8aaIvZu+wIAs5s38UkzFCdyDdJJF91ebtU+lhQd2lDI4J4RYzGZ5L9+Z9E0jeuG3cOoyEkANFjrmLf6Vkrq96kNJjrUtvKNzN9yeFTw1uS59PKNV5jINRgNTbT841P09C32ExpYzp2C23lT0eTnUruFeUfx+Ph/Mj76VAAMDBZtf5XnMmbTqLAsZH1LLY+tvs3xYZYJE7ckzeHiAdfJjBMhRI+Qu+EdDN2+pCs28TLcvWQ5V2vIOwLR7T2w9t8YuAPQz6+GqTFSzqizmTUzd418jAFBwwB7qaR5q2+ltrlacTLRESoby3k2417Hutrz+s5kbPRUxamcn1FZQ/MrH6DvzLOfsFhwm3kulompaoN1Ex4WL+5OeZIrBv0fGvYO8Mp9i7l/xTWU1hd1eZ7KxnLmrLqBTWVrAHA3ezJ71HOcGn9+l2cRQggVGqr3sm/HvwEwu/sQP/wKxYlch3TSRbf2U+F6dtfYp7mbaOLRUecqTtRzeFi8+NuoF4jyse+gX1ibyxNr76TZ1qQ4mTgRNt3Kcxn3caCxFIDEkFSmD/6L4lTOT99XYi+xVmT/d8PHC/ebp2FOGqg2WDejaRoXDbiW+0Y97ygLmVu9g7/+Mp2t5eu7LMe+2nxmr7ianKrtAPi6BfDw2NdJi5zYZRmEEEK13PX/xNBtAMQNuwI3zwDFiVyHdNJFt6XrOk9v+BUOjqhMinKjd4CUNOpK/h5BPDjmFQLcgwHYemADL254EN3QFScT7bVw2ytklacDEOwZxt0pT2A2WRSncm62nXvsJdaqagHQQgLtO7j37qU4WfeVFjmRpya8R6R3DADVzRU8tOomftzzWae/9q6KLdy34mr21xcCEOYVyeMn/VPKEgohepT6qgKKdtqr/Fjc/YgbdrniRK5FOumi23p9y7dUtgQC4GGqYU6alFxTIdInhgfGvISn2b6h2Kp9i49Yyyxcx6/7fuLL7PcBMGsWZqU+TaCn7NB6PLZ1WbS89Sk02ne11eKi7DXQw4MVJ+v+Yv368PTJC0gKHQ2A1bDyeuZjvJn5BNaD6yM72oaSVTy46gaqmysAiPPrxxMnzSfWr0+nvJ4QQjirnIy3MQz7KHp80gzcPPwUJ3It0kkX3VJ1cx0f7j68BvGqAQl4WzwVJurZ+gUO4a+pT2E6uJPx1zmL+Cp7oeJUoi321u7h5Y1zHe2rh94lI4PHYRgG1h9X0fLhd6DbZ46YEvvhfsulaH5S/rGr+LkHMGfMy5zd5/AIzg97PuHhX/+P6qaKDn2tpQXf8NiaO2i02TeqGxIyksdP+ichXuEd+jpCCOHs6ipyKd79PQBuHgHEJl6qOJHrkU666Jbmrv2EFsP+RjjEvYKrBp2mOJFIiTiJm4f/zdF+d8vzrNz7o8JEorUarPU8te6vNFjrAJjQ60+c0Xua4lTOy7DZsP7rB6w/rHCcM48fgdtV50mJNQXMJgvXJP6VW5PnYjHZ//2zytOZtXwGe6p2nvDzG4bBF7vf48UNDzo2UxwbNZWHxryKj5uMHAkhep6cjLfh4NLG+OSZWNzlw+m2kk666HZ2VOSzquRQS+dvIydKyTUncUr8eUwbcIOj/cKGOWwpz1CYSPwRwzB4bdM8CmpyAPv03VuS5kj5qN9hNDbR8o/Psa3Z7DhnOWcSlgtOkRJrik2JO4d5494iyCMUgJL6fdy34mp+3fdTu59TN3Te3fI872990XHuTwkXc3fqk7ibPU44sxBCuJraA7vZn20fhHHzDCJm6CWKE7kmeccgup0H136DgX20ZEhAPeOjhylOJH5r2sAbmRpn32XfqrfwxNq7yK/OVpxK/J5vcz9ixd7/AOBt8eWetGfwtHgpTuUcDF1H352P29Zc9N356BVVNL/6IfqOXPsFZjNuM8/BMmmUfKjhJAYFJ/HMyQvpFzgUgEZbA0+nz+Kj7W+0eUPLFlszf1//N77OWeQ4d/mgW7hh2GzMB5f2CCFET5OT8RZgAJAw4iosbt5qA7ko2ZJXdCvf5q5mT529vIOJRuaNlnq0zkbTNG4afj8HGkvZULKKupYa5q25lacmvEewZ5jqeOI3tpVvPGKTv1tHzKWXb7zCRM7DlrmTli9+gqoafAArgKaBYX9jgpcn7teej6lPrMKU4lhCvMJ5dPzbvL7pUZYVfgfAxzvfYk/1Lm4fOQ8vyx+/oaxvqeWpdX8ls2wtACbNzM1Jf+OUuPM6M7oQQji1mvKdlOTYZye5e4cQM/hCxYlcl4yki27Dqtv4++bDU6dPj/Eixlc27HFGFpMbs1Kfpm/AYADKGoqZt/ov1LfUKk4mDqloLOOZ9Hsca2zP73clY6KmKE7lHGyZO2mZ/yVU1Rz5hUMddF9v+w7u0kF3Wh5mT24fMY8rh9yB6eBboTXFS7hv+VXsr9t73MdWNJbxwMrrHR10d7Mns9Oekw66EKLHy0l/w3GckHw1ZjeZedde0kkX3cZLm/5NjTUQAC9TNfeNvEhtIHFcXhZvHhj9EhHe9lrRe6p38fS6WbR0Umkk0Xo23cpzGfdR0VQGQGJoKlcM+j/FqZyDoev2EfTjMZnQwoK6JpBoN03TOK/fTP425kW8Lb4A5NXsZtbyGWwuWweAzbCRVZbOuvKlZJWlU1iTy30rria3egcAvm4BPDL2DdIiT1b29xBCCGdQXbqN0j3LAPDwCafX4AsUJ3JtMt1ddAvljdV8tqccsE9TvGHwQDws7mpDiT8U6BnCnDEvc9+Ka6hprmRT2Rpe2/gIt414RNbwKrRg28uODf1CPMO5e+QTmE3y6wJAzyk8egT9f1XXoucUYu4X1zWhxAkZGT6ep09+nyfW3sXe2j3UNFcy99dbmBJ7NhtKVlHeeHAn0mzQ0DAOrrUM84rkwTGvEuPXW2F6IYRwDr8dRe894hrMFtk880TISLroFh5a8wlWw95Bj/Ss4PKBMi3XVfTyTeD+UX/H3WT/Yb608FsWbX9Fcaqea9W+//Lv7AUAWDQLf019ikDPEMWpnEh1K5dktPY64RR6+Sbw1IT3GBk+HgDdsPHf/C8Pd9APcnTQPSN5csJ70kHvBgzdRsW+DCoLl1KxLwNDt6mOJITLqdq/mbJ8e9lRT99IogedqziR65NOunB5m8uyWVd+aJTPxpyUU5XmEW03KDiJO1MeQ8M+ev7Zrnf5IfcTxal6nsKaXF7eMNfRvjrxLgYFJ6kL5Iz8fTv2OuE0fNz8uH/0C5zXd8YfXmtDJ8AjuAtSic5UkvMzKz44iw3f3kRhxjNs+PYmVnxwFiU5P6uOJoRLyf7tKPrIazGZZTbriZJOunB5c9N/4NDKjaSgRlIjBqoNJNplTNQUrht2j6P99uanWFO0VF2gHqbBWs9T6/5Ko60egJN7/Zk/J0xTnMr5mPrEQIDf8S8K9LNfJ1yOWTOTEjHhD6870FjCtvINXZBIdJaSnJ/JXDyLprojZ0s01ZWQuXiWdNSFaKXKog0cKFwNgJdfL6IGnK04UfcgnXTh0j7bvZzC+kAAzDQwb7RsFufKzug9jfP7XQWAjs7z6+9nx4FMtaF6AMMweG3jIxTW2ut7x/n14+akB2RfgGPQTCYsp4077jVu501FM8mvV1dV0VjWodcJ52PoNnaseua41+xY9axMfReiFbLT33Qc9065DpPZTWGa7kPeRQiX1ay38OqWzY72WXH+RHjL9ENXN33wXzi5158BaLY18tjaO9hbm6c4Vff2Te6HrNj3IwDeFl/uTXsWT4uUTfk9RlnFsb8Q6IfbVedhHj6gawOJDhXkGdqh1wnnU1G84agR9P/VVLefimKZLSHE8RzYl07FPns1DC//WCL7n6E4Ufch2/UKl/Xchi+pswUA4GOu4q8jr1ecSHQEk2biLyPmUtFUxuayddQ0VzJv9a08edK7soFZJ9havoH3trzgaN824mGifWVX8t9jNDZh+3WjvWEyYZ5xFtXlFQTERGHuFycj6N3A4JARhHiGU95YCgc3ijuSRqhnOINDRnR1NNFBmutbNwuitdcJ0RMZhkHOusNr0fuk3IBJKsF0GHk3IVzS/voDfJ1X5Wj/JXEY7iaZXtNduJncuDftWeL9+gGwv76Qx9beTqO1QXGy7qWisYxn0+/FZlgBOL/fVYyOmqw4lXOzrc6ExmYAzGmJmIcNoGVIb0zSQe82zJqZaxNnHWz975IPe/uaxL9i1sxdmkt0HHfv1s2CaO11QvREB/aupfLgbBPvwAQi+52uOFH3Iu8ohEuas+ZTbNin4/byquSCvn+80Y9wLT5ufjww5mVCPCMA2F251d6h1K2Kk3UPVr2FZzNmU9FkHylKDE3likG3KE7l3AybDesv6Y62eVKawjSiM42Nnso9qU8T4hl2xPlQz3DuSX2asdFTFSUTHSEocgTuXsdfHufhE0FQpMyWEOJYDMM4oi56n5Qb0UzywWVHkjkJwuWk79/BpgrPgy0rD6f9SWke0XlCvSKYM+Zl7l9xDfXWWjJKVvBG5hPcIpuanbCF215ha/l6AEI8w7k75UnMMk3tuPSN26GyBgDT0L6YIkLQdV1xKtFZxkZPZVTUJLaUZpBXmkN8WB+GhqXICHo3oJnMeAXE0dxw4Hev6T/mNul0CPE7ygtWUbXfvrGvT1BfIvqeojhR9yMj6cLlzMtYDNh/caaFWBkW2ldtINGp4v37cd+o57EcXM7w3/wv+GTnPxSncm2r9v2Xf2cvAMCiWZiV+jSBUvP5uAzDwLpknaNtmTxKYRrRVcyamcTQVNJCJpEYmiod9G6ioaaI6v2HNp499ge+Vfs3H/O8ED3dUaPoqTeiadKl7GjyLypcygc7fqK4MQgAi1bPw6MvVpxIdIXE0FRuG/GIo/3hjtf5Kf/fChO5roKaHF7eMNfRvjrxbgYGD1cXyEXou/Iw9tl3g9biotB6Sx10IVxV/uYPMAx7ebXeI69jxJlvEJMyi8EnP4hmcgegIOsjygt+VRlTCKdUlrec6tKtAPiGDCS8t+xl0xmkky5cRpO1mbe27XS0L0wIIcTTX2Ei0ZUm9Dqdq4bc6Wi/tulRNpSsUpjI9TRY63l63SwabfUAnNzrz/w54RLFqVyDbclax7Fl8ihZbiGEi2ppqmbfti8AMJk9iE2cRlB0CoExk4gaeDb9x97uuHbL0rk0N1YqSiqE8zEMnezfjKL3lVH0TiP/qsJlPLH+Uxp0e6fcz1LJbUnnKk4kuto5fadzZu/LANANG0+vm0V25TbFqVyDYRi8uvFhCmtzAYjz68fNsra/VfR9Jeg79gCgBQdgGtZfbSAhRLvt3foZtoOVQqIHno27V9ARX48degnBMWMAewm27csfxzCOVYpPiJ6nNHcpteU7APAPG0Jo/MmKE3Vf0kkXLqGwtoT/FB4uv3XnsBQssqFLj6NpGlcn3sXYKPvOyo22Bh5dcxv76/YqTub8vsn5gJX7FgPgbfHl3rRn8bR4KU7lGqxLD69FN09Mk1JrQrgo3dZMftaHB1saccOnH3WNppkYOmkubh4BAJTk/ETRrm+7MKUQzskwdLIzfrsW/Sb5oL8TyTsN4RLmrPkCHfuO7gk+VZzZe4ziREIVs2bm9pHzGBScDEBlUznz1txKdXOl0lzObGv5et7b+qKjfduIR4j2jVOYyHUYlTXo6w/O1vD2xDwqUW0gIUS7Fe/6geb6cgDCe0/BOyD2mNd5+IQx6OT7He0dK56moWZfl2QUwlmV5PyXugPZAASEDyMkdpziRN2bdNKF01u5bzNbq7wB0GjhkVFnKU4kVPMwe3L/qL/TyzcBgL21e3hizZ002RrVBnNCBxpLeTZ9NjbDXl/+gn5XMzpqktpQLsS6PAMOllkzjx+B5uGuOJEQoj0MQycvc4GjHZ8047jXR/Q5hagBZwJga6ljy89zMHRbp2YUwlkZuo2c9Lcc7T5pMore2aSTLpyarus8tn4Zh/6rjguHgUEyAijAzz2AB8e8QpBHKADbKzbx94y/YTPkTdQhVr2F59JnU9FUBsCw0DQuH3Sz4lSuw2hswvbrRnvDYsZy0kileYQQ7VdesIq6ihwAAiOTCYgY9oePGThuFp6+UQBUFm8kb9P7nZpRCGdVnP0f6irte9oERiYT3Gu04kTdn3TShVObv/1Hypvtm7q4abXMHSUl18Rh4d7RPDDmJTzN9pkWa4qX8E7Ws7LJz0ELtr7M1gMbAAjxDOeulCcwmyyKU7kO2+pN0NgMgDl1KJqfj+JEQoj2+m0HO+4PRtEPsXj4MXTKPA7VUs9Of4Pqsu2dEU8Ip6XrVnIz3na0+6TdLKPoXUA66cJp1Vsbmb9zj6N9Wb9o/N3lTbI4Up+AQdyb9gxmzd75/C73Y77MltGOlfsW81XOQgAsmoVZqU8T6BGsOJXrMGw2rL9kONrmSWkK0wghTkR16VYq9tm/n70D4wlrw47UQVEjSEi+EgBDt7LlpwewWWVpleg5ind9T31VPgBB0akER6cqTtQzSCddOK156z6hSfcDINCtkpuHnqk4kXBWyeFjuSVpjqP9/tYXWV74g8JEahXU5PDKhrmO9jWJf2Vg8HB1gVyQvmE7VNYAYBraD1N4iOJEQoj2ytv0m7Xow6e3ua5zn9Sb8AsdCEBdZS6717zcofmEcFa6rYXc9b8ZRU+9SWGankU66cIp5VbtY2lRy8GWwT0jxmKSskfiOKbEnc3lg25xtF/a8CCby9Yd5xHdU4O1jqfW/ZVGm71k4cSYM/hTgiwTaQvDMLAuWetoWybLKLoQrqqhei/7c/4LgLtXMJH92/6Bv8nsxtApj2IyewBQkPUR5QW/dmhOIZxR0c5vaKi2l7kNjhlNUNQIxYl6Dun1CKf0wNp/o2P/ZdjPr4apMbJhk/hjF/W/ltPiLwDAalh5cu3d5FXvUpyq6xiGwSsbH2Fv7R4A4v36cfPwv8nasTbSd+ZhFJUCoMVFofWOUZxICNFe+Zs/AMNeoSEmcRpmi0e7nsc3qA/9Rt/qaG9ZOpfmxsqOiCiEU7KPov/D0e6bKhvPdiXppAun81PhenbX2Ke5azTz6KhzFScSrkLTNG4YNpvUiAkA1Ftrmbf6Vsoa9itO1jW+zlnEqn2LAfC2+HJP2rN4WLwUp3I9tqW/HUUfJR9yCOGiWhqr2Lv9SwBMFk9ihlx0Qs8XmziN4JgxADTXl7F9+eOyUanotvZt/5LG2mIAQuLGt6oigug40kkXTkXXdZ7e8CuHdlKdHGWhd0C02lDCpZhNFu5OeZL+gYkAlDeWMG/1rdS11ChO1rm2lq/nva0vOtq3jXiEaF8pV9hW+t4S9B17ANBCAjEN6682kBCi3Qq3fop+cJO36IHn4u4ZeELPp2kmhk6ai5tHAAAlOT9RtOvbE40phNOxWZvI3fCOo91X1qJ3OemkC6fy+pZvqWwJBMDDVMOcNFlLK9rO0+LF/aNfINLbPk05v2Y3T669mxZbs+JkneNAYynPps9GP1gj/sL+VzM6apLaUC7KuuzwPgbmialosheGEC7JZm2iIOtje0MzETf88g55Xg+fMAadfL+jvWPF0zTU7OuQ5xbCWezd/gVNdSUAhCVMxD9siOJEPY+8+xBOo7q5jg93H/5Fd+WABLwtngoTCVcW6BHMg2Newd89EICs8nRe2vgQ+sG1id2FVW/h2fR7qWgqA2B46Cgu+80GeqL1jIpq9PXb7A0fL8yjZGqfEK6qeNd3NDeUAxDeewre/h23t0REn1OIGmDfgM7WUseWnx/E0G0d9vxCqGSzNrLnN6PofVJuVJim55JOunAaD6/9hBbDF4AQ9wquHnSa4kTC1UX5xvG30S/ibrZ/2LNi739YsPUlxak61vtbX2LbgY0AhHhGcFfKE5g1s9pQLsq6PAN0+4c45nHJaO5uihMJIdrDMHTyMhc62vFJMzv8NQaOm4WnbxQAlcUbyNv0foe/hhAqFG79lOb6Qx9wTXWUHxRdSzrpwinsqMhnZcmhls7fRk6UkmuiQwwIGsZfU57AdPDH3ZfZ7/NtzkeKU3WMlXt/5OucRQBYNAv3pD1NgEeQ4lSuyWhowvbrJnvDYsZyklSUEMJVleWtoL5yDwCBUSMJCB/a4a9h8fBj6JR5HNpDJzv9DarLtnf46wjRlWwtDezZMP9gS6NP6g0q4/Ro0gsSTuHBtd9gYB+1GhxQx/homWYqOk5a5ERuGD7b0f5n1jP8uu8nhYlOXEFNDq9sfNjRvjZxFgOC5PumvWyrN0GTfc8Cc2oimp+P4kRCiPbKy1zgOI5PmtFprxMUNYKE5CsBMHQrW356ANvBjeqEcEUFW/5FS2MFABF9T8M3uJ/iRD1Xqzrp1dXVrf4jRFt9m7uaPXX2nVJNNPLo6AsUJxLd0ekJF3FR/2sBMDB4Yf0DbCvfqDZUOzVY63hq3V9ptDUAMCnmTE5POLHSQj2ZYbVh/SXd3tDAPClVbSAhRLtVlWRRWbQeAO/ABELjTurU1+uTepNjOnBdZS6717zcqa8nRGexNteRt+k9e0Mz0SflerWBerhWddIDAwMJCgo67p9D1wjRFlbdxt83Zzjap/XyIsY3XGEi0Z1dPugWJsWcBUCz3sTja++ksCZXcaq2MQyDlzc8zN7aPQAk+PfnpuH3Sy3vE6Bv3A5VtQCYhvbDFB6iOJEQor1+uzY8PmkGmta5k0ZNZjeGTnkUk9kDgIKsjygvXN2prylEZyjI+oiWxioAIvv9CZ+g3ooT9WyW1ly0ZMmSzs4heqiXNn1FjTUQAC9TNfelXKM2kOjWNE3jluQ5VDSVsal0NbUtVcxbcytPnjSfIM9Q1fFa5aucRfxa9F8AvC2+3JP6DB4WL8WpXJdhGFiXrHW0LZNGKUwjhDgR9VUFlOTa37O6e4UQ1f+MLnld36A+9Bt9KztXPQvA1iVzGX3xRydcl12IrmJtqiFvk32ZiKaZZRTdCbSqkz5x4sTOziF6oPLGaj7bUwZ4A3DD4AF4WjzUhhLdnpvJjXtSn+aBldeTW72Dkvp9PLrmNh4d/zZeFudeh7ylPIP3t77oaN8+ch5RvnEKE7k+fecejKJSALT4aLTevRQnEkK0V/7mD+Bgmc3YxEsxmd277LVjE6dRlr+cA4VraKovZfvyxxl2ylMyy0m4hPzNH2JtrgEgcsCZeAfIewvVWtVJz8zMbPUTDh8+vN1hRM8yd+0nWA17Bz3Cs4LLB16hOJHoKbzdfHlgzEvMXn4lpQ3F5FRt55l193D/6BewmJyz7NaBxlKeTZ+Nbthr8V7Y/xpGRcoHqCfKtmSd49gyKU3eUAvhopobK9m3498AmC1exAy5sEtfX9NMDJ30MKs/mUZLUxUlOT9RtOtbogec1aU5hGirlsYq8jbbSxZqJjO9R16rOJGAVnbSk5OT0TQNwzCOe52madhstg4JJrq3zWXZrCs79N/PxpyUU5TmET1PsGcYc8a8wv0rrqG2pZoNpb/y+qZH+UvyXKfrqFn1Fp5Jv5fKJnvd0qTQ0Vw26GbFqVyfvnc/+s49AGghgZiG9VcbSAjRboVbPkG3NgEQPehc3DwDujyDh08Yg06+n82L7wVgx4qnCYoaiZdfdJdnEaK18jIXYWuuAyB6wDl4+8coTiSglZ303FzX2lhJOL+56T9gEAhAUlATaRGD1AYSPVKsXx/uG/V35v56My16Mz8XfE2oV6TTdYDf3/oi2w9sBCDUK5I7Ux7HrJnVhuoGrEsPj6KbJ6WhmaQqqRCuyGZtpCDrY3tDMxE3XN3MvIg+p1A24EyKdn6LraWOLT8/SMrZb6KZ5Ge2cD7NDRUUZH0IgGaykCCj6E6jVZ30+Pj4zs4hepDPs5dTWB8IgJkG5o3u2ilpQvzWkJAR3DHyUZ5NvxcDg3/tfJsQrwhOi3eOUoAr9/7I1zkfAGDRLMxKfYoAD6mkcaKMimr0DdvtDR8vzGmJagMJIdqtaOd3h2s79zlF+cj1wHGzqNi3nsbaIiqLN5CXuYCE5KuUZhLiWPIyF2BrqQeg16Dz8fKLUpxIHNKqTvpXX33Fn//8Z9zc3Pjqq6+Oe+0555zTIcFE99Sst/BK1mbAPg3trDh/IryD1YYSPd646FO4OvFu3smy78z7ZuYTBHuGkRoxQWmugpocXtn4sKN97bB7GBA0TGGi7sO6PAN0+wZT5vEj0Nydcy8CIcTxGYZOfuZCRzs+aabCNHYWDz+GTplHxlfXAwbZ614nOGYM/qEya1A4j6b6cscMFJPZnYQRVytOJH6rVZ308847j+LiYsLDwznvvPN+9zpZky7+yHMbvqTOZu+ge5ur+OtIKfEgnMPZfS6nrL6Yr3IWohs2nk2/l3nj3qZ/0FAleepbanlq3V9ptDUAMCnmLE6Pl1knHcFoaML26yZ7w2LBMn6E2kBCiHYrzfuF+qo8AIKiU/EPG6w4kV1Q1Ajik2eSt/E9DN3Klp8eYNSFCzFbPFVHEwKAvE3voVsbAeg1+AI8fSMUJxK/1aoFeLquEx4e7jj+vT/SQRfHs7/+AF/nVTnafxk6DHcn3Ulb9ExXDr2Dk6JPA6DJ1sija26jqK6gy3MYhsErGx9mb+0eABL8+3PT8PucbkM7V2X7dSM0NQNgThuK5ufcpfeEEL8vb9P7juP4pBkKkxytb+rN+IYMBKCuMpfda15WnEgIu6a6Ugq3fAqAyeIho+hOqM275BQU/P4b1tWrV59QGNG9zVnzKTa8AOjlVcmF/dROJRbif5k0E7eNeIShISkAVDdXMG/1X6hqqujSHF9lL+TXop8A8Lb4ck/as3hYvLo0Q3dlWG32qe4AGpgnpqoNJIRot8riTKqK7bNifIL6EBI7XnGiI5nMbiROfRST2QOAgqyPKC+U98pCvT0b56Pb7NUQYoZcjId3qOJE4n+1uZN+6qmnUl5eftT5lStX8qc//alDQonuJ33/DjZVHJriZeXhNPm/IpyTm9md2WnPEevXF4CiugIeX3s7TdaGLnn9LeUZvL/tJUf7jpHziPKJ7ZLX7gn0DdugqhYA09B+mMJDFCcSQrRXXuYCx3H88BlOOdvIN6gP/Ubf6mhvXTKXlsaq4zxCiM7VWLufwq2fAWCyeJKQfKXiROJY2txJnzBhAqeddho1NTWOc7/88gtnnHEGDz30UIeGE93HvIzFgL38SFqIlWGhfdUGEuI4fN39mTP6JYI9wwDYWZHFc+vvx6ZbO/V1DzSW8mz6bHTDvnToov7XkhY5sVNfsycxDOOIsmuWyaMUphFCnIj6qnxKc5cA4O4dSmR/5/3wPzZxGsExowFoqi9l2/LHMAxDcSrRU+3Z8A6G3gLY/2+6e8kGzs6ozZ30t956i969e3PmmWfS2NjIkiVLOPPMM3nkkUe48847OyOjcHEf7PiZ4kZ7ySiLVs/Doy9WnEiIPxbmHcUDo1/Gy2Jfr7yueBn/yHqm095YWfUWnkm/l8om+0ylpLAxXDropk55rZ5K37kHo6gUAC0+Gi2hl+JEQoj2ys9cBNh/HscNuwyT2V1toOPQNBNDJs3F4uEPQEnOTxTt+lZxKtETNdQUsXf7lwCY3bydohqCOLY2d9I1TePDDz/E09OTqVOncs455/DEE09w++23d0Y+4eKarM28tW2Ho31+Qgghnv4KEwnRer0DBjA77Vksmr0Qxg97PuHz3e92ymu9t/UFth/YCECoVyR3jXwMs2bulNfqqWxL1jqOLZNHOeXUWCHEH2tuqGDfjq8Be0ej12Dnr3zh6RPO4An3O9o7VjxNQ80+hYlET5S7/h8YB2cFxg27DHfPQLWBxO9qVSc9MzPziD/btm3joYceoqCggOnTp3PyySc7vibEbz2x/lMadHun3M9SyR1J5ypOJETbDA8bzV9GzHW0F257haUF33Toayzf+x++yfkQAIvJjVmpT+PvEdShr9HT6Xv3o++0l2nSQgMxJfZTnEgI0V6FWz5xbHrVa9D5uHn4KU7UOhF9TyVqwJkA2Frq2PLzgxi6VEYSXaO+qoCiQx9uufsQN3y64kTieFpVJz05ORlN046Y5nmo/eabb/LWW29hGIbUSRdH2Ftbyn8KGwD7hnF3DkvBYpKRQeF6JsacQVnDfhZus5fPeWXjIwR5hpEUNvqEnzu/OpvXNj7iaF+bOIsBQYkn/LziSNYlh9eimyemoZnaPJFMCOEEbC0NFGz5GABNMxM77DLFidpm4LhZVOxbT2NtEZXFG8jLXEBC8lWqY4keIHf9PzEO7nkTP2w6bh4ys9WZtaqTnpub29k5RDc0Z83n6PgCkOBTxZm9xyhOJET7XdDvKsoaivlhzyfYDCtPrfsrj43/B70DBrb7Oetbanlq3V9ptNl3jp8cezanxzv/tE1XY1RUo2/cZm/4eGFOkw9BhHBVRTu/oaWxErCPTHv5RakN1EYWDz+GTplHxlfXAwbZ614nOGYM/qGDVEcT3Vh9Vb5jHwSLhz9xLvbhVk/Uqk56fHx8Z+cQ3czKfZvZUuUNgEYLj4w6S3EiIU6MpmlcN+weDjSWsrZ4KQ3WOuatvpWnJrxHmHfb3yQahsHLG+eyr84+BTvBfwA3Dpst66Q7gfWXdNDtM8HM40egubspTiSEaA9Dt5GXucjRjkuaoTBN+wVFjSA+eSZ5G9/D0K1s+XkOoy5YgNni+ccPFqIdctLfAkMH7OUKLS6yRKQna/V8v927d5ORkXHEuZ9++onJkyczatQoHn/88Q4PJ1yTrus8vn4Zh/57jQ0zGBgUpzaUEB3ArJm5a+RjDAgaBkBFUxmPrP4Ltc3VbX6uf2cvYHXRzwB4W3y5J+0ZPCxeHZpXgNHQiG31JnvDYsEyfoTaQEKIdivds4yG6gIAgnuNcunR576pN+MbYp+JVVeRw+41LytOJLqruopcinf/AICbZwCxidMUJxKt0epO+qxZs/jyyy8d7dzcXM4++2zc3d0ZO3YsTzzxBC+88EInRBSuZv72xZQ12ze9ctPqeHj0JYoTCdFxPCxe/G3UC0T52D94KqzN5Ym1d9J8cBOj1sgqy2DBtsNvyO4Y+ShRPrEdnlWA7ddN0GSvB2tOG4rm56M4kRCivfIyFziO4110FP0Qk9mNxKmPYjJ7AFCQ9RHlhasVpxLdUU7GWxwqVxifdBUWd/k96Apa3UlPT0/njDPOcLQXLVrEgAED+M9//sOLL77ICy+8wPz58zsjo3Ah9dZG5u88vIfBZX0j8ZcfBqKb8fcI4sExrxDgHgzA1gMbeHHDg+gHp5IdT3lDCc+m34t+cPOWiwdcR1rkyZ2at6cyrDasyw/OANPsG8YJIVxTZfFGqvbbqwj5BvcjOGas4kQnzjeoD/1G3+pob10yl5bGKoWJRHdTW76L/dmLAXD3CiZ26MWKE4nWanUnvaysjJiYGEd7yZIlnH322Y72pEmT2LNnT4eGE67n0XWf0KTb17kEulVyc6KsRRfdU6RPDA+MeQlPs32K+qp9i5m/5e/HfUyL3sKz6fdS1XwAgOSwMUwbeGOnZ+2p9A3boKoWANPQ/pjCgxUnEkK0V96mw6PocUkzus3+HbGJ0wiOsVcKaaovZdvyx4+opiTEicj+7Sh68lWY3WRZnatodSc9ODiYoqIiwL7mOD09ndGjD5cfam5ulh8qPdye6iKWFLUcbBnMSh6DScociW6sX+AQ/pr6FCbNXlrw65xFfJW98Hevf2/LC2yvsK+PDvOK5M6Rj2HWpCxhZzAMA+vSw2XXLJNHKUwjhDgRdZV5lO5ZBoCHTziRfU9XnKjjaJqJIZPmYjlYDqsk578U7/pOcSrRHVSXbac01773jbt3KDFDpHqMK2l1D2rixInMmzePgoICXnjhBXRdZ/LkyY6vb926lYSEhM7IKFzEA2u+RMe+tqqfXw2nxKYoTiRE50uJOImbh//N0Z6/5e+s3Lf4qOuWF/7At7kfAmAxuTEr9Rn8PYK6LGdPo+/Yg1FUCoCWEI2pdy/FiYQQ7ZWfuZBDo4GxiZdhMnevCg2ePuEMnnC/o719xVM01OxTmEh0BznpbzmOe4+4RqoHuJhWd9Ife+wxtm3bRkJCAvfeey9PP/00Pj6H1xovWLCAKVOmtDnAa6+9Ru/evfH09CQlJYXly5cf9/qmpib+9re/ER8fj4eHB3379uWdd95p8+uKjrWkcAO7auzT3DWaeXTUuYoTCdF1Tok/j2kDbgDAwOCF9Q+wuWwdWWXprCtfys8FX/HKxocd11+XOIv+QUNVxe0RbEvWOo4tk2QUXQhX1dxwgKKd3wBgdvMhZvD5ihN1joi+pxLZ/0wAbC11bPn5QQzdpjiVcFVVJVsoyzs0+ySC6EHnqQ0k2qxVddIBevfuzbZt29i6dSthYWFER0cf8fWHH374iDXrrfHxxx9zxx138NprrzF+/HjefPNN/vznP7N161bi4o5dsuuSSy5h//79/POf/6Rfv36UlJRgtVrb9LqiY+m6zpMbVgGBAEyKNNM7IPq4jxGiu5k28EbKGvfzU/6/seotPLTqJgyOXgI0JfZsTouXKWedSS/cj77LXn9eCw3ElNhPcSIhRHsVZH2MbmsGoNfg87t1fedB42dRWbSextoiKos3kJe5gITkq1THEi4oJ/1Nx3HvkddgtngoTCPao00Lht3c3EhKSjqqgw6QlJRESEhIm178+eef59prr+W6665j8ODBvPDCC8TGxvL6668f8/offviBZcuW8d1333HKKaeQkJDAqFGjGDduXJteV3SsN7Z8R2VLIAAeWg1zUmXnSNHzaJrGTcPvp7e/ve7tsTroAMlhY7vNhkfO6rdr0c0T09BkbwwhXJKtpYHCLZ8AoJnMxA27THGizmXx8GPolHmA/XdE9rrXqS7brjaUcDmVxZmUF6wEwNM3iuiBMrvVFbV6JL2jNTc3k5GRwezZs484f9ppp7Fq1apjPuarr74iNTWVp59+mgULFuDj48M555zDvHnz8PI69m6FTU1NNDUdrl9cXV0N2Ed/df2PyyWppOs6hmE4dc7q5jo+2L0X8AVgZv94vCweTp3ZWbjC/RVtYxhQ1XTguNe8t/VFxkRNlQ3jOolRUY2+cZu94eOFljKkU7/H5Pu4+5N7rM7e7f+mpclekiy8z2m4e4d3yn1wpnscEJFEXNIM8je9j6Fb2fLTA6Se/76sJz4BznR/u0J2+huO44QR14Jm7vZ/d1e5x23Jp6yTXlZWhs1mIyIi4ojzERERFBcXH/MxOTk5rFixAk9PT7744gvKysq45ZZbOHDgwO+uS3/iiSd4+OGHjzpfWlpKY2Pjif9FOpGu61RVVWEYhtPukv7o1q9oMewd9CBLOWcET6akpERxKtfgCvdXtM3O6kwONJUe95ryxv38mr2EAf7DuyhVz+L5czqeun0WQ2NSfyorKzr19eT7uPuTe6yGYdjI3Xi47JpvzBmd9v7C2e6xb9wFeO5ZQWNVDnWVuWQte5aoYTeojuWynO3+dqa68iwq9q4BwM07EnNgWo94X+4q97impqbV1yrrpB/yv9M+DcP43amguq6jaRqLFi0iICAAsE+Zv+iii3j11VePOZp+3333cddddzna1dXVxMbGEhYWhr+/fwf+TTreob9vWFiYU/6H21GRT3ql+8GWzv0jTyYyMlJpJlfi7PdXtN2Oltbtj2F4WQkPD+/kND2P0dBIy+Zd9obFgt9pJ+Hv692prynfx92f3GM1SnJ+oqXePmgT1Gs08f3HdNprOeM99j3lcdK/nIlua6Y859/EDjzVUU9dtI0z3t/Osn7tvxzH/dJuJCKyZ+wR5Sr32NOz9TNilHXSQ0NDMZvNR42al5SUHDW6fkhUVBS9evVydNABBg8ejGEYFBYW0r9//6Me4+HhgYfH0ZslmEwmp76Jh2ia5rRZ56Z/h4H9XgwOqOPkmGS1gVyQM99f0XYhXq3reId4hcs97wTWNZuhqQUA86hEzP6+XfK68n3c/ck97lqGYZC/+fAoekLyzE7/t3e2e+wf2p9+o29j56pnAdi27GHGXPwxbp4Bf/BIcSzOdn87w4G966gsygDAOyCeqAFndOu/7/9yhXvclmxt/ltkZmYe88/mzZvZtWvXEeu/j8fd3Z2UlBQWLz6ynvDixYt/dyO48ePHs2/fPmprax3ndu7ciclkavPO8uLEfLdnDXvq7L8oTDTy6OgLFCcSQr3BISMI8Qzn0KY/R9MI9YxgcMiIrozVIxhWG9Zf7G9O0MA8MVVtICFEu1UWb6S6ZAsAviEDCO7VM0eQYxOnOUbPm+pL2bb8cQzj2JuSip7NMAyy0w9vvN0n5XpMJuUTpsUJaHMnPTk5mREjRhz1Jzk5mUGDBhEQEMCVV17ZqvXed911F//4xz9455132LZtG3feeSf5+fncdNNNgH2q+syZMx3XX3755YSEhHD11VezdetWfvnlF2bNmsU111zzuxvHiY5n1W08n5nuaJ/Wy4sYX5m6K4RZM3Nt4qyDrf/tqNvb1yT+VTaN6wT6hm1Qbf8A15TYH1NYsOJEQoj2ytv0vuM4fviMHlsRQ9NMDJk0F4uHfXlmSc5/Kd71neJUwhkdKFxNVfEmAHwCexPR9zTFicSJanMn/YsvvqB///689dZbbNy4kQ0bNvDWW28xcOBAPvjgA/75z3/y888/88ADD/zhc02bNo0XXniBRx55hOTkZH755Re+++474uPjASgqKiI/P99xva+vL4sXL6ayspLU1FSuuOIKzj77bF566aW2/jXECXhp01fUWAMB8DRVc1+K1HwW4pCx0VO5J/VpQjzDjjgf6hnOPalPMzZ6qqJk3ZdhGFiXrHW0LZNGKUwjhDgRdRW5lOX9AoCHTwQRfU9VnEgtT59wBk+439HevvIpGmqKFCYSzsY+in54R/c+qTegmWQwwNW1eR7EY489xosvvsjpp5/uODd8+HBiYmKYM2cOa9euxcfHh7vvvptnn332D5/vlltu4ZZbbjnm1+bPn3/UuUGDBh01RV50nfLGaj7bUwbYN2O6fnB/PC1Hr/kXoicbGz2VUVGT2FKaQV5pDvFhfRgaliIj6J1E356LUVwGgJbQC1PvXooTCSHaKy9zoeM4bthlmMxuCtM4h4i+p1Kat5ziXd9ia65jy5IHSTnrDemICQDKC1ZSXZIFgG9wP8L7nKI4kegIbR5J37x5s2Ok+7fi4+PZvHkzYJ8SX1Qkn/J1R3PXfoLVsHfQIzwrmD5QfhAIcSxmzUxiaCppIZNIDE2VDnonsi1d5zi2TEpTmEQIcSKa6sso2vktAGZ3H3oNPl9xIucxaPwsPH2jAKgsWk9e5oI/eIToCQzDIHvdb0fRb0TTnHfjNNF6bb6LgwYN4sknn6S5udlxrqWlhSeffJJBgwYBsHfv3t/doV24rqzyHNaVHZp8YWNOinTQhRBq6YXF6LvyANDCgjAl9lOcSAjRXgVZ/8LQ7RUaYgZfhMW9ayo0uAKLhx9DJz/Cof1Nste9TnXZdrWhhHKlecuoKdsGgF/oQMISJitOJDpKm6e7v/rqq5xzzjnExMQwfPhwNE0jMzMTm83GN998A0BOTs7vTmEXruuhdd9jEAhAUlATaRGD1AYSQvR41t+MopsnpqI5cekVIcTvs7bUU7j1EwA0k4XYYZcqTuR8gqJHEp88k7yN72HoVrb8PIdRFyzAbGl97WXRfRiGTk76m452n5Sbeuwmi91Rmzvp48aNY8+ePSxcuJCdO3diGAYXXXQRl19+OX5+fgDMmDGjw4MKtT7PXk5hfSAAZhqYN1o2ixNCqGUcqELfeHAkyccLc2qi2kBCiHbbt+MrrE3VAET2+xOePlI15lj6pt5MecFqast3UFeRw+41rzBw/F9VxxIKlOQuobZ8JwD+YUMJjZ+gOJHoSO0qoOfr6+sokya6v2a9hVeyNgP2uuhnxvkR4S3ljYQQalmXZ4BurxlsOWkkmrtsMCWEK9J1K/mZixzt+OHTFaZxbiazG4lT5rH28+notmYKsj4kNP4kQmLGqI4mupCh244cRU+VUfTupl2d9J07d7J06VJKSkrQdf2Irz344IMdEkw4j+c3fEmdzd5B9zZXMWvk9YoTCSF6OqOhEdtqe01YLBbM40eoDSSEaLfS3J9prNkHQEjsOHxD+itO5Nx8g/vSb/St7Fz1HABbl8xlzMUf4+YZoDiZ6Cr7c/5LXUU2AAERwwmJHas4kehobe6kv/3229x8882EhoYSGRl5xKc2mqZJJ72bKa2v4Ku8KsALgL8MHYa7SUarhBBq2VZtgib7BlPmUYlovt6KEwkh2sMwDPI2Hd6pPD5Jlky2RmzipZTlr+BA4Rqa6kvZtvxxhp3ypIym9gCGbiMnQ0bRu7s2d9IfffRRHnvsMe69997OyCOczANrPsV2sCZ6L69KLux3heJEQoiezrDasC5Ptzc0+4ZxQgjXVFGUQXXpVsC+O3VQtJRRbA1NMzFk0lxWfzINa1M1JTn/pXjXd0QNOFN1NNHJinf/QH2lvapJYNRIgnuNUpxIdIY2b4NbUVHBxRdf3BlZhJNJ37+DjRUeB1tWHko9XWkeIYQAsK3fCtV1AJgSB2AKkz0yhHBV+UeMos+UEcE28PQJZ/CE+x3t7SufoqGmSGEi0dl03UpOxluOdl8ZRe+22txJv/jii/nxxx87I4twMvMyFgNmAFJDWkgKk/rDQgi1DMPA9puya5bJMuomhKuqrcihLH8FAJ6+kYT3nqo4keuJ6Hsqkf3to+e25jq2LHkQQ7cpTiU6S/HO72ioLgQgKDqNoOgUxYlEZ2nzdPd+/foxZ84cVq9ezbBhw3BzO3J98m233dZh4YQ6H+z4meLGIAAsWj2PjJ6mOJEQQoC+PRejuAwALaEXpoReihMJIdorf9NCx3HcsCswmWXPm/YYNH4WlUXraawtorJoPXmZC0lIvlJ1LNHBdFsLOevfdrT7pkmlre6szZ30t956C19fX5YtW8ayZcuO+JqmadJJ7waarM28tW0H4A/A+QkhhHj6qw0lhBCAbelax7FlsqzDE8JVNdWVUrTrOwAs7r5EDzpXcSLXZfHwY+jkR8j4+gbAIHvdawTHjMY/dJDqaKID7dvx9eEqCDFjCYxMVhtIdKo2d9Jzc3M7I4dwIk+s/4wG3d4p97NUckfSpYoTCSEE6IXF6LvyAdDCgjAN7as4kRCivQqyPsLQ7RUaYoZchMXdR3Ei1xYUPZL45JnkbXwPQ7ey5ec5jLpgAWaLp+poogPotmZyN/zD0e6TeqPCNKIrtHlNuuje9taW8p/Cekf7jmEjsZjMChMJIYSddcnhtejmiWloJvkVJoQrsjbXUbj1MwA0k4XYRBkM6Ah9U2/GN2QgAHUVOexe84riRKKj7N3+JU21+wEIjTuJgIhhihOJztaqkfS77rqLefPm4ePjw1133XXca59//vkOCSbUmLPmc3R8AYj3qeSs3mMVJxJCCDAOVKFv2m5v+HpjTh2qNpAQot32bf831uYaACL7n4GHT5jiRN2DyexG4pR5rP18OrqtmYKsDwmNP4mQmDGqo4kTYLM2sWf9O452n1RZi94TtKqTvmHDBlpaWhzHv0dKALi2VUVZbKmy10TXaOGRtLMUJxJCCDvrL+mgGwBYxo9Ac5cNpoRwRbpuJX/zIkc7fvh0hWm6H9/gvvQbfSs7Vz0HwNYlcxlz8ce4eQYoTibaa++2z2iqLwUgLGES/mGDFScSXaFVnfQlS5Yc81h0H7qu81jGUsC+o/vYMINBwfFKMwkhBIBR34htdaa9YbFgHj9CbSAhRLuV5PyXxtpiAELixuMbLHtLdLTYxEspy1vBgb1raKovZdvyxxl2ypMymOaCbC0N7Nkw39GWteg9R5sX9C1evJiGhobOyCIUmr99MWXN9g66m1bHw6MvUZxICCHsbL9uhGb7bC7zqEQ0X2+1gYQQ7WIYBnmbFjja8UkzFabpvjTNxJDJc7F42DcBLsn5L8UHd9IXrqVw66c0N5QDEN5nKn4hAxQnEl2lzZ30Cy+8kMDAQMaNG8d9993Hf/7zH2prazsjm+gi9dZG5u88vGv/ZX0j8ZddVoUQTsCwWrEuz7A3NPuGcUII11Sxbx01Zfa9JfzDhhAUlaI4Uffl6RPO4An3O9rbVz5FQ02RwkSirawt9ezZOP9gS6NPioyi9yRt7qRXVFSwdOlSzjnnHDZs2MDFF19McHAwY8aMYfbs2Z2RUXSyR9d9QpPuB0CgWyU3J8padCGEc7Ct3wbVdQCYEgdgCgtSnEgI0V6/HUWPGz5Dpl93soi+pxLZ/0wAbM11bFnyIIZuU5xKtFZh1se0NFYCENHvNFka0sO0uZNuNpsZO3Yss2fP5ocffmDVqlVcfvnlZGRk8Mwzz3RGRtGJ9lQXsaSo5WDLYFbyGExS1kgI4QQMw8C2ZK2jbZk8SmEaIcSJqD2wm/KCVQB4+kUT3meK4kQ9w6Dxs/D0jQKgsmg9eZkLFScSrWFtrj38oZZmok/KDWoDiS7X5t7Ytm3beOONN7j00kuJiopiypQpVFdX89xzz7F+/frOyCg60QNrvkTHA4B+fjWcEitTz4QQzkHfnoux374WT+vdC1NCtOJEQoj2OmIUfdjlmEyt2rtYnCCLhx9DJz8C2GctZK97jZqyHWpDiT+Un/URLU1VAET1/zM+gQlqA4ku1+afkEOHDiUsLIw77riDOXPmMHSo1Kp1VUsKN7Crxj7NXaOZR0edqziREEIcJqPoQnQPjXUlFO/+AQCLhz/Rg+T9RlcKih5JfNJM8ja9h6Fbyfr5AUZdsACzxVN1NHEMLU015G+yz3jQNDO9R16vOJFQoc0j6bfddhu9evVi7ty5XHPNNdx77718//33snmci9F1nac2rOLQJ6sTI830DpBRKiGEc9ALitF35wOghQVhGtJPcSIhRHsVbP4IQ7cCEDPkIixuUqGhq/VNuwnfkIEA1FXksHvNK4oTid+Tn7kIa3MNAFEDzsI7IFZxIqFCmzvpL7zwAuvXr2f//v088MAD2Gw2HnzwQUJDQxkzZkxnZBSd4I0t31HREgiAh1bDg6kXqw0khBC/YV16eBTdPDENzSQbTAnhiqzNtRRu+xQAzeRGbOI0xYl6JpPZncQp8zCZ3QEoyPqQ8sLVilOJ/9XSWEX+5g8A0Exmeo+8VnEioUq7dwjTdR2r1UpzczNNTU20tLSwZ8+eDowmOktNcz0f7t7raM8YEI+Pu5fCREIIcZh+oAp908E1k77emFNlWZUQrmrv9i+xNdsrNEQNOBMP71DFiXou3+C+9Bt9q6O9dclcWhqrFCYS/ysvcyG2Fvv3S/TA8/Dy76U4kVClzZ3022+/naSkJMLDw7nxxhvZt28fN9xwA5s2baK4uLgzMooO9vDaf9Fs+AIQ4l7BtYNPV5xICCEOsy1LB90AwDJ+BJq7m+JEQoj20G0tjlFBgPjh0xWmEQCxiZcS3Gs0AE31pWxf/gSGYShOJQCaGyoo2PwhYJ910nvE1YoTCZXavHHc3r17uf7665k0aRKJiYmdkUl0ol2VBawoOdTSuW/kyVJyTQjhNIz6RmxrMu0NNwvm8SPUBhJCtNv+nMU01e4HIDT+ZHyCeitOJDTNxJDJc1n9yTSsTdXsz1lM6K6TiRpwhupoPV7epvexWRsA6DX4fDz9ohQnEiq1uZP+6aefdkYO0UXmrPkagwAABgXUMSF6uOJEQghxmO3XjdDcAoB51DA0X9lgSghXZBjGEWXX4ofPUJhG/JanTziDJ9zP5v/OBmD7yicJjBqBl3QKlWmqL6dgy8eAff+ABBlF7/HaNYSanZ3NrbfeyimnnMKpp57KbbfdRnZ2dkdnEx3suz1ryK2zd9BNNPLY6AsUJxJCiMMMqxXrLxn2hgbmialqAwkh2u3A3rXUlu8EwD98KIFRMivGmUT0PZXI/mcCYGuuY8uSBzF0m+JUPdeejfPRrU0A9BpyEZ4+4YoTCdXa3En/z3/+w5AhQ1i7di3Dhw8nMTGRNWvWMHToUBYvXtwZGUUH0HWdv2emO9qn9vIixld+AAghnIdt/TaosW+YYxo2AFNokOJEQoj2ytv0vuM4PmkmmiYVGpzNoPGz8PS1j55XFq0nL3Oh4kQ9U1NdKXu32mcqmyweJCRfqTiRcAZtnu4+e/Zs7rzzTp588smjzt97772ceuqpHRZOdJwXM/9NtTUQAE9TNfenXKM2kBBC/IahG9iWHC67Zpk0SmEaIcSJqCnfyYGD5b28/HsRnjBZcSJxLBYPP4ZOfoSMr28ADLLXvUZIzBj8Qgeqjtaj5G54F93WDEDskEukAoIA2jGSvm3bNq699uiafddccw1bt27tkFCiY1U01vBpbqmjff3g/nhaPBQmEkKII+nbczD2lwOg9Y7BlBCtOJEQor3yNh0ekY0bNh3NZFaYRhxPUPRI4pNmAmDoVrJ+noPN2qg4Vc/RWFvM3m2fA2C2eBEvo+jioDZ30sPCwti4ceNR5zdu3Eh4uEyfdkYPrv0Eq+EDQLhHBdMHnqI4kRBCHOmIUfTJaQqTCCFORGNtMfuzfwDAzSOA6IFnK04k/kjftJvwDRkAQF1FNrvXvKI4Uc+Ru/4dDN2+WWps4qW4e8kyL2HX5unu119/PTfccAM5OTmMGzcOTdNYsWIFTz31FHfffXdnZBQnIKs8h3Vlhz7BtvFgqnTQhRDORS8oQs8uAEALC8I0pJ/iREKI9irY/JFjA7KYoRdjdvNSnEj8EZPZncQpj7L28+notmYKsj4kNP4kQmLGqI7WrTXU7GPfji8BMLv5EJc0XW0g4VTa3EmfM2cOfn5+PPfcc9x3330AREdHM3fuXG677bYODyhOzEPrvscgEIDhQY2kRQxSG0gIIf6Hdek6x7F5UhqaSTaYEsIVWZtqKDw4dddkdidm6CWKE4nW8g3uS7/Rt7Jz1XMAbF0ylzEXf4ybZ4DiZN1X7vp/OD7Qiht2Ge6egWoDCafS5unumqZx5513UlhYSFVVFVVVVRQWFnL77bfLzp1O5svsFRTWBwJgpoFHR1+kNpAQQvwP/UAV+qYd9oavN+bURLWBhBDttnf7F9ha7BUaogachYd3iOJEoi1iEy8luNdoAJrqS9m+/AkMw1CcqnuqryqgaMc3AFjcfYkbLqPo4kjtqpN+iJ+fH35+fh2VRXSgZr2Fl7IyHe0z4/yI8A5WmEgIIY5mW5YOuv1NoOWkkWhubZ7gJYRwArqthfzNHx5sacQNv0JpHtF2mmZiyOS5WDz8Adifs5jiXd8rTtU95a5/G8M4OIo+fDpuHtKfEkdqcyd9//79zJgxg+joaCwWC2az+Yg/wjk8v+FL6mz2KUre5ipmjbxAcSIhhDiSUd+Ibc3BDxPdLJjHJSvNI4Rov+Ls/9BUVwJAWMLJ+AQmqA0k2sXTJ5zBE+5ztLevfJKGmiKFibqfuso9FB388MPi4U/csMsUJxLOqM1DFldddRX5+fnMmTOHqKgomeLuhErrK/gqrwqwb9byf0MTcTe5qQ0lhBD/w7ZqIzTbd7U1jxqG5uutNpAQol0MwyB/0wJH+1BJL+GaIvqeRmneCop3fYutuY4tSx4k5aw3pJReB8nNeBsMHYD4pBlY3H0VJxLOqM2d9BUrVrB8+XKSk5M7IY7oCA+s+RQb9je7vbwquaifTDkTQjgXw2rFujzD3tA0zBNT1QYSQrTbgcJfqT2wG4CA8GEERCQpTiRO1KDxs6gsWk9jbRGVRevJy1xIgtTwPmG1B7Ip3v0fANw8A4lNvFRxIuGs2jzdPTY2VjaRcGLrS3awscLjYMvKQ6mnK80jhBDHYsvYCjX2DaZMwwdgCpXasEK4qrxNCx3H8UkzZZZlN2Dx8GPo5EcA+73MXvcaNWU71IbqBnIy3gLs/aiE5CuxuMkMMnFsbe6kv/DCC8yePZs9e/Z0Qhxxoh5O/y9gn46UGtJCUpjUGxZCOBdDN7D9puyaZVKawjRCiBNRXbadA3vXAODlH0tYwkTFiURHCYoe6Vi6YOhWsn6eg83apDiV66op30VJzn8BcPcKIWbIxYoTCWfW5unu06ZNo76+nr59++Lt7Y2b25FrnQ8cONBh4UTbfLhzCcWNgQBYtHoeGT1NbSAhhDgGfXsOxv5yALQ+MZjioxUnEkK01xFr0YdfIeuWu5m+aTdRXvgrteU7qavIZvfaVxg47m7VsVxSTvqbjuOE5Kswu3kpTCOcXZs76S+88EInxBAnqsnazJtbtwP2shnnxQcT4umvNpQQQhyDbclax7GMogvhuhpritifvRiwr6+NGnCW4kSio5nM7iROeZS1n09HtzVTsPkDQuPGExIzRnU0l1Jduo3SPUsA8PAOo9cQqbokjq/VnfSWlhbc3Ny48srf3zQiKyurQ0KJtnti/Wc06PZOuZ+lkjuTb1KcSAghjqYXFKFnFwCghQdjGiJLcoRwVfmbP3TUeo4ZeomMDHZTvsF96Tf6Vnaueg6ArUvmMubij3HzDFCczHUcMYo+4hrMFk+FaYQraPWa9Msuu+y4G8ZlZWUxderUDgkl2mZvbSn/Kax3tO8YNhKLTDcTQjgh65LDa9HNE9PQTLLBlBCuqKWphr3bvwDAZPYgduglihOJzhSbeCnBvUYD0FRfyvblT8hG0q1UVZJFWf5yADx8I+g1+Dy1gYRLaHUnfc2aNdx4443H/NqWLVuYOnUqJ598cocFE603Z+3n6Ng/kYv3ruKs3mMVJxJCiKPp5ZXomw7uDuzrjTl1qNpAQoh227vtM2wt9gGCqIFn4e4lFRq6M00zMWTyXCwe9lmb+3MWU7zre8WpXEPOujccx71HXIfJ7K4wjXAVre6k//jjj3zxxRfMnj37iPPbtm1j6tSpjB8/no8++qjDA4rjW1WUxZZKe/kGjRYeGXWm4kRCCHFstl8y4ODIi2XCSDS3Nm+LIoRwArqthfzNHx5sacQPn640j+ganj7hDJ5wn6O9feWTNNQUKUzk/CqLN1Je+CsAnn7RRA88W3Ei4Spa3UkfPHgw3333Ha+99hrPPPMMANu3b2fKlCmMHj2aTz75BLNZplh3JV3XeSxjKYdu45gwg0HB8UozCSHEsRh1DdjWZNob7m6Yx41QG0gI0W7Fu7+nub4MgLDek/EOiFOcSHSViL6nEdn/DABszXVsWfIQhm5TnMp5/XYteu+R12Eyux3naiEOa9MwRlpaGl9++SVnnXUWdXV1vP3226SmpvLpp59KB12B+dsXU9Zsn17mptXxcNplihMJIcSx2X7dCM0tAJhHDUPzkQ2mhHBFhmGQt2mhox0/fIbCNEKFQePvobJoPY21xVQWZZCXuZCE5N/fWLqnqtiXwYG99momXv4xRB38cEOI1mj1SPohU6ZM4YMPPuCxxx4jOTmZzz///Kha6aLz1Vsbmb8z19Ge1jeSAE8/hYmEEOLYDKsV6/L19oamYT45RW0gIUS7lResoq4iG4CAyCQCI4crTiS6msXDj6GT5wH2jT+z171GTdkOtaGcjGEYZKcfXoveJ+UGGUUXbdLqkfSgoCA07chdeJcvX05ERMQR5w4cONAxycRxPZr+KU26vVMe4FbJ/yXKKLoQwjnZ0rdCTR0ApuEDMIXKBlNCuKq8Te87jmUUvecKih5JfNJM8ja9h6Fbyfp5DqMuWIDZ4qE6mlOo2LuOyiL7h9PegfFE9DtdcSLhalrdSX/hhRc6MYZoiz3VRSzZ1wx4AAazkkZjMrV5UoQQQnQ6QzewLTtcds0yKU1hGiHEiagu3UbFvnQAvAPiCUuYqDiRUKlv2k2UF/5KbflO6iqy2b32FQaOu1t1LOWOOYpuko1SRdu0+n/MlVfKWhNnMWftl+jYS2D09avh1LhUxYmEEOLY9G3ZGPvLAdD6xGCKj1acSAjRXnmbFjiO44ZPR9NkgKAnM5ndSZzyKGs/n45ua6Zg8weExp1ESMxo1dGUOlD4K1X7NwHgE9SHiD6nKk4kXJH8dHUxSwo3sLPaPs1do5l5o85RnEgIIX6fdelvRtEnj1KYRAhxIhpq9lGS818A3DyDiBogm2AJ8A3uS79RtzraW5fOpaWxSmEitQzDIPs3ddH7pN6IZpLNtUXbSSfdhei6zlMbVnFoo46JkWb6BvRSG0oIIX6Hnl+EkV0AgBYejGlwX8WJhBDtlZ/5AYZhL7UVmzgNs8VTcSLhLGKHXUpwL/voeVNdCduXP4FhGIpTqVGWv5zq0i0A+Ab3J7z3FMWJhKuSTroLeWvL91S0BALgodXwYOrFagMJIcRxWJeudRybJ6WhmbTjXC2EcFYtTdXs2/4lACaLBzFDLlIbSDgVTTMxZPJcLB72pZj7cxZTvOt7xam6nmEY5KT/zyi6LAkR7dSq/znV1dWdnUP8gZrmehbtLnS0ZwyIx8dd6gwLIZyTXl6JvmmnveHrjTllqNpAQoh2K9z6KTZrAwDRA8/F3UsqNIgjefqEM3jCfY729pVP0lBTpDBR1yvds8RRis4vdBBhCZPUBhIurVWd9KCgIEpKSgB7nfTKysrOzCSO4eG1/6LZ8AUgxL2CawdLKQchhPOyLUuHg9MdLRNGornJzrZCuCLd1kxB1kf2hmYibtjlagMJpxXR9zQi+9v3KrA117FlyUMYuk1xqq5hGDo56W862n1TbzqqdLUQbdGqTrqvry/l5fbdeZcuXUpLS0unhhJH2lVZyIqSQy2d+0ZOkJJrQginZdQ1YFu72d5wd8M8boTaQEKIdiva9T3N9fb3gOG9J+MdEKs4kXBmg8bfg6dvJACVRRnkZS5SnKhrlOT8RO2B3QD4hw8lJO4kxYmEq2vV0MYpp5zC5MmTGTx4MADnn38+7u7ux7z2559/7rh0AoCH1n2DQQAAgwLqmBCdpDiREEL8PtuqjdBs/zDXPGoYmo8szRHCFRmGTv5vyq7FD5+hMI1wBRYPP4ZOfoSMr28EDLLXvUpIzGj8QgeqjtZpDN1GTsZbjraMoouO0KpO+sKFC3nvvffIzs5m2bJlDB06FG9v787O1qM1Njfw0fL5/FpdSq5h3xHZRBOPjjpfcTIhhPh9RosV64r19oamYZ6YqjaQEKLdyvJXUFeZC0Bg5AgCIoYpTiRcQVB0CvFJM8nb9B6GbiXr5zmMumABZouH6midYn/2j9RV5AAQEJlEcMxYxYlEd9CqTrqXlxc33XQTAOnp6Tz11FMEBgZ2Zq4e7bnvnuHzRj+s+AP+jvOxplJi/SLUBRNCiD9gy9gCNXUAmIYPwBQSqDaQEKLd8jYtdBzHJ81UmES4mr5pN1Fe+Cu15Tupq8hm99pXGDjubtWxOpyuW8nJeNvRllF00VHavLB5yZIljg66YRg9tg5iZ3nuu2f4V2MUVnz/5ysGeXovnvvuGSW5hBDijxi6gW3pOkfbMnmUwjRCiBNRVbKFyqIMALwDEwiNlzW2ovVMZncSpzyKyWxfHluw+QPKC9coTtXxinf/QH1VHgCBUSkERacpTiS6i3btPvb+++8zbNgwvLy88PLyYvjw4SxYsOCPHyiOq7G5gc8b/Q62/vdTOHv780ZfGpsbujSXEEK0hr4tG6PkAABanxhMcVGKEwkh2uvItejTpd6zaDPf4L70G3Wro7116VxaGqsUJupYuq2FXBlFF52kzT9xn3/+eW6++WbOOOMM/vWvf/Hxxx/zpz/9iZtuuom///3vnZGxx/h4xXtY8ePoDvohGlb8+XjFe10ZSwghWsW6ZK3jWEbRhXBd9dWF7M/9CQB3rxBHWS0h2ip22KUE97L/PmiqK2H78ie6zSzcol3f0lBdCEBwr9EERY9UnEh0J20uXPvyyy/z+uuvM3Pm4bVJ5557LkOHDmXu3LnceeedHRqwJ9lXVwH4tfI6IYRwHnpeEUaO/c2KFh6MaXBfxYmEEO2Vn/kBGDoAsYnTuu2GX6LzaZqJIZPmsvrTS7E2VbM/ZzGhu04maoBrf/BjH0X/h6PdJ/VGhWlEd9TmkfSioiLGjRt31Plx48ZRVFTUIaF6qmifoA69Tgghuop16eFRdPOkNDSTTPkTwhU1N1ayb8e/ATBZPIkZcpHiRMLVefpGMHjCfY729pVP0lDj2n2GfTu+orHW/ncIiR1HYKSURxYdq82d9H79+vGvf/3rqPMff/wx/fv375BQPdW0k67EQg3we9OADCxUM+2kK7sylhBCHJdeXomeudPe8PPBnDJUbSAhRLsVbv0U3doIQK9B5+LmGaA4kegOIvqe5lg2YWuuY8uShzB0m+JU7WOzNpG7/p+Otoyii87Q5unuDz/8MNOmTeOXX35h/PjxaJrGihUr+Omnn47ZeRet5+nuxQWeNfyr0Rd7R/23I1H2jvvJ9UvY/0MD8efcpSKiEEIcxbYsHQ6uMbScNBLNrc2/WoQQTsBmbaIw62N7QzMRN+wKtYFEtzJo/D1UFq2nsbaYyqIM8jIXkZDseqX99m3/kqa6/QCExp9MQHii4kSiO2rzSPqFF17ImjVrCA0N5csvv+Tzzz8nNDSUtWvXcv7553dGxh7l7jNmcYlnERZqjzhvoYYpDV8xtS6bXUWL2PnhbHRdV5RSCCHsjLoGbGs32xvubpjHJSvNI4Rov+Jd39LcYK/QENF7Kl7+vRQnEt2JxcOPoZMf4dAgVPa6V6kp36k2VBvZrI3kbnjH0ZZRdNFZ2jXckZKSwsKFCzs6izjo7jNm8X/NDXy0fD551WXE+4dyyfiZFHyWQwHZAORXL6Z5fjmDZ7yK2c1dcWIhRE9lW7URmlsAMI8ahubjpTaQEKJdDEMnL/Pwe7t4FxzhFM4vKDqF+KSZ5G16D0O3kvXTA4y6YIHLbE64d+vnNNeXARCWMBn/0EGKE4nuSopeOilPdy9mTr6RG8dez8zJN+Lt6cPAK56hX+SljiXrxS3r2fTOdKy13afmpBDCdRgtVqwr1tsbmoZ5YqraQEKIdivLW059ZR5g70j5hw1RnEh0V33TbsI3ZAAAdRXZ7F77iuJErWNraWDPxncd7b4yii46kXTSXUzCubMY2v82NMN+6w6QTcaCy2gq26s4mRCip7FlbIGaOgBMSQMwhQSqDSSEaLe8Te87juOHz1CYRHR3JrM7iVMexWS2zwQt2PwB5YVrFKf6YwVbPjm8HKTPqfiGyIbZovNIJ90FRU29kqSUhzHrbgDUmPaT/q8Z1OVvU5xMCNFTGLqBbek6R9syaZTCNEKIE1G1fzOVxRsB8AnqQ0jceLWBRLfnG9yXfqNudbS3Lp1LS6Pzzgy1NteRt2n+wZZG79QbVMYRPYB00l1UaNoZpEx+EXebNwAN5irSv7meqi2rFCcTQvQE+rZsjBL7iILWNxZTXJTiREKI9srbtMBxHDd8Opombw9F54sddinBvewf8DbVlbB9xZMYxu+VIVarYMvHjg8RIvudjm9QH8WJRHfX5p/C8+fPp76+vjOyiDbyHzSatHPewcsWBECLuYH1v9xJ6ep/K04mhOjurEvWOo4tk9IUJhFCnIj6qgJKcn8GwN07lKj+f1acSPQUmmZiyKS5WNz9ANif/SPFu79XnOpo1qaawx9kaSZ6p8gouuh8be6k33fffURGRnLttdeyapWM2qrmFdOftEsX4a9HA2AzWcnc+Cj7fvyH4mRCiO5Kz9uHkVMIgBYejGlwX8WJhBDtlZ+5iEM70sYlXupYJyxEV/D0jWDwyfc72ttXPElDTZHCREfLz/oQa1M1AFH9z8AnMF5xItETtLmTXlhYyMKFC6moqGDy5MkMGjSIp556iuLi4s7IJ1rBPTiCkVd+QIhm3ynT0HS25r5O7uePK04mhOiOrL9Zi26eNArNpClMI4Ror+aGCvbt/AoAs8WLXkMuVJxI9EQRfU8jsv8ZANia69iy5CEM3aY4lV1LU/XBD7JA08z0Hnmd4kSip2hzJ91sNnPOOefw+eefU1BQwA033MCiRYuIi4vjnHPO4d///je6rndGVnEcFm8/hl/9HlEehzdvyi79jG0L7kC3OccPOiGE69PLK9Ezd9obfj6YU6RMkxCuqnDrJ+jWJgCiB5+Pm4e/4kSipxo0/h48fSMBqCzKIO9gx1i1/MyFWJtrAYgaeBbeAbGKE4me4oR2BgkPD2f8+PGMHTsWk8nE5s2bueqqq+jbty9Lly7toIiitcxu7gye+SoJgWc6zu2tX87md6/B1iT7CAghTpxtWToc3NjHMmEkmptFcSIhRHvYrI0UZP0LsI8Qxg27XHEi0ZNZPPwYOvkRwD4zK3vdq9SU71SaqbmxkvzNHwKgmSwyii66VLs66fv37+fZZ59l6NChTJo0ierqar755htyc3PZt28fF1xwAVdeeWVHZxWtYDKZ6DftEQbEXAWG/QddqS2LDe9eQUv1AbXhhBAuzahrwLZ2s73h7oZ5bLLSPEKI9iva+S0tjRUAhPc9BS8/qdAg1AqKTiE+aQYAhm4l66cHsB2c6aFC/qYF2Frsg1zRg87Fyy9aWRbR87S5k3722WcTGxvL/Pnzuf7669m7dy8ffvghp5xyCgBeXl7cfffdFBQUdHhY0XpxZ97KsKGzMOlmACq1fNIXXkbj/jzFyYQQrsq2agM0twBgHj0czcdLcSIhRHsYuo38zIWOdvzwGQrTCHFY37Sb8Q2x77FUV5FN9tpXleRobjhAQdbHAGgmN3qPuEZJDtFztbmTHh4ezrJly8jKyuKOO+4gODj4qGuioqLIzc3tkICi/SImTGPE2Cex2DwAqDOXse6zmdRkb1QbTAjhcowWK9bl6+0NTcN8coraQEKIdivN+4X6qnwAgqLT8A8brDiREHYmszuJUx51VBnI37yI8sI1XZ5jz8b3sFkbAOg1+ALHenkhukqbO+kTJ05k5MiRR51vbm7m/fffB0DTNOLjpTyBMwhKnkLK6a/hYfMFoMlcS8Z/bqFi40+KkwkhXIktfQvU2qf9mZIGYAoJVBtICNFujprPQHzSTIVJhDiab3Bf+o261dHeunQuLY1VXfb6TXWlFG75BACT2YPeI67ustcW4pA2d9KvvvpqqqqO/kapqanh6qvlP7Ez8uubTNqF7+NjCwXAam5iw6/3sX/5x4qTCSFcgaEb2JYdLrtmmTzqOFcLIZxZZfEmqvZvAsAnuC8hsWMVJxLiaLHDLiW4l/13TVNdCdtXPIlxcNPSzrZn43voNvta+JghF+HhE9YlryvEb7W5k24YBpp2dE3cwsJCAgICOiSU6HieEfGkTv+QQMM+w0E32di85Rnyv31ZcTIhhLPTt+7GKLFvPKn1jcUUKxtMCeGqjhhFHz7jmO/phFBN00wMmTQXi7sfAPuzf6R49/ed/rqNtfvZu+0zAEwWT+KTZSNsoUara+eMGDECTdPQNI2pU6disRx+qM1mIzc3lz/96U+dElJ0DDf/YEZc/QFZC26k1JYFmsHOwvk0f7yfPhc/gsl0QhX5hBDdlHWpjKIL0R3UVeZRumcpAB7eYUT2k/dtwnl5+kYwaML9ZP10HwA7VjxFYOSITq1EsGfDu+i2ZgBih07Dwzuk015LiONpdSf9vPPOA2Djxo2cfvrp+Pr6Or7m7u5OQkICF154YYcHFB3L7OHJsKvfYecHf6Ww/hcA9lR+T9N7ZQya/hJmN3fFCYUQzkTP24eRUwiAFhGCaVAfxYmEEO2Vv3kRYJ8yHDvsMkxmN7WBhPgDkf1Ooyx/OcW7vsPaXMuWJQ+RctbraCZzh79WY00Re7d/AYDZzdtRDk4IFVrdSX/ooYcASEhIYNq0aXh6enZaKNG5TGYzg2b8HY/PHye71D6lp6h5Hc3vzmTY9LexePspTiiEcBa/HUU3T0pDM8nUWCFcUXPDAYp2fAOA2c2HmMEXKE4kROsMGn8PlUXraawtprIog7zMRSQkd/yGh7kb3sHQrQDEJl6Ku1dQh7+GEK3V5vnNV155pXTQu4neF9zPkN43oxn2/wblxi7Wv3c5zQf2K04mhHAGelkFeuZOe8PPB3PKELWBhBDtVrDlE8dmWL0Gn4fFQz6QF67B4uHH0MmPAPYPibPXvUpN+c4OfY366kL27fg3AGZ3H+KHT+/Q5xeirVrVSQ8ODqasrAyAoKAggoODf/ePcC3Rp13H8OQHMev2KW/Vpn2s++hy6gt2KE4mhFDNtiwdDu6ma5kwEs3S6slXQggnYmtpoHCLvaKLZjITN+xyxYmEaJug6BTH9HNDt5L10wPYrE0d9vy56/+JodsAiBt2BW6eshm2UKtV77j+/ve/4+fn5ziWnUC7l7AxZzPSL4SNy+6hxdxAg7mS9K+vI3nKs/gPGq06nhBCAaO2HtvazfaGuxvmsclK8wgh2m/fzq8ddaYj+pyGp2+k4kRCtF3ftJspL1xNbflO6iqyyV77KgPG3XXCz1tflU/xzm8BsLj7yYdYwim0qpN+5ZWHyw9cddVVnZVFKBQwdBypfm+z8Zv/o8FcRbO5nowltzOs5kFC085QHU8I0cVsqzZCi31tnnn0cDQfL7WBhBDtYug28jMXOdqyGZZwVSazO4lT5rH28xnotmbyNy8iNO4kgmNOrOpITsY/MIyDo+hJ03GTpSDCCbRqunt1dXWr/wjX5RM3mNRLFuKn2z9ht5la2JTxEEU/zVcbTAjRpYwWK9YV6+0NTcM8MVVtICFEu5XsWUpDtb1CQ3Cv0fiFDlScSIj28w3uR79RtzraW5Y+5Jgl0h51FbmO+utuHgHEJV56whmF6Ait6qQHBgYSFBR03D+HrhGuzSM0mpQZHxBMXwAMTWfLrpfZ8+XTipMJIbqKLT0LausBMCUNxBQsa/OEcEWGYZC36X1HW0bRRXcQO+xSgnvZR8+b6krYvuJJjIP7p7RVTsbbYOgAxCfPxOLu+wePEKJrtGq6+5IlSzo7h3AiFt8Akq5ZyLYF/0dxy3rQYPf+j2laVEL/S5/CZO742pRCCOdg6Aa235Rds0xOU5hGCHEiqoo3Ul2SBYBvcH+CY8YoTiTEidM0E0MmzWX1J9OwNtewP/tHQuMnENW/bcszaw/sZn/2jwC4eQYRM/SSzogrRLu0qpM+ceLEzs4hnIzZzZ0hV72J+8d/I7/a/gOsoHYJTfOvY+j01zF7SBk+IbojfetujNIKAEx9YzHFRilOJIRorz2bFjiO45NmyMa/otvw9I1g0IT7yfrpPgB2rHiKoMgRePq1/ndWTvpbgH0EPiH5Sixu3p0RVYh2aVUnPTMzk8TEREwmE5mZmce9dvjw4R0STKhnMpkYcNkTeHwdzq69i0AzKLFm0jL/CoZf/jZuflJyT4juxrrk8Ci6efKJbcYjhFCnrnIPZXnLAPDwCSei72mKEwnRsSL7nUZZ3i8U7/4ea3MtW5Y+xMiz3kDT/ng1b03ZDkpyfwLA3TuEmCEXdXZcIdqkVZ305ORkiouLCQ8PJzk5GU3Tjrn2Q9M0bDZbh4cUasWffSceSyPYuu0FdJONCvaQseByki96G8/wWNXxhBAdRN+zDyPXvsGUFhGCaVAfxYmEEO2Vv2mh4zhu2GWYzG4K0wjROQaedC+VxRtorC2mYl8G+ZkLiU+a+YePy8l403GckHw1ZjepYCKcS6s66bm5uYSFhTmORc8TOely3P3DyFz9IFZzM7XmUtI/nUnyn1/Gt3ei6nhCiA5gXbrWcWyelIZmkqmxQriipvpyinbZ6z6b3X3oNfgCxYmE6BxuHn4MnfwIGV/fCBjsXvsqwTFj8AsZ8LuPqS7dRumeg7NMvMPk+0M4pVbt7h4fH+9YxxQfH3/cP6L7Ch55KimnvIKHzQeARnM1Gd/fSGXmMsXJhBAnSi+rQN+8097w88GcMkRtICFEuxVu+Rjd1gxAzOALZcdq0a0FRac4KhcYupWsnx7AZm363euz0193HCeMvAazxaPTMwrRVq3qpP+vHTt28Je//IWpU6dyyimn8Je//IUdO3Z0dDbhhPwGpJB63rt42+zr0VvMjaxfOYuSlZ8pTiaEOBG2ZemH9s/BMiEFzdKqiVZCCCdja2mgcMunAGgmM7FS91n0AH3Tbsb34Oh5XUU22WtfPeZ1Vfs3U56/EgBP30h6DTqvqyIK0SZt7qR/+umnJCYmkpGRQVJSEsOHD2f9+vUkJibyySefdEZG4WS8ovuSesUH+OsxAOgmG5mbn6DwhzcUJxNCtIdRW49t7WZ7w90N87hkpXmEEO23b8dXtDRVARDZ7094+kYoTiRE5zOZ3UmcMg+T2R2A/M2LOFC49qjrstMPv1ftPfJax/VCOJs2d9Lvuece7rvvPn799Veef/55nn/+eVatWsX999/Pvffe2xkZhRNyDwhj5FUfEGoabD+hGWzPe5vsTx5G13W14YQQbWJbuQFarACYRw9H85YSi0K4Il23kp+5yNGOGz5DYRohupZvcD/6jbrV0d6y9CFamqod7crijRwoXA2Al18vogac3eUZhWitNnfSi4uLmTnz6F0Tp0+fTnFxcYeEEq7B4uXD8KvfpZfXOMe53ANfsX3BbejWFoXJhBCtZTS3YF25wd4waZgnpqoNJIRot9LcJTTU7AUgJGYsfiH9FScSomvFDruU4F728qFNdSVs++VxDuxNp7JwKTtWPuO4rvfI66TigXBqbe6kT5o0ieXLlx91fsWKFUyYMKFDQgnXYbK4MXD6i/QJOc9xbl/jr2S+exXWhjp1wYQQrWJL3wK19QCYkgZiCg5QnEgI0R6GYZC3aYGjHZcko+ii59E0E0MmzcXi7gdASc5iNn53M4UZz1B3wL45qrtXCJEDzlAZU4g/1Kqdgb766ivH8TnnnMO9995LRkYGY8aMAWD16tV88sknPPzww52TUjg1k8lEn4vm4PFDGNv3/ANDMyjTt7N+/uUkX/o27kHhqiMKIY7B0A1sy9Y52pZJoxSmEUKciMqiDVSXbgHAN2SgYzRRiJ7G0zeCqIHnULB50TG/3txQTtmeXwjvM6WLkwnReq3qpJ933nlHnXvttdd47bXXjjj3f//3f9x0000dEky4nl5/ugm3lWFkZT6NbrJSbSok/cMrGHHeG3hF91UdTwjxP/StuzFKKwAw9YvDFBupOJEQor3yNr3vOE5ImuEonStET2PoNkpyFh/3mh2rniUsYSKaydxFqYRom1ZNd9d1vVV/bDZbZ+cVTi58/IWMPOkZ3Gz2jafqzQdY9+XVVO9MV5xMCPG/rEsO73xrnpSmMIkQ4kTUVuRQlm9fiujhG0F4n1MUJxJCnYriDTTVlRz3mqa6/VQUb+iiREK0XbvqpAtxPIHDTiblz2/iafMHoNlcR8ZPt3Ig40fFyYQQh+h79mLk2jeY0iJCMA3uoziREKK98jMXOo7jhl0hG2KJHq25vqxDrxNChXZ10uvq6vjuu+944403eOmll47401avvfYavXv3xtPTk5SUlGNuSncsK1euxGKxkJyc3ObXFJ3Pt3ciqRe9j6/Nvh7dZmpmw9q/Ubxk4R88UgjRFaxLDq9FN08eJVNjhXBRTXWlFO38DgCLuy+9Bp2nNpAQirl7h3bodUKo0Ko16b+1YcMGzjjjDOrr66mrqyM4OJiysjK8vb0JDw/ntttua/Vzffzxx9xxxx289tprjB8/njfffJM///nPbN26lbi4uN99XFVVFTNnzmTq1Kns37+/rX8F0UU8w2NJmfkhmYuuo4JcDJNO1o6/01RTQvw5d6mOJ0SPpZdWoGfZd7nF3wfzyMFqAwkh2q1gy78wdHvZ015DLsTi7qM4kRBqBUWOwMMn/LhT3j18IgiKHNGFqYRomzaPpN95552cffbZHDhwAC8vL1avXk1eXh4pKSk8++yzbXqu559/nmuvvZbrrruOwYMH88ILLxAbG8vrr79+3MfdeOONXH755YwdO7at8UUXc/MNJPmaRYRbku0nNNhVtIidH85G13Wl2YToqWzL1oFhP7ZMSEGztPnzWiGEE7C21FO49VMANJOFuMTLFCcSQj3NZGbguFnHu4KB4/4qm8YJp9bmd2YbN27kzTffxGw2YzabaWpqok+fPjz99NNceeWVXHDBBa16nubmZjIyMpg9e/YR50877TRWrVr1u4979913yc7OZuHChTz66KN/+DpNTU00NTU52tXV1cDhzfCcma7rGIbh9Dn/iGZ2Y8iVb+Lx8X0U1P4MQH71YprmlzPoipcwu3koTqhGd7m/4vc54z02auuxrc2yN9zd0EYPd6p8rsYZ77HoWM58j/du+xJrk/19TUS/P+HmFeKUOZ2dM99j0T6hCZNIPOUpdv363BEj6h4+EfQfexehCZPkfncjrvI93JZ8be6ku7m5OdYuRkREkJ+fz+DBgwkICCA/P7/Vz1NWVobNZiMiIuKI8xERERQXFx/zMbt27WL27NksX74cSytHfp544olj1m8vLS2lsbGx1XlV0HWdqqoqDMPAZHL9Pf4Cpt6NscyfwoovQYP9LetpemcG0ac/gcnbT3W8Ltfd7q84mjPeY4+Vm/CyWgFoHNaPytpqqK1WnMp1OeM9Fh3LWe+xodvYs+nwPi++vc6gpOT4O1qLY3PWeyxOkE8i/ab+g9rSLKorCvEPisE3LBE0s3yvdDOu8j1cU1PT6mvb3EkfMWIE6enpDBgwgMmTJ/Pggw9SVlbGggULGDZsWFuf7qjNigzDOOYGRjabjcsvv5yHH36YAQMGtPr577vvPu666/D65+rqamJjYwkLC8Pf37/NebuSrutomkZYWJhT/4dri/CL/4b/kli27XoVQ9OpNOVi++F2hl/0Fh6h0arjdanueH/FkZztHhstLbRsPLgW3aThd/pJ+AcHqA3l4pztHouO56z3eH/2j7TU2/flCYkdR1w/KaPYXs56j0XHCAuLoLS0VO5vN+Yq38Oenp6tvrbNnfTHH3/c8SnAvHnzuPLKK7n55pvp168f7777bqufJzQ0FLPZfNSoeUlJyVGj62D/5CE9PZ0NGzbwl7/8BTg8tcFisfDjjz8yZcqUox7n4eGBh8fR06lNJpNT38RDNE1zmaytFT31Ktz9w9mc/gg2Uws15v2s/3QmyWe+gk/8ENXxulR3vL/iSM50j60Z26CuAQBT0iDMoUGKE3UPznSPRedwtntsGAb5mQsc7fikmU6TzVU52z0WHUvub/fnCve4Ldna3ElPTU11HIeFhfHdd9+19SkAcHd3JyUlhcWLF3P++ec7zi9evJhzzz33qOv9/f3ZvHnzEedee+01fv75Zz799FN69+7drhxCjdC0M0jxC2Xjz3fTbK6nwVxF+rc3kDzxaQKGjlMdT4hux9B1+4ZxB1kmyaibEK6qYl86NWXbAfALHURQdOofPEIIIYQrafeWviUlJezYsQNN0xg4cCBhYWFtfo677rqLGTNmkJqaytixY3nrrbfIz8/npptuAuxT1ffu3cv777+PyWQiMTHxiMeHh4fj6el51HnhGvwHjSLN9x3Wf3UzDeYKWswNrP/lThKr7yds7NEf1Agh2k/fko1RWgGAqV8cpthIxYmEEO2V9z+j6MdaJiiEEMJ1tXk+QHV1NTNmzKBXr15MnDiRk08+mejoaKZPn05VVVWbnmvatGm88MILPPLIIyQnJ/PLL7/w3XffER8fD0BRUVGbNqMTrscrpj9ply7CX7evR7eZrGRumsfeH99WnEyI7sW6ZK3j2Dx5lMIkQogTUXsgm/L8lQB4+kYR3meq4kRCCCE6Wps76ddddx1r1qzhm2++obKykqqqKr755hvS09O5/vrr2xzglltuYc+ePTQ1NZGRkcHJJ5/s+Nr8+fNZunTp7z527ty5bNy4sc2vKZyLe3AEI6/8gBBtIACGZrAt9w1yP39ccTIhugc9dy/Gnr0AaJGhmAbJ8iAhXFXepsOj6HHDL8dkavekSCGEEE6qzZ30b7/9lnfeeYfTTz8df39//Pz8OP3003n77bf59ttvOyOj6AEs3n4kXfMeUR6jHeeySz9j24I70G02hcmEcH3WpYfXopsnpcnUWCFcVFNdKcW7vwfA4u5H9KDz1AYSQgjRKdrcSQ8JCSEg4OiSPQEBAQQFyU7Bov1MFjcGz3yFhKAzHef21i9n87vXYGuqV5hMCNellx5AzzpYds3fB/PIwWoDCSHaLT/rQwzdCkDMkIuwuHkrTiSEEKIztLmT/sADD3DXXXdRVFTkOFdcXMysWbOYM2dOh4YTPY/JZKLfJY8wMOZqMOyjfaW2LNa/ewUtVeWK0wnhemzL0sGwH1smpKJZZGqsEK7I2lzH3q2fAaCZ3IhNnKY4kRBCiM7SqndrI0aMOGJ65K5du4iPjycuLg6A/Px8PDw8KC0t5cYbb+ycpKJHiT3zL7gvD2NL1nPoJhtVWj7piy5jxAVv4hkp62mFaA2jth7b2ix7w8MN87gktYGEEO22d/uXWJtrAYgacAYePm2vqiOEEMI1tKqTft5553VyDCGOFjFhGu5+YWxa9QBWcxN15nLWfX4Vyae/iF/fZNXxhHB6tpUbwGqfGmsePRzNy1NxIiFEe+i2Fgo2f+Boxw+foTCNEEKIztaqTvpDDz3U2TmEOKag5Cmk+L3Gxv/cQZO5hiZzLRn/uYWkcfMISpayM0L8HqO5BeuK9faGScNycqraQEKIdivJ+S+NtcUAhMZNwCdIZpQJIUR31uY16YdkZGSwcOFCFi1axIYNGzoykxBH8OubTNqF7+FjCwXAam5iw6/3sf+XjxUnE8J52dKzoK4BAFPSILTgozf8FEI4P8Mw2PObsmvxSTKKLoQQ3V2bdxAqKSnh0ksvZenSpQQGBmIYBlVVVUyePJmPPvqIsDBZIyU6nmdEPKnTP2TTB9dRqeWhm2xs3voMTTUlxJ15q+p4QjgVQ9ftG8YdZJmcpjCNEOJEVOxdR235DgD8w4YSGDVScSIhhBCdrc0j6bfeeivV1dVs2bKFAwcOUFFRQVZWFtXV1dx2222dkVEIANz8gxlx9QeEmRPtJzSDnYXz2f3xA+i6rjacEE5Ez9qNUVoBgKl/HKaYSMWJhBDtlbfpfcdxfNL0IzbyFUII0T21uZP+ww8/8PrrrzN48OFau0OGDOHVV1/l+++/79BwQvwvs4cnw65+hxifiY5zeyq/Z9t7t2BraVaYTAjnYV26znFsnjRKYRIhxImoKd9FeeGvAHj59SKs9xTFiYQQQnSFNnfSdV3Hzc3tqPNubm4ymim6hMlsZtD05+kbdpHjXFHzOjLfnYm1vkZhMiHU03P3YuzZC4AWGYppkGwwJYSrys88vBY9bvgVmExtXqUohBDCBbW5kz5lyhRuv/129u3b5zi3d+9e7rzzTqZOld22RdfpfcF9DOlzC5ph/29cbuwi473LaD5QrDiZEOpYl651HJsnpcnUWCFcVGPtfop3/wCAm0cA0QPPUZxICCFEV2lzJ/2VMC6bIgAAMxpJREFUV16hpqaGhIQE+vbtS79+/ejduzc1NTW8/PLLnZFRiN8Vfeq1DE9+ELNun91RYypi3UdXUF+wQ3EyIbqeXnIAPWuXveHvi3nkELWBhBDtVpD1EYZuAyBm6EWY3bwUJxJCCNFV2jxvKjY2lvXr17N48WK2b9+OYRgMGTKEU045pTPyCfGHwsaczUj/EDYuvYcWcwMN5krSv76W5CnP4T9otOp4QnQZ2y/pYNiPLRNS0CxmtYGEEO1iba6lcNtnAJjM7sQMnaY4kRBCiK7Upk661WrF09OTjRs3cuqpp3Lqqad2Vi4h2iRgyDhSfd9m4zf/R4O5imZzAxlLbmdYzYOEpp2hOp4Qnc6oqcO2Nsve8HDDPC5JbSAhRLvt3fYFtuY6AKL6n4mHd4jiREIIIbpSm6a7WywW4uPjsdlsnZVHiHbziRtM6iUL8dPt5aZsphY2ZTxE0U/z1QYTogtYV24AqxUA85gkNC9PxYmEEO2h21rI3/yhox2XNF1hGiGEECq0eU36Aw88wH333ceBAwc6I48QJ8QjNJqUGR8QTF8ADE1ny66X2fPlU4qTCdF5jOYWbCs32BsmDcvJqWoDCSHabX/2Yprq9gMQGj8Rn8AEtYGEEEJ0uTavSX/ppZfYvXs30dHRxMfH4+Pjc8TX169f32HhhGgPi28ASdcsZNuC/6O4ZT1osHv/v2haVEr/S5/CZJZ1uqJ7sa3LgroGAEzJg9CC/BUnEkK0h2EY5G1639FOSJqhMI0QQghV2txJP/fcc6Wkj3B6Zjd3hlz1Ju4f/4386h8BKKhdQtP86xg6/XXMHjIVWHQPhq5jW7bO0bZMGqUwjRDiRBwoXE3tAXuFBv/wRAIik9UGEkIIoUSbO+lz587thBhCdDyTycSAy57A8+sIdu5dCJpBiTWTlvlXMPzyt3HzC1YdUYgTpmftxiirBMDUPx5TTITaQEKIdsvLXOA4jk+aKYMiQgjRQ7V6TXp9fT3/93//R69evQgPD+fyyy+nrKysM7MJ0SHizr6DxEF3YdLt09wr2EPGgstpLClQnEyIE2ddutZxbJ6UpjCJEOJE1JTt4EDhGgC8/GMIT5ikNpAQQghlWt1Jf+ihh5g/fz5nnnkml156KYsXL+bmm2/uzGxCdJjISZeTPPoxLDZ3AGrNpaR/OpPa3EzFyYRoPz23EGPPPgC0yFBMg3orTiSEaK+8zIWO47jhV6CZZP8UIYToqVo93f3zzz/nn//8J5deeikA06dPZ/z48dhsNsyyEZdwAcEjTyXFN5iN/72TJnMdjeZq0r+/ieSTniBw+ETV8YRoM+uS36xFnzxKpsYK4aIaa4vZn/0fANw8A4gecLbiREIIIVRq9Uh6QUEBEyZMcLRHjRqFxWJh3759nRJMiM7gNyCF1PPm420LAcBqbmL9ylmUrPxMcTIh2kYvOYC+xb7BFP6+mEYMVhtICNFu+Zs/xNBtAMQMuQSzm5fiREIIIVRqdSfdZrPh7u5+xDmLxYLVau3wUEJ0Jq/oPqResYgAIwYA3WQjc/MTFHz/muJkQrSebdk6MOzHlpNT0Cwyo0kIV2RtqmHvti8AMJk9iE28RHEiIYQQqrV6urthGFx11VV4eHg4zjU2NnLTTTcdUSv9888/79iEQnQC94AwRlz5AVkLb6RM3waawY78f9L8SQm9L3wQk6nVn18J0eWMmjps67bYGx5umMcmqQ0khGi3wm2fY2upAyBqwFm4e0nlESGE6Ola3Um/8sorjzo3ffr0Dg0jRFeyePkw/Op32fHBXextWAVA7oGvaVpQxqAr/o7J4qY4oRDHZl25AQ7OYjKPSULz8lScSAjRHrqthYLNHx5sacQnyfsqIYQQbeikv/vuu52ZQwglTBY3Bk5/EY/PHyOn/EsA9jX+SvO7V5E4/S0sXj7HfwIhupjR3IJt5QZ7w6RhOTlVbSAhRLsV7/6BpvpSAMISJuEdEKc4kRBCCGcgc3pFj2cymehz0RwGJ9yIZth3xy7Tt7N+/uU0V5QoTifEkWzrsqCuAQBT8mC0IH/FiYQQ7WEYBnmZCxzt+KQZCtMIIYRwJtJJF+KgXqffwLDhf8Ok2yeYVJsKSf/wChr2ZStOJoSdoevYlv627FqawjRCiBNRXvArdQfsv18CIpIIjJS9JYQQQthJJ12I3wgfdz4jT3oGN5t9jW+9+QDrvrya6p3pipMJAXrWbozySgBMA+Ix9YpQG0gI0W55m953HMtadCGEEL8lnXQh/kfgsJNJ+fObeNrs04ibzXVk/HQrBzJ+UJxM9GSGYWBdstbRNk8apTCNEOJEVJduo2KffVaMd0AcYfETFScSQgjhTKSTLsQx+PZOJO3iBfjawgGwmZrZsHYOxUsWKk4meipjz16MvH0AaFFhmAYmqA0khGi3365Fjxt+BZrJrDCNEEIIZyOddCF+h0dYDCkzPySI3gAYJp2sHX8n76vnFScTPdFvR9Etk9LQNE1hGiFEezXUFFGS/V8A3DyDiBpwluJEQgghnI100oU4DjffQJKvWUS4Jdl+QoNdRYvY+cG96LquNJvoOfSScvQtu+2NAF9MIwarDSSEaLf8zR9gGDYAYhMvwWzxVJxICCGEs5FOuhB/wOzmQeLVbxPrd4rjXH7Nf9k6/0ZsLU0Kk4mewrY0HQz7sWVCCppFpsYK4YpamqrZt+0LAEwWD2KGXKw4kRBCCGcknXQhWsFkMjHw8qfoF3mZo7NU3LKeje9Mx1pbpTac6NaMmjps6Vn2hoc75rHJSvMIIdpv79bPsFkbAIgecA7uXkGKEwkhhHBG0kkXog0Szv0rQwfcjmbYv3UqyCFjwWU0le1VnEx0V9aVG8BqnxprHpuE5uWhOJEQoj10WzP5WR8ebGnEDb9CaR4hhBDOSzrpQrRR1JSZJKc8jFl3A6DGtJ/0f82gLm+r4mSiuzGaW7Ct3GBvmExYJqSoDSSEaLfiXT/QXF8OQHjvKXgHxCpOJIQQwllJJ12IdghJO4OUyS/hbvMGoMFcRfq3N1C1ZYXiZKI7sa3Lgjr71FhT8iC0IH/FiYQQ7WEY+hFl1+KTZihMI4QQwtlJJ12IdvIfNIq0c97By2ZfU9hibmD9L3dT+uu/FScT3YGh69iWrnO0LZPTFKYRQpyI8oJV1FXkABAYmUxAxDDFiYQQQjgz6aQLcQK8YvqTduki/PVoAGwmK5mb5rH3x7cVJxOuTt+8C6O8EgDTgHhMvSLUBhJCtFvepvcdx/H/396dh0dRJ2gcf6u7c0Ag4cpB5AogIYY7YYZkRGFWQFBWHUbAEUFAZ9kBUTO4Cs7OgMrljiyjowgu5zoqMw+Ko4sHIogcCsQEFDCcciZEEEgESUh37R9NGpqEK+mkKs3388CTrl9Xd72dH0fequrqjkMtTAIAqAko6UAlhTaIVZdhb6ihkShJMg1T2/e+qj1LJvNZ6qgQ0zRVsnKDb9nZ82cWpgFQGQXfb9Pxw5mSpNr1mqtR8+4WJwIA2B0lHQgAV+266jhioeLDfu4b23P0beX87XF53G4Lk6EmMvcekrk/V5JkNI6Wo00LawMBqLB9my94L3qHITIMfvQCAFwe/1MAAeJwhajt0L8qof6dvrFDp9fo6/nD5S46bWEy1DQlq84fRXf1/JkMw7AwDYCK+qngkI7s+USSFFqrgeJuvMPiRACAmoCSDgSQw+FQq4GTlNhkuGR6i9X37q36av79OnvymMXpUBN48o/Js3WXdyGqjhyd2lobCECF7f/6Dcn0vu2pSbtBcrrCLE4EAKgJKOlAFWh6xxi1b/ekHB6nJOmksV+b/nafzuTttTgZ7M69apNkem+7uqfIcDmtDQSgQs6eOalD3y6VJDlc4Wpy06+tDQQAqDEo6UAVib35XnVOny6X23vk5JTzmDa+/aAKd2XJU3JWxzYuU+GX/9CxjcvkKTlrcVrYgVl4Su5N33gXwkLlTOtkaR4AFXdw2xJ5Ss5IkuIT71JoeD1rAwEAagyX1QGAYFa/Y0+l1nlVWR+NVZGzUEXOH7Xx41FyyqWzTu8Pb/vypNDM59Um6beK6znE4sSwUsmar6QS74UGnWkdZdTi1FigJnKXFOnAN295FwyHmnX4jbWBAAA1CkfSgSpWp1UHdR2wUBHuRpIkj7NEZx1n/NYpdpzSNzn/rbyVr1sRETZgFhXLvTbLu+BwyNU9xdpAACosb+cyFf/kvQ5JTMIvVTuyicWJAAA1CSUdqAbhsc3VZfAiGZ5zf+Uuvlj3ueUd2+dw6vt1yr3xG+m0d+eNo3NbGfUjLU4EoCJM06N9W87vcG3ecaiFaQAANRElHagmhbszZTo8l17BkIqdp3Rs0wfVFwq2YHo8cn+2ybfs6vEzC9MAqIyj+9bo9InvJEn1GqcoKibZ2kAAgBqH96QD1aT4RO5Vrbcl6xnVyZqjurVbqG7MTYpKSFVEq05yhoRWcUJYxfP1TpnHTkiSHG1ayHFDjLWBAFTYvi3/67vdvCPXGQEAXDtKOlBNQus1vqr1TIepQuWq8EyutH+9tH+uHCudilCM6tZOUN2YJIp7EDFNUyUrN/iWnT27WpgGQGWczP9GJ3K/kiRF1EtQo2Y3W5wIAFATUdKBatKgcy+FbpymYsepsu9JlyRTcphOhZi1VeQs9LvL43BfUNzXUdyDiLn3oMz93rMsjMbRcrRpYW0gABW2b/P5o+jNOg6RYfCuQgDAtaOkA9XE4QpRm6Tf6puc/5ZM+Rd10/vlprZjFddziIpPfq+Cb79UwcFsFR7focLiAzrjLPB7vqsq7i1TFdGS4m5nJSs3+m67ev5MhlHeHhwAdne64KDy934qSQqt3VCNb+xncSIAQE1FSQeqUennoO/YPkfFzlO+8VBPhN/npIdGRavRz+9Uo5/f6VvHv7jnqLD44NUV90/PF/fI2GRFJqQoomVHirsNeI4ck2frLu9CVB05Ore1NhCACtu/5W+S6b04aNN2g+Vw8m8sAKBiKOlANYvrOUQx3QfpWOZHOpG3V/XiEtQwpY8crpDLPq7c4n48XydzvlThwWwVnthxxeJ+eN86ad9rvuIeGZGgujEUd6u4P7vgKPotqTKcTgvTAKio4jMndDjnXUmS01VLTW76tcWJAAA1GSUdsIDDFaKGXfvJnZ+vhjExcjgq9r7F0Poxiu7WX9Hq7xu7puL+U650QXGvo1jVjWhxvri36iini+JeFczCU3Jv2updCA+VM62jtYEAVNjBrf+Qp6RIkhTf9m6FhEVanAgAUJNR0oEgU5niXqDDKvjp8KWLe8tU1WnZ8YpH/XFlJWu+kkrckiRnt44ywsMsTgSgItwlRTrwzWJJkmE41azDbyxOBACo6SjpwHXgSsW94PgO/Xj2Kov7itLinqC6MTdR3CvALCqWe22Wd8HhkKt7irWBAFRY7o7/09kzxyVJMS1vU6268RYnAgDUdJR04DpVbnH/4YhO7vhChQe3XGVxX1umuEfG3KS6FPfLcm/4Rjp9RpLk6JIkoz6nxgI1kWl6tH/L677l5h0fsDANACBYUNIB+IQ2iFV0t7sUrbt8Y9da3A9R3C/L9Hj8Lxh3a1cL0wCojO/3rdbpk/skSfXjUxUZnWRxIgBAMKCkA7isKxf3nHPFvdDvcVcs7rHJqpuQct0Vd8+WHTJ/OClJciS2kOOGGIsTAaiofZsX+W437zjUwiQAgGBCSQdwzS5X3AsOblah74j7ZYr7d2ul7yTHCpfqKMZX3CMTUhXRskNQFnfTNFWycoNv2dnjZxamAVAZJ/K26GTeZklSRP1Watg03eJEAIBgQUkHEBDlF/c8ncz5UgWHLlfcSy4q7nMuKu7tzn2Oe80v7uaegzIP5EmSjPgYOdo0tzgRgIrat+V/fbebdxwiwzAsTAMACCaUdABVJrRBnKLTAlHcLzzi3tJ7xL1lqiIS2teo4l6y6vxRdFePrvxQD9RQp08e0Pd7V0qSQms3Ulzr2y1OBAAIJpR0ANXqysU9R4VnD6nossV9jfTdbDk8Lr/3uNu5uHuOHJNn627vQr26cnRua20gABXmvaK7KUlq1v4+OZyh1gYCAAQVSjoAy122uB/MVuGJHZcp7odU8NOhssW9ToIiY5IV2aqrIlq0s7y4u1ddcEX3W1JkOJ0WpgFQUcU/HdfhnPckSc6Q2rohaYDFiQAAwYaSDsCWLl3c16vg4JYrF/fT9inuZsEpuTdt9S6Eh8rZrWO1bBdA4B3c+g953EWSpBva3qOQsLoWJwIABBtKOoAaw1vc71G07vGNlS3uB1Xk/NHvcVdf3NvL4Qr8P4vutVmS2y1JcqZ1khEeFvBtAKh67rM/6cDWxZIkw3CqWfv7LE4EAAhGlHQANVp5xb3o6GEV7PzSW9yP71BhSQWKe2yyIlsGoLgXn5VnXbb3tsMhV/eUij8XAEvl7vw/nT1zQpIU26qXwus2tjYQACAoUdIBBJ2wRvGKbnSp4r5Zhcd3Xrm4710j7Z0tp8elCMWqbp2Wioy9yVvcEzrIcYX3lHtKzupY5kc6vSVTUqGi1EjOLsky6nFqLFATmR639m1+3bfcrOMDFqYBAAQzSjqA68KlivvJHV+q8NCli7vbr7h/Xk5xL72q/Pninrfyde3YPkfFzlPeJ2kmhZ6tpRvrNhDH3YCa6ft9n+mnggOSpAY3/EyRjfiEBgBA1aCkA7huhTWKV0yjexRT6eIuX3F3OWrpB3OX5PDfVrHrJ209NFvGytqK6zmkOl4egADyfuyaV3OOogMAqhAlHQAuUH5xP6STOzao8NBmFRzfoR9LDl2yuEuSjHKe2JBkStu+fVFHzV1yhYTL4QqVwxkmh/PcV1eonM4w3+3ScaczVA7Xhet5bzvPPdZw8HFuVjA9bh0//JVO5O1WSEkrNYjvwlwEqVPHtqkg/2tJUp0GrdWgSZrFiQAAwYySDgBXENbohksX94PZKjixs9ziXoYheQy38na+F9B8hsPpX/adoXK6ws6PXbQzwOk3Fua77bxgZ4H/c4WfW75oZ8G5McMob69EcMvf86ly1v2Xik7lS5IOSgqLiFFi+hOKaflLa8MhYEp3xOR+Pcc31qzjA9fln3kAQPWhpANABZRX3Pe9N1M7D/9vtWcxPW65PaflPnu62rct6aICf6mdAefPAnBeWPKveDZB6VkD5ewgcIXJ4aj+/8by93yqLcufKDNedCpfW5Y/oQ69/ouiHgQu3hEjSTIccjhCrQsFALguUNIBIEAi4tpIh6+8XtukMYpM6iaPu0iekmJ53EVyu71fPe7ic2NnvGMlF4z71j8/5i4pKvd+t7tInpIiSWaVv27vtoul4iucSVAFDMNZftk/N+Z0hl/F2QTndzBc6mwC786FMBmGU9+uff6ymXLW/VnRLW7l1Pca7FI7YmR69M2K8XI4XOyIAQBUGUo6AARIg869FLpxmoodp8p/X7ophXoiFJ8+RA5XSJXnMU1TpqfkgvJfujOg2G8HQemyt/BfvLPggh0IvvW9t/12LFy0A8FdUiTTc7YaXqNb7pKf5C75qcq3dbWKTh3RygU95HSEnPtzYEgyZBgO37IhQzK8v41z9/tuG6Xrl45Lhhy+ce8vQzIc59bT+ec/d//5bTjOr1+6jdLn8duG/7Yv3mbpNmQ4zq139du48LmrYxv+31Od/75cZhvex5ybH9PU7o2zLjvH7IgBAFQlSjoABIjDFaI2Sb/VNzn/7T2AfWFRP3dAu03Sb6uloEuSYRgynCFyOKtnexczTY/vKLvHtwOg9Ci/f+Ev/2yCy58t4Lu/3B0IRZLpseR1S5Ln7GlZt3VUtaJTR3Q8L0sN4lOtjgIACEKUdAAIoNKPV/P7nHR5j6C3SfrtdfXxa4bhkNMVLqcrXAqr/u17PCVldhB4Ss5c/myCC95u4HdmwLmvZwqP6OSR7CtuOywiRg5nmLx7Z0zvL9MjyZR5blkyfWPnl01JnnNDpmSaMs8ty7fs/Xp+/Qtu+5ZR1YpPH7U6AgAgSFHSASDA4noOUUz3QTqW+ZFO5O1VvbgENUzpU21H0OHlcLi8F5YLqR2w5zQ9bq15407/i4ldJCwiVjf/5j3LT4X2lnbPBaVe8u0YOLfsvc9zrvx7R2SW7jwoXd9UeTsILly+cBvndzyUXTbLyXB+B4PnfO5ydjycz3outy/r+fXLz3RuJ4k855/Lt9Oj9PtzPtep43u1b/PCK35/Q2s3uvZJAQDgKlDSAaAKOFwhati1n9z5+WoYEyOHw2F1JASA4XAqMf2J8i8q5l1DienjLC/okvftDjKc5V4eAZdmetzK2/XBFXfE1I/rXI2pAADXE35qBADgGsS0/KU69PovhUXE+I2HRcSqQ6/nuep3DVe6I+Yya9hmRwwAIDhxJB0AgGsU0/KXim5xq344/JW+z9ut6LhWahDfheIWJEp3xFz8OelhEbFKTB/HjhgAQJWipAMAUAGGw6n68Sk662qq+jExMnhLQ1BhRwwAwCqUdAAAgHKwIwYAYAX+twEAAAAAwCYo6QAAAAAA2AQlHQAAAAAAm6CkAwAAAABgE5R0AAAAAABsgpIOAAAAAIBNUNIBAAAAALAJSjoAAAAAADZBSQcAAAAAwCYo6QAAAAAA2AQlHQAAAAAAm6CkAwAAAABgE5R0AAAAAABsgpIOAAAAAIBNUNIBAAAAALAJSjoAAAAAADZBSQcAAAAAwCYo6QAAAAAA2AQlHQAAAAAAm6CkAwAAAABgE5R0AAAAAABsgpIOAAAAAIBNUNIBAAAAALAJSjoAAAAAADZBSQcAAAAAwCYsL+mvvPKKEhISFB4erpSUFH3++eeXXPftt99Wr169FB0drcjISKWlpemjjz6qxrQAAAAAAFQdS0v64sWL9dhjj+npp59WVlaWunfvrr59+2r//v3lrr969Wr16tVLy5YtU2Zmpnr27Kn+/fsrKyurmpMDAAAAABB4lpb0GTNmaOTIkXrooYeUlJSkmTNnqmnTppo1a1a568+cOVP/8R//oa5du+rGG2/UlClTdOONN+q9996r5uQAAAAAAASey6oNFxcXKzMzU0899ZTfeO/evbVu3bqreg6Px6PCwkI1aNDgkusUFRWpqKjIt1xQUOB7rMfjqUDy6uPxeGSapu1zomKY3+DHHAc/5jj4McfBjzkObsxv8Kspc3wt+Swr6UePHpXb7VZsbKzfeGxsrPLy8q7qOV544QWdOnVKAwcOvOQ6U6dO1aRJk8qMf//99zpz5sy1ha5mHo9HJ0+elGmacjgsv3wAAoz5DX7McfBjjoMfcxz8mOPgxvwGv5oyx4WFhVe9rmUlvZRhGH7LpmmWGSvPm2++qYkTJ+rdd99VTEzMJdcbP368MjIyfMsFBQVq2rSp7+JzdubxeGQYhqKjo239Bw4Vw/wGP+Y4+DHHwY85Dn7McXBjfoNfTZnj8PDwq17XspLeqFEjOZ3OMkfN8/Pzyxxdv9jixYs1cuRI/eMf/9Btt9122XXDwsIUFhZWZtzhcNh6EksZhlFjsuLaMb/BjzkOfsxx8GOOgx9zHNyY3+BXE+b4WrJZ9ipCQ0OVkpKi5cuX+40vX75c6enpl3zcm2++qQcffFBvvPGG7rjjjqqOCQAAAABAtbH0dPeMjAw98MADSk1NVVpamubMmaP9+/dr1KhRkrynqh86dEiLFi2S5C3oQ4cO1V/+8hd169bNdxS+Vq1aioqKsux1AAAAAAAQCJaW9EGDBunYsWN65plnlJubq3bt2mnZsmVq3ry5JCk3N9fvM9Nnz56tkpISjR49WqNHj/aNDxs2TAsWLKju+AAAAAAABJTlF4773e9+p9/97nfl3ndx8V61alXVBwIAAAAAwCL2fWc9AAAAAADXGUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOkAAAAAANgEJR0AAAAAAJugpAMAAAAAYBOUdAAAAAAAbIKSDgAAAACATVhe0l955RUlJCQoPDxcKSkp+vzzzy+7/meffaaUlBSFh4erZcuWevXVV6spKQAAAAAAVcvSkr548WI99thjevrpp5WVlaXu3burb9++2r9/f7nr7927V/369VP37t2VlZWlCRMmaOzYsVqyZEk1JwcAAAAAIPAsLekzZszQyJEj9dBDDykpKUkzZ85U06ZNNWvWrHLXf/XVV9WsWTPNnDlTSUlJeuihhzRixAj9+c9/rubkAAAAAAAEnsuqDRcXFyszM1NPPfWU33jv3r21bt26ch+zfv169e7d22+sT58+mjt3rs6ePauQkJAyjykqKlJRUZFv+eTJk5KkEydOyOPxVPZlVCmPx6OCggKFhobK4bD8nQkIMOY3+DHHwY85Dn7McfBjjoMb8xv8asocFxQUSJJM07ziupaV9KNHj8rtdis2NtZvPDY2Vnl5eeU+Ji8vr9z1S0pKdPToUTVu3LjMY6ZOnapJkyaVGW/evHkl0gMAAAAAcG0KCwsVFRV12XUsK+mlDMPwWzZNs8zYldYvb7zU+PHjlZGR4Vv2eDz64Ycf1LBhw8tuxw4KCgrUtGlTHThwQJGRkVbHQYAxv8GPOQ5+zHHwY46DH3Mc3Jjf4FdT5tg0TRUWFio+Pv6K61pW0hs1aiSn01nmqHl+fn6Zo+Wl4uLiyl3f5XKpYcOG5T4mLCxMYWFhfmP16tWreHALREZG2voPHCqH+Q1+zHHwY46DH3Mc/Jjj4Mb8Br+aMMdXOoJeyrKT9kNDQ5WSkqLly5f7jS9fvlzp6enlPiYtLa3M+h9//LFSU1PLfT86AAAAAAA1iaXvrM/IyND//M//aN68edq+fbsef/xx7d+/X6NGjZLkPVV96NChvvVHjRqlffv2KSMjQ9u3b9e8efM0d+5cjRs3zqqXAAAAAABAwFj6nvRBgwbp2LFjeuaZZ5Sbm6t27dpp2bJlvou65ebm+n1mekJCgpYtW6bHH39cL7/8suLj4/Xiiy9qwIABVr2EKhUWFqY//elPZU7XR3BgfoMfcxz8mOPgxxwHP+Y4uDG/wS8Y59gwr+Ya8AAAAAAAoMrZ94PkAAAAAAC4zlDSAQAAAACwCUo6AAAAAAA2QUkHAAAAAMAmKOk2tHr1avXv31/x8fEyDENLly61OhICaOrUqeratavq1q2rmJgY3X333crJybE6FgJo1qxZ6tChgyIjIxUZGam0tDR98MEHVsdCFZk6daoMw9Bjjz1mdRQEyMSJE2UYht/vuLg4q2MhwA4dOqQhQ4aoYcOGql27tjp16qTMzEyrYyFAWrRoUebvsWEYGj16tNXRECAlJSX6wx/+oISEBNWqVUstW7bUM888I4/HY3W0SrP0I9hQvlOnTqljx44aPnx40H683PXss88+0+jRo9W1a1eVlJTo6aefVu/evbVt2zZFRERYHQ8B0KRJE02bNk2tW7eWJC1cuFB33XWXsrKylJycbHE6BNLGjRs1Z84cdejQweooCLDk5GR98sknvmWn02lhGgTa8ePH9Ytf/EI9e/bUBx98oJiYGO3evVv16tWzOhoCZOPGjXK73b7lb775Rr169dK9995rYSoE0vTp0/Xqq69q4cKFSk5O1qZNmzR8+HBFRUXp0UcftTpepVDSbahv377q27ev1TFQRT788EO/5fnz5ysmJkaZmZm65ZZbLEqFQOrfv7/f8uTJkzVr1ix98cUXlPQg8uOPP+r+++/Xa6+9pueee87qOAgwl8vF0fMgNn36dDVt2lTz58/3jbVo0cK6QAi46Ohov+Vp06apVatWuvXWWy1KhEBbv3697rrrLt1xxx2SvH+H33zzTW3atMniZJXH6e6AxU6ePClJatCggcVJUBXcbrfeeustnTp1SmlpaVbHQQCNHj1ad9xxh2677Taro6AK7Ny5U/Hx8UpISNDgwYO1Z88eqyMhgP75z38qNTVV9957r2JiYtS5c2e99tprVsdCFSkuLtbrr7+uESNGyDAMq+MgQG6++WatWLFCO3bskCRt3rxZa9asUb9+/SxOVnkcSQcsZJqmMjIydPPNN6tdu3ZWx0EAff3110pLS9OZM2dUp04dvfPOO7rpppusjoUAeeutt/TVV19p48aNVkdBFfj5z3+uRYsWqU2bNjpy5Iiee+45paena+vWrWrYsKHV8RAAe/bs0axZs5SRkaEJEyZow4YNGjt2rMLCwjR06FCr4yHAli5dqhMnTujBBx+0OgoC6Mknn9TJkyfVtm1bOZ1Oud1uTZ48Wffdd5/V0SqNkg5YaMyYMdqyZYvWrFljdRQEWGJiorKzs3XixAktWbJEw4YN02effUZRDwIHDhzQo48+qo8//ljh4eFWx0EVuPAtZ+3bt1daWppatWqlhQsXKiMjw8JkCBSPx6PU1FRNmTJFktS5c2dt3bpVs2bNoqQHoblz56pv376Kj4+3OgoCaPHixXr99df1xhtvKDk5WdnZ2XrssccUHx+vYcOGWR2vUijpgEUeeeQR/fOf/9Tq1avVpEkTq+MgwEJDQ30XjktNTdXGjRv1l7/8RbNnz7Y4GSorMzNT+fn5SklJ8Y253W6tXr1af/3rX1VUVMRFxoJMRESE2rdvr507d1odBQHSuHHjMjtNk5KStGTJEosSoars27dPn3zyid5++22royDAnnjiCT311FMaPHiwJO9O1X379mnq1KmUdADXxjRNPfLII3rnnXe0atUqJSQkWB0J1cA0TRUVFVkdAwHwL//yL/r666/9xoYPH662bdvqySefpKAHoaKiIm3fvl3du3e3OgoC5Be/+EWZjz/dsWOHmjdvblEiVJXSC/SWXlwMweP06dNyOPwvseZ0OvkINlSNH3/8Ubt27fIt7927V9nZ2WrQoIGaNWtmYTIEwujRo/XGG2/o3XffVd26dZWXlydJioqKUq1atSxOh0CYMGGC+vbtq6ZNm6qwsFBvvfWWVq1aVebK/qiZ6tatW+YaEhEREWrYsCHXlggS48aNU//+/dWsWTPl5+frueeeU0FBQY0/MoPzHn/8caWnp2vKlCkaOHCgNmzYoDlz5mjOnDlWR0MAeTwezZ8/X8OGDZPLRe0JNv3799fkyZPVrFkzJScnKysrSzNmzNCIESOsjlZphmmaptUh4G/VqlXq2bNnmfFhw4ZpwYIF1R8IAXWpq4rOnz+fC5oEiZEjR2rFihXKzc1VVFSUOnTooCeffFK9evWyOhqqSI8ePdSpUyfNnDnT6igIgMGDB2v16tU6evSooqOj1a1bNz377LNcUyLIvP/++xo/frx27typhIQEZWRk6OGHH7Y6FgLo448/Vp8+fZSTk6M2bdpYHQcBVlhYqP/8z//UO++8o/z8fMXHx+u+++7TH//4R4WGhlodr1Io6QAAAAAA2ASfkw4AAAAAgE1Q0gEAAAAAsAlKOgAAAAAANkFJBwAAAADAJijpAAAAAADYBCUdAAAAAACboKQDAAAAAGATlHQAAAAAAGyCkg4AAGqs7777ToZhKDs72+ooAAAEBCUdAIAAW7dunZxOp26//fYy902cOFGdOnUqM24YhpYuXVr14a7Sgw8+qLvvvvuq1jMMQ9OmTfMbX7p0qQzDqKJ0AAAEL0o6AAABNm/ePD3yyCNas2aN9u/fb3WcKhceHq7p06fr+PHjVkcJmOLiYqsjAACuU5R0AAAC6NSpU/r73/+uf//3f9edd96pBQsW+O5bsGCBJk2apM2bN8swDBmGoQULFqhFixaSpHvuuUeGYfiWJem9995TSkqKwsPD1bJlS02aNEklJSW++w3D0OzZs3XnnXeqdu3aSkpK0vr167Vr1y716NFDERERSktL0+7du32PKT2aP3v2bDVt2lS1a9fWvffeqxMnTvjuX7hwod59911fzlWrVl3yNd92222Ki4vT1KlTL7lOeWcQzJw50++1lh69nzJlimJjY1WvXj3f633iiSfUoEEDNWnSRPPmzSvz/N9++63S09MVHh6u5OTkMnm3bdumfv36qU6dOoqNjdUDDzygo0eP+u7v0aOHxowZo4yMDDVq1Ei9evW65GsBAKAqUdIBAAigxYsXKzExUYmJiRoyZIjmz58v0zQlSYMGDdLvf/97JScnKzc3V7m5uRo0aJA2btwoSZo/f75yc3N9yx999JGGDBmisWPHatu2bZo9e7YWLFigyZMn+23z2Wef1dChQ5Wdna22bdvqN7/5jf7t3/5N48eP16ZNmyRJY8aM8XvMrl279Pe//13vvfeePvzwQ2VnZ2v06NGSpHHjxmngwIG6/fbbfTnT09Mv+ZqdTqemTJmil156SQcPHqzU9+/TTz/V4cOHtXr1as2YMUMTJ07UnXfeqfr16+vLL7/UqFGjNGrUKB04cMDvcU888YR+//vfKysrS+np6frXf/1XHTt2TJKUm5urW2+9VZ06ddKmTZv04Ycf6siRIxo4cKDfcyxcuFAul0tr167V7NmzK/U6AACoKEo6AAABNHfuXA0ZMkSSdPvtt+vHH3/UihUrJEm1atVSnTp15HK5FBcXp7i4ONWqVUvR0dGSpHr16ikuLs63PHnyZD311FMaNmyYWrZsqV69eunZZ58tUyCHDx+ugQMHqk2bNnryySf13Xff6f7771efPn2UlJSkRx99tMyR5TNnzmjhwoXq1KmTbrnlFr300kt66623lJeXpzp16qhWrVoKCwvz5QwNDb3s677nnnvUqVMn/elPf6rU969BgwZ68cUXlZiYqBEjRigxMVGnT5/WhAkTdOONN2r8+PEKDQ3V2rVr/R43ZswYDRgwQElJSZo1a5aioqI0d+5cSdKsWbPUpUsXTZkyRW3btlXnzp01b948rVy5Ujt27PA9R+vWrfX8888rMTFRbdu2rdTrAACgoijpAAAESE5OjjZs2KDBgwdLklwulwYNGlTu6dlXIzMzU88884zq1Knj+/3www8rNzdXp0+f9q3XoUMH3+3Y2FhJUvv27f3Gzpw5o4KCAt9Ys2bN1KRJE99yWlqaPB6PcnJyKpRVkqZPn66FCxdq27ZtFX6O5ORkORznfzyJjY31ey1Op1MNGzZUfn6+3+PS0tJ8t10ul1JTU7V9+3ZJ3u/jypUr/b6PpSX8wrcBpKamVjg3AACB4rI6AAAAwWLu3LkqKSnRDTfc4BszTVMhISE6fvy46tevf03P5/F4NGnSJP3qV78qc194eLjvdkhIiO926RXVyxvzeDyX3FbpOpW5Ivstt9yiPn36aMKECXrwwQf97nM4HL7T/kudPXu2zHNcmLs0T3ljl3stF64neV93//79NX369DLrNG7c2Hc7IiLiis8JAEBVo6QDABAAJSUlWrRokV544QX17t3b774BAwbob3/7m8aMGaPQ0FC53e4yjw8JCSkz3qVLF+Xk5Kh169YBz7t//34dPnxY8fHxkqT169fL4XCoTZs2knTJnFcybdo0derUyfc8paKjo5WXlyfTNH3lOZCfbf7FF1/olltukeSdi8zMTN/78Lt06aIlS5aoRYsWcrn40QcAYG+c7g4AQAC8//77On78uEaOHKl27dr5/f71r3/te390ixYttHfvXmVnZ+vo0aMqKiryja9YsUJ5eXm+jzL74x//qEWLFmnixInaunWrtm/frsWLF+sPf/hDpfOGh4dr2LBh2rx5sz7//HONHTtWAwcOVFxcnC/Pli1blJOTo6NHj5Z71Ls87du31/3336+XXnrJb7xHjx76/vvv9fzzz2v37t16+eWX9cEHH1T6dZR6+eWX9c477+jbb7/V6NGjdfz4cY0YMUKSNHr0aP3www+67777tGHDBu3Zs0cff/yxRowYUaEdEQAAVCVKOgAAATB37lzddtttioqKKnPfgAEDlJ2dra+++koDBgzQ7bffrp49eyo6OlpvvvmmJOmFF17Q8uXL1bRpU3Xu3FmS1KdPH73//vtavny5unbtqm7dumnGjBlq3rx5pfO2bt1av/rVr9SvXz/17t1b7dq10yuvvOK7/+GHH1ZiYqJSU1MVHR1d5kJtl/Pss8+WObU9KSlJr7zyil5++WV17NhRGzZs0Lhx4yr9OkpNmzZN06dPV8eOHfX555/r3XffVaNGjSRJ8fHxWrt2rdxut/r06aN27drp0UcfVVRUlN/73wEAsAPDvPh/UQAAENQmTpyopUuXBvR0cwAAEBjsPgYAAAAAwCYo6QAAAAAA2ASnuwMAAAAAYBMcSQcAAAAAwCYo6QAAAAAA2AQlHQAAAAAAm6CkAwAAAABgE5R0AAAAAABsgpIOAAAAAIBNUNIBAAAAALAJSjoAAAAAADbx/0lAkNqy63AlAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Example 3: Learning progression visualization\n", "print(\"\\nExample 3: Learning Progression Visualization\")\n", "\n", "# Create different learning scenarios\n", "scenarios = {\n", "    'Quick Learner': [0, 0, 1, 1, 1, 1, 1, 1],\n", "    'Slow Learner': [0, 0, 0, 0, 1, 0, 1, 1],\n", "    'Inconsistent': [1, 0, 1, 0, 1, 0, 1, 1],\n", "    'Expert': [1, 1, 1, 1, 1, 1, 1, 1]\n", "}\n", "\n", "plt.figure(figsize=(12, 8))\n", "for scenario_name, history in scenarios.items():\n", "    probas = models['bkt'].predict_proba(history, skill_example)\n", "    plt.plot(range(1, len(probas) + 1), probas, marker='o', label=scenario_name, linewidth=2)\n", "\n", "plt.xlabel('Attempt Number')\n", "plt.ylabel('Probability of Knowing Skill')\n", "plt.title(f'Learning Progression for Skill: {skill_example}')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.ylim(0, 1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 22, "id": "b6038e4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example 4: Interactive Prediction Function\n", "Prediction result:\n", "  skill: Range\n", "  history: [0, 1, 0, 1, 1]\n", "  knowledge_progression: ['0.602', '0.327', '0.800', '0.416', '0.848']\n", "  current_knowledge_probability: 0.848\n", "  next_response_probability: 0.848\n", "  recommendation: <PERSON><PERSON> mastered\n"]}], "source": ["# Example 4: Interactive prediction function\n", "def predict_next_response(user_history, skill_name, model=models['bkt']):\n", "    \"\"\"\n", "    Predict the probability of correct response for the next attempt\n", "    \n", "    Args:\n", "        user_history: List of 0s and 1s representing incorrect/correct responses\n", "        skill_name: Name of the skill\n", "        model: Trained BKT model\n", "    \n", "    Returns:\n", "        Dictionary with prediction details\n", "    \"\"\"\n", "    if skill_name not in model.skill_params:\n", "        return {\"error\": f\"Skill '{skill_name}' not found in trained model\"}\n", "    \n", "    probas = model.predict_proba(user_history, skill_name)\n", "    \n", "    if not probas:\n", "        return {\"error\": \"Could not generate predictions\"}\n", "    \n", "    current_knowledge = probas[-1]\n", "    \n", "    return {\n", "        \"skill\": skill_name,\n", "        \"history\": user_history,\n", "        \"knowledge_progression\": probas,\n", "        \"current_knowledge_probability\": current_knowledge,\n", "        \"next_response_probability\": current_knowledge,\n", "        \"recommendation\": \"Continue practicing\" if current_knowledge < 0.8 else \"Skill mastered\"\n", "    }\n", "\n", "# Test the function\n", "print(\"\\nExample 4: Interactive Prediction Function\")\n", "test_history = [0, 1, 0, 1, 1]\n", "skill_example = list(models['bkt'].skill_params.keys())[11]\n", "result = predict_next_response(test_history, skill_example)\n", "\n", "print(f\"Prediction result:\")\n", "for key, value in result.items():\n", "    if key == 'knowledge_progression':\n", "        print(f\"  {key}: {[f'{p:.3f}' for p in value]}\")\n", "    elif isinstance(value, float):\n", "        print(f\"  {key}: {value:.3f}\")\n", "    else:\n", "        print(f\"  {key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "id": "e36615f4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8ceb7a8c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}