2025-07-19 22:36:30,751 - INFO - Loading dataset...
2025-07-19 22:36:32,041 - INFO - Dataset loaded: (401756, 30)
2025-07-19 22:36:32,042 - INFO - Cleaning data...
2025-07-19 22:36:32,255 - INFO - Data cleaned: (325637, 30)
2025-07-19 22:36:32,275 - INFO - Train set: (260509, 30)
2025-07-19 22:36:32,276 - INFO - Test set: (65128, 30)
2025-07-19 22:36:36,214 - INFO - Loading pre-trained models...
2025-07-19 22:36:36,224 - INFO - ✅ BKT model loaded
2025-07-19 22:36:37,775 - INFO - ✅ PFA model loaded and reconstructed
2025-07-19 22:36:37,780 - WARNING - ❌ Error loading DKT model with proper method: Unknown layer: 'NotEqual'. Please ensure you are using a `keras.utils.custom_object_scope` and that this object is included in the scope. See https://www.tensorflow.org/guide/keras/save_and_serialize#registering_the_custom_object for details.
2025-07-19 22:36:37,781 - INFO - ✅ DKT model loaded as dictionary with wrapper
2025-07-19 22:36:37,782 - INFO - Loaded 3 models: ['bkt', 'pfa', 'dkt']
2025-07-19 22:36:52,957 - INFO - Evaluating individual models...
2025-07-19 22:36:52,969 - INFO - Using entire test set: 65128 samples for evaluation
2025-07-19 22:36:52,970 - INFO - Evaluating bkt model...
2025-07-19 22:36:56,942 - INFO - bkt metrics: {'accuracy': 0.6065, 'precision': 0.7253, 'recall': 0.6993, 'f1_score': 0.7121, 'auc_roc': 0.5663}
2025-07-19 22:36:56,943 - INFO - Evaluating pfa model...
2025-07-19 22:41:48,445 - INFO - pfa metrics: {'accuracy': 0.6499, 'precision': 0.7552, 'recall': 0.7352, 'f1_score': 0.7451, 'auc_roc': 0.6289}
2025-07-19 22:41:48,445 - INFO - Evaluating dkt model...
2025-07-19 22:46:31,707 - INFO - dkt metrics: {'accuracy': 0.6447, 'precision': 0.7566, 'recall': 0.7216, 'f1_score': 0.7387, 'auc_roc': 0.622}
2025-07-19 22:52:54,617 - INFO - Training ensemble models...
2025-07-19 22:52:54,642 - INFO - Using entire dataset: 260509 train, 65128 test samples
2025-07-19 22:52:54,643 - INFO - Training weighted average ensemble...
2025-07-19 22:52:54,644 - INFO - Training ensemble model using weighted_average...
2025-07-19 22:52:54,645 - INFO - Training weighted average ensemble...
2025-07-19 22:52:54,647 - INFO - Getting predictions from bkt model...
2025-07-19 22:59:13,814 - INFO - Getting predictions from pfa model...
2025-07-19 23:01:47,008 - INFO - Getting predictions from dkt model...
2025-07-19 23:06:48,267 - INFO - Getting predictions from bkt model...
2025-07-19 23:07:54,006 - INFO - Getting predictions from pfa model...
2025-07-19 23:08:31,130 - INFO - Getting predictions from dkt model...
2025-07-19 23:09:20,570 - INFO - Optimal weights: {'bkt': 0.8, 'pfa': 0.1, 'dkt': 0.09999999999999995}
2025-07-19 23:09:20,571 - INFO - Ensemble training completed
2025-07-19 23:09:20,572 - INFO - Weighted average metrics: {'accuracy': 0.6825175039921385, 'precision': 0.7388396751245421, 'recall': 0.8410414827890556, 'f1_score': 0.7866348842728745, 'auc': 0.6095824961555023}
2025-07-19 23:09:20,572 - INFO - Training stacking ensemble...
2025-07-19 23:09:20,573 - INFO - Training ensemble model using stacking...
2025-07-19 23:09:20,575 - INFO - Training stacking ensemble...
2025-07-19 23:09:20,576 - INFO - Getting predictions from bkt model...
2025-07-19 23:15:58,210 - INFO - Getting predictions from pfa model...
2025-07-19 23:18:31,900 - INFO - Getting predictions from dkt model...
2025-07-19 23:23:26,875 - INFO - Getting predictions from bkt model...
2025-07-19 23:24:37,474 - INFO - Getting predictions from pfa model...
2025-07-19 23:25:15,691 - INFO - Getting predictions from dkt model...
2025-07-19 23:26:05,838 - INFO - Ensemble training completed
2025-07-19 23:26:05,839 - INFO - Stacking metrics: {'accuracy': 0.712612086967203, 'precision': 0.7351708774597338, 'recall': 0.9175198587819947, 'f1_score': 0.8162856666110462, 'auc': 0.6150008974059177}
2025-07-19 23:26:05,839 - INFO - Training voting ensemble...
2025-07-19 23:26:05,840 - INFO - Training ensemble model using voting...
2025-07-19 23:26:05,843 - INFO - Training voting ensemble...
2025-07-19 23:26:05,844 - INFO - Getting predictions from bkt model...
2025-07-19 23:27:15,241 - INFO - Getting predictions from pfa model...
2025-07-19 23:27:53,364 - INFO - Getting predictions from dkt model...
2025-07-19 23:28:42,527 - INFO - Ensemble training completed
2025-07-19 23:28:42,528 - INFO - Voting metrics: {'accuracy': 0.48049993858248374, 'precision': 0.7396911519198665, 'recall': 0.39106354810238303, 'f1_score': 0.5116339491916859, 'auc': 0.5380953847135501}
2025-07-19 23:39:10,013 - INFO - Saving ensemble models to ../models/outputs...
2025-07-19 23:39:10,017 - INFO - Ensemble model saved to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:39:10,018 - INFO - ✅ Saved weighted_average ensemble to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:39:10,020 - INFO - Ensemble model saved to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:39:10,021 - INFO - ✅ Saved stacking ensemble to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:39:10,023 - INFO - Ensemble model saved to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:39:10,024 - INFO - ✅ Saved voting ensemble to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:39:10,026 - INFO - Ensemble model saved to ../models/outputs/ensemble_model.joblib
2025-07-19 23:39:10,027 - INFO - ✅ Saved best ensemble (stacking) as main ensemble model
2025-07-19 23:39:10,028 - INFO - Generating comparison report...
2025-07-19 23:39:10,031 - INFO - ✅ Report saved to ../models/outputs/ensemble_comparison_report.json
2025-07-19 23:39:50,579 - INFO - Saving ensemble models to ../models/outputs...
2025-07-19 23:39:50,584 - INFO - Ensemble model saved to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:39:50,585 - INFO - ✅ Saved weighted_average ensemble to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:39:50,588 - INFO - Ensemble model saved to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:39:50,589 - INFO - ✅ Saved stacking ensemble to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:39:50,592 - INFO - Ensemble model saved to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:39:50,592 - INFO - ✅ Saved voting ensemble to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:39:50,595 - INFO - Ensemble model saved to ../models/outputs/ensemble_model.joblib
2025-07-19 23:39:50,596 - INFO - ✅ Saved best ensemble (stacking) as main ensemble model
2025-07-19 23:39:50,597 - INFO - Generating comparison report...
2025-07-19 23:40:12,941 - INFO - Saving ensemble models to ../models/outputs...
2025-07-19 23:40:12,944 - INFO - Ensemble model saved to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:40:12,945 - INFO - ✅ Saved weighted_average ensemble to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:40:12,948 - INFO - Ensemble model saved to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:40:12,949 - INFO - ✅ Saved stacking ensemble to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:40:12,950 - INFO - Ensemble model saved to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:40:12,951 - INFO - ✅ Saved voting ensemble to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:40:12,953 - INFO - Ensemble model saved to ../models/outputs/ensemble_model.joblib
2025-07-19 23:40:12,954 - INFO - ✅ Saved best ensemble (stacking) as main ensemble model
2025-07-19 23:40:12,955 - INFO - Generating comparison report...
2025-07-19 23:41:42,061 - INFO - Saving ensemble models to ../models/outputs...
2025-07-19 23:41:42,067 - INFO - Ensemble model saved to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:41:42,069 - INFO - ✅ Saved weighted_average ensemble to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:41:42,073 - INFO - Ensemble model saved to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:41:42,074 - INFO - ✅ Saved stacking ensemble to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:41:42,076 - INFO - Ensemble model saved to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:41:42,077 - INFO - ✅ Saved voting ensemble to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:41:42,081 - INFO - Ensemble model saved to ../models/outputs/ensemble_model.joblib
2025-07-19 23:41:42,083 - INFO - ✅ Saved best ensemble (stacking) as main ensemble model
2025-07-19 23:41:42,084 - INFO - Generating comparison report...
2025-07-19 23:42:19,264 - INFO - Saving ensemble models to ../models/outputs...
2025-07-19 23:42:19,267 - INFO - Ensemble model saved to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:42:19,269 - INFO - ✅ Saved weighted_average ensemble to ../models/outputs/ensemble_weighted_average_model.joblib
2025-07-19 23:42:19,271 - INFO - Ensemble model saved to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:42:19,272 - INFO - ✅ Saved stacking ensemble to ../models/outputs/ensemble_stacking_model.joblib
2025-07-19 23:42:19,274 - INFO - Ensemble model saved to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:42:19,275 - INFO - ✅ Saved voting ensemble to ../models/outputs/ensemble_voting_model.joblib
2025-07-19 23:42:19,278 - INFO - Ensemble model saved to ../models/outputs/ensemble_model.joblib
2025-07-19 23:42:19,278 - INFO - ✅ Saved best ensemble (stacking) as main ensemble model
2025-07-19 23:42:19,279 - INFO - Generating comparison report...
