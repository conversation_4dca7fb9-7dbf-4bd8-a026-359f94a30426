{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Knowledge Tracing Prediction Demo\n", "\n", "This notebook demonstrates how to use the knowledge tracing prediction system to predict student knowledge states and analyze learning mastery. It showcases real-time prediction capabilities for adaptive learning applications.\n", "\n", "## Features\n", "- Load and use trained KT models (BKT, PFA, DKT, Ensemble)\n", "- Predict student performance for specific skills\n", "- Analyze learning mastery states\n", "- Compare different model predictions\n", "- Simulate real-time learning scenarios\n", "\n", "## Use Cases\n", "- Real-time student assessment\n", "- Adaptive learning path recommendations\n", "- Learning analytics and progress tracking\n", "- Educational intervention triggers"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-20 20:31:30.592747: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📚 Libraries imported successfully!\n", "Current working directory: /home/<USER>/workspace/AClass/Final/notebooks\n"]}], "source": ["# Import required libraries\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add pipeline directory to path\n", "sys.path.append('../pipeline')\n", "\n", "# Import prediction module\n", "from prediction import KTPredictor\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📚 Libraries imported successfully!\")\n", "print(f\"Current working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Initialize Prediction System"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Initializing Knowledge Tracing Predictor...\n", "\n", "📊 Loading trained models...\n", "\n", "✅ Models loaded successfully!\n", "Available models: ['bkt', 'pfa', 'dkt', 'ensemble']\n", "\n", "🎯 Ready for prediction with 4 models!\n"]}], "source": ["# Initialize the knowledge tracing predictor\n", "print(\"🚀 Initializing Knowledge Tracing Predictor...\")\n", "predictor = KTPredictor(models_dir=\"../models/outputs\")\n", "\n", "# Load trained models\n", "print(\"\\n📊 Loading trained models...\")\n", "load_status = predictor.load_models()\n", "\n", "print(f\"\\n✅ Models loaded successfully!\")\n", "loaded_models = [model for model, status in load_status.items() if status]\n", "print(f\"Available models: {loaded_models}\")\n", "\n", "if not loaded_models:\n", "    print(\"⚠️ No models loaded. Please ensure models are trained and saved.\")\n", "else:\n", "    print(f\"\\n🎯 Ready for prediction with {len(loaded_models)} models!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Basic Prediction Examples"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📈 Example 1: Single Prediction\n", "========================================\n", "Student history: [1, 0, 1, 1, 0, 1]\n", "Target skill: Median\n", "Recent performance: 0.67\n", "\n", "Predictions from different models:\n", "  BKT         : 0.580\n", "  PFA         : 0.600\n", "  DKT         : 0.671\n", "  ENSEMBLE    : 0.571\n"]}], "source": ["# Example 1: Single prediction\n", "print(\"📈 Example 1: Single Prediction\")\n", "print(\"=\" * 40)\n", "\n", "# Student interaction history (1=correct, 0=incorrect)\n", "user_history = [1, 0, 1, 1, 0, 1]\n", "target_skill = \"Median\"\n", "\n", "print(f\"Student history: {user_history}\")\n", "print(f\"Target skill: {target_skill}\")\n", "print(f\"Recent performance: {np.mean(user_history[-3:]):.2f}\")\n", "\n", "# Get predictions from all available models\n", "print(\"\\nPredictions from different models:\")\n", "for model_name in loaded_models:\n", "    try:\n", "        prediction = predictor.predict_student_performance(\n", "            user_history=user_history,\n", "            skill=target_skill,\n", "            model_type=model_name\n", "        )\n", "        print(f\"  {model_name.upper():12}: {prediction:.3f}\")\n", "    except Exception as e:\n", "        print(f\"  {model_name.upper():12}: Error - {e}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Example 2: Model Comparison Visualization\n", "=============================================\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📋 Summary Statistics:\n", "                              mean    std\n", "<PERSON><PERSON>                        \n", "BKT      Average Student     0.614  0.114\n", "         High Performer      0.614  0.114\n", "         Improving Student   0.614  0.114\n", "         Struggling Student  0.614  0.114\n", "DKT      Average Student     0.671  0.000\n", "         High Performer      0.788  0.000\n", "         Improving Student   0.646  0.000\n", "         Struggling Student  0.452  0.000\n", "ENSEMBLE Average Student     0.590  0.066\n", "         High Performer      0.579  0.066\n", "         Improving Student   0.592  0.066\n", "         Struggling Student  0.611  0.065\n", "PFA      Average Student     0.600  0.000\n", "         High Performer      0.800  0.000\n", "         Improving Student   0.600  0.000\n", "         Struggling Student  0.400  0.000\n"]}], "source": ["# Example 2: Model comparison visualization\n", "print(\"\\n📊 Example 2: Model Comparison Visualization\")\n", "print(\"=\" * 45)\n", "\n", "# Test different scenarios\n", "scenarios = {\n", "    \"Struggling Student\": [0, 0, 1, 0, 0, 1],\n", "    \"Average Student\": [1, 0, 1, 1, 0, 1],\n", "    \"High Performer\": [1, 1, 1, 1, 0, 1],\n", "    \"Improving Student\": [0, 0, 0, 1, 1, 1]\n", "}\n", "\n", "skills = [\"Median\", \"Mean\", \"Nets of 3D Figures\", \"Linear Equations\"]\n", "\n", "# Create comparison data\n", "comparison_data = []\n", "for scenario_name, history in scenarios.items():\n", "    for skill in skills:\n", "        for model_name in loaded_models:\n", "            try:\n", "                prediction = predictor.predict_student_performance(\n", "                    user_history=history,\n", "                    skill=skill,\n", "                    model_type=model_name\n", "                )\n", "                comparison_data.append({\n", "                    'Scenario': scenario_name,\n", "                    'Skill': skill,\n", "                    'Model': model_name.upper(),\n", "                    'Prediction': prediction,\n", "                    'Recent_Performance': np.mean(history[-3:])\n", "                })\n", "            except Exception as e:\n", "                print(f\"Error with {model_name} for {scenario_name}-{skill}: {e}\")\n", "\n", "# Convert to DataFrame\n", "comparison_df = pd.DataFrame(comparison_data)\n", "\n", "if not comparison_df.empty:\n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    axes = axes.flatten()\n", "    \n", "    for i, skill in enumerate(skills):\n", "        skill_data = comparison_df[comparison_df['Skill'] == skill]\n", "        if not skill_data.empty:\n", "            sns.barplot(data=skill_data, x='Scenario', y='Prediction', hue='Model', ax=axes[i])\n", "            axes[i].set_title(f'{skill} - Model Predictions', fontweight='bold')\n", "            axes[i].set_xlabel('Student Scenario')\n", "            axes[i].set_ylabel('Predicted Success Probability')\n", "            axes[i].tick_params(axis='x', rotation=45)\n", "            axes[i].legend(title='Model')\n", "            axes[i].set_ylim(0, 1)\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle('Knowledge Tracing Model Predictions Comparison', fontsize=16, fontweight='bold', y=1.02)\n", "    plt.show()\n", "    \n", "    print(\"\\n📋 Summary Statistics:\")\n", "    print(comparison_df.groupby(['Model', 'Sc<PERSON><PERSON>'])['Prediction'].agg(['mean', 'std']).round(3))\n", "else:\n", "    print(\"No comparison data available.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Learning Mastery State Analysis"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🎯 Learning Mastery State Analysis\n", "========================================\n", "Using ENSEMBLE model for mastery analysis\n", "\n", "📊 Student A Mastery Analysis:\n", "------------------------------\n", "  Median          🟠 Developing   (Score: 0.549)\n", "    💡 📖 Review Median fundamentals and practice\n", "  Mean            🟠 Developing   (Score: 0.486)\n", "    💡 📖 Review Mean fundamentals and practice\n", "  Mode            🟡 Proficient   (Score: 0.706)\n", "    💡 📚 Practice more complex Mode problems\n", "  Linear Equations 🟡 Proficient   (Score: 0.696)\n", "    💡 📚 Practice more complex Linear Equations problems\n", "\n", "📊 Student B Mastery Analysis:\n", "------------------------------\n", "  Median          🟠 Developing   (Score: 0.552)\n", "    💡 📖 Review Median fundamentals and practice\n", "  Mean            🟠 Developing   (Score: 0.508)\n", "    💡 📖 Review Mean fundamentals and practice\n", "  Mode            🟡 Proficient   (Score: 0.685)\n", "    💡 📚 Practice more complex Mode problems\n", "  Linear Equations 🟡 Proficient   (Score: 0.631)\n", "    💡 📚 Practice more complex Linear Equations problems\n", "\n", "📊 Student C Mastery Analysis:\n", "------------------------------\n", "  Median          🟡 Proficient   (Score: 0.613)\n", "    💡 📚 Practice more complex Median problems\n", "  Mean            🟠 Developing   (Score: 0.559)\n", "    💡 📖 Review Mean fundamentals and practice\n", "  Mode            🟡 Proficient   (Score: 0.708)\n", "    💡 📚 Practice more complex Mode problems\n", "  Linear Equations 🟡 Proficient   (Score: 0.699)\n", "    💡 📚 Practice more complex Linear Equations problems\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Mastery Distribution:\n", "  Proficient  :  7 ( 58.3%)\n", "  Developing  :  5 ( 41.7%)\n"]}], "source": ["# Learning mastery analysis\n", "print(\"🎯 Learning Mastery State Analysis\")\n", "print(\"=\" * 40)\n", "\n", "def analyze_mastery_state(prediction_score):\n", "    \"\"\"Analyze learning mastery based on prediction score\"\"\"\n", "    if prediction_score >= 0.8:\n", "        return \"Mastered\", \"🟢\"\n", "    elif prediction_score >= 0.6:\n", "        return \"Proficient\", \"🟡\"\n", "    elif prediction_score >= 0.4:\n", "        return \"Developing\", \"🟠\"\n", "    else:\n", "        return \"Struggling\", \"🔴\"\n", "\n", "def get_learning_recommendations(mastery_state, skill):\n", "    \"\"\"Get learning recommendations based on mastery state\"\"\"\n", "    recommendations = {\n", "        \"Mastered\": f\"✅ Move to advanced {skill} topics or related skills\",\n", "        \"Proficient\": f\"📚 Practice more complex {skill} problems\",\n", "        \"Developing\": f\"📖 Review {skill} fundamentals and practice\",\n", "        \"Struggling\": f\"🆘 Provide Medianal support and scaffolding for {skill}\"\n", "    }\n", "    return recommendations.get(mastery_state, \"Continue practicing\")\n", "\n", "# Analyze different student profiles\n", "student_profiles = {\n", "    \"Student A\": {\n", "        \"Median\": [1, 1, 1, 1, 1],\n", "        \"Mean\": [1, 0, 1, 1, 1],\n", "        \"Mode\": [0, 1, 0, 1, 0],\n", "        \"Linear Equations\": [0, 0, 1, 0, 0]\n", "    },\n", "    \"Student B\": {\n", "        \"Median\": [1, 1, 0, 1, 1],\n", "        \"Mean\": [0, 1, 1, 0, 1],\n", "        \"Mode\": [1, 1, 1, 1, 0],\n", "        \"Linear Equations\": [1, 0, 1, 1, 1]\n", "    },\n", "    \"Student C\": {\n", "        \"Median\": [0, 0, 0, 1, 0],\n", "        \"Mean\": [0, 0, 1, 0, 0],\n", "        \"Mode\": [0, 0, 0, 0, 1],\n", "        \"Linear Equations\": [0, 0, 0, 0, 0]\n", "    }\n", "}\n", "\n", "# Use the best available model for analysis\n", "best_model = 'ensemble' if 'ensemble' in loaded_models else loaded_models[0]\n", "print(f\"Using {best_model.upper()} model for mastery analysis\\n\")\n", "\n", "mastery_results = []\n", "\n", "for student_name, skills_data in student_profiles.items():\n", "    print(f\"📊 {student_name} Mastery Analysis:\")\n", "    print(\"-\" * 30)\n", "    \n", "    for skill, history in skills_data.items():\n", "        try:\n", "            prediction = predictor.predict_student_performance(\n", "                user_history=history,\n", "                skill=skill,\n", "                model_type=best_model\n", "            )\n", "            \n", "            mastery_state, emoji = analyze_mastery_state(prediction)\n", "            recommendation = get_learning_recommendations(mastery_state, skill)\n", "            \n", "            print(f\"  {skill:15} {emoji} {mastery_state:12} (Score: {prediction:.3f})\")\n", "            print(f\"    💡 {recommendation}\")\n", "            \n", "            mastery_results.append({\n", "                'Student': student_name,\n", "                'Skill': skill,\n", "                'Prediction': prediction,\n", "                'Mastery_State': mastery_state,\n", "                'Recent_Performance': np.mean(history[-3:])\n", "            })\n", "            \n", "        except Exception as e:\n", "            print(f\"  {skill:15} ❌ Error: {e}\")\n", "    \n", "    print()\n", "\n", "# Create mastery visualization\n", "if mastery_results:\n", "    mastery_df = pd.DataFrame(mastery_results)\n", "    \n", "    # Create heatmap of mastery states\n", "    pivot_df = mastery_df.pivot(index='Student', columns='Skill', values='Prediction')\n", "    \n", "    plt.figure(figsize=(10, 6))\n", "    sns.heatmap(pivot_df, annot=True, cmap='RdYlGn', center=0.5, \n", "                fmt='.3f', cbar_kws={'label': 'Predicted Success Probability'})\n", "    plt.title('Student Mastery Heatmap', fontsize=14, fontweight='bold')\n", "    plt.xlabel('Skills')\n", "    plt.ylabel('Students')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"\\n📈 Mastery Distribution:\")\n", "    mastery_counts = mastery_df['Mastery_State'].value_counts()\n", "    for state, count in mastery_counts.items():\n", "        percentage = (count / len(mastery_df)) * 100\n", "        print(f\"  {state:12}: {count:2d} ({percentage:5.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Real-Time Learning Simulation"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n", "PFA sklearn model error: X has 2 features, but LogisticRegression is expecting 118 features as input.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔄 Real-Time Learning Simulation\n", "========================================\n", "🎯 Simulating learning session for 'Median' using ENSEMBLE model\n", "Initial history: [0, 1, 0, 1, 0]\n", "Initial performance: 0.40\n", "\n", "Interaction  1: ❌ Incorrect | Prediction: 0.599 | State: 🟠 Developing\n", "Interaction  2: ✅ Correct | Prediction: 0.614 | State: 🟡 Proficient\n", "Interaction  3: ✅ Correct | Prediction: 0.588 | State: 🟠 Developing\n", "Interaction  4: ❌ Incorrect | Prediction: 0.578 | State: 🟠 Developing\n", "Interaction  5: ❌ Incorrect | Prediction: 0.598 | State: 🟠 Developing\n", "Interaction  6: ❌ Incorrect | Prediction: 0.605 | State: 🟡 Proficient\n", "Interaction  7: ✅ Correct | Prediction: 0.605 | State: 🟡 Proficient\n", "Interaction  8: ❌ Incorrect | Prediction: 0.591 | State: 🟠 Developing\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Session Summary:\n", "Initial prediction: 0.599\n", "Final prediction: 0.591\n", "Improvement: -0.008\n", "Session accuracy: 0.38\n", "Total interactions: 8\n", "Final mastery state: 🟠 <PERSON><PERSON>ping\n"]}], "source": ["# Real-time learning simulation\n", "print(\"🔄 Real-Time Learning Simulation\")\n", "print(\"=\" * 40)\n", "\n", "def simulate_learning_session(predictor, initial_history, skill, model_type, n_interactions=10):\n", "    \"\"\"Simulate a real-time learning session\"\"\"\n", "    history = initial_history.copy()\n", "    predictions = []\n", "    interactions = []\n", "    \n", "    print(f\"🎯 Simulating learning session for '{skill}' using {model_type.upper()} model\")\n", "    print(f\"Initial history: {initial_history}\")\n", "    print(f\"Initial performance: {np.mean(initial_history):.2f}\\n\")\n", "    \n", "    for i in range(n_interactions):\n", "        # Get current prediction\n", "        try:\n", "            current_prediction = predictor.predict_student_performance(\n", "                user_history=history,\n", "                skill=skill,\n", "                model_type=model_type\n", "            )\n", "            predictions.append(current_prediction)\n", "            \n", "            # Simulate student response based on prediction + some randomness\n", "            # Higher prediction = higher chance of correct response\n", "            response_prob = current_prediction * 0.8 + np.random.normal(0, 0.1)\n", "            response_prob = max(0.1, min(0.9, response_prob))  # Clamp between 0.1 and 0.9\n", "            \n", "            student_response = 1 if np.random.random() < response_prob else 0\n", "            history.append(student_response)\n", "            interactions.append(student_response)\n", "            \n", "            # Real-time feedback\n", "            status = \"✅ Correct\" if student_response == 1 else \"❌ Incorrect\"\n", "            mastery_state, emoji = analyze_mastery_state(current_prediction)\n", "            \n", "            print(f\"Interaction {i+1:2d}: {status} | Prediction: {current_prediction:.3f} | State: {emoji} {mastery_state}\")\n", "            \n", "            # Adaptive feedback\n", "            if current_prediction < 0.4 and i > 2:\n", "                print(f\"              💡 Suggestion: Provide Medianal scaffolding\")\n", "            elif current_prediction > 0.8:\n", "                print(f\"              🚀 Suggestion: Increase difficulty level\")\n", "                \n", "        except Exception as e:\n", "            print(f\"Interaction {i+1:2d}: Error - {e}\")\n", "            predictions.append(0.5)\n", "            interactions.append(0)\n", "    \n", "    return history, predictions, interactions\n", "\n", "# Run simulation\n", "if loaded_models:\n", "    initial_student_history = [0, 1, 0, 1, 0]  # Mixed performance\n", "    target_skill = \"Median\"\n", "    model_to_use = 'ensemble' if 'ensemble' in loaded_models else loaded_models[0]\n", "    \n", "    final_history, prediction_trajectory, new_interactions = simulate_learning_session(\n", "        predictor=predictor,\n", "        initial_history=initial_student_history,\n", "        skill=target_skill,\n", "        model_type=model_to_use,\n", "        n_interactions=8\n", "    )\n", "    \n", "    # Visualize learning trajectory\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # Plot 1: Prediction trajectory\n", "    plt.subplot(2, 1, 1)\n", "    interaction_numbers = range(1, len(prediction_trajectory) + 1)\n", "    plt.plot(interaction_numbers, prediction_trajectory, 'b-o', linewidth=2, markersize=6)\n", "    plt.axhline(y=0.8, color='g', linestyle='--', alpha=0.7, label='Mastery Threshold')\n", "    plt.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='Proficiency Threshold')\n", "    plt.axhline(y=0.4, color='r', linestyle='--', alpha=0.7, label='Struggling Threshold')\n", "    plt.xlabel('Interaction Number')\n", "    plt.ylabel('Predicted Success Probability')\n", "    plt.title(f'Learning Trajectory for {target_skill} ({model_to_use.upper()} Model)', fontweight='bold')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    plt.ylim(0, 1)\n", "    \n", "    # Plot 2: Student responses\n", "    plt.subplot(2, 1, 2)\n", "    colors = ['red' if x == 0 else 'green' for x in new_interactions]\n", "    plt.bar(interaction_numbers, new_interactions, color=colors, alpha=0.7)\n", "    plt.xlabel('Interaction Number')\n", "    plt.ylabel('Student Response (1=Correct, 0=Incorrect)')\n", "    plt.title('Student Responses During Session', fontweight='bold')\n", "    plt.ylim(-0.1, 1.1)\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Session summary\n", "    print(f\"\\n📊 Session Summary:\")\n", "    print(f\"Initial prediction: {prediction_trajectory[0]:.3f}\")\n", "    print(f\"Final prediction: {prediction_trajectory[-1]:.3f}\")\n", "    print(f\"Improvement: {prediction_trajectory[-1] - prediction_trajectory[0]:+.3f}\")\n", "    print(f\"Session accuracy: {np.mean(new_interactions):.2f}\")\n", "    print(f\"Total interactions: {len(new_interactions)}\")\n", "    \n", "    final_mastery, final_emoji = analyze_mastery_state(prediction_trajectory[-1])\n", "    print(f\"Final mastery state: {final_emoji} {final_mastery}\")\n", "else:\n", "    print(\"No models available for simulation.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Production Integration Example"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏭 Production Integration Example\n", "========================================\n", "\n", "🔄 Simulating Real-Time System:\n", "-----------------------------------\n", "📚 Started session for Student student_123\n", "  Median       ✅ Correct | Prediction: 0.580 | 🟠 Developing\n", "               💡 📖 Review Median concepts before proceeding\n", "  Median       ❌ Incorrect | Prediction: 0.580 | 🟠 Developing\n", "               💡 📖 Review Median concepts before proceeding\n", "  Median       ✅ Correct | Prediction: 0.580 | 🟠 Developing\n", "               💡 📖 Review Median concepts before proceeding\n", "  Mean         ✅ Correct | Prediction: 0.467 | 🟠 Developing\n", "               💡 📖 Review Mean concepts before proceeding\n", "  Mean         ✅ Correct | Prediction: 0.467 | 🟠 Developing\n", "               💡 📖 Review Mean concepts before proceeding\n", "\n", "📊 Session Summary for Student 123:\n", "  Duration: 0.0 minutes\n", "  Interactions: 5\n", "  Accuracy: 0.80\n", "  Final Prediction: 0.467\n", "  Improvement: -0.113\n", "\n", "✅ Prediction demo completed successfully!\n", "\n", "💡 This system can be integrated into:\n", "   - Adaptive learning platforms\n", "   - Intelligent tutoring systems\n", "   - Educational assessment tools\n", "   - Learning analytics dashboards\n"]}], "source": ["# Production integration example\n", "print(\"🏭 Production Integration Example\")\n", "print(\"=\" * 40)\n", "\n", "class RealTimeKTSystem:\n", "    \"\"\"Example of how to integrate KT prediction in a real-time system\"\"\"\n", "    \n", "    def __init__(self, predictor, model_type='ensemble'):\n", "        self.predictor = predictor\n", "        self.model_type = model_type\n", "        self.student_sessions = {}\n", "    \n", "    def start_session(self, student_id, initial_history=None):\n", "        \"\"\"Start a new learning session for a student\"\"\"\n", "        self.student_sessions[student_id] = {\n", "            'history': initial_history or [],\n", "            'start_time': datetime.now(),\n", "            'interactions': 0,\n", "            'predictions': []\n", "        }\n", "        print(f\"📚 Started session for Student {student_id}\")\n", "    \n", "    def record_interaction(self, student_id, skill, response):\n", "        \"\"\"Record a student interaction and get real-time prediction\"\"\"\n", "        if student_id not in self.student_sessions:\n", "            self.start_session(student_id)\n", "        \n", "        session = self.student_sessions[student_id]\n", "        session['history'].append(response)\n", "        session['interactions'] += 1\n", "        \n", "        # Get prediction\n", "        try:\n", "            prediction = self.predictor.predict_student_performance(\n", "                user_history=session['history'],\n", "                skill=skill,\n", "                model_type=self.model_type\n", "            )\n", "            session['predictions'].append(prediction)\n", "            \n", "            # Generate adaptive recommendations\n", "            mastery_state, emoji = analyze_mastery_state(prediction)\n", "            recommendation = self.get_adaptive_recommendation(prediction, skill, session)\n", "            \n", "            return {\n", "                'prediction': prediction,\n", "                'mastery_state': mastery_state,\n", "                'emoji': emoji,\n", "                'recommendation': recommendation,\n", "                'session_progress': session['interactions']\n", "            }\n", "            \n", "        except Exception as e:\n", "            return {'error': str(e)}\n", "    \n", "    def get_adaptive_recommendation(self, prediction, skill, session):\n", "        \"\"\"Generate adaptive learning recommendations\"\"\"\n", "        recent_performance = np.mean(session['history'][-3:]) if len(session['history']) >= 3 else np.mean(session['history'])\n", "        \n", "        if prediction >= 0.8:\n", "            return f\"🚀 Ready for advanced {skill} challenges\"\n", "        elif prediction >= 0.6:\n", "            return f\"📚 Continue with current {skill} difficulty\"\n", "        elif prediction >= 0.4:\n", "            return f\"📖 Review {skill} concepts before proceeding\"\n", "        else:\n", "            return f\"🆘 Provide immediate support for {skill}\"\n", "    \n", "    def get_session_summary(self, student_id):\n", "        \"\"\"Get summary of student session\"\"\"\n", "        if student_id not in self.student_sessions:\n", "            return None\n", "        \n", "        session = self.student_sessions[student_id]\n", "        duration = datetime.now() - session['start_time']\n", "        \n", "        return {\n", "            'duration_minutes': duration.total_seconds() / 60,\n", "            'total_interactions': session['interactions'],\n", "            'accuracy': np.mean(session['history']) if session['history'] else 0,\n", "            'final_prediction': session['predictions'][-1] if session['predictions'] else None,\n", "            'improvement': session['predictions'][-1] - session['predictions'][0] if len(session['predictions']) > 1 else 0\n", "        }\n", "\n", "# Demo the real-time system\n", "if loaded_models:\n", "    rt_system = RealTimeKTSystem(predictor, model_type=loaded_models[0])\n", "    \n", "    # Simulate real-time interactions\n", "    print(\"\\n🔄 Simulating Real-Time System:\")\n", "    print(\"-\" * 35)\n", "    \n", "    # Student interactions\n", "    interactions = [\n", "        (\"student_123\", \"Median\", 1),\n", "        (\"student_123\", \"Median\", 0),\n", "        (\"student_123\", \"Median\", 1),\n", "        (\"student_123\", \"Mean\", 1),\n", "        (\"student_123\", \"Mean\", 1),\n", "    ]\n", "    \n", "    for student_id, skill, response in interactions:\n", "        result = rt_system.record_interaction(student_id, skill, response)\n", "        \n", "        if 'error' not in result:\n", "            status = \"✅ Correct\" if response == 1 else \"❌ Incorrect\"\n", "            print(f\"  {skill:12} {status} | Prediction: {result['prediction']:.3f} | {result['emoji']} {result['mastery_state']}\")\n", "            print(f\"               💡 {result['recommendation']}\")\n", "        else:\n", "            print(f\"  Error: {result['error']}\")\n", "    \n", "    # Session summary\n", "    summary = rt_system.get_session_summary(\"student_123\")\n", "    if summary:\n", "        print(f\"\\n📊 Session Summary for Student 123:\")\n", "        print(f\"  Duration: {summary['duration_minutes']:.1f} minutes\")\n", "        print(f\"  Interactions: {summary['total_interactions']}\")\n", "        print(f\"  Accuracy: {summary['accuracy']:.2f}\")\n", "        print(f\"  Final Prediction: {summary['final_prediction']:.3f}\")\n", "        print(f\"  Improvement: {summary['improvement']:+.3f}\")\n", "else:\n", "    print(\"No models available for real-time system demo.\")\n", "\n", "print(\"\\n✅ Prediction demo completed successfully!\")\n", "print(\"\\n💡 This system can be integrated into:\")\n", "print(\"   - Adaptive learning platforms\")\n", "print(\"   - Intelligent tutoring systems\")\n", "print(\"   - Educational assessment tools\")\n", "print(\"   - Learning analytics dashboards\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}