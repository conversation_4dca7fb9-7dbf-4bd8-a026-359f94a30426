{"timestamp": "2025-07-19T23:39:10.030274", "test_sample_size": "entire_dataset", "models_evaluated": ["bkt", "pfa", "dkt"], "ensemble_methods": ["weighted_average", "stacking", "voting"], "individual_model_metrics": {"bkt": {"accuracy": 0.6065, "precision": 0.7253, "recall": 0.6993, "f1_score": 0.7121, "auc_roc": 0.5663}, "pfa": {"accuracy": 0.6499, "precision": 0.7552, "recall": 0.7352, "f1_score": 0.7451, "auc_roc": 0.6289}, "dkt": {"accuracy": 0.6447, "precision": 0.7566, "recall": 0.7216, "f1_score": 0.7387, "auc_roc": 0.622}}, "ensemble_metrics": {"weighted_average": {"accuracy": 0.6825175039921385, "precision": 0.7388396751245421, "recall": 0.8410414827890556, "f1_score": 0.7866348842728745, "auc": 0.6095824961555023}, "stacking": {"accuracy": 0.712612086967203, "precision": 0.7351708774597338, "recall": 0.9175198587819947, "f1_score": 0.8162856666110462, "auc": 0.6150008974059177}, "voting": {"accuracy": 0.48049993858248374, "precision": 0.7396911519198665, "recall": 0.39106354810238303, "f1_score": 0.5116339491916859, "auc": 0.5380953847135501}}, "best_model": "pfa", "best_ensemble": "stacking", "summary": {"total_models_trained": 3, "total_ensembles_trained": 3, "best_individual_f1": 0.7451, "best_ensemble_f1": 0.8162856666110462}}