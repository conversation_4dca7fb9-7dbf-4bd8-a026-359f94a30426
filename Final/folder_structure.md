# Final Folder Structure

This document describes the structure and purpose of each subfolder and file in the `Final` directory, with a focus on their roles in the modeling and knowledge tracing pipeline.

---

## Top-Level Structure

- **notebooks/**: Jupyter notebooks for data exploration, model training, evaluation, and demonstration.
- **models/**: Scripts and saved models for training and inference.
- **pipeline/**: Core pipeline code for feature engineering, training, evaluation, and prediction.
- **reports/**: Visualizations and analysis results from experiments and model runs.
- **Training/**: (Legacy or auxiliary) training reports and outputs.
- **datasets/**: Raw and processed datasets used for modeling.
- **.ipynb_checkpoints/**: Jupyter notebook autosave files (can be ignored).

---

## Subfolder and File Descriptions

### notebooks/
- **prediction_demo_notebook.ipynb**: Demonstrates end-to-end prediction using trained models.
- **2009_math_dataset.ipynb**: Data exploration and preprocessing for the 2009 math dataset.
- **math_bkt.ipynb**: Bayesian Knowledge Tracing (BKT) model training and analysis.
- **math_dkt.ipynb**: Deep Knowledge Tracing (DKT) model training and analysis.
- **math_pfa.ipynb**: Performance Factors Analysis (PFA) model training and analysis.
- **ensemble_training_notebook.ipynb**: Training and evaluation of ensemble models.
- **ensemble_training.log**: Log file for ensemble model training runs.
- **skills_all.csv**: List of all skills used in the dataset.
- **v0.1/**: Archived or versioned notebooks and data for reproducibility.
- **.ipynb_checkpoints/**: Autosave versions of notebooks.

#### notebooks/v0.1/
- **math_bkt.ipynb, math_dkt.ipynb, math_pfa.ipynb**: Older or experimental versions of model notebooks.
- **2009_math_dataset.ipynb**: Older data exploration notebook.
- **skills_all.csv**: Skills list for v0.1 experiments.

### models/
- **train_ensemble.py**: Script to train ensemble models (e.g., voting, stacking, weighted average).
- **train_dkt.py**: Script to train Deep Knowledge Tracing models.
- **train_pfa.py**: Script to train Performance Factors Analysis models.
- **train_bkt.py**: Script to train Bayesian Knowledge Tracing models.
- **outputs/**: Saved model files and metrics (see below).
- **tmp/**: Temporary files, often for intermediate model artifacts.
- **__pycache__/**: Compiled Python bytecode for faster loading.

#### models/outputs/
- **dkt_model.joblib, dkt_model.h5**: Saved DKT models (joblib for scikit-learn, h5 for Keras/TensorFlow).
- **bkt_model.joblib**: Saved BKT model.
- **pfa_model.joblib**: Saved PFA model.
- **ensemble_model.joblib, ensemble_voting_model.joblib, ensemble_stacking_model.joblib, ensemble_weighted_average_model.joblib**: Saved ensemble models.
- **ensemble_summary.txt, ensemble_comparison_report.json**: Text and JSON reports comparing ensemble model performance.
- **bkt_skills_parameters.csv, pfa_skills_parameters.csv**: Skill-specific parameters learned by BKT and PFA models.
- **model_metrics.json**: Evaluation metrics for trained models.

#### models/tmp/
- **dkt_model.joblib, dkt_model.h5**: Temporary or experimental DKT model files.

### pipeline/
- **kt_training_pipeline.py**: Main pipeline for orchestrating feature engineering, training, and evaluation.
- **kt_model_trainer.py**: Utilities for training knowledge tracing models.
- **kt_ensemble.py**: Code for ensemble model creation and evaluation.
- **kt_evaluation.py**: Evaluation metrics and analysis for model predictions.
- **kt_feature_engineer.py**: Feature engineering for knowledge tracing models.
- **prediction.py**: Script for running predictions using trained models.
- **README.md**: Documentation for the pipeline code and usage.
- **math_training.log**: Log file for math model training runs.
- **outputs/**: Model outputs and metrics (see below).
- **tests/**: Unit and integration tests for the pipeline.
- **__pycache__/**: Compiled Python bytecode.

#### pipeline/outputs/
- **math/**: Model outputs and metrics for math experiments.
  - **model_metrics.json**: Metrics for math models.
  - **bkt_model.joblib**: Saved BKT model for math.

#### pipeline/tests/
- **run_kt_tests.py, test_kt_pipeline.py**: Automated tests for the pipeline and model training.

### reports/
- **math_dataset.png, bkt_progress.png, bkt_fig.png, all_metrics_comparison.png, Recall Comparison.png, Precision Comparison.png, Auc Roc Comparison.png, F1 Score Comparison.png, Accuracy Comparison.png**: Visualizations and plots comparing model performance and skill analysis.
- **math/**: Additional reports and analysis for math experiments.
  - **skills_analysis.csv**: CSV file with skill-level analysis results.
- **.ipynb_checkpoints/**: Autosave versions of report notebooks.

### Training/
- **reports/**: Training run reports (may be legacy or for experiment tracking).
  - **math/**: Placeholder for math training reports (currently empty).

### datasets/
- **math/**: Math-specific datasets.
  - **skills_all.csv**: List of all skills in the math dataset.
  - **skill_builder_data_corrected.csv**: Main dataset for skill builder experiments.

---

## Usage in the Modeling Process

- **notebooks/**: Used for interactive exploration, model development, and result visualization.
- **models/**: Contains scripts for training models and the resulting saved models for inference and evaluation.
- **pipeline/**: The core automation for feature engineering, model training, evaluation, and prediction. Used for reproducible and scalable experiments.
- **reports/**: Stores visualizations and analysis to compare models and understand results.
- **Training/**: (If used) for storing training run outputs and reports.
- **datasets/**: Source data for all modeling and experiments.

---

For more details on any specific file or subfolder, see the README files in the respective directories or the code comments. 