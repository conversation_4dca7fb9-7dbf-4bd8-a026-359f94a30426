This research proposes an adaptive AI framework for personalized learning that guides learners through dynamic knowledge paths to reduce cognitive load and enhance engagement. The system is built on four integrated components: a Data Collection Layer, a Knowledge Tracing Engine, an Adaptive Sequencing Layer, and an Explainable AI Layer—each contributing to real-time, transparent, and scalable personalization.
The Data Collection Layer captures learner activity through assessments and behavioral tracking (e.g., navigation behavior, response time, session duration), enabling a real-time view of learner progress. Micro and cumulative assessments feed data into the system for continuous monitoring. These multimodal data streams feed into our knowledge tracing pipeline that combines three complementary models: Bayesian Knowledge Tracing (BKT), which measures student mastery of a specific skill based on their performance history, considering the probabilities of learning, slipping, and guessing,, Performance Factor Analysis (PFA) based on logistic knowledge tracing providing a probability of correctness, and Deep Knowledge Tracing (DKT) utilizing RNN/LSTM-network for predicting knowledge mastery. These models are combined into an ensemble model that predicts mastery levels and knowledge gaps. These models are dynamically weighted by data density and learning domain, providing real-time mastery estimates that drive adaptive responses. Prior work has shown this ensemble can reach up to 89% accuracy in predicting performance. These insights drive the personalization of content and assessment paths.
The Adaptive Sequencing Layer uses LSTM-based deep learning to generate learning paths tailored to individual learner states to determine optimal next-step content. Sequencing decisions are informed by cognitive load indicators—such as latency, error patterns, and self-reported effort—and adjust dynamically by introducing scaffolding or modifying complexity when overload is detected (threshold index >0.7).
To promote transparency and trust, the Explainable AI Layer provides natural language feedback and visual progress maps. These explanations enhance learner awareness and instructor decision-making through open learner models.
A pilot implementation is planned in two domains: Turkish A1 language learning, and Machine Learning and Data Mining for university students. The system’s effectiveness will be measured using pre/post testing, engagement analytics, and learner satisfaction (e.g., User Satisfaction Inventory). Results are expected to show reduced time-to-mastery (25–30%), increased retention (up to 40%), and improved learning engagement in alignment with prior studies’ results.
This research demonstrates how AI-driven adaptive assessment can transform educational experiences through precise, data-driven personalization while maintaining transparency and pedagogical alignment. By providing this detailed implementation framework, we extend prior studies into a comprehensive, deployable system for next-generation adaptive learning.

