this is the first build of the app
the project is for building an AI-driven adaptive learning platform for turoring that is domain agnostic.
idea:
- build an app that can be used to build a course for any domain
- the course is built by the user, consist of modules, each module is tagged for one or more of the skills 
- the course will have a knowledge domain skills set 

Read all documenation in the folder ./Documentation to build a good understanding of the project

Implement the Phase1 to create the app folder and intial components to run the app blue print


Implmenation:
- build a front end app using react js
- build a back end api using python
- will use a docker container for db mysql db
- backend and frontend will be containerized and running using docker compose

app folder structure:
- app
  - frontend
  - backend
  - docker
  - documentation

Phase 1:
- build the front end app
- build the back end api
- build the docker container for the db
- build the docker compose file


Phase 2:
- create DB scheme and load to mysql DB
- create DB seeder for the app
- build the user management feature
- build the course building feature
- build the module building feature
- build the skill tagging feature
- build the knowledge domain skills set feature

Phase 3:
- build students managment component
-Enhance the course building interface
Implement more detailed module creation with rich content
Add skill dependency mapping
Develop adaptive content sequencing based on knowledge state
Implement assessment creation and evaluation
Build a progress tracking and reporting system